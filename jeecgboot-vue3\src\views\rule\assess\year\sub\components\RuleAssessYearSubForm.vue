<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="RuleAssessYearSubForm">
          <a-row>
            <a-col :span="24">
              <a-form-item label="评估任务主键" v-bind="validateInfos.yearId" id="RuleAssessYearSubForm-yearId" name="yearId">
                <a-input v-model:value="formData.yearId" placeholder="请输入评估任务主键" disabled allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="子任务编号" v-bind="validateInfos.taskCode" id="RuleAssessYearSubForm-taskCode" name="taskCode">
                <a-input v-model:value="formData.taskCode" placeholder="请输入子任务编号" disabled allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="评估机构" v-bind="validateInfos.assessOrgCode" id="RuleAssessYearSubForm-assessOrgCode" name="assessOrgCode">
                <j-select-dept v-model:value="formData.assessOrgCode" disabled :multiple="true" checkStrictly allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="评估部门" v-bind="validateInfos.assessDeptCode" id="RuleAssessYearSubForm-assessDeptCode" name="assessDeptCode">
                <j-select-dept v-model:value="formData.assessDeptCode" disabled :multiple="true" checkStrictly allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item
                label="下年度需修订制度数量"
                v-bind="validateInfos.needEditNumber"
                id="RuleAssessYearSubForm-needEditNumber"
                name="needEditNumber"
              >
                <a-input-number v-model:value="formData.needEditNumber" placeholder="请输入下年度需修订制度数量" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item
                label="下年度需新增制度数量"
                v-bind="validateInfos.needAddNumber"
                id="RuleAssessYearSubForm-needAddNumber"
                name="needAddNumber"
              >
                <a-input-number v-model:value="formData.needAddNumber" placeholder="请输入下年度需新增制度数量" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item
                label="下年度需废止制度数量"
                v-bind="validateInfos.needDeleteNumber"
                id="RuleAssessYearSubForm-needDeleteNumber"
                name="needDeleteNumber"
              >
                <a-input-number v-model:value="formData.needDeleteNumber" placeholder="请输入下年度需废止制度数量" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="反馈人" v-bind="validateInfos.feedbackPerson" id="RuleAssessYearSubForm-feedbackPerson" name="feedbackPerson">
                <j-select-user v-model:value="formData.feedbackPerson" disabled allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="反馈时间" v-bind="validateInfos.feedbackTime" id="RuleAssessYearSubForm-feedbackTime" name="feedbackTime">
                <a-date-picker
                  placeholder="请选择反馈时间"
                  v-model:value="formData.feedbackTime"
                  showTime
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                  disabled
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="任务状态" v-bind="validateInfos.taskStatus" id="RuleAssessYearSubForm-taskStatus" name="taskStatus">
                <j-dict-select-tag
                  v-model:value="formData.taskStatus"
                  dictCode="rule_assess_pilot_sub_task_status"
                  placeholder="请选择任务状态"
                  disabled
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="流程状态" v-bind="validateInfos.processStatus" id="RuleAssessYearSubForm-processStatus" name="processStatus">
                <j-dict-select-tag
                  v-model:value="formData.processStatus"
                  dictCode="rule_assess_year_sub_process_status"
                  placeholder="请选择流程状态"
                  disabled
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
  import JSelectUser from '/@/components/Form/src/jeecg/components/JSelectUser.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../RuleAssessYearSub.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({}) },
    formBpm: { type: Boolean, default: true },
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    yearId: '',
    taskCode: '',
    assessOrgCode: '',
    assessDeptCode: '',
    needEditNumber: undefined,
    needAddNumber: undefined,
    needDeleteNumber: undefined,
    feedbackPerson: '',
    feedbackTime: '',
    taskStatus: '',
    processStatus: '',
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({});
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record: any) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if (record.hasOwnProperty(key)) {
          tmpData[key] = record[key];
        }
      });
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
