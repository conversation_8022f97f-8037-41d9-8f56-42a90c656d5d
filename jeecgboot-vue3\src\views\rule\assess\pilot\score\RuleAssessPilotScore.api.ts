import { defHttp } from "/@/utils/http/axios";
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();
enum Api {
  scoreList = "/rule/assess/pilot/score/list",
  getScoreDetail = "/rule/assess/pilot/score/scoreDetail",
  submitScore = "/rule/assess/pilot/score/submitScore",
  getTextMappings = "/rule/assess/pilot/score/textMappings",

  submitRequest = '/rule/assess/pilot/score/submitRequest',
}

/**
 * 查询评估评分列表（主任务和子任务关联数据）
 * @param params
 */
export const list = (params: any) => defHttp.get({
  url: Api.scoreList,
  params
});

/**
 * 获取制度评分详情
 * @param subId 子任务ID
 */
export const getScoreDetail = (subId: string) => defHttp.get({
  url: Api.getScoreDetail,
  params: { subId }
}, {isTransformResponse: false});

/**
 * 提交制度评分结果
 * @param data 评分数据
 */
export const submitScore = (data: any) => defHttp.post({
  url: Api.submitScore,
  data
}, {isTransformResponse: false});

/**
 * 获取文本映射配置
 */
export const getTextMappings = () => defHttp.get({
  url: Api.getTextMappings
}, {isTransformResponse: false});

/**
 * 提交审核
 * @param params
 * @param handleSuccess
 */
export const submitRequest = (params: any, handleSuccess: Function) => {
  createConfirm({
    iconType: 'warning',
    title: '确认提交审核',
    content: '是否对选中数据提交审核?',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      return defHttp
        .post(
          {
            url: Api.submitRequest,
            data: params,
          },
          { joinParamsToUrl: true }
        )
        .then(() => {
          handleSuccess();
        });
    },
  });
};
