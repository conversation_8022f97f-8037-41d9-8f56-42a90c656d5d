package com.gientech.orr.clue.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gientech.common.process.service.ICommonProcessService;
import com.gientech.kri.indicator.entity.KriIndicatorInfo;
import com.gientech.kri.indicator.entity.KriIndicatorInfoThreshold;
import com.gientech.orr.clue.entity.OrrReportClue;
import com.gientech.orr.clue.mapper.OrrReportClueMapper;
import com.gientech.orr.clue.service.IOrrReportClueService;
import com.gientech.orr.clue.vo.OrrReportClueVo;
import com.gientech.orr.report.regular.entity.OrrRegularReport;
import com.gientech.orr.report.regular.mapper.OrrRegularReportMapper;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import jodd.bean.BeanUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.DateUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Description: 操作风险报告-线索表
 * @Author: jeecg-boot
 * @Date:   2025-07-10
 * @Version: V1.0
 */
@Service
public class OrrReportClueServiceImpl extends ServiceImpl<OrrReportClueMapper, OrrReportClue> implements IOrrReportClueService {

    public static final String businessKey = "orrReportClue";

    @Autowired
    private OrrRegularReportMapper orrRegularReportMapper;

    @Autowired
    private OrrReportClueMapper orrReportClueMapper;

    @Autowired
    private IWorkflowInstanceService instanceService;

    @Autowired
    private ICommonProcessService processService;

    @Autowired
    private IWorkflowTaskService workflowTaskService;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 添加线索
     *
     * @param orrReportClueVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<String> add(OrrReportClueVo orrReportClueVo) {

        OrrReportClue orrReportClue = new OrrReportClue();
        BeanUtils.copyProperties(orrReportClueVo, orrReportClue);

        String reportId = orrReportClueVo.getReportId();

        // 查询报告的信息
        OrrRegularReport orrRegularReport = orrRegularReportMapper.selectById(reportId);
        Date reportYear = orrRegularReport.getReportYear();
        String reportQuarterly = orrRegularReport.getReportQuarterly();

        RLock lock = redissonClient.getLock("orrReportClue_add_lock:" + reportId);
        lock.lock();
        try {
            // 设置编号(CLUE+报告年度+报告季度+编号（001、002、...）)
            String clueNumber = "CLUE" + DateUtils.formatDate(orrRegularReport.getReportYear(), "yyyy") + reportQuarterly;

            QueryWrapper<OrrReportClue> clueQueryWrapper = new QueryWrapper<>();
            clueQueryWrapper.likeRight("clue_number", clueNumber);
            clueQueryWrapper.orderByDesc("clue_number").last("LIMIT 1");

            OrrReportClue oldReportClue = orrReportClueMapper.selectOne(clueQueryWrapper);

            String serialNumber = "001";
            if (null != oldReportClue) {
                String oldClueNumber = oldReportClue.getClueNumber();
                oldClueNumber = oldClueNumber.substring(oldClueNumber.length() - 3);
                serialNumber = String.format("%03d", Integer.parseInt(oldClueNumber) + 1);
            }
            clueNumber += serialNumber;
            orrReportClue.setClueNumber(clueNumber);

            // 设置其它的字段
            orrReportClue.setReportYear(reportYear);
            orrReportClue.setReportQuarterly(reportQuarterly);
            orrReportClue.setReportName(orrRegularReport.getReportName());

            // 添加线索
            orrReportClueMapper.insert(orrReportClue);

            // 更新报告
            OrrRegularReport regularReport = orrReportClueVo.getRegularReport();
            regularReport.setId(reportId);

            orrRegularReportMapper.updateById(regularReport);

        } catch (Exception e) {
            log.error("添加线索错误", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return Result.ok("线索派发成功！");
    }

    /**
     * 批量提交
     *
     * @param ids
     * @return
     */
    public Result<String> batchSubmit(List<String> ids) {

        List<OrrReportClue> orrReportClueList = orrReportClueMapper.selectBatchIds(ids);

        for (OrrReportClue orrReportClue: orrReportClueList) {
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, orrReportClue);

            // 开始工作流
            instanceService.createWorkflowInstance(businessKey, orrReportClue.getId(), variables);
        }

        // 添加处理过程
        processService.saveProcessBatch(businessKey, ids, "提交审核");

        return Result.ok("提交成功，请等待审核！");
    }

    /**
     * 批量撤销
     *
     * @param ids
     * @return
     */
    public Result<String> batchRevoke(List<String> ids) {

        List<OrrReportClue> orrReportClueList = orrReportClueMapper.selectBatchIds(ids);

        for (OrrReportClue orrReportClue: orrReportClueList) {
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, orrReportClue);
            if ("2".equals(orrReportClue.getClueStatus())) {
                // 审核中
                WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, orrReportClue.getId());
                variables.put("revoke", true);
                workflowTaskService.completeTask(workflowTask.getId(), variables);
            }else{
                // 审核通过 开始撤销申请
                instanceService.createWorkflowInstance(businessKey, orrReportClue.getId(), variables);
            }
        }

        // 添加处理过程
        processService.saveProcessBatch(businessKey, ids, "撤销");

        return Result.ok("撤销成功，请等待审核！");
    }

    /**
     * 批量通过
     *
     * @param ids
     * @return
     */
    public Result<String> passBatch(List<String> ids) {

        List<OrrReportClue> orrReportClueList = orrReportClueMapper.selectBatchIds(ids);

        for (OrrReportClue orrReportClue: orrReportClueList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, orrReportClue.getId());
            if (workflowTask != null) {
                Map<String, Object> variables = new HashMap<>();
                variables.put("approve", true);
                variables.put(businessKey, orrReportClue);
                workflowTaskService.completeTask(workflowTask.getId(), variables);
            }
        }

        processService.saveProcessBatch(businessKey, ids, "审核通过");

        return Result.ok("通过成功！");
    }

    /**
     * 批量退回
     *
     * @param ids
     * @return
     */
    public Result<String> returnBatch(List<String> ids) {

        List<OrrReportClue> orrReportClueList = orrReportClueMapper.selectBatchIds(ids);

        for (OrrReportClue orrReportClue: orrReportClueList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, orrReportClue.getId());
            if (workflowTask != null) {
                Map<String, Object> variables = new HashMap<>();
                variables.put("approve", false);
                variables.put(businessKey, orrReportClue);
                workflowTaskService.completeTask(workflowTask.getId(), variables);
            }
        }

        processService.saveProcessBatch(businessKey, ids, "审核退回");

        return Result.ok("退回成功！");
    }
}
