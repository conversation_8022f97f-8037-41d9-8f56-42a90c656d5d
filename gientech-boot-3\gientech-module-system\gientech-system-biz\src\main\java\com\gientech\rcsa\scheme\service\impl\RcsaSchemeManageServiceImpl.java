package com.gientech.rcsa.scheme.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gientech.rcsa.history.entity.RcsaPlanManageHistory;
import com.gientech.rcsa.history.mapper.RcsaPlanManageHistoryMapper;
import com.gientech.rcsa.scheme.entity.RcsaSchemeManage;
import com.gientech.rcsa.scheme.entity.RcsaSchemeMatrixRel;
import com.gientech.rcsa.scheme.mapper.RcsaSchemeManageMapper;
import com.gientech.rcsa.scheme.mapper.RcsaSchemeMatrixRelMapper;
import com.gientech.rcsa.scheme.service.IRcsaSchemeManageService;
import com.gientech.rcsa.task.entity.RcsaTaskBasicInfo;
import com.gientech.rcsa.task.entity.RcsaTaskControlMeasure;
import com.gientech.rcsa.task.entity.RcsaTaskManage;
import com.gientech.rcsa.task.mapper.RcsaTaskManageMapper;
import com.gientech.rcsa.task.service.IRcsaTaskBasicInfoService;
import com.gientech.rcsa.task.service.IRcsaTaskControlMeasureService;
import com.gientech.sr.matrix.entity.SrRiskControlMatrix;
import com.gientech.sr.matrix.entity.SrRiskControlMatrixTache;
import com.gientech.sr.matrix.mapper.SrRiskControlMatrixTacheMapper;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.mapper.SysDepartMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Description: RCSA评估方案表
 * @Author: jeecg-boot
 * @Date:   2025-07-16
 * @Version: V1.0
 */
@Service
public class RcsaSchemeManageServiceImpl extends ServiceImpl<RcsaSchemeManageMapper, RcsaSchemeManage> implements IRcsaSchemeManageService {

    @Autowired
    private RcsaSchemeManageMapper schemeManageMapper;
    @Autowired
    private RcsaPlanManageHistoryMapper historyMapper;
    @Autowired
    private RcsaSchemeMatrixRelMapper relMapper;
    @Autowired
    private RcsaTaskManageMapper taskManageMapper;
    @Autowired
    private SrRiskControlMatrixTacheMapper matrixTacheMapper;
    @Autowired
    private IRcsaTaskBasicInfoService basicInfoService;
    @Autowired
    private IRcsaTaskControlMeasureService measureService;
    @Autowired
    private IWorkflowInstanceService workflowInstanceService;
    @Autowired
    private IWorkflowTaskService workflowTaskService;
    private final String businessKey = "rcsaSchemeProcess";
    @Autowired
    private SysDepartMapper departMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> submit(RcsaSchemeManage schemeManage) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();

        // 需要校验数据
//        RcsaSchemeManage manage = schemeManageMapper.selectById(schemeManage.getId());
//        if (StringUtils.isBlank(manage.getSchemeTitle())) {
//            return Result.error("方案标题不能为空！");
//        }
//        if (StringUtils.isBlank(manage.getEnterContact())) {
//            return Result.error("录入人联系方式不能为空！");
//        }
//        // 校验评估信息
//        QueryWrapper wrapper = new QueryWrapper();
//        wrapper.eq("scheme_id", schemeManage.getId());
//        wrapper.orderByAsc("sort_num");
//        List<RcsaSchemeMatrixRel> pageList = relMapper.selectList(wrapper);
//        int index = 1;
//        for (RcsaSchemeMatrixRel rel : pageList) {
//            // 数据校验
//            String message = judgeRel(rel, index);
//            if (StringUtils.isNotBlank(message)) {
//                return Result.error(message);
//            }
//            index++;
//        }

        schemeManage.setSchemeState("2");
        schemeManageMapper.updateById(schemeManage);

        // 入库操作记录表
        RcsaPlanManageHistory history = new RcsaPlanManageHistory();
        history.setPlanManageId(schemeManage.getId());
        history.setIsAudit("0");
        history.setOperateTime(new Date());
        history.setState("2");
        history.setOperateUser(userName);
        history.setOperateType("提交审核");
        history.setOperateDepart(sysUser.getOrgId());
        historyMapper.insert(history);

        return Result.ok("提交成功");

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditPass(RcsaSchemeManage schemeManage) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        schemeManage.setSchemeState("3");
        schemeManage.setEffectTime(new Date());
        schemeManageMapper.updateById(schemeManage);
        // 入库操作记录表
        RcsaPlanManageHistory history = new RcsaPlanManageHistory();
        history.setPlanManageId(schemeManage.getId());
        history.setIsAudit("1");
        history.setOperateTime(new Date());
        history.setState("3");
        history.setOperateUser(userName);
        history.setOperateType("审核通过");
        history.setOperateDepart(sysUser.getOrgId());
        historyMapper.insert(history);

        // 通过之后生成评估任务
        RcsaSchemeManage manage = schemeManageMapper.selectById(schemeManage.getId());
        // 获取方案编号
        String schemeCode = manage.getSchemeCode();

        // 校验评估信息
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("scheme_id", schemeManage.getId());
        wrapper.orderByAsc("sort_num");
        List<RcsaSchemeMatrixRel> matrixRelList = relMapper.selectList(wrapper);

        int taskIndex = 1;
        for (RcsaSchemeMatrixRel matrixRel : matrixRelList) {
            // 查询环节信息（去重）
            List<SrRiskControlMatrixTache> matrixTacheList = matrixTacheMapper.getDistinctTacheList(matrixRel.getMatrixId());
            // 先判断是否存在分行
            String isFitBranch = matrixRel.getIsFitBranch() == null ? "" : matrixRel.getIsFitBranch();
            // 定义全部的分行的机构列表
            List<String> departAllList = new ArrayList<>();
            // 定义分行的机构列表
            List<String> departList = new ArrayList<>();
            departAllList.add(manage.getEvaluateDepart());
            if ("1".equals(isFitBranch)) {
                departAllList.addAll(Arrays.asList(matrixRel.getBranchNo().split(",")));
                departList.addAll(Arrays.asList(matrixRel.getBranchNo().split(",")));
            }
            // 首先生成本机构的评估任务
            // 评估编号：方案编号+PG+2位序号（如： 202301FA01PG01）

            RcsaTaskManage taskManage = generateTask(schemeCode, taskIndex, matrixRel, manage, manage.getEvaluateDepart());
            // 入库总行部门的评估任务
            taskManageMapper.insert(taskManage);
            taskIndex++;
            //todo：huanjing 生成总行部门的评估基本信息，需要提取最近一次评估结果
            List<RcsaTaskBasicInfo> basicInfoBMList = getMatrixTacheSelfList(taskManage, matrixRel, matrixTacheList);
            if (basicInfoBMList.size()>0) {
                basicInfoService.saveBatch(basicInfoBMList);
            }

            // 查询控制措施
            for (RcsaTaskBasicInfo basicInfo : basicInfoBMList) {
                SrRiskControlMatrixTache tache = new SrRiskControlMatrixTache();
                tache.setFatherId(matrixRel.getMatrixId());
                tache.setTacheName(basicInfo.getTacheName());
                tache.setRiskDescription(basicInfo.getRiskDescription());
                List<RcsaTaskControlMeasure> measureList = matrixTacheMapper.queryControlNoPageList(tache);
                for (RcsaTaskControlMeasure measure : measureList) {
                    measure.setTaskId(taskManage.getId());
                    measure.setTacheNum(basicInfo.getTacheNum());
                    measure.setTacheName(basicInfo.getTacheName());
                    measure.setRiskNum(basicInfo.getRiskNum());
                    measure.setRiskDescription(basicInfo.getRiskDescription());
                    measure.setEvaluateDepart(basicInfo.getEvaluateDepart());
                    measure.setMatrixName(basicInfo.getMatrixName());
                    measure.setBasicId(basicInfo.getId());
                    measure.setPlanId(basicInfo.getPlanId());
                    measure.setSchemeId(basicInfo.getSchemeId());
                    measure.setLeadDepart(basicInfo.getLeadDepart());
                    measure.setDistributeDepart(basicInfo.getDistributeDepart());
                }
                if (measureList.size() > 0) {
                    measureService.saveBatch(measureList);
                }
            }

            if ("1".equals(isFitBranch)) {
                for (String branchNo : departList) {
                    RcsaTaskManage taskManageFH = generateTask(schemeCode, taskIndex, matrixRel, manage, branchNo);
                    taskManageFH.setProcessFlag("3");
                    // 入库分行部门的评估任务
                    taskManageMapper.insert(taskManageFH);
                    // 复制对象
                    int sortNum = basicInfoBMList.size()+1;
                    for (RcsaTaskBasicInfo basicInfo : basicInfoBMList) {
                        RcsaTaskBasicInfo basicInfoFH = new RcsaTaskBasicInfo();
                        BeanUtils.copyProperties(basicInfo, basicInfoFH);
                        // 评估机构修改为分行
                        basicInfoFH.setId(null);
                        basicInfoFH.setEvaluateDepart(branchNo);
                        basicInfoFH.setTaskId(taskManageFH.getId());
                        basicInfoFH.setSortNum(sortNum);
                        //todo： 查询最近一次的评估结果
                        sortNum++;
                        basicInfoService.save(basicInfoFH);

                        // 入库分行的措施
                        SrRiskControlMatrixTache tache = new SrRiskControlMatrixTache();
                        tache.setFatherId(matrixRel.getMatrixId());
                        tache.setTacheName(basicInfo.getTacheName());
                        tache.setRiskDescription(basicInfo.getRiskDescription());
                        List<RcsaTaskControlMeasure> measureList = matrixTacheMapper.queryControlNoPageList(tache);
                        for (RcsaTaskControlMeasure measure : measureList) {
                            measure.setTaskId(taskManageFH.getId());
                            measure.setTacheNum(basicInfoFH.getTacheNum());
                            measure.setTacheName(basicInfoFH.getTacheName());
                            measure.setRiskNum(basicInfoFH.getRiskNum());
                            measure.setRiskDescription(basicInfoFH.getRiskDescription());
                            measure.setEvaluateDepart(basicInfoFH.getEvaluateDepart());
                            measure.setMatrixName(basicInfoFH.getMatrixName());
                            measure.setBasicId(basicInfoFH.getId());
                            measure.setPlanId(basicInfoFH.getPlanId());
                            measure.setSchemeId(basicInfoFH.getSchemeId());
                            measure.setLeadDepart(basicInfoFH.getLeadDepart());
                            measure.setDistributeDepart(basicInfoFH.getDistributeDepart());
                        }
                        if (measureList.size() > 0) {
                            measureService.saveBatch(measureList);
                        }
                    }
                    taskIndex++;
                }
            }
        }
    }

    private RcsaTaskManage generateTask(String schemeCode,
                                        int taskIndex,
                                        RcsaSchemeMatrixRel matrixRel,
                                        RcsaSchemeManage manage,
                                        String evaluateDepart) {
        String taskCode = schemeCode + "PG" + String.format("%02d", taskIndex);
        RcsaTaskManage taskManage = new RcsaTaskManage();
        taskManage.setPlanId(matrixRel.getPlanId());
        taskManage.setSchemeId(matrixRel.getSchemeId());
        taskManage.setRelId(matrixRel.getId());
        taskManage.setTaskCode(taskCode);
        taskManage.setPlanType(manage.getPlanType());
        taskManage.setSchemeTitle(manage.getSchemeTitle());
        taskManage.setMatrixId(matrixRel.getMatrixId());
        taskManage.setMatrixName(matrixRel.getMatrixName());
        taskManage.setEvaluateDepart(evaluateDepart);
        taskManage.setProcessFlag(manage.getProcessFlag());
        taskManage.setLeadDepart(manage.getLeadDepart());
        taskManage.setDistributeDepart(manage.getEvaluateDepart());
        taskManage.setSortNum(taskIndex);
        taskManage.setPlanTitle(manage.getPlanTitle());
        taskManage.setEvaluateEndDate(manage.getEvaluateEndDate());
        taskManage.setMatrixNum(matrixRel.getMatrixNum());
        return taskManage;
    }

    private List<RcsaTaskBasicInfo> getMatrixTacheSelfList(RcsaTaskManage taskManage,
                                                           RcsaSchemeMatrixRel matrixRel,
                                                           List<SrRiskControlMatrixTache> matrixTacheList) {
        List<RcsaTaskBasicInfo> basicInfoList = new ArrayList<>();
        int index = 1;
        for (SrRiskControlMatrixTache tache : matrixTacheList) {
            RcsaTaskBasicInfo basicInfo = new RcsaTaskBasicInfo();
            basicInfo.setTaskId(taskManage.getId());
            basicInfo.setPlanId(taskManage.getPlanId());
            basicInfo.setSchemeId(taskManage.getSchemeId());
            basicInfo.setLeadDepart(taskManage.getLeadDepart());
            basicInfo.setDistributeDepart(taskManage.getDistributeDepart());
            basicInfo.setEvaluateDepart(taskManage.getEvaluateDepart());
            basicInfo.setMatrixName(matrixRel.getMatrixName());
            basicInfo.setTacheNum(tache.getTacheNum());
            basicInfo.setTacheName(tache.getTacheName());
            basicInfo.setRiskNum(tache.getRiskNum());
            basicInfo.setRiskDescription(tache.getRiskDescription());
            basicInfo.setMatrixId(matrixRel.getMatrixId());
            basicInfo.setSortNum(index);
            index++;
            basicInfoList.add(basicInfo);
        }
        return basicInfoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditReject(RcsaSchemeManage schemeManage) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        schemeManage.setSchemeState("4");
        schemeManageMapper.updateById(schemeManage);
        // 入库操作记录表
        RcsaPlanManageHistory history = new RcsaPlanManageHistory();
        history.setPlanManageId(schemeManage.getId());
        history.setIsAudit("1");
        history.setOperateTime(new Date());
        history.setState("4");
        history.setOperateUser(userName);
        history.setOperateType("审核退回");
        history.setOperateDepart(sysUser.getOrgId());
        historyMapper.insert(history);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> edit(RcsaSchemeManage rcsaSchemeManage) {
        List<RcsaSchemeMatrixRel> insertList = new ArrayList<>();
        List<RcsaSchemeMatrixRel> updateList = new ArrayList<>();
        List<String> deleteId = new ArrayList<>();

        List<RcsaSchemeMatrixRel> matrixRelList = rcsaSchemeManage.getMatrixRelList();
        int index = 1;
        for (RcsaSchemeMatrixRel rel : matrixRelList) {
            // 数据校验
            String message = judgeRel(rel, index);
            if (StringUtils.isNotBlank(message)) {
                return Result.error(message);
            }
            rel.setSortNum(index);
            if (StringUtils.isBlank(rel.getIsEdit())) {
                rel.setIsEdit("1");
            }
            if (StringUtils.isBlank(rel.getId())) {
                rel.setPlanId(rcsaSchemeManage.getPlanId());
                rel.setSchemeId(rcsaSchemeManage.getId());
                rel.setMatrixType("2");
                insertList.add(rel);
            } else {
                updateList.add(rel);
            }
            index++;
        }

        schemeManageMapper.updateById(rcsaSchemeManage);
        if (insertList.size()>0) {
            for (RcsaSchemeMatrixRel rel : insertList) {
                relMapper.insert(rel);
                deleteId.add(rel.getId());
            }
        }
        if (updateList.size()>0) {
            for (RcsaSchemeMatrixRel rel : updateList) {
                relMapper.updateById(rel);
                deleteId.add(rel.getId());
            }
        }
        relMapper.deleteNotExistRel(deleteId, rcsaSchemeManage.getId());
        return Result.ok("方案制定成功");
    }

    @Override
    public Result<String> submitByWorkflow(RcsaSchemeManage schemeManage) {
        // 需要校验数据
        RcsaSchemeManage manage = schemeManageMapper.selectById(schemeManage.getId());
        if (StringUtils.isBlank(manage.getSchemeTitle())) {
            return Result.error("方案标题不能为空！");
        }
        if (StringUtils.isBlank(manage.getEnterContact())) {
            return Result.error("录入人联系方式不能为空！");
        }
        // 校验评估信息
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("scheme_id", schemeManage.getId());
        wrapper.orderByAsc("sort_num");
        List<RcsaSchemeMatrixRel> pageList = relMapper.selectList(wrapper);
        int index = 1;
        for (RcsaSchemeMatrixRel rel : pageList) {
            // 数据校验
            String message = judgeRel(rel, index);
            if (StringUtils.isNotBlank(message)) {
                return Result.error(message);
            }
            index++;
        }

        // 获取机构号
        SysDepart sysDepart = departMapper.selectById(manage.getEvaluateDepart());
        manage.setEvaluateDepartCode(sysDepart.getOrgCode());
        Map<String, Object> variables = new HashMap<>();
        variables.put(businessKey, manage);
        workflowInstanceService.createWorkflowInstance(businessKey, schemeManage.getId(), variables);
        //processService.saveProcess(businessKey, schemeManage.getId(), "提交审核");
        return Result.OK("提交审核成功!");
    }

    private String judgeRel(RcsaSchemeMatrixRel rel, int index) {
        String matrixName = rel.getMatrixName();
        if (StringUtils.isBlank(matrixName)) {
            return "请选择矩阵名称" + index;
        }
        String isFitBranch = rel.getIsFitBranch();
        if (StringUtils.isBlank(isFitBranch)) {
            return "请选择是否适用于分行评估" + index;
        } else {
            if ("1".equals(isFitBranch)) {
                String branchNo = rel.getBranchNo();
                List<String> branchNos = rel.getBranchNos();
                if (branchNos == null || branchNos.isEmpty()) {
                    if (StringUtils.isBlank(branchNo)) {
                        return "请选择分行评估机构" + index;
                    }
                } else {
                    rel.setBranchNo(String.join(",", branchNos));
                }
            }
        }
        return null;
    }
}
