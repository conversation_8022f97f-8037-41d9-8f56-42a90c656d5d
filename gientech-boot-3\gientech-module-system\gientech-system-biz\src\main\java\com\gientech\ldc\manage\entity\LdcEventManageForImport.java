package com.gientech.ldc.manage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 损失事件管理表
 * @Author: jeecg-boot
 * @Date:   2025-04-29
 * @Version: V1.0
 */
@Data
@TableName("ldc_event_manage")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="损失事件管理表")
public class LdcEventManageForImport implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;

	@Excel(name = "数据类型", width = 15)
	private String dataType;

	/**事件编号*/
	@Excel(name = "事件编号", width = 15)
    private String eventCode;

	/**事件分类(一级)*/
	@Excel(name = "事件分类(一级)", width = 15)
    @Schema(description = "事件分类(一级)")
    private String eventClassifyFirst;
	/**事件分类(二级)*/
	@Excel(name = "事件分类(二级)", width = 15)
    @Schema(description = "事件分类(二级)")
    private String eventClassifySecond;
	/**事件名称*/
	@Excel(name = "事件名称", width = 15)
    @Schema(description = "事件名称")
    private String eventName;
	/**事件描述*/
	@Excel(name = "事件描述", width = 15)
    @Schema(description = "事件描述")
    private String eventDescrption;
	/**事件原因描述*/
	@Excel(name = "事件原因描述", width = 15)
    @Schema(description = "事件原因描述")
    private String eventReasonDescription;
	/**第几次填报*/
	@Excel(name = "第几次填报", width = 15)
    @Schema(description = "第几次填报")
    private Integer fillCount;
	/**是否结束*/
	@Excel(name = "是否结束", width = 15, dicCode = "whether")
	//@Dict(dicCode = "whether")
    @Schema(description = "是否结束")
    private String isEnd;
	/**状态*/
	@Excel(name = "状态", width = 15)
    @Schema(description = "状态")
	//@Dict(dicCode = "ldc_manage_status")
    private String status;
	/**是否为损失事件*/
	@Excel(name = "是否为损失事件", width = 15, dicCode = "whether")
	@Dict(dicCode = "whether")
    //@Schema(description = "是否为损失事件")
    private String isLossEvent;
	/**是否经监管认可剔除*/
	@Excel(name = "是否经监管认可剔除", width = 15, dicCode = "whether")
	//@Dict(dicCode = "whether")
    @Schema(description = "是否经监管认可剔除")
    private String isRemove;
	/**初次填报日期*/
	@Excel(name = "初次填报日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "初次填报日期")
    private Date firstFillDate;
	/**本次填报日期*/
	@Excel(name = "本次填报日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "本次填报日期")
    private Date thisFillDate;
	/**事件初始发生日期*/
	@Excel(name = "事件初始发生日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "事件初始发生日期")
    private Date initialHappenDate;
	/**事件发现日期*/
	@Excel(name = "事件发现日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "事件发现日期")
    private Date findDate;
	/**首笔损失入账(或确认)日期*/
	@Excel(name = "首笔损失入账(或确认)日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "首笔损失入账(或确认)日期")
    private Date firstEntryDate;
	/**最大预估损失金额*/
	@Excel(name = "最大预估损失金额", width = 15)
	@Schema(description = "最大预估损失金额")
	private BigDecimal maxEstimateLossMoney;
	/**总损失金额*/
	@Excel(name = "总损失金额", width = 15)
	@Schema(description = "总损失金额")
	private BigDecimal totalLossMoney;
	/**净损失金额*/
	@Excel(name = "净损失金额", width = 15)
	@Schema(description = "净损失金额")
	private BigDecimal netLossMoney;
	/**已确认的回收总金额*/
	@Excel(name = "已确认的回收总金额", width = 15)
	@Schema(description = "已确认的回收总金额")
	private BigDecimal cfmdRecycleMoney;
	/**其中：已确认的保险赔付*/
	@Excel(name = "其中：已确认的保险赔付", width = 15)
	@Schema(description = "其中：已确认的保险赔付")
	private BigDecimal cfmdIndemnity;

	/**类别*/
	@Excel(name = "类别", width = 15, dicCode = "ldc_recycle_category")
	@Dict(dicCode = "ldc_recycle_category")
	//@Schema(description = "类别")
	private String category;
	/**子类*/
	@Excel(name = "子类", width = 15, dicCode = "ldc_recycle_sub_class")
	//@Dict(dicCode = "ldc_recycle_sub_class")
	@Schema(description = "子类")
	private String subClass;
	/**损失形态*/
	@Excel(name = "损失形态", width = 15, dicCode = "ldc_recycle_loss_form")
	//@Dict(dicCode = "ldc_recycle_loss_form")
	@Schema(description = "损失形态")
	private String lossForm;
	/**机构*/
	@Excel(name = "机构", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
	//@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
	@Schema(description = "机构")
	private String orgId;
	/**部门*/
	@Excel(name = "部门", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
	//@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
	@Schema(description = "部门")
	private String departNo;
	/**科目代码*/
	@Excel(name = "会计科目代码", width = 15)
	//@Dict(dicCode = "ldc_recycle_category")
	@Schema(description = "会计科目代码")
	private String subjectCode;
	/**科目名称*/
	@Excel(name = "会计科目名称", width = 15)
	//@Dict(dicCode = "ldc_recycle_category")
	@Schema(description = "会计科目名称")
	private String subjectName;
	/**记账币种*/
	@Excel(name = "记账币种", width = 15)
	//@Dict(dicCode = "ldc_recycle_category")
	@Schema(description = "记账币种")
	private String currency;
	/**记账金额*/
	@Excel(name = "记账金额", width = 15)
	@Schema(description = "记账金额")
	private BigDecimal amount;
	/**对人民币汇率*/
	@Excel(name = "对人民币汇率", width = 15)
	@Schema(description = "对人民币汇率")
	private String exchangeRate;
	/**人民币金额（元）*/
	@Excel(name = "人民币金额(元)", width = 15)
	@Schema(description = "人民币金额(元)")
	private String cnyAmount;
	/**入账日期*/
	@Excel(name = "入账日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@Schema(description = "入账日期")
	private Date postingDate;
	/**说明*/
	@Excel(name = "说明", width = 15)
	@Schema(description = "说明")
	private String explanation;
	/**附件*/
	@Excel(name = "附件", width = 15)
	@Schema(description = "附件")
	private String document;

	@Excel(name = "数据标记", width = 15)
	@Schema(description = "数据标记")
	private java.lang.String clueMark;
	/**识别码*/
	@Excel(name = "识别码", width = 15)
	@Schema(description = "识别码")
	private java.lang.String identificationCode;
	/**摘要*/
	@Excel(name = "摘要", width = 15)
	@Schema(description = "摘要")
	private java.lang.String summary;

	/**广大客户服务质量*/
	@Excel(name = "广大客户服务质量", width = 15, dicCode = "ldc_non_financial")
	@Schema(description = "广大客户服务质量")
	//@Dict(dicCode = "ldc_non_financial")
	private String customerServiceQuality;
	/**员工安全*/
	@Excel(name = "员工安全", width = 15, dicCode = "ldc_non_financial")
	@Schema(description = "员工安全")
	//@Dict(dicCode = "ldc_non_financial")
	private String employeeSafety;
	/**运营中断*/
	@Excel(name = "运营中断", width = 15, dicCode = "ldc_non_financial")
	@Schema(description = "运营中断")
	//@Dict(dicCode = "ldc_non_financial")
	private String operationInterruption;
	/**监管行动*/
	@Excel(name = "监管行动", width = 15, dicCode = "ldc_non_financial")
	@Schema(description = "监管行动")
	//@Dict(dicCode = "ldc_non_financial")
	private String regulatoryActions;
	/**声誉受损*/
	@Excel(name = "声誉受损", width = 15, dicCode = "ldc_non_financial")
	@Schema(description = "声誉受损")
	@Dict(dicCode = "ldc_non_financial")
	private String reputationDamage;
	/**事件严重度分级*/
	@Excel(name = "事件严重度分级", width = 15, dicCode = "ldc_non_financial")
	@Schema(description = "事件严重度分级")
	@Dict(dicCode = "ldc_non_financial")
	private String severityClassification;
	/**是否重大操作风险事件*/
	@Excel(name = "是否重大操作风险事件", width = 15, dicCode = "whether")
	@Schema(description = "是否重大操作风险事件")
	@Dict(dicCode = "whether")
	private String isMajorRiskEvent;
	/**主要成因分类*/
	@Excel(name = "主要成因分类", width = 15)
	@Schema(description = "主要成因分类")
	private String mainCauseClassification;
	/**损失事件类型(一级)*/
	@Excel(name = "损失事件类型(一级)", width = 15)
	@Schema(description = "损失事件类型(一级)")
	private String eventTypeFirst;
	/**损失事件类型(二级)*/
	@Excel(name = "损失事件类型(二级)", width = 15)
	@Schema(description = "损失事件类型(二级)")
	private String eventTypeSecond;
	/**损失事件类型(三级)*/
	@Excel(name = "损失事件类型(三级)", width = 15)
	@Schema(description = "损失事件类型(三级)")
	private String eventTypeThird;
	/**与信用/市场风险相关*/
	@Excel(name = "与信用/市场风险相关", width = 15, dicCode = "ldc_creditRiskRelevant")
	@Schema(description = "与信用/市场风险相关")
	@Dict(dicCode = "ldc_creditRiskRelevant")
	private String creditRiskRelevant;
	/**是否纳入计量*/
	@Excel(name = "是否纳入计量", width = 15, dicCode = "whether")
	@Schema(description = "是否纳入计量")
	@Dict(dicCode = "whether")
	private String isIntoMetering;
	/**事件填报机构*/
	@Excel(name = "事件填报机构", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Schema(description = "事件填报机构")
    private String fillOrgan;
	/**事件填报部门*/
	@Excel(name = "事件填报部门", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Schema(description = "事件填报部门")
    private String fillDepart;
	/**事件填报人*/
	@Excel(name = "事件填报人(登录账号)", width = 15)
	@Dict(dictTable = "sys_user", dicText = "username", dicCode = "username")
    @Schema(description = "事件填报人(登录账号)")
    private String fillUser;
	/**填报人联系方式*/
	@Excel(name = "填报人联系方式", width = 15)
    @Schema(description = "填报人联系方式")
    private String fillUserContact;
	/**总行对口管理部门*/
	@Excel(name = "总行对口管理部门", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Schema(description = "总行对口管理部门")
    private String duetManageDepart;
	/**发生机构*/
	@Excel(name = "发生机构", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Schema(description = "发生机构")
    private String happenOrgan;
	/**发生部门*/
	@Excel(name = "发生部门", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Schema(description = "发生部门")
    private String happenDepart;
	/**关联台账类型*/
	@Excel(name = "关联台账类型", width = 15)
	@Schema(description = "关联台账类型")
	@Dict(dicCode = "ldc_ledger_clue_type")
	private String relLedgerType;
	/**关联台账编号*/
	@Excel(name = "关联台账编号", width = 15)
	@Schema(description = "关联台账编号")
	private String relLedgerCode;
	/**发生操作风险事件的主要流程*/
	@Excel(name = "发生操作风险事件的主要流程", width = 15)
	@Schema(description = "发生操作风险事件的主要流程")
	private String mainProcess;
	/**是否发起触发式评估计划*/
	@Excel(name = "是否发起触发式评估计划", width = 15, dicCode = "whether")
	@Schema(description = "是否发起触发式评估计划")
	@Dict(dicCode = "whether")
	private String isInitiateRcsa;
	/**计划编号*/
	@Excel(name = "计划编号", width = 15)
	@Schema(description = "计划编号")
	private String rcsaPlanCode;
	/**计划名称*/
	@Excel(name = "计划名称", width = 15)
	@Schema(description = "计划名称")
	private String rcsaPlanName;
	/**计划名称*/
	@Excel(name = "计划状态", width = 15)
	@Schema(description = "计划状态")
	private String rcsaPlanState;
	/**是否录入操作风险管理系统-问题收集模块*/
	@Excel(name = "是否录入操作风险管理系统-问题收集模块", width = 15, dicCode = "whether")
	@Schema(description = "是否录入操作风险管理系统-问题收集模块")
	@Dict(dicCode = "whether")
	private String isIntoHistory;
	/**问题编号*/
	@Excel(name = "问题编号", width = 15)
	@Schema(description = "问题编号")
	private String historyCode;
	/**问题词条(一级分类)*/
	@Excel(name = "问题词条(一级分类)", width = 15)
	@Schema(description = "问题词条(一级分类)")
	private String historyCode1;
	/**问题具体描述*/
	@Excel(name = "问题具体描述", width = 15)
	@Schema(description = "问题具体描述")
	private String historyCode2;
	/**风险等级*/
	@Excel(name = "风险等级", width = 15)
	@Schema(description = "风险等级")
	private String historyCode3;
	/**整改措施类型*/
	@Excel(name = "整改措施类型", width = 15)
	@Schema(description = "整改措施类型")
	private String historyCode4;
	/**整改措施描述*/
	@Excel(name = "整改措施描述", width = 15)
	@Schema(description = "整改措施描述")
	private String historyCode5;
	/**整改执行状态*/
	@Excel(name = "整改执行状态", width = 15)
	@Schema(description = "整改执行状态")
	private String historyCode6;
	/**整改完成日期*/
	@Excel(name = "整改完成日期", width = 15)
	@Schema(description = "整改完成日期")
	private String historyCode7;
	/**是否违反内外规*/
	@Excel(name = "是否违反内外规", width = 15, dicCode = "whether")
	@Schema(description = "是否违反内外规")
	@Dict(dicCode = "whether")
	private String isViolateRegulations;
	/**制度文号*/
	@Excel(name = "制度文号", width = 15)
	@Schema(description = "制度文号")
	private String isViolateRegulations1;
	/**制度名称*/
	@Excel(name = "制度名称", width = 15)
	@Schema(description = "制度名称")
	private String isViolateRegulations2;
	/**发文机构*/
	@Excel(name = "制度发文机构", width = 15)
	@Schema(description = "制度发文机构")
	private String isViolateRegulations3;
	/**发文机构*/
	@Excel(name = "制度时效性", width = 15)
	@Schema(description = "制度时效性")
	private String isViolateRegulations4;
	/**外规文号*/
	@Excel(name = "外规文号", width = 15)
	@Schema(description = "外规文号")
	private String isViolateRegulations5;
	/**外规名称*/
	@Excel(name = "外规名称", width = 15)
	@Schema(description = "外规名称")
	private String isViolateRegulations6;
	/**发文机构*/
	@Excel(name = "外规发文机构", width = 15)
	@Schema(description = "外规发文机构")
	private String isViolateRegulations7;
	/**时效性*/
	@Excel(name = "外规时效性", width = 15)
	@Schema(description = "外规时效性")
	private String isViolateRegulations8;
	/**合并状态 1-未合并	2-被合并	 3-合并后*/
	@Excel(name = "合并状态", width = 15,dicCode = "ldc_mergeState")
	@Schema(description = "合并状态")
	@Dict(dicCode = "ldc_mergeState")
	private String mergeState;
	/**合并事件编号*/
	@Excel(name = "合并事件编号", width = 15)
	@Schema(description = "合并事件编号")
	private String mergeEventCode;
	/**合并事件名称*/
	@Excel(name = "合并事件名称", width = 15)
	@Schema(description = "合并事件名称")
	private String mergeEventName;
	/**合并事件描述*/
	@Excel(name = "合并事件描述", width = 15)
	@Schema(description = "合并事件描述")
	private String mergeEventDescription;
	/**合并事件发生损失事件的机构*/
	@Excel(name = "合并事件发生损失事件的机构", width = 15)
	@Schema(description = "合并事件发生损失事件的机构")
	private String mergeEventHappenDepart;
	/**合并事件发生损失事件的机构*/
	@Excel(name = "合并事件总损失金额", width = 15)
	@Schema(description = "合并事件总损失金额")
	private String mergeEventTotalLossMoney;
	/**合并事件净损失金额*/
	@Excel(name = "合并事件净损失金额", width = 15)
	@Schema(description = "合并事件净损失金额")
	private String mergeEventNetLossMoney;
	/**附件类型*/
	@Excel(name = "附件类型", width = 15, dicCode = "ldc_document_type")
	@Schema(description = "附件类型")
	@Dict(dicCode = "ldc_document_type")
	private String documentType;
	/**附件说明*/
	@Excel(name = "附件说明", width = 15)
	@Schema(description = "附件说明")
	private String documentExplain;
	/**附件名称*/
	@Excel(name = "附件名称", width = 15)
	@Schema(description = "附件名称")
	private String documentName;





	@Schema(description = "关联台账主键")
	private String relLedgerId;

	/**关联制度主键*/
    @Schema(description = "关联制度主键")
    private String regulationId;

	/**被合并的事件(父节点)*/
    @Schema(description = "被合并的事件ID，只记录子节点")
    private String mergeId;
	/**是否是根节点*/
    @Schema(description = "是否是根节点")
    private String isRoot;
	/**是否合并删除*/
    @Schema(description = "是否合并删除")
    private String isDelete;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**所属机构*/
	@Excel(name = "所属机构", width = 15)
    @Schema(description = "所属机构")
    private String sysOrgId;

	@Schema(description = "入账功能关联的LDC模块，临时字段，用于LDC确认")
	private String temporaryEntryId;

	@Schema(description = "关联的入账线索ID")
	private String relIds;
	@Schema(description = "关联的手工填写线索ID")
	private String manualRelIds;

	@Schema(description = "是否隐藏展示 1-是 0-否（用于合并之后）")
	private String isHidden;
	@Schema(description = "展示页面 1-全部事件 2-已合并 3-被合并，需注意合并/解除合并之后的展示页面归属")
	private String showPage;
	@Schema(description = "流程类型 1-分行/分行部门 2-总行各部门 3-总行ldc管理岗 4-子公司 5-村镇银行")
	private String processFlag;
	@Schema(description = "展示页面在全部事件")
	private String isShowPage1;
	@Schema(description = "展示页面在已合并")
	private String isShowPage2;
	@Schema(description = "展示页面在被合并")
	private String isShowPage3;
	@Schema(description = "所处合并层级")
	private String nodeLevel;
	@Schema(description = "被合并的原始数据的id（第一层）")
	private String mergerRootId;
	@Schema(description = "是否参与流程，1-是 0-否")
	private String isParticipateProcess;

	@Schema(description = "申请人")
	private String applyUser;
	@Schema(description = "申请人姓名")
	private String applyUserName;
	@Schema(description = "申请时间")
	private String applyTime;
	@Schema(description = "申请人所属机构")
	private String applyDepart;
	@Schema(description = "申请人所属机构名称")
	private String applyDepartName;
	@Schema(description = "单据到达时间")
	private String reachTime;
	@Schema(description = "填报次数变更标记 1-是 0-否")
	private String fillCountFlag;
	@Schema(description = "是否作废 1-是 0-否")
	@Dict(dicCode = "whether")
	private String isNullify;
	@Schema(description = "意见类型 1-退回修改 2-终止报送")
	private String opinionType;
	@Schema(description = "编号序号")
	private String serialIndex;
	@Schema(description = "父节点")
	private String parentId;
	@Schema(description = "是否处于更新中")
	private String isUpdateProcess;

	@Schema(description = "用于记录废止时当前状态，方便回滚")
	private String statusOld;
	@TableField(exist = false)
	List<LdcEventRecycleDetails> detailsList;
	@TableField(exist = false)
	List<LdcEventDocument> documentList;

	@TableField(exist = false)
	List<String> ids;

	@TableField(exist = false)
	private String auditOpinion;

	@TableField(exist = false)
	private String operateUser;
	@TableField(exist = false)
	private String nodeRole;
	@TableField(exist = false)
	private String beCopyId;
	@TableField(exist = false)
	private List<String> clueIds;

}
