<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="TrainingTaskForm">
          <a-row>
            <a-col style="color: orange; display: flex; font-weight: 800">
              |
              <a-col style="color: black; padding-left: 5px"> 培训任务 </a-col>
            </a-col>
            <a-col :span="12">
              <a-form-item label="任务编号" v-bind="validateInfos.taskNum">
                <a-input v-model:value="formData.taskNum" placeholder="系统自动生成" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="培训名称" v-bind="validateInfos.trainingName">
                <a-input v-model:value="formData.trainingName" placeholder="请输入培训名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="培训类型" v-bind="validateInfos.trainingType">
                <j-dict-select-tag v-model:value="formData.trainingType" dictCode="rule_training_type" placeholder="请选择培训类型" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="制度名称" v-bind="validateInfos.regulationName">
                <a-input v-model:value="formData.regulationName" placeholder="点击选择制度" readonly @click="handleRegulationSelect" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="培训形式" v-bind="validateInfos.trainingForm">
                <j-dict-select-tag
                  v-model:value="formData.trainingForm"
                  dictCode="rule_training_form"
                  placeholder="请选择培训形式"
                  @change="handleTrainingFormChange"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="任务状态" v-bind="validateInfos.taskStatus">
                <j-dict-select-tag v-model:value="formData.taskStatus" dictCode="rule_task_status" placeholder="系统自动生成" allow-clear disabled />
              </a-form-item>
            </a-col>
            <a-col style="color: orange; display: flex; font-weight: 800">
              |
              <a-col style="color: black; padding-left: 5px"> 学习安排 </a-col>
            </a-col>
            <a-col :span="12">
              <a-form-item label="学习范围" v-bind="validateInfos.trainingScope">
                <j-dict-select-tag v-model:value="formData.trainingScope" dictCode="rule_training_scope" placeholder="请选择培训范围" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="学习人员" v-bind="validateInfos.participants">
                <a-input v-model:value="formData.participants" placeholder="请明确需要参与培训的部门、人员或岗位" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12" >
              <a-form-item label="最低学习时长" v-bind="validateInfos.minTrainingDuration">
                <a-input-number v-model:value="formData.minTrainingDuration" :min="0.5" :step="0.5" addon-after="小时" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col style="color: orange; display: flex; font-weight: 800">
              |
              <a-col style="color: black; padding-left: 5px"> 考试安排 </a-col>
            </a-col>
            <!-- 考试相关字段 -->
            <template v-if="formData.needExam === '1'">
              <a-col :span="12">
                <a-form-item label="考试时长" v-bind="validateInfos.examDuration">
                  <a-input-number v-model:value="formData.examDuration" :min="10" :step="10" addon-after="分钟" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="考试通过分值" v-bind="validateInfos.passScore">
                  <a-input-number v-model:value="formData.passScore" :min="1" :max="100" addon-after="%" style="width: 100%" />
                </a-form-item>
              </a-col>
              <!-- 动态考题配置 -->
              <a-col :span="24" v-if="formData.needExam === '1'">
                <div class="ant-form-item" v-bind="validateInfos.questionConfigs">
                  <div class="ant-form-item-label">
                    <label class="ant-form-item-required" title="考题设置">
                      考题设置
                      <span style="color: #999; font-size: 12px">(单选题、多选题、判断题，分别选择三种题型的数量，且总数量不得低于5)</span>
                    </label>
                  </div>
                  <div class="ant-form-item-control">
                    <div v-for="(item, index) in formData.questionConfigs" :key="index" style="margin-bottom: 12px; width: 100%">
                      <a-row :gutter="8">
                        <a-col :span="6">
                          <div>
                            <a-form-item label="考题类型">
                              <a-select
                                v-model:value="item.questionType"
                                placeholder="请选择考题类型"
                                @change="handleQuestionTypeChange"
                                :status="getFieldStatus('questionType', index)"
                                style="width: 100%"
                              >
                                <a-select-option v-for="type in getAvailableQuestionTypes(index)" :key="type.value" :value="type.value">
                                  {{ type.label }}
                                </a-select-option>
                              </a-select>
                            </a-form-item>
                          </div>
                        </a-col>
                        <a-col :span="6">
                          <div>
                            <a-form-item label="考题数量">
                              <a-input-number
                                v-model:value="item.questionCount"
                                :min="0"
                                placeholder="考题数量"
                                style="width: 100%"
                                @change="handleQuestionCountChange"
                                :status="getFieldStatus('questionCount', index)"
                              />
                            </a-form-item>
                          </div>
                        </a-col>
                        <a-col :span="6">
                          <div>
                            <a-form-item label="考题分值">
                              <a-input-number
                                v-model:value="item.questionScore"
                                :min="1"
                                :max="100"
                                placeholder="考题分值"
                                style="width: 100%"
                                @change="handleQuestionScoreChange"
                                :status="getFieldStatus('questionScore', index)"
                              />
                            </a-form-item>
                          </div>
                        </a-col>
                        <a-col :span="6">
                          <a-space>
                            <a-button @click="addQuestionConfig" :disabled="formData.questionConfigs.length >= 3" type="primary" size="small">
                              +
                            </a-button>
                            <a-button @click="removeQuestionConfig(index)" :disabled="formData.questionConfigs.length <= 1" size="small">
                              -
                            </a-button>
                          </a-space>
                        </a-col>
                      </a-row>
                    </div>
                    <div style="margin-top: 8px; color: #666; font-size: 12px">
                      总考题数量：{{ totalQuestionCount }} 题
                      <span v-if="totalQuestionCount < 5" style="color: #ff4d4f; margin-left: 8px"> （总数量不得低于5题） </span>
                      <span v-else-if="!formData.id" style="color: #ff4d4f; margin-left: 8px"> （请先保存培训任务基本信息） </span>
                    </div>
                    <!-- 去出题按钮 -->
                    <div style="text-align: center; margin-top: 16px">
                      <a-button type="primary" @click="openQuestionModal" :disabled="!canGoToQuestion"> 去出题 </a-button>
                    </div>
                    <!-- 手动显示验证错误信息 -->
                    <div v-if="validateInfos.questionConfigs?.errorMsg" class="ant-form-item-explain ant-form-item-explain-error">
                      <div role="alert">{{ validateInfos.questionConfigs.errorMsg }}</div>
                    </div>
                  </div>
                </div>
              </a-col>
            </template>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
  <div class="p-2" style="width: 100%" v-show="infoFlag">
    <FileUploadSection v-if="infoFlag" :relevancyId="formData.id" :disabled="disabled" ref="fileUploadSectionRef" />
  </div>
  <!-- 出题弹窗 -->
  <QuestionManageModal
    v-model:visible="questionModalVisible"
    :totalQuestions="totalQuestionCount"
    :questionConfigs="formData.questionConfigs"
    :trainingTaskId="formData.id"
    :oldId="formData.oldId"
    @ok="handleQuestionModalOk"
  />
  <basisComponentModal ref="basisComponentRef" @associate-data="handleAssociateData" />
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, computed } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { saveOrUpdate } from '../LeariningTask.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  import QuestionManageModal from './questionManage/QuestionManageModal.vue';
  import basisComponentModal from '/@/views/rule/trainingAndLearning/taskManage/trainingTask/components/system/SystemList.vue';
  import FileUploadSection from './relevancyFile/FileUploadSection.vue';

  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({}) },
    formBpm: { type: Boolean, default: true },
  });

  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);

  // 初始化formData
  const formData = reactive<Record<string, any>>({
    id: '',
    taskNum: '',
    trainingName: '',
    trainingType: '',
    regulationName: '',
    regulationId: '',
    trainingForm: '',
    trainingScope: '',
    trainingTime: '',
    participants: '',
    minTrainingDuration: null,
    materialType: '',
    needExam: '1',
    examDuration: null,
    passScore: null,
    taskStatus: '1', // 默认草稿状态
    type: '',
    oldId: '',
    questionConfigs: [
      {
        questionType: '',
        questionCount: null,
        questionScore: null,
      },
    ], // 考题配置数组
  });

  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);

  // 出题弹窗相关
  const questionModalVisible = ref<boolean>(false);

  // 表单验证规则
  const validatorRules = reactive({
    trainingName: [{ required: true, message: '请输入培训名称' }],
    trainingType: [{ required: true, message: '请选择培训类型' }],
    trainingForm: [{ required: true, message: '请选择培训形式' }],
    trainingScope: [{ required: true, message: '请选择培训范围' }],
    trainingTime: [{ required: true, message: '请选择培训时间' }],
    participants: [{ required: true, message: '请输入参训人员' }],
    materialType: [{ required: true, message: '请选择培训材料类型' }],
    needExam: [{ required: true, message: '请选择是否需要考试' }],
    questionConfigs: [
      {
        validator: async (rule, value) => {
          // 验证考题配置
          if (formData.needExam === '1') {
            // 需要考试时验证考题配置
            for (let i = 0; i < formData.questionConfigs.length; i++) {
              const config = formData.questionConfigs[i];
              if (!config.questionType) {
                throw new Error(`第${i + 1}行考题类型不能为空`);
              }
              if (!config.questionCount || config.questionCount <= 0) {
                throw new Error(`第${i + 1}行考题数量必须大于0`);
              }
              if (!config.questionScore || config.questionScore <= 0) {
                throw new Error(`第${i + 1}行考题分值必须大于0`);
              }
            }
            // 验证总数量
            if (totalQuestionCount.value < 5) {
              throw new Error('总考题数量不得低于5题');
            }
          }
          return true;
        },
        trigger: 'change',
      },
    ],
  });

  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  // 计算总考题数量
  const totalQuestionCount = computed(() => {
    return formData.questionConfigs.reduce((total, config) => {
      return total + (config.questionCount || 0);
    }, 0);
  });

  // 计算是否可以去出题
  const canGoToQuestion = computed(() => {
    // 1. 题目数量必须大于等于5
    if (totalQuestionCount.value < 5) {
      return false;
    }
    // 2. 必须是编辑状态（有ID）
    if (!formData.id) {
      return false;
    }
    return true;
  });

  // 获取指定行可选的考题类型
  const getAvailableQuestionTypes = (currentIndex: number) => {
    const allTypes = [
      { value: '1', label: '单选题' },
      { value: '2', label: '多选题' },
      { value: '3', label: '判断题' },
    ];

    // 获取其他行已选择的类型
    const selectedTypes = formData.questionConfigs
      .map((config, index) => (index !== currentIndex ? config.questionType : null))
      .filter((type) => type && type !== '');

    // 返回未被选择的类型
    return allTypes.filter((type) => !selectedTypes.includes(type.value));
  };

  // 处理培训形式变化
  const handleTrainingFormChange = (value: string) => {
    if (value !== '1') {
      formData.minTrainingDuration = null;
    }
  };

  // 添加考题配置行
  const addQuestionConfig = () => {
    if (formData.questionConfigs.length < 3) {
      formData.questionConfigs.push({
        questionType: '',
        questionCount: null,
        questionScore: null,
      });
    }
  };

  // 删除考题配置行
  const removeQuestionConfig = (index: number) => {
    if (formData.questionConfigs.length > 1) {
      formData.questionConfigs.splice(index, 1);
    }
  };

  // 处理考题类型变化
  const handleQuestionTypeChange = () => {
    // 手动触发验证
    nextTick(() => {
      validate(['questionConfigs']).catch(() => {});
    });
  };

  // 处理考题数量变化
  const handleQuestionCountChange = () => {
    // 手动触发验证
    nextTick(() => {
      validate(['questionConfigs']).catch(() => {});
    });
  };

  // 处理考题分值变化
  const handleQuestionScoreChange = () => {
    // 手动触发验证
    nextTick(() => {
      validate(['questionConfigs']).catch(() => {});
    });
  };

  // 获取字段验证状态
  const getFieldStatus = (fieldName: string, index: number) => {
    const error = getFieldError(fieldName, index);
    return error ? 'error' : '';
  };

  // 获取字段错误信息
  const getFieldError = (fieldName: string, index: number) => {
    if (formData.needExam !== '1') return '';

    const config = formData.questionConfigs[index];
    if (!config) return '';

    switch (fieldName) {
      case 'questionType':
        if (!config.questionType) {
          return `第${index + 1}行考题类型不能为空`;
        }
        break;
      case 'questionCount':
        if (!config.questionCount || config.questionCount <= 0) {
          return `第${index + 1}行考题数量必须大于0`;
        }
        break;
      case 'questionScore':
        if (!config.questionScore || config.questionScore <= 0) {
          return `第${index + 1}行考题分值必须大于0`;
        }
        break;
    }
    return '';
  };

  // 打开出题弹窗
  const openQuestionModal = () => {
    if (totalQuestionCount.value < 5) {
      createMessage.warning('总考题数量不得低于5题');
      return;
    }
    if (!formData.id) {
      createMessage.warning('请先保存培训任务基本信息后再进行出题');
      return;
    }
    questionModalVisible.value = true;
  };

  // 处理出题弹窗确定
  const handleQuestionModalOk = (questions: any[]) => {
    createMessage.success(`成功保存 ${questions.length} 道题目`);
    // 这里可以将题目数据保存到表单数据中
    // formData.questions = questions;
  };

  // 处理是否需要考试变化
  const handleNeedExamChange = (value: string) => {
    if (value !== '1') {
      formData.examDuration = null;
      formData.passScore = null;
      // 重置考题配置为默认状态
      formData.questionConfigs = [
        {
          questionType: '',
          questionCount: null,
          questionScore: null,
        },
      ];
    }
  };
  const basisComponentRef = ref();
  // 处理制度选择
  const handleRegulationSelect = () => {
    //createMessage.info('制度选择功能待实现');
    nextTick(() => {
      if (basisComponentRef.value && basisComponentRef.value.show) {
        basisComponentRef.value.show();
      }
    });
  };
  // 处理关联数据
  const handleAssociateData = (selectedData: any) => {
    // 根据组件类型获取不同字段
    formData.regulationName = selectedData['systemName'];
    formData.basisId = selectedData['id'];
    // 关闭模态框
    handleBasisModalCancel();
  };
  // 关闭模态框
  const handleBasisModalCancel = () => {
    basisComponentRef.value = false;
  };

  /**
   * 新增
   */
  function add(flag) {
    nextTick(() => {
      resetFields();
      // 重置为初始状态
      Object.assign(formData, {
        id: '',
        taskNum: '',
        trainingName: '',
        trainingType: '',
        regulationName: '',
        regulationId: '',
        trainingForm: '',
        trainingScope: '',
        trainingTime: '',
        participants: '',
        minTrainingDuration: null,
        materialType: '',
        needExam: '1',
        examDuration: null,
        passScore: null,
        taskStatus: '1', // 默认草稿状态
        type: flag, // 判断是学习任务还是考试任务
        questionConfigs: [
          {
            questionType: '',
            questionCount: null,
            questionScore: null,
          },
        ], // 重置考题配置数组
      });
    });
  }

  /**
   * 编辑
   */
  function edit(record) {
    formData.id = record.id;
    nextTick(() => {
      if (fileUploadSectionRef.value) {
        fileUploadSectionRef.value.reloadTable();
      }
      infoFlag.value = false;
      if (record.trainingForm === '1') {
        infoFlag.value = true;
      }
      resetFields();
      const tmpData = { ...record };
      Object.assign(formData, tmpData);
      // 处理考题配置数据回显
      if (record.questionConfigList && Array.isArray(record.questionConfigList)) {
        // 使用后台转换好的questionConfigList
        formData.questionConfigs = record.questionConfigList;
      } else if (record.questionConfigs) {
        // 处理questionConfigs字段
        if (Array.isArray(record.questionConfigs)) {
          // 如果是数组，直接使用
          formData.questionConfigs = record.questionConfigs;
        } else if (typeof record.questionConfigs === 'string') {
          // 如果是JSON字符串，尝试解析
          try {
            const parsedConfigs = JSON.parse(record.questionConfigs);
            if (Array.isArray(parsedConfigs)) {
              formData.questionConfigs = parsedConfigs;
            } else {
              throw new Error('解析结果不是数组');
            }
          } catch (error) {
            console.error('解析questionConfigs失败:', error);
            // 解析失败时设置默认配置
            formData.questionConfigs = [
              {
                questionType: '',
                questionCount: null,
                questionScore: null,
              },
            ];
          }
        } else {
          // 其他情况设置默认配置
          formData.questionConfigs = [
            {
              questionType: '',
              questionCount: null,
              questionScore: null,
            },
          ];
        }
      } else {
        // 如果没有考题配置数据，从旧字段转换
        formData.questionConfigs = [];
        if (record.singleChoiceCount || record.singleChoiceScore) {
          formData.questionConfigs.push({
            questionType: '1',
            questionCount: record.singleChoiceCount || 0,
            questionScore: record.singleChoiceScore || 0,
          });
        }
        if (record.multipleChoiceCount || record.multipleChoiceScore) {
          formData.questionConfigs.push({
            questionType: '2',
            questionCount: record.multipleChoiceCount || 0,
            questionScore: record.multipleChoiceScore || 0,
          });
        }
        if (record.judgmentCount || record.judgmentScore) {
          formData.questionConfigs.push({
            questionType: '3',
            questionCount: record.judgmentCount || 0,
            questionScore: record.judgmentScore || 0,
          });
        }
        // 如果没有任何配置，至少保留一行空配置
        if (formData.questionConfigs.length === 0) {
          formData.questionConfigs.push({
            questionType: '',
            questionCount: null,
            questionScore: null,
          });
        }
      }
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    let model = { ...formData };
    if (model.id) {
      isUpdate.value = true;
    }

    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  const fileUploadSectionRef = ref();
  const infoFlag = ref<boolean>(false);

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
