package com.gientech.rule.manage.reformedAndAbolished.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gientech.rule.manage.reformedAndAbolished.entity.Abolition;
import com.gientech.rule.manage.reformedAndAbolished.entity.Publish;
import com.gientech.rule.manage.reformedAndAbolished.entity.RuleSystemReformedAndAbolished;
import com.gientech.rule.manage.reformedAndAbolished.mapper.AbolitionMapper;
import com.gientech.rule.manage.reformedAndAbolished.mapper.PublishMapper;
import com.gientech.rule.manage.reformedAndAbolished.mapper.RuleSystemReformedAndAbolishedMapper;
import com.gientech.rule.manage.reformedAndAbolished.service.IPublishService;
import com.gientech.rule.trainingAndLearning.taskManage.trainingTask.entity.TrainingTask;
import com.gientech.rule.trainingAndLearning.taskManage.trainingTask.mapper.TrainingTaskMapper;
import com.gientech.rule.trainingAndLearning.taskManage.trainingTask.service.impl.TrainingTaskServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 内外规-制度立改废任务管理-发文
 * @Author: jeecg-boot
 * @Date:   2025-07-16
 * @Version: V1.0
 */
@Service
public class PublishServiceImpl extends ServiceImpl<PublishMapper, Publish> implements IPublishService {

    @Autowired
    private RuleSystemReformedAndAbolishedMapper mapper;
    @Autowired
    private AbolitionMapper abolitionmapper;
    @Autowired
    private TrainingTaskMapper taskMapper;
    @Autowired
    private TrainingTaskServiceImpl taskImpl;

    @Override
    public String newsave(List<Publish> publishs) {
        boolean flag=this.saveBatch(publishs);
        for(Publish publish : publishs){
            //需要回调OA系统更新状态等信息，但现在没有，固直接更新
            RuleSystemReformedAndAbolished rule=mapper.selectById(publish.getRelevancyId());
            rule.setTaskStatus("8");//已完成
            rule.setTimeliness("1");//现行有效
            //废止相应制度
            if("3".equals(rule.getTaskType())){
                QueryWrapper<Abolition> abolitionWrapper = new QueryWrapper<>();
                abolitionWrapper.eq("relevancy_id", publish.getRelevancyId());
                List<Abolition> abolitions=abolitionmapper.selectList(abolitionWrapper);
                if(abolitions.size()>0){
                    for(Abolition abolition : abolitions){
                        RuleSystemReformedAndAbolished rr=mapper.selectById(abolition.getAbolitionId());
                        rr.setTimeliness("2");//已废止
                        mapper.updateById(rr);
                    }
                }
                rule.setTimeliness("2");//已废止
                mapper.updateById(rule);
            }
            //生成制度培训任务
            TrainingTask task=new TrainingTask();
            task.setCreateBy(rule.getSystemManagerUsername());
            task.setTrainingName(rule.getSystemName()+"培训任务");//培训名称
            task.setRegulationName(rule.getSystemName());//制度名称
            task.setRegulationId(rule.getId());//制度ID
            task.setTrainingType("2");//培训类型
            task.setTaskStatus("1");//任务状态
            // 生成任务编号
            String taskNum = taskImpl.generateTaskNum();
            task.setTaskNum(taskNum);
            taskMapper.insert(task);
            //生成制度学习任务
        }
        if(flag){
            return "000000";
        }else{
            return "保存失败";
        }
    }
}
