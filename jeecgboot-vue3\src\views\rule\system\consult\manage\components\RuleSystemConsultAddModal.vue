<template>
  <a-modal v-model:open="open" :title="title" :width="1400" @cancel="handleClose" centered
           ok-text="保存" @ok="handleSave" :ok-button-props="{disabled:disabled}">
    <a-spin :spinning="confirmLoading">
      <div class="title-container">
        <div class="title-bar"></div>
        <h1 class="title-text">制度信息</h1>
      </div>
      <div class="jeecg-basic-table-form-container">
        <a-form ref="formRef" :model="data" :label-col="{span:9}" :wrapper-col="{span:15}"
                :disabled="disabled">
          <a-row>
            <a-col :span="8">
              <a-form-item name="systemName" v-bind="validateInfos.systemId"
                           id="RuleSystemConsultAddForm-systemName">
                <template #label>
                  <span title="制度名称">制度名称</span>
                </template>
                <a-input
                  v-model:value="data.systemName"
                  :placeholder="data.id ? '制度名称' : '请点击选择制度'"
                  readonly
                  @click="handleOpenSystemSelectModal"
                  :disabled="disabled"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="systemIssuingBody" v-bind="validateInfos.systemIssuingBody"
                           id="RuleSystemConsultAddForm-systemIssuingBody">
                <template #label>
                  <span title="制度发文机构">制度发文机构</span>
                </template>
                <j-select-dept placeholder="请选择" v-model:value="data.systemIssuingBody" disabled
                               checkStrictly allow-clear rowKey="orgCode"/>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="issuingDeptOne" v-bind="validateInfos.issuingDeptOne"
                           id="RuleSystemConsultAddForm-issuingDeptOne">
                <template #label>
                  <span title="制度发文部门（一级）">制度发文部门（一级）</span>
                </template>
                <j-select-dept placeholder="请选择" v-model:value="data.issuingDeptOne" disabled
                               checkStrictly allow-clear rowKey="orgCode"/>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="issuingDeptTwo" v-bind="validateInfos.issuingDeptTwo"
                           id="RuleSystemConsultAddForm-issuingDeptTwo">
                <template #label>
                  <span title="制度发文部门（二级）">制度发文部门（二级）</span>
                </template>
                <j-select-dept placeholder="请选择" v-model:value="data.issuingDeptTwo" disabled
                               checkStrictly allow-clear rowKey="orgCode"/>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <div class="title-container">
        <div class="title-bar"></div>
        <h1 class="title-text">制度咨询</h1>
      </div>

      <j-vxe-table ref="consultTableRef" :keep-source="true" resizable :maxHeight="264"
                   :loading="consultTable.loading" :toolbarConfig="consultTable.toolbarConfig"
                   :columns="consultTable.columns" :dataSource="consultTable.dataSource"
                   :height="340" :disabled="disabled" :rowSelection="true" :toolbar="true">
        <template #toolbarPrefix>
          <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleConsultAdd"
                    v-if="!disabled">新增
          </a-button>
        </template>
      </j-vxe-table>

      <RuleSystemConsultModal ref="consultModal" @save="handleConsultSave" />

      <!-- 制度选择弹窗 -->
      <RuleSystemSelectModal @register="registerSystemSelectModal" @success="handleSystemSelect" />
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref } from "vue";
import JSelectDept from "@/components/Form/src/jeecg/components/JSelectDept.vue";
import { saveOrUpdate } from "@/views/rule/system/consult/RuleSystemConsult.api";
import { consultColumns } from "@/views/rule/system/consult/RuleSystemConsult.data";
import { JVxeTable } from "@/components/jeecg/JVxeTable";
import { Form } from "ant-design-vue";
import RuleSystemConsultModal
  from "@/views/rule/system/consult/manage/components/RuleSystemConsultModal.vue";
import RuleSystemSelectModal from "@/views/rule/system/component/RuleSystemSelectModal.vue";
import { useMessage } from "@/hooks/web/useMessage";
import { useModal } from "@/components/Modal";

const useForm = Form.useForm;
const { createMessage } = useMessage();
const emit = defineEmits(["success"]);

const open = ref<boolean>(false);
const title = ref<string>();
const confirmLoading = ref<boolean>(false);
const disabled = ref<boolean>(false);
const formRef = ref();
const data = reactive({
  id: "",
  systemId: "",
  systemName: "",
  systemIssuingBody: "",
  issuingDeptOne: "",
  issuingDeptTwo: ""
});

const consultTable = reactive<Recordable>({
  loading: false,
  toolbarConfig: {
    btn: ["remove"],
    slot: ["prefix"]
  },
  columns: consultColumns,
  dataSource: []
});
const consultTableRef = ref();
const consultModal = ref();

//表单验证
const validatorRules = reactive({
  systemName: [{ required: true, message: "请选择制度信息！" }]
});
const {
  resetFields,
  validate,
  validateInfos
} = useForm(data, validatorRules, { immediate: false });

// 制度选择弹窗
const [registerSystemSelectModal, { openModal: openSystemSelectModal }] = useModal();

// 打开制度选择弹窗
function handleOpenSystemSelectModal() {
  if (!disabled.value) {
    openSystemSelectModal();
  }
}

// 处理制度选择
function handleSystemSelect(selectedSystem) {
  data.systemId = selectedSystem.id;
  data.systemName = selectedSystem.systemName;
  data.systemIssuingBody = selectedSystem.systemIssuingBody;
  data.issuingDeptOne = selectedSystem.issuingDeptOne;
  data.issuingDeptTwo = selectedSystem.issuingDeptTwo;
}

// 建议子表按钮
function handleConsultAdd() {
  consultModal.value.disableSubmit = false;
  consultModal.value.add();
}

// 建议子表数据保存到主表弹窗
function handleConsultSave(record: any) {
  consultTableRef.value.pushRows(record);
}

/**
 * 新增
 */
function handleAdd() {
  title.value = "新建";
  resetFields();
  consultTable.dataSource = [];
  disabled.value = false;
  open.value = true;
}

/**
 * 编辑
 * @param record
 */
function handleEdit(record: Recordable) {
  title.value = "编辑";
  disabled.value = false;
  open.value = true;
  nextTick(() => {
    Object.assign(data, record);
    consultTable.dataSource = [...record.consultList];
  });
}

/**
 * 详情
 * @param record
 */
function handleDetail(record: Recordable) {
  title.value = "详情";
  disabled.value = true;
  open.value = true;
  nextTick(() => {
    Object.assign(data, record);
    consultTable.dataSource = [...record.consultList];
  });
}

/**
 * 保存按钮
 */
async function handleSave() {
  try {
    // 触发表单验证
    await validate();
  } catch ({ errorFields }) {
    if (errorFields) {
      const firstField = errorFields[0];
      if (firstField) {
        formRef.value.scrollToField(firstField.name, { behavior: "smooth", block: "center" });
      }
    }
    return Promise.reject(errorFields);
  }
  confirmLoading.value = true;
  let formData = {};
  Object.assign(formData, data);
  formData["consultList"] = consultTableRef.value.getTableData();
  let isUpdate = false;
  if (formData.id) {
    isUpdate = true;
  }
  saveOrUpdate(formData, isUpdate).then((res) => {
    if (res.success) {
      // 保存成功
      createMessage.success(res.message);
      emit("success");
      handleClose();
    } else {
      // 保存失败
      createMessage.error(res.message);
    }
  }).finally(() => {
    confirmLoading.value = false;
  });
}

/**
 * 关闭弹窗
 */
function handleClose() {
  open.value = false;
}

defineExpose({
  handleAdd,
  handleEdit,
  handleDetail
});
</script>

<style scoped lang="less">
.title-container {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.title-bar {
  width: 5px;
  height: 22px;
  background: #ffc53d;
  border-radius: 3px;
  margin-right: 12px;
  box-shadow: 0 2px 4px rgba(255, 197, 61, 0.3);
}

.title-text {
  font-size: 14px;
  font-weight: 600;
  color: #1f1f1f;
  margin: 0;
  letter-spacing: 0.5px;
}
</style>
