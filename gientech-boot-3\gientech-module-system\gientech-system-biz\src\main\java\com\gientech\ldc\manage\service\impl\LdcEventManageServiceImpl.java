package com.gientech.ldc.manage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.ldc.clue.entry.ldcEntryClue.entity.LdcEntryClue;
import com.gientech.ldc.clue.entry.ldcEntryClue.entity.LdcEntryClueHistory;
import com.gientech.ldc.clue.entry.ldcEntryClue.mapper.LdcEntryClueHistoryMapper;
import com.gientech.ldc.clue.entry.ldcEntryClue.service.ILdcEntryClueService;
import com.gientech.ldc.clue.entry.ldcEntryClue.service.impl.LdcEntryClueServiceImpl;
import com.gientech.ldc.clue.ledger.entity.LdcLedgerEvent;
import com.gientech.ldc.clue.ledger.mapper.LdcLedgerEventMapper;
import com.gientech.ldc.manage.entity.*;
import com.gientech.ldc.manage.mapper.*;
import com.gientech.ldc.manage.service.ILdcEventManageService;
import com.gientech.ldc.param.grade.entity.LdcParamOrGradeCriterionVersion;
import com.gientech.ldc.param.grade.mapper.LdcParamOrGradeCriterionVersionMapper;
import com.gientech.module.entity.ModuleRel;
import com.gientech.module.mapper.ModuleRelMapper;
import com.gientech.rcsa.plan.entity.RcsaPlanManage;
import com.gientech.rcsa.plan.mapper.RcsaPlanManageMapper;
import com.gientech.rule.external.entity.RuleExternalBasic;
import com.gientech.rule.external.mapper.RuleExternalBasicMapper;
import com.gientech.rule.manage.reformedAndAbolished.entity.RuleSystemReformedAndAbolished;
import com.gientech.rule.manage.reformedAndAbolished.mapper.RuleSystemReformedAndAbolishedMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.FileDownloadUtils;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.mapper.SysDictItemMapper;
import org.jeecg.modules.system.mapper.SysUserMapper;
import org.jeecg.modules.system.mapper.SysUserRoleMapper;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 损失事件管理表
 * @Author: jeecg-boot
 * @Date:   2025-04-29
 * @Version: V1.0
 */
@Service
public class LdcEventManageServiceImpl extends ServiceImpl<LdcEventManageMapper, LdcEventManage> implements ILdcEventManageService {


    @Autowired
    private LdcEventManageMapper ldcEventManageMapper;
    @Autowired
    private LdcEventRecycleDetailsMapper detailsMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private ILdcEntryClueService iLdcEntryClueService;
    @Autowired
    private LdcEventHistoryMapper historyMapper;
    @Autowired
    private LdcEventDocumentMapper documentMapper;
    @Autowired
    private LdcLedgerEventMapper ledgerEventMapper;
    @Autowired
    private LdcEventNameHistoryMapper nameHistoryMapper;
    @Autowired
    private LdcEventDepartMapper eventDepartMapper;
    @Autowired
    private LdcEventClueRelMapper clueRelMapper;
    @Autowired
    private LdcEntryClueHistoryMapper ldcEntryClueHistoryMapper;
    @Autowired
    private LdcEventRecycleDetailsHistoryMapper detailsHistoryMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private LdcParamOrGradeCriterionVersionMapper gradeCriterionVersionMapper;
    @Autowired
    private SysDictItemMapper sysDictItemMapper;
    @Autowired
    private RcsaPlanManageMapper planManageMapper;
    @Autowired
    private ModuleRelMapper moduleRelMapper;
    @Autowired
    private LdcEntryClueServiceImpl clueService;
    @Autowired
    private LdcProcessRelMapper processRelMapper;
    @Autowired
    private LdcRuleRelMapper ruleRelMapper;
    @Autowired
    private RuleSystemReformedAndAbolishedMapper andAbolishedMapper;
    @Autowired
    private RuleExternalBasicMapper externalBasicMapper;

    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;

    // 全字段导出
    private static final String[] HEADERS = {
            "数据类型", "事件编号", "事件分类(一级)", "事件分类(二级)", "事件名称", "事件描述",
            "事件原因描述", "第几次填报", "是否结束", "状态", "是否为损失事件", "是否经监管认可剔除",
            "初次填报日期", "本次填报日期", "事件初始发生日期", "事件发现日期", "首笔损失入账(或确认)日期",
            "最大预估损失金额", "总损失金额", "净损失金额", "已确认的回收总金额", "其中：已确认的保险赔付",
            "类别", "子类", "损失形态", "机构", "部门", "会计科目代码", "会计科目名称", "记账币种",
            "记账金额", "对人民币汇率", "人民币金额(元)", "入账日期", "说明", "附件", "数据标记",
            "识别码", "摘要", "广大客户服务质量", "员工安全", "运营中断", "监管行动", "声誉受损",
            "事件严重度分级", "是否重大操作风险事件", "主要成因分类", "损失事件类型(一级)",
            "损失事件类型(二级)", "损失事件类型(三级)", "与信用/市场风险相关", "是否纳入计量",
            "事件填报机构", "事件填报部门", "事件填报人(登录账号)", "填报人联系方式", "总行对口管理部门",
            "发生机构", "发生部门", "关联台账类型", "关联台账编号", "发生操作风险事件的主要流程",
            "是否发起触发式评估计划", "计划编号", "计划名称", "计划状态", "是否录入操作风险管理系统-问题收集模块",
            "问题编号", "问题词条(一级分类)", "问题具体描述", "风险等级", "整改措施类型", "整改措施描述",
            "整改执行状态", "整改完成日期", "是否违反内外规", "制度文号", "制度名称", "制度发文机构", "制度时效性",
            "外规文号", "外规名称", "外规发文机构", "外规时效性", "合并状态", "合并事件编号", "合并事件名称", "合并事件描述",
            "合并事件发生损失事件的机构", "合并事件总损失金额", "合并事件净损失金额", "附件类型", "附件名称", "附件说明"
    };

    // 精简版字段导出
    private static final String[] STREAMLINE_HEADERS = {
            "数据类型", "事件编号", "事件分类(一级)", "事件分类(二级)", "事件名称", "事件描述",
            "事件原因描述", "是否结束", "事件初始发生日期", "事件发现日期",
            "总损失金额", "净损失金额", "已确认的回收总金额",
            "类别", "子类", "损失形态", "机构", "部门", "会计科目代码", "会计科目名称",
            "人民币金额(元)", "入账日期",
            "识别码", "摘要", "事件严重度分级",  "损失事件类型(一级)", "损失事件类型(二级)", "损失事件类型(三级)",
            "事件填报机构", "事件填报部门"
    };

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(LdcEventManage ldcEventManage) throws Exception {

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 判断流程类型
        String processFlag = getProcessFlag();
        if (StringUtils.isBlank(processFlag)) {
            throw new Exception("您没有权限新建");
        }
        ldcEventManage.setProcessFlag(processFlag);
        ldcEventManageMapper.insert(ldcEventManage);
        Set<String> relIdsSet = new HashSet<>();
        Set<String> manualRelIdsSet = new HashSet<>();
        List<LdcEventRecycleDetails> detailsList = ldcEventManage.getDetailsList();
        int index = 1;
        // 定义总损失金额
        BigDecimal allLostMoney = new BigDecimal("0");
        // 定义回收金额
        BigDecimal recycleMoney = new BigDecimal("0");

        for (LdcEventRecycleDetails details : detailsList) {
            details.setLdcEventId(ldcEventManage.getId());
            details.setId(null);
            detailsMapper.insert(details);

            String relIds = details.getRelIds();
            String manualRelIds = details.getManualRelIds();
            if (StringUtils.isBlank(relIds) && StringUtils.isBlank(manualRelIds)) {
                throw new Exception("损失/回收明细第" + index + "行未关联入账线索！");
            }
            String category = details.getCategory();
            String subClass = details.getSubClass();
            String lossForm = details.getLossForm();
            String document = details.getDocument();
            if (StringUtils.isBlank(category)) {
                throw new Exception("损失/回收明细第" + index + "行类别不能为空！");
            } else {
                if ("1".equals(category)) {
                    if (StringUtils.isNotBlank(subClass)) {
                        throw new Exception("损失/回收明细第" + index + "行，当类别为损失/成本时，无需填写子类字段！");
                    }
                    if (StringUtils.isBlank(lossForm)) {
                        throw new Exception("损失/回收明细第" + index + "行，当类别为损失/成本时，损失形态字段不能为空！");
                    }
                } else if ("2".equals(category)) {
                    if (StringUtils.isBlank(subClass)) {
                        throw new Exception("损失/回收明细第" + index + "行，当类别回收时，子类字段不能为空！");
                    }
                    if (StringUtils.isNotBlank(lossForm)) {
                        throw new Exception("损失/回收明细第" + index + "行，当类别为回收时，无需填写损失形态字段！");
                    }
                    if (StringUtils.isBlank(document)) {
                        throw new Exception("损失/回收明细第" + index + "行，当类别为回收时，必须上传回收证明！");
                    }
                }
            }

            if (StringUtils.isNotBlank(relIds)) {
                for (String relId : relIds.split(",")) {
                    relIdsSet.add(relId);
                }
            }
            if (StringUtils.isNotBlank(manualRelIds)) {
                for (String manualRelId : manualRelIds.split(",")) {
                    manualRelIdsSet.add(manualRelId);
                }
            }

            // 计算金额
            BigDecimal accountingAmt = iLdcEntryClueService.getAccountingAmtTenYear(relIdsSet, manualRelIdsSet);
            if ("1".equals(category)) {
                allLostMoney.add(accountingAmt);
            } else {
                recycleMoney.add(accountingAmt);
            }
            index++;
        }
        ldcEventManage.setDetailsList(detailsList);

        // 如果是复制新建
        String beCopyId = ldcEventManage.getBeCopyId();
        if (StringUtils.isNotBlank(beCopyId)) {
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("event_id", beCopyId);
            queryMap.put("del_flag", 0);
            List<LdcLedgerEvent> ledgerEventList = ledgerEventMapper.selectByMap(queryMap);
            for (LdcLedgerEvent event : ledgerEventList) {
                LdcLedgerEvent insertEvent = new LdcLedgerEvent();
                insertEvent.setLedgerId(event.getLedgerId());
                insertEvent.setEventId(ldcEventManage.getId());
                insertEvent.setDelFlag(0);
                ledgerEventMapper.insert(insertEvent);
            }
        }

        // 保存附件
        List<LdcEventDocument> documentList = ldcEventManage.getDocumentList();
        if (documentList != null && !documentList.isEmpty()) {
            for (LdcEventDocument document : documentList) {
                if (StringUtils.isBlank(document.getDocumentType())) {
                    throw new Exception("文件类型不能为空！");
                }
                if (StringUtils.isBlank(document.getDocumentName())) {
                    throw new Exception("文件不能为空！");
                }
                document.setEventId(ldcEventManage.getId());
                document.setId(null);
                documentMapper.insert(document);
            }
        }
        // 判断是否需要生成评估任务
        //RcsaPlanManage planManage = new RcsaPlanManage();
        String orgId = sysUser.getOrgId();
        SysDepart sysDepart = sysDepartService.getDepartById(orgId);
        String mainProcesses[] = ldcEventManage.getMainProcess().split(",");

        // 入库流程关联表
        for (String mainProcess : mainProcesses) {
            LdcProcessRel rel = new LdcProcessRel();
            rel.setMatrixName(mainProcess);
            rel.setLdcId(ldcEventManage.getId());
            processRelMapper.insert(rel);
        }

        // 校验是否为合并事件
        String nodeLevel = ldcEventManage.getNodeLevel();
        if (StringUtils.isNotBlank(nodeLevel) && Integer.parseInt(nodeLevel) > 1) {
            // 如果是，获取子节点
            String mergeIds[] = ldcEventManage.getMergeId().split(",");
            for (String mergeId : mergeIds) {
                LdcEventManage updateMergedManage = new LdcEventManage();
                updateMergedManage.setId(mergeId);
                LdcEventManage mergedManage = ldcEventManageMapper.selectById(mergeId);
                // 如果是第一层，直接放入被合并页面，合并状态更新为被合并，流程状态结束，第一层不显示
                // 如果是合并项，放入第二层，合并状态还是合并后，流程状态结束，第一层不显示
                updateMergedManage.setIsShowPage1("0");
                updateMergedManage.setIsParticipateProcess("0");
                updateMergedManage.setMergeState("2");
                if (mergedManage.getNodeLevel().equals("1")) {
                    updateMergedManage.setIsShowPage2("0");
                    updateMergedManage.setIsShowPage3("1");
                } else {
                    updateMergedManage.setIsShowPage2("1");
                    updateMergedManage.setIsShowPage3("0");
                }
                updateMergedManage.setParentId(ldcEventManage.getId());
                ldcEventManageMapper.updateById(updateMergedManage);

                // 更新关联关系表的删除状态为删除，且年份是当前的
                // moduleRelMapper.updateDelFlag("1", mergeId, nowYear);
            }
            String relLedgerId = ldcEventManage.getRelLedgerId();
            if (StringUtils.isNotBlank(relLedgerId)) {
                // 如果不存在，则入库
                int count = ledgerEventMapper.getCountByEventId(ldcEventManage.getId());
                if (count == 0) {
                    String[] relLedgerIds = relLedgerId.split(",");
                    for (String ledgerId: relLedgerIds) {
                        LdcLedgerEvent event = new LdcLedgerEvent();
                        event.setLedgerId(ledgerId);
                        event.setEventId(ldcEventManage.getId());
                        event.setDelFlag(0);
                        ledgerEventMapper.insert(event);
                    }
                }
            }
        }

        // 入库关联RCSA数据
        if (StringUtils.isNotBlank(ldcEventManage.getIsInitiateRcsa())) {
            if ("1".equals(ldcEventManage.getIsInitiateRcsa())) {
                RcsaPlanManage planManage = new RcsaPlanManage();
                planManage.setPlanType("2");
                planManage.setState("1");
                planManage.setRelModule("2");
                planManage.setPlanDescription(sysDepart.getDepartName() + "LDC关联流程" + ldcEventManage.getMainProcess());
                // 生成编号
                String year = DateUtils.formatDate(new Date(), "yyyy");
                String code = planManageMapper.getMaxCodeIndex(year);
                String codeIndex = "01";
                if (StringUtils.isNotBlank(code)) {
                    codeIndex = String.format("%02d", (Integer.parseInt(code) + 1));
                }
                planManage.setPlanCode(year + codeIndex);
                planManage.setCodeIndex(codeIndex);
                planManage.setMatrixName(ldcEventManage.getMainProcess());
                planManageMapper.insert(planManage);
                // 入库关联表
                ModuleRel moduleRel = new ModuleRel();
                moduleRel.setModuleId1(planManage.getId());
                moduleRel.setModuleId2(ldcEventManage.getId());
                moduleRel.setModuleFlag("1"); // rcsa-ldc : moduleId1-moduleId2
                moduleRelMapper.insert(moduleRel);
            }
        }

        // 关联内外规
        // 外规
        List<LdcRuleRel> externalList = ldcEventManage.getExternalList();
        for (LdcRuleRel ruleRel : externalList) {
            ruleRel.setLdcId(ldcEventManage.getId());
            ruleRel.setRuleType("2");
            ruleRelMapper.insert(ruleRel);
        }
        // 内规
        List<LdcRuleRel> interiorList = ldcEventManage.getInteriorList();
        for (LdcRuleRel ruleRel : interiorList) {
            ruleRel.setLdcId(ldcEventManage.getId());
            ruleRel.setRuleType("1");
            ruleRelMapper.insert(ruleRel);
        }

        LdcEventManage updateManage = new LdcEventManage();
        // 判断入账线索处是否包含ldc
        List<String> temporaryEntryIdUpdate = judgeTemporaryEntryId(ldcEventManage);

        // 判断经损失金额是否大于15W
        if (allLostMoney.subtract(recycleMoney).compareTo(new BigDecimal("150000")) >= 0) {
            updateManage.setIsIntoMetering("1");
        } else {
            updateManage.setIsIntoMetering("0");
        }

        updateManage.setId(ldcEventManage.getId());
        updateManage.setRelIds(String.join(",", relIdsSet));
        updateManage.setManualRelIds(String.join(",", manualRelIdsSet));
        if (temporaryEntryIdUpdate.size()>0) {
            updateManage.setTemporaryEntryId(String.join(",", temporaryEntryIdUpdate));
        }
        ldcEventManageMapper.updateById(updateManage);

        // 变更入账线索状态
        Map<String, Object> queryClueMap = new HashMap<>();
        queryClueMap.put("event_id", updateManage.getId());
        List<LdcEventClueRel> relList = clueRelMapper.selectByMap(queryClueMap);
        List<String> relClueIdList = new ArrayList<>();
        for (LdcEventClueRel clueRel : relList) {
            relClueIdList.add(clueRel.getClueId());
        }

        // 取出relIds中存在的，relClueIdList中没有的，直接入库
        Set<String> needInset = relIdsSet.stream()
                .filter(id -> !relClueIdList.contains(id))
                .collect(Collectors.toSet());
        for (String clueId : needInset) {
            // 入库关联关系表
            LdcEventClueRel rel = new LdcEventClueRel();
            rel.setClueId(clueId);
            rel.setEventId(updateManage.getId());
            rel.setIsRelState("1");
            clueRelMapper.insert(rel);
            // 更新clue表的状态
            LdcEntryClue clue = new LdcEntryClue();
            clue.setId(clueId);
            clue.setClueStatus("9");
            iLdcEntryClueService.updateById(clue);
            // 入库操作记录表
            LdcEntryClueHistory clueHistory = new LdcEntryClueHistory();
            clueHistory.setEntryClueId(clueId);
            String description = sysUser.getUsername() + "在ldc管理模块将损失事件["+ldcEventManage.getEventName()+"]关联入账线索";
            clueHistory.setProcessDescribe(description);
            ldcEntryClueHistoryMapper.insert(clueHistory);
        }

        // 取出relIds中存在的，relClueIdList中存在的，判断是否更新状态值
        Set<String> needUpdate = relIdsSet.stream()
                .filter(relClueIdList::contains)
                .collect(Collectors.toSet());
        for (String clueId : needUpdate) {
            LdcEventClueRel eventClueRel = relList.stream().filter(
                    o -> o.getClueId().equals(clueId))
                    .collect(Collectors.toList()).get(0);
            if (eventClueRel.getIsRelState().equals("0")) {
                eventClueRel.setIsRelState("1");
                clueRelMapper.updateById(eventClueRel);
                // 入库操作记录表
                LdcEntryClueHistory clueHistory = new LdcEntryClueHistory();
                clueHistory.setEntryClueId(clueId);
                String description = sysUser.getUsername() + "在ldc管理模块确认损失事件["+ldcEventManage.getEventName()+"]关联入账线索";
                clueHistory.setProcessDescribe(description);
                ldcEntryClueHistoryMapper.insert(clueHistory);
            }
        }

        // 取出relIds中不存在的，relClueIdList中存在的，删除并判断是否变更状态
        List<String> needDelete = relClueIdList.stream()
                .filter(id -> !relIdsSet.contains(id))
                .collect(Collectors.toList());
        for (String clueId : needDelete) {
            // 如果是在relClueIdList中存在，且状态为0的，不变动
            LdcEventClueRel eventClueRel = relList.stream().filter(
                            o -> o.getClueId().equals(clueId))
                    .collect(Collectors.toList()).get(0);
            if (!eventClueRel.getSource().equals("1")) {
                Map<String, Object> relDeleteMap = new HashMap<>();
                relDeleteMap.put("event_id", ldcEventManage.getId());
                relDeleteMap.put("clue_id", clueId);
                clueRelMapper.deleteByMap(relDeleteMap);
                // 判断是否需要变更状态
                int count = clueRelMapper.selectCountByClueId(clueId);
                if (count == 0) {
                    // 表示没有其他地方使用，状态变更为待反馈
                    LdcEntryClue clue = new LdcEntryClue();
                    clue.setId(clueId);
                    clue.setClueStatus("3");
                    iLdcEntryClueService.updateById(clue);
                }
                // 入库操作记录表
                LdcEntryClueHistory clueHistory = new LdcEntryClueHistory();
                clueHistory.setEntryClueId(clueId);
                String description = sysUser.getUsername() + "在ldc管理模块将损失事件["+ldcEventManage.getEventName()+"]解除关联入账线索";
                clueHistory.setProcessDescribe(description);
                ldcEntryClueHistoryMapper.insert(clueHistory);
            } else {
                // 如果是1，表示从损失事件关联
                if (eventClueRel.getIsRelState().equals("1")) {
                    eventClueRel.setIsRelState("0");
                    clueRelMapper.updateById(eventClueRel);
                    // 入库操作记录表
                    LdcEntryClueHistory clueHistory = new LdcEntryClueHistory();
                    clueHistory.setEntryClueId(clueId);
                    String description = sysUser.getUsername() + "在ldc管理模块将损失事件["+ldcEventManage.getEventName()+"]解除关联入账线索";
                    clueHistory.setProcessDescribe(description);
                    ldcEntryClueHistoryMapper.insert(clueHistory);
                }
            }
        }

        // 根据流程类型判断新增的
        String nodeRole = getRole(ldcEventManage.getProcessFlag());

        LdcEventHistory history = new LdcEventHistory();
        history.setEventId(ldcEventManage.getId());
        history.setNodeRole(nodeRole);
        history.setStatus(ldcEventManage.getStatus() == null ? "1":ldcEventManage.getStatus());
        history.setOperateDepart(sysUser.getOrgId());
        history.setOperateTime(new Date());
        history.setOperateUser(sysUser.getUsername());
        history.setOperateType("创建事件");
        historyMapper.insert(history);
    }

    private String getRole(String processFlag) {
        String nodeRole = "";
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 获取人员角色
        List<String> roleList = sysUserRoleMapper.getRoleCodeByUserId(sysUser.getId());
        if (roleList.contains("fh_cfzg") && "1".equals(processFlag)) {
            // 如果是分行-操风专岗
            nodeRole = "fh_cfzg";
        } else if (roleList.contains("fhbm_cfglg") && "1".equals(processFlag)) {
            // 分行部门-操风管理岗
            nodeRole = "fhbm_cfglg";
        } else if (roleList.contains("zhgbm_cfglh") && "2".equals(processFlag)) {
            // 如果是总行各部门-操风管理岗
            nodeRole = "zhgbm_cfglh";
        } else if (roleList.contains("zh_ldc_glg") && "3".equals(processFlag)) {
            // 如果是总行-LDC管理岗
            nodeRole = "zh_ldc_glg";
        } else if (roleList.contains("zgs_cfzg") && "4".equals(processFlag)) {
            // 如果是子公司-操风专岗
            nodeRole = "zgs_cfzg";
        }  else if (roleList.contains("czyh_cfzg") && "5".equals(processFlag)) {
            // 如果是村镇银行-操风专岗
            nodeRole = "czyh_cfzg";
        } else {
            nodeRole = sysUser.getRealname();
        }
        return nodeRole;
    }

    private List<String> judgeTemporaryEntryId(LdcEventManage ldcEventManage) {
        List<String> temporaryEntryIdUpdate = new ArrayList<>();
        String temporaryEntryId = ldcEventManage.getTemporaryEntryId();
        if (StringUtils.isNotBlank(temporaryEntryId)) {
            String relIds = ldcEventManage.getRelIds();
            if (StringUtils.isNotBlank(relIds)) {
                List<String> relIdsList = Arrays.asList(relIds.split(","));
                String temporaryEntryIds[] = temporaryEntryId.split(",");
                for (String id : temporaryEntryIds) {
                    if (!relIdsList.contains(id)) {
                        temporaryEntryIdUpdate.add(id);
                    }
                }
            } else {
                temporaryEntryIdUpdate = Arrays.asList(temporaryEntryId.split(","));
            }
        }
        return temporaryEntryIdUpdate;
    }

    private String getProcessFlag() {
        String processFlag = "";
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 获取人员角色
        List<String> roleList = sysUserRoleMapper.getRoleCodeByUserId(sysUser.getId());
        if (roleList.contains("fh_cfzg")) {
            // 如果是分行-操风专岗
            processFlag = "1";
        } else if (roleList.contains("fhbm_cfglg")) {
            // 分行部门-操风管理岗
            processFlag = "1";
        } else if (roleList.contains("zhgbm_cfglh")) {
            // 如果是总行各部门-操风管理岗
            processFlag = "2";
        } else if (roleList.contains("zh_ldc_glg")) {
            // 如果是总行-LDC管理岗
            processFlag = "3";
        } else if (roleList.contains("zgs_cfzg")) {
            // 如果是子公司-操风专岗
            processFlag = "4";
        }  else if (roleList.contains("czyh_cfzg")) {
            // 如果是村镇银行-操风专岗
            processFlag = "5";
        } else {

        }
        return processFlag;
    }

    @Override
    public Result<List<LdcEventRecycleDetails>> getRecycleListByManageId(LdcEventManage ldcEventManage) {
        String removeIds = ldcEventManage.getRelIds();
        String manualRemoveIds = ldcEventManage.getManualRelIds();
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("ldc_event_id", ldcEventManage.getId());
        List<LdcEventRecycleDetails> result = detailsMapper.selectByMap(queryMap);
        for (LdcEventRecycleDetails details : result) {
            details.setRemoveIds(removeIds);
            // 还要考虑手工新增的，先不考虑 TODO:huanjing
            details.setManualRemoveIds(manualRemoveIds);
        }
        return Result.OK(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(LdcEventManage ldcEventManage) throws Exception {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 判断流程类型
        if (StringUtils.isBlank(ldcEventManage.getProcessFlag())) {
            String processFlag = getProcessFlag();
            if (StringUtils.isBlank(processFlag)) {
                throw new Exception("您没有权限进行操作");
            }
            ldcEventManage.setProcessFlag(processFlag);
        }

        Set<String> relIdsSet = new HashSet<>();
        Set<String> manualRelIdsSet = new HashSet<>();

        // 删除所有的明细
        // detailsMapper.deleteByMainId(ldcEventManage.getId());
        List<LdcEventRecycleDetails> detailsList = ldcEventManage.getDetailsList();
        int index = 1;
        // 定义总损失金额
        BigDecimal allLostMoney = new BigDecimal("0");
        // 定义回收金额
        BigDecimal recycleMoney = new BigDecimal("0");
        for (LdcEventRecycleDetails details : detailsList) {
            String insertFlag = "1";
            String id = details.getId();
            if (StringUtils.isNotBlank(id)) {
                if (id.contains("row")) {
                    details.setId(null);
                } else {
                    insertFlag = "0";
                }
            }
            details.setLdcEventId(ldcEventManage.getId());
            if ("0".equals(insertFlag)) {
                detailsMapper.updateById(details);
            } else {
                // 重新入库
                detailsMapper.insert(details);
            }

            String relIds = details.getRelIds();
            String manualRelIds = details.getManualRelIds();
            if (StringUtils.isBlank(relIds) && StringUtils.isBlank(manualRelIds)) {
                throw new Exception("损失/回收明细第" + index + "行未关联入账线索！");
            }
            String category = details.getCategory();
            String subClass = details.getSubClass();
            String lossForm = details.getLossForm();
            String document = details.getDocument();
            if (StringUtils.isBlank(category)) {
                throw new Exception("损失/回收明细第" + index + "行类别不能为空！");
            } else {
                if ("1".equals(category)) {
                    if (StringUtils.isNotBlank(subClass)) {
                        throw new Exception("损失/回收明细第" + index + "行，当类别为损失/成本时，无需填写子类字段！");
                    }
                    if (StringUtils.isBlank(lossForm)) {
                        throw new Exception("损失/回收明细第" + index + "行，当类别为损失/成本时，损失形态字段不能为空！");
                    }
                } else if ("2".equals(category)) {
                    if (StringUtils.isBlank(subClass)) {
                        throw new Exception("损失/回收明细第" + index + "行，当类别回收时，子类字段不能为空！");
                    }
                    if (StringUtils.isNotBlank(lossForm)) {
                        throw new Exception("损失/回收明细第" + index + "行，当类别为回收时，无需填写损失形态字段！");
                    }
                    if (StringUtils.isBlank(document)) {
                        throw new Exception("损失/回收明细第" + index + "行，当类别为回收时，必须上传回收证明！");
                    }
                }
            }

            if (StringUtils.isNotBlank(relIds)) {
                for (String relId : relIds.split(",")) {
                    relIdsSet.add(relId);
                }
            }
            if (StringUtils.isNotBlank(manualRelIds)) {
                for (String manualRelId : manualRelIds.split(",")) {
                    manualRelIdsSet.add(manualRelId);
                }
            }

            // 计算金额
            BigDecimal accountingAmt = iLdcEntryClueService.getAccountingAmtTenYear(relIdsSet, manualRelIdsSet);
            if ("1".equals(category)) {
                allLostMoney.add(accountingAmt);
            } else {
                recycleMoney.add(accountingAmt);
            }
            index++;
        }

        // 删除多余的回收明细
        detailsMapper.deleteRedundant(detailsList, ldcEventManage.getId());

        ldcEventManage.setRelIds(String.join(",", relIdsSet));
        ldcEventManage.setManualRelIds(String.join(",", manualRelIdsSet));

        // 判断入账线索处是否包含ldc
        List<String> temporaryEntryIdUpdate = judgeTemporaryEntryId(ldcEventManage);
        if (temporaryEntryIdUpdate.size()>0) {
            ldcEventManage.setTemporaryEntryId(String.join(",", temporaryEntryIdUpdate));
        }


        // 保存附件
        documentMapper.deleteByMainId(ldcEventManage.getId());
        List<LdcEventDocument> documentList = ldcEventManage.getDocumentList();
        if (documentList != null && !documentList.isEmpty()) {
            for (LdcEventDocument document : documentList) {
                if (StringUtils.isBlank(document.getDocumentType())) {
                    throw new Exception("文件类型不能为空！");
                }
                if (StringUtils.isBlank(document.getDocumentName())) {
                    throw new Exception("文件不能为空！");
                }
                document.setEventId(ldcEventManage.getId());
                document.setId(null);
                documentMapper.insert(document);
            }
        }

        // 删除所有的关联关系
        Map<String, Object> deleteRelMap = new HashMap<>();
        deleteRelMap.put("ldc_id", ldcEventManage.getId());
        processRelMapper.deleteByMap(deleteRelMap);
        String mainProcess1 = ldcEventManage.getMainProcess();
        if (StringUtils.isNotBlank(mainProcess1)) {
            String mainProcesses[] = ldcEventManage.getMainProcess().split(",");
            // 入库流程关联表
            for (String mainProcess : mainProcesses) {
                LdcProcessRel rel = new LdcProcessRel();
                rel.setMatrixName(mainProcess);
                rel.setLdcId(ldcEventManage.getId());
                processRelMapper.insert(rel);
            }
        }

        // 删除所有的关联关系
        // moduleRelMapper.deleteRcsaLdcRel("1", ldcEventManage.getId());
        // 判断编辑时是否需要关联rcsa，如果不关联，isCancelRel置为 1，
        // 如果关联，先判断是否存在关联关系数据，没有则新增，有则更新，将isCancelRel置为0，delFlag置为0
        String isInitiateRcsa = ldcEventManage.getIsInitiateRcsa();
        int countRcsa = moduleRelMapper.queryExistRcsaRelCount("1", ldcEventManage.getId());
        String orgId = sysUser.getOrgId();
        SysDepart sysDepart = sysDepartService.getDepartById(orgId);



//        if ("1".equals(isInitiateRcsa) && "1".equals(isRcsaPlan)) {

//            for (String mainProcess : mainProcesses) {
                // 判断RCSA计划是否已经存在
//                RcsaPlanManage rcsaPlanManage = planManageMapper.judgePlanIsExist(nowYear, mainProcess, "2");
//                if (rcsaPlanManage == null ) {
//                    RcsaPlanManage planManage = new RcsaPlanManage();
//                    planManage.setPlanType("2");
//                    planManage.setState("1");
//                    planManage.setRelModule("2");
//                    planManage.setPlanDescription(sysDepart.getDepartName() + "LDC关联" + mainProcess);
//                    // 生成编号
//                    String year = DateUtils.formatDate(new Date(), "yyyy");
//                    String code = planManageMapper.getMaxCodeIndex(year);
//                    String codeIndex = "01";
//                    if (StringUtils.isNotBlank(code)) {
//                        codeIndex = String.format("%02d", (Integer.parseInt(code) + 1));
//                    }
//                    planManage.setPlanCode(year+codeIndex);
//                    planManage.setCodeIndex(codeIndex);
//                    planManage.setMatrixName(mainProcess);
//                    planManageMapper.insert(planManage);
//                    // 入库关联表
//                    ModuleRel moduleRel = new ModuleRel();
//                    moduleRel.setModuleId1(planManage.getId());
//                    moduleRel.setModuleId2(ldcEventManage.getId());
//                    moduleRel.setModuleFlag("1"); // rcsa-ldc : moduleId1-moduleId2
//                    moduleRelMapper.insert(moduleRel);
//                } else {
//                    // 入库关联表
//                    ModuleRel moduleRel = new ModuleRel();
//                    moduleRel.setModuleId1(rcsaPlanManage.getId());
//                    moduleRel.setModuleId2(ldcEventManage.getId());
//                    moduleRel.setModuleFlag("1"); // rcsa-ldc : moduleId1-moduleId2
//                    moduleRelMapper.insert(moduleRel);
//                }
//            }
//        }

        if ("1".equals(isInitiateRcsa)) {
            if (countRcsa == 0) {
                RcsaPlanManage planManage = new RcsaPlanManage();
                planManage.setPlanType("2");
                planManage.setState("1");
                planManage.setRelModule("2");
                planManage.setPlanDescription(sysDepart.getDepartName() + "LDC关联" + ldcEventManage.getMainProcess());
                // 生成编号
                String year = DateUtils.formatDate(new Date(), "yyyy");
                String code = planManageMapper.getMaxCodeIndex(year);
                String codeIndex = "01";
                if (StringUtils.isNotBlank(code)) {
                    codeIndex = String.format("%02d", (Integer.parseInt(code) + 1));
                }
                planManage.setPlanCode(year+codeIndex);
                planManage.setCodeIndex(codeIndex);
                planManage.setMatrixName(ldcEventManage.getMainProcess());
                planManageMapper.insert(planManage);
                // 入库关联表
                ModuleRel moduleRel = new ModuleRel();
                moduleRel.setModuleId1(planManage.getId());
                moduleRel.setModuleId2(ldcEventManage.getId());
                moduleRel.setModuleFlag("1"); // rcsa-ldc : moduleId1-moduleId2
                moduleRelMapper.insert(moduleRel);
            } else {
                moduleRelMapper.updateRcsaRel("1", ldcEventManage.getId(), "0");
                // 变更关联流程（万一流程改了）
                planManageMapper.updateMainProcess(ldcEventManage);
            }
        } else {
            moduleRelMapper.updateRcsaRel("1", ldcEventManage.getId(), "1");
        }

        // 关联内外规
        // 先删除全部
        Map<String, Object> ruleDeleteMap = new HashMap<>();
        ruleDeleteMap.put("ldc_id", ldcEventManage.getId());
        ruleRelMapper.deleteByMap(ruleDeleteMap);
        // 外规
        List<LdcRuleRel> externalList = ldcEventManage.getExternalList();
        for (LdcRuleRel ruleRel : externalList) {
            ruleRel.setLdcId(ldcEventManage.getId());
            ruleRel.setRuleType("2");
            ruleRelMapper.insert(ruleRel);
        }
        // 内规
        List<LdcRuleRel> interiorList = ldcEventManage.getInteriorList();
        for (LdcRuleRel ruleRel : interiorList) {
            ruleRel.setLdcId(ldcEventManage.getId());
            ruleRel.setRuleType("1");
            ruleRelMapper.insert(ruleRel);
        }
        // 判断经损失金额是否大于15W
        if (allLostMoney.subtract(recycleMoney).compareTo(new BigDecimal("150000")) >= 0) {
            ldcEventManage.setIsIntoMetering("1");
        } else {
            ldcEventManage.setIsIntoMetering("0");
        }

        ldcEventManageMapper.updateById(ldcEventManage);

        // 变更入账线索状态
        Map<String, Object> queryClueMap = new HashMap<>();
        queryClueMap.put("event_id", ldcEventManage.getId());
        List<LdcEventClueRel> relList = clueRelMapper.selectByMap(queryClueMap);
        List<String> relClueIdList = new ArrayList<>();
        for (LdcEventClueRel clueRel : relList) {
            relClueIdList.add(clueRel.getClueId());
        }

        // 取出relIds中存在的，relClueIdList中没有的，直接入库
        Set<String> needInset = relIdsSet.stream()
                .filter(id -> !relClueIdList.contains(id))
                .collect(Collectors.toSet());
        for (String clueId : needInset) {
            // 入库关联关系表
            LdcEventClueRel rel = new LdcEventClueRel();
            rel.setClueId(clueId);
            rel.setEventId(ldcEventManage.getId());
            rel.setIsRelState("1");
            clueRelMapper.insert(rel);
            // 更新clue表的状态
            LdcEntryClue clue = new LdcEntryClue();
            clue.setId(clueId);
            clue.setClueStatus("9");
            iLdcEntryClueService.updateById(clue);
            // 入库操作记录表
            LdcEntryClueHistory clueHistory = new LdcEntryClueHistory();
            clueHistory.setEntryClueId(clueId);
            String description = sysUser.getUsername() + "在ldc管理模块将损失事件["+ldcEventManage.getEventName()+"]关联入账线索";
            clueHistory.setProcessDescribe(description);
            ldcEntryClueHistoryMapper.insert(clueHistory);
        }

        // 取出relIds中存在的，relClueIdList中存在的，判断是否更新状态值
        Set<String> needUpdate = relIdsSet.stream()
                .filter(relClueIdList::contains)
                .collect(Collectors.toSet());
        for (String clueId : needUpdate) {
            LdcEventClueRel eventClueRel = relList.stream().filter(
                            o -> o.getClueId().equals(clueId))
                    .collect(Collectors.toList()).get(0);
            if (eventClueRel.getIsRelState().equals("0")) {
                eventClueRel.setIsRelState("1");
                clueRelMapper.updateById(eventClueRel);
                // 入库操作记录表
                LdcEntryClueHistory clueHistory = new LdcEntryClueHistory();
                clueHistory.setEntryClueId(clueId);
                String description = sysUser.getUsername() + "在ldc管理模块确认损失事件["+ldcEventManage.getEventName()+"]关联入账线索";
                clueHistory.setProcessDescribe(description);
                ldcEntryClueHistoryMapper.insert(clueHistory);
            }
        }

        // 取出relIds中不存在的，relClueIdList中存在的，删除并判断是否变更状态
        List<String> needDelete = relClueIdList.stream()
                .filter(id -> !relIdsSet.contains(id))
                .collect(Collectors.toList());
        for (String clueId : needDelete) {
            // 如果是在relClueIdList中存在，且状态为0的，不变动
            LdcEventClueRel eventClueRel = relList.stream().filter(
                            o -> o.getClueId().equals(clueId))
                    .collect(Collectors.toList()).get(0);
            if (!eventClueRel.getSource().equals("1")) {
                Map<String, Object> relDeleteMap = new HashMap<>();
                relDeleteMap.put("event_id", ldcEventManage.getId());
                relDeleteMap.put("clue_id", clueId);
                clueRelMapper.deleteByMap(relDeleteMap);
                // 判断是否需要变更状态
                int count = clueRelMapper.selectCountByClueId(clueId);
                if (count == 0) {
                    // 表示没有其他地方使用，状态变更为待反馈
                    LdcEntryClue clue = new LdcEntryClue();
                    clue.setId(clueId);
                    clue.setClueStatus("3");
                    iLdcEntryClueService.updateById(clue);
                }
                // 入库操作记录表
                LdcEntryClueHistory clueHistory = new LdcEntryClueHistory();
                clueHistory.setEntryClueId(clueId);
                String description = sysUser.getUsername() + "在ldc管理模块将损失事件["+ldcEventManage.getEventName()+"]解除关联入账线索";
                clueHistory.setProcessDescribe(description);
                ldcEntryClueHistoryMapper.insert(clueHistory);
            } else {
                // 如果是1，表示从损失事件关联
                if (eventClueRel.getIsRelState().equals("1")) {
                    eventClueRel.setIsRelState("0");
                    clueRelMapper.updateById(eventClueRel);
                    // 入库操作记录表
                    LdcEntryClueHistory clueHistory = new LdcEntryClueHistory();
                    clueHistory.setEntryClueId(clueId);
                    String description = sysUser.getUsername() + "在ldc管理模块将损失事件["+ldcEventManage.getEventName()+"]解除关联入账线索";
                    clueHistory.setProcessDescribe(description);
                    ldcEntryClueHistoryMapper.insert(clueHistory);
                }
            }
        }

        // 根据流程类型判断新增的
//        String nodeRole = getRole(ldcEventManage.getProcessFlag());
//
//        LdcEventHistory history = new LdcEventHistory();
//        history.setEventId(ldcEventManage.getId());
//        history.setNodeRole(nodeRole);
//        history.setStatus(ldcEventManage.getStatus());
//        history.setOperateDepart(sysUser.getOrgId());
//        history.setOperateTime(new Date());
//        history.setOperateUser(sysUser.getUsername());
//        history.setOperateType("编辑事件");
//        historyMapper.insert(history);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitFromForm(LdcEventManage ldcEventManage) throws Exception {
        LoginUser sysUser =(LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userId = sysUser.getUsername();
        ldcEventManage.setApplyUser(userId);
        ldcEventManage.setApplyUserName(sysUser.getRealname());
        ldcEventManage.setApplyTime(DateUtils.now());
        ldcEventManage.setReachTime(DateUtils.now());
        String ordId = sysUser.getId();
        // 获取机构名称
        SysDepart sysDepart = sysDepartService.getById(ordId);
        ldcEventManage.setApplyDepart(sysUser.getOrgId());
        ldcEventManage.setApplyDepartName(sysDepart.getDepartName());
        // 判断是否存在该数据
        String id = ldcEventManage.getId();
        if (StringUtils.isBlank(id)) { // 新增
            String processFlag = getProcessFlag();
            String status = getStatusSecondStep(processFlag);
            ldcEventManage.setStatus(status);
            add(ldcEventManage);
        } else { // 保存
            // 根据processFlag，判断流转状态
            String status = getStatusSecondStep(ldcEventManage.getProcessFlag());
            ldcEventManage.setStatus(status);
            // 判断状态 (还是无法避免分布式操作，心理安慰)
            LdcEventManage queryManage = ldcEventManageMapper.selectById(ldcEventManage.getStatus());
            if (!queryManage.getStatus().equals("1")) {
                throw new Exception("该损失事件已被提交！");
            }
            // 保存数据
            edit(ldcEventManage);
        }
        String nodeRole = getSubmitRole(ldcEventManage.getStatus());
        // 入库提交记录
        LdcEventHistory history = new LdcEventHistory();
        history.setEventId(ldcEventManage.getId());
        history.setNodeRole(nodeRole);
        history.setStatus(ldcEventManage.getStatus());
        history.setOperateDepart(sysUser.getOrgId());
        history.setOperateTime(new Date());
        history.setOperateUser(sysUser.getUsername());
        history.setOperateType("提交事件");
        historyMapper.insert(history);
    }

    private String getSubmitRole(String status) {
        String nodeRole = "";
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 获取人员角色
        List<String> roleList = sysUserRoleMapper.getRoleCodeByUserId(sysUser.getId());
        if (status.equals("2")) {
            if(roleList.contains("fh_cfzg")) {
                nodeRole = "fh_cfzg";
            } else if (roleList.contains("fhbm_cfglg")) {
                nodeRole = "fhbm_cfglg";
            }
        } else if (status.equals("4")) {
            nodeRole = "zhgbm_cfglh";
        } else if (status.equals("5")) {
            nodeRole = "zh_ldc_glg";
        } else if (status.equals("15")) {
            nodeRole = "zgs_cfzg";
        } else if (status.equals("17")) {
            nodeRole = "czyh_cfzg";
        } else if (status.equals("3")) {
            nodeRole = "fh_cfshg";
        } else if (status.equals("14")) {
            nodeRole = "zh_ldc_shg";
        }
        return nodeRole;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitFormList(LdcEventManage ldcEventManage) throws Exception {
        LoginUser sysUser =(LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userId = sysUser.getUsername();
        String ordId = sysUser.getOrgId();
        // 获取机构名称
        SysDepart sysDepart = sysDepartService.getById(ordId);
        List<String> ids = ldcEventManage.getIds();
        for (String id : ids) {
            LdcEventManage manage = ldcEventManageMapper.selectById(id);
            if (manage.getOpinionType().equals("2")) {
                throw new Exception(manage.getEventName() + "的审核意见类型为终止报送，不允许提交，请废止");
            }

            if (!manage.getStatus().equals("1") && !manage.getStatus().equals("11")
                && !manage.getStatus().equals("27")) {
                throw new Exception(manage.getEventName() + "不允许提交，请检查事件状态");
            }
            LdcEventManage updateManage = new LdcEventManage();
            updateManage.setId(id);
            String status = getStatusSecondStep(manage.getProcessFlag());
            updateManage.setStatus(status);
            updateManage.setApplyUser(userId);
            updateManage.setApplyUserName(sysUser.getRealname());
            updateManage.setApplyTime(DateUtils.now());
            updateManage.setReachTime(DateUtils.now());
            updateManage.setApplyDepart(sysUser.getOrgId());
            updateManage.setApplyDepartName(sysDepart.getDepartName());
            ldcEventManageMapper.updateById(updateManage);

            String nodeRole = getSubmitRole(status);
            // 入库提交记录
            LdcEventHistory history = new LdcEventHistory();
            history.setEventId(id);
            history.setNodeRole(nodeRole);
            history.setStatus(status);
            history.setOperateDepart(sysUser.getOrgId());
            history.setOperateTime(new Date());
            history.setOperateUser(sysUser.getUsername());
            history.setOperateType("提交事件");
            historyMapper.insert(history);

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleWithdrawToFirst(LdcEventManage ldcEventManage) throws Exception {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 判断状态是否允许撤回
        List<String> ids = ldcEventManage.getIds();
        for (String id : ids) {
            LdcEventManage manage = ldcEventManageMapper.selectById(id);
            String processFlag = manage.getProcessFlag();
            String status = manage.getStatus();
            String canWithdrawToFirst = withdrawToFirst(processFlag, status);
            if ("0".equals(canWithdrawToFirst)) {
                throw new Exception(manage.getEventName() + "不允许撤回！");
            }
            LdcEventManage updateManage = new LdcEventManage();
            updateManage.setId(id);
            // 从历史表查找最新的状态
            LdcEventHistory historyList = historyMapper.selectPreviousHistory(id, status);
            updateManage.setStatus(historyList.getStatus());
            ldcEventManageMapper.updateById(updateManage);

            // 入库提交记录
            LdcEventHistory history = new LdcEventHistory();
            history.setEventId(id);
            history.setNodeRole(canWithdrawToFirst);
            history.setStatus("-1");
            history.setOperateDepart(sysUser.getOrgId());
            history.setOperateTime(new Date());
            history.setOperateUser(sysUser.getUsername());
            history.setOperateType("撤回事件");
            historyMapper.insert(history);
        }
    }


    @Override
    public void getAuditListByRole(List<String> statuList, List<String> departList, List<String> processFlagList) {
        // 获取人员角色
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> roleList = sysUserRoleMapper.getRoleCodeByUserId(sysUser.getId());
        String orgId = sysUser.getOrgId();

        List<String> subDepIdsByDepId = sysDepartService.getSubDepIdsByDepId(orgId);
        // 放入机构
        departList.addAll(subDepIdsByDepId);

        // 待定：要是某个人既有A角色又有B角色，应该从最大的角色开始遍历
        if (roleList.contains("nkhgfzr_ldc_shg")) { // 内控合规负责人-LDC审核岗
            // 放入状态
            Collections.addAll(statuList, "14");
            // 放入流程
            Collections.addAll(processFlagList, "1", "2", "3", "4", "5");
        } else if (roleList.contains("zh_ldc_shg")) { // 总行-LDC审核岗
            // 放入状态
            Collections.addAll(statuList, "5");
            // 放入流程
            Collections.addAll(processFlagList, "1", "2", "3", "4", "5");
        } else if (roleList.contains("fhfzr_cfshg")) { // 分行负责人-操风审核岗
            // 放入状态
            Collections.addAll(statuList, "3");
            // 放入流程
            Collections.addAll(processFlagList, "1");
        } else if (roleList.contains("fh_cfshg")) { // 分行操风审核岗
            // 放入状态
            Collections.addAll(statuList, "2");
            // 放入流程
            Collections.addAll(processFlagList, "1");
        } else if (roleList.contains("zhgbm_cfshg")) { // 总行各部门-操风审核岗
            // 放入状态
            Collections.addAll(statuList, "4");
            // 放入流程
            Collections.addAll(processFlagList, "2");
        } else if (roleList.contains("zgsfzr_cfshg")) { // 子公司负责人-操风审核岗
            // 放入状态
            Collections.addAll(statuList, "16");
            // 放入流程
            Collections.addAll(processFlagList, "4");
        } else if (roleList.contains("zgshgfzr_cfshg")) { // 子公司合规负责人-操风审核岗
            // 放入状态
            Collections.addAll(statuList, "15");
            // 放入流程
            Collections.addAll(processFlagList, "4");
        } else if (roleList.contains("czzbfzr_cfshg")) { // 村镇总部负责人-操风审核岗
            // 放入状态
            Collections.addAll(statuList, "19");
            // 放入流程
            Collections.addAll(processFlagList, "5");
        } else if (roleList.contains("czyhzbhgfzr_cfshg")) { // 村镇银行总部合规负责人-操风审核岗
            // 放入状态
            Collections.addAll(statuList, "18");
            // 放入流程
            Collections.addAll(processFlagList, "5");
        } else if (roleList.contains("czyhzb_cfshg")) { // 村镇银行总部-操风审核岗
            // 放入状态
            Collections.addAll(statuList, "17");
            // 放入流程
            Collections.addAll(processFlagList, "5");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditManagePass(LdcEventManage ldcEventManage) throws Exception {
        // 获取人员角色
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 获取原数据信息
        LdcEventManage manage = ldcEventManageMapper.selectById(ldcEventManage.getId());
        String fillCountFlag = manage.getFillCountFlag();
        String processFlag = manage.getProcessFlag();
        String status = manage.getStatus();
        String nextStatus = getStatusNextStep(processFlag, status);
        if (StringUtils.isBlank(nextStatus)) {
            throw new Exception("未找到下一步流程节点，请检查数据");
        }
        ldcEventManage.setStatus(nextStatus);

        // 放入上一环节审核人信息
        ldcEventManage.setPreviousAuditState("审核通过");
        ldcEventManage.setPreviousAuditUser(sysUser.getUsername());
        ldcEventManage.setPreviousAuditUserName(sysUser.getRealname());
        ldcEventManage.setPreviousAuditOpinion(ldcEventManage.getAuditOpinion());

        if ("0".equals(manage.getIsNullify())) {
            if ("1".equals(fillCountFlag)) { // 如果不是第二次更新
                ldcEventManage.setFillCount(manage.getFillCount() + 1);
            } else {
                // 如果最后一步流程，变更填报次数标记
                if ("6".equals(nextStatus)) {
                    ldcEventManage.setFillCountFlag("1");
                    // 先删除总行对口管理部门
                    Map<String, Object> deleteMap = new HashMap<>();
                    deleteMap.put("event_id", ldcEventManage.getId());
                    eventDepartMapper.deleteByMap(deleteMap);
                    // 重新入库
                    String[] duetManageDeparts = manage.getDuetManageDepart().split(",");
                    for (String duetManageDepart : duetManageDeparts) {
                        LdcEventDepart eventDepart = new LdcEventDepart();
                        eventDepart.setDepartNo(duetManageDepart);
                        eventDepart.setEventId(ldcEventManage.getId());
                        eventDepartMapper.insert(eventDepart);
                    }

                    // 更新附件表数据
                    documentMapper.updateDocumentState(ldcEventManage.getId());
                    // 将更新流程置为空
                    ldcEventManage.setIsUpdateProcess("0");
                    // 判断是否入库变更记录
                    String insertFlag = "0";
                    List<LdcEventNameHistory> historyList = nameHistoryMapper.getCountByEventId(ldcEventManage.getId());
                    if (historyList == null || historyList.isEmpty()) {
                        insertFlag = "1";
                    } else {
                        //比较字段是否变化
                        LdcEventNameHistory history = historyList.get(0);
                        if (!manage.getEventName().equals(history.getEventName())) {
                            insertFlag = "1";
                        }
                        if (!manage.getEventDescrption().equals(history.getEventDescrption())) {
                            insertFlag = "1";
                        }
                        if (!manage.getEventReasonDescription().equals(history.getEventReasonDescription())) {
                            insertFlag = "1";
                        }
                    }
                    if ("1".equals(insertFlag)) {
                        // 直接入库
                        LdcEventNameHistory insertHistory = new LdcEventNameHistory();
                        insertHistory.setEventId(manage.getId());
                        insertHistory.setEventName(manage.getEventName());
                        insertHistory.setEventDescrption(manage.getEventDescrption());
                        insertHistory.setEventReasonDescription(manage.getEventReasonDescription());
                        nameHistoryMapper.insert(insertHistory);
                    }
                }
            }
        }
        if ("6".equals(nextStatus)) {
            List<LdcEventRecycleDetailsHistory> addHistoryList = new ArrayList<>();
            // 如果不是作废
            if ("0".equals(manage.getIsNullify())) {
                Integer fillCount = manage.getFillCount();
                // 原本的数据
                Map<String, Object> detailsMap = new HashMap<>();
                detailsMap.put("ldc_event_id", manage.getId());
                List<LdcEventRecycleDetails> detailsList = detailsMapper.selectByMap(detailsMap);
                // 入库损失事件明细，第一次填报直接入库历史表(先做在这，后续讨论在改地方)
                if ("1".equals(fillCountFlag)) {
                    // 表示第二次审批
                    // 找到历史表上一次填报的数据，和这一次的数据进行比较
                    Map<String, Object> previousMap = new HashMap<>();
                    previousMap.put("ldc_event_id", manage.getId());
                    previousMap.put("fill_count", fillCount);
                    List<LdcEventRecycleDetailsHistory> previousHistoryList = detailsHistoryMapper.selectPreviousList(previousMap);

                    // 取出新增的
                    List<String> existingDetailIds = previousHistoryList.stream()
                            .map(LdcEventRecycleDetailsHistory::getDetailId)
                            .collect(Collectors.toList());
                    List<LdcEventRecycleDetails> addDetailsList = detailsList.stream()
                            .filter(detail -> !existingDetailIds.contains(detail.getId()))
                            .collect(Collectors.toList());
                    // 表示新增
                    for (LdcEventRecycleDetails details : addDetailsList) {
                        addHistory(addHistoryList, fillCount+1, details, manage, "1");
                    }

                    // 取出删除的
                    // 获取detailsList中所有id的集合
                    List<String> existingIds = detailsList.stream()
                            .map(LdcEventRecycleDetails::getId)
                            .collect(Collectors.toList());
                    List<LdcEventRecycleDetailsHistory> deleteDetailsList = previousHistoryList.stream()
                            .filter(history -> !existingIds.contains(history.getDetailId()))
                            .collect(Collectors.toList());
                    // 表示删除
                    for (LdcEventRecycleDetailsHistory details : deleteDetailsList) {
                        details.setFillCount(fillCount+1);
                        details.setId(null);
                        details.setOperateType("3");
                        details.setOperateTime(manage.getApplyTime());
                        details.setRelIds("");
                        details.setManualRelIds("");
                        List<String> deleteClueIdList = new ArrayList<>();
                        if (StringUtils.isNotBlank(details.getAddClueId())) {
                            deleteClueIdList.addAll(Arrays.asList(details.getAddClueId().split(",")));
                            details.setAddClueId(null);
                        }
                        if (StringUtils.isNotBlank(details.getUpdateClueId())) {
                            deleteClueIdList.addAll(Arrays.asList(details.getUpdateClueId().split(",")));
                            details.setUpdateClueId(null);
                        }
                        // 已经是删除的，不再显示
//                        if (StringUtils.isNotBlank(details.getDeleteClueId())) {
//                            deleteClueIdList.addAll(Arrays.asList(details.getDeleteClueId().split(",")));
//                        }
                        details.setDeleteClueId(String.join(",", deleteClueIdList));
                        addHistoryList.add(details);
                    }

                    // 取出全部相同的
                    List<String> existingSameIds = detailsList.stream()
                            .map(LdcEventRecycleDetails::getId)
                            .collect(Collectors.toList());
                    List<LdcEventRecycleDetailsHistory> deleteUpdateList = previousHistoryList.stream()
                            .filter(history -> existingSameIds.contains(history.getDetailId()))
                            .collect(Collectors.toList());
                    for (LdcEventRecycleDetailsHistory history : deleteUpdateList) {
                        history.setId(null);
                        history.setFillCount(fillCount+1);
                        history.setOperateType("2");
                        history.setOperateTime(manage.getApplyTime());
                        LdcEventRecycleDetails details = detailsMapper.selectById(history.getDetailId());
                        // 获取当前的关联入账
                        List<String> detailsClueIdList = new ArrayList<>();
                        String relIds = details.getRelIds();
                        if (StringUtils.isNotBlank(relIds)) {
                            detailsClueIdList.addAll(Arrays.asList(relIds.split(",")));
                        }
                        String manualRelIds = details.getManualRelIds();
                        if (StringUtils.isNotBlank(manualRelIds)) {
                            detailsClueIdList.addAll(Arrays.asList(manualRelIds.split(",")));
                        }

                        // 取出上一版本的关联入账线索
                        List<String> detailsClueIdHisList = new ArrayList<>();

                        String relIdsHis = history.getRelIds();
                        String manualRelIdsHis = history.getManualRelIds();
                        if (StringUtils.isNotBlank(relIdsHis)) {
                            detailsClueIdHisList.addAll(Arrays.asList(relIdsHis.split(",")));
                        }
                        if (StringUtils.isNotBlank(manualRelIdsHis)) {
                            detailsClueIdHisList.addAll(Arrays.asList(manualRelIdsHis.split(",")));
                        }

                        // 先将add和delete置空
                        history.setAddClueId("");
                        history.setDeleteClueId("");

                        // 进行判断
                        String flag = "0";
                        // 取出新增的
                        List<String> addList = detailsClueIdList.stream()
                                .filter(item -> !detailsClueIdHisList.contains(item))
                                .collect(Collectors.toList());
                        if (addList.size() > 0) {
                            history.setAddClueId(String.join(",", addList));
                            flag = "1";
                        }

                        List<String> deleteList = detailsClueIdHisList.stream()
                                .filter(item -> !detailsClueIdList.contains(item))
                                .collect(Collectors.toList());
                        if (deleteList.size() > 0) {
                            history.setDeleteClueId(String.join(",", deleteList));
                            flag = "1";
                        }
                        // 更新当前的数据
                        history.setRelIds(details.getRelIds());
                        history.setManualRelIds(details.getManualRelIds());
                        if ("0".equals(flag)) {
                            history.setOperateType("4");
                        }
                        addHistoryList.add(history);
                    }
                } else {
                    // 表示第一次走流程，直接入库
                    for (LdcEventRecycleDetails details : detailsList) {
                        addHistory(addHistoryList, fillCount, details, manage, "1");
                    }
                }
            } else {
                // 如果是作废审批通过

                // 取出不存在于关联关系表中的入账线索
                List<String> clueIds = clueRelMapper.selectClueIdList(manage.getId());
                // 删除
                Map<String, Object> deleteMap = new HashMap<>();
                deleteMap.put("eventId", manage.getId());
                clueRelMapper.deleteByMap(deleteMap);

                List<LdcEntryClue> entryClueList = iLdcEntryClueService.selectNotInRelList(clueIds);
                if (entryClueList != null && !entryClueList.isEmpty()) {
                    for (LdcEntryClue ldcEntryClue : entryClueList) {
                        // 更新状态为待反馈
                        LdcEntryClue clue = new LdcEntryClue();
                        clue.setId(ldcEntryClue.getId());
                        clue.setClueStatus("9");
                        iLdcEntryClueService.updateById(clue);
                        // 入库操作记录表
                        LdcEntryClueHistory clueHistory = new LdcEntryClueHistory();
                        clueHistory.setEntryClueId(ldcEntryClue.getId());
                        String description = sysUser.getUsername() + "作废审核通过，将损失事件["+ldcEventManage.getEventName()+"]作废，解除关联入账线索";
                        clueHistory.setProcessDescribe(description);
                        ldcEntryClueHistoryMapper.insert(clueHistory);
                    }
                }

            }
            for (LdcEventRecycleDetailsHistory history : addHistoryList) {
                detailsHistoryMapper.insert(history);
            }
        }
        ldcEventManage.setReachTime(DateUtils.now());
        ldcEventManageMapper.updateById(ldcEventManage);

        // 根据当前节点状态和流程获取流程节点
        String nodeRole = getNodeRole(status, processFlag);
        // 入库提交记录
        LdcEventHistory history = new LdcEventHistory();
        history.setEventId(ldcEventManage.getId());
        history.setNodeRole(nodeRole);
        history.setStatus(status);
        history.setOperateDepart(sysUser.getOrgId());
        history.setOperateTime(new Date());
        history.setOperateUser(sysUser.getUsername());
        history.setOperateType("审核通过");
        history.setOperateOpinion(ldcEventManage.getAuditOpinion());
        history.setIsAudit("1");
        history.setFillCount(manage.getFillCount());
        historyMapper.insert(history);
    }

    private void addHistory(List<LdcEventRecycleDetailsHistory> addHistoryList,
                            Integer fillCount,
                            LdcEventRecycleDetails details,
                            LdcEventManage manage,
                            String operateType) {
        // 历史表状态均为新增
        LdcEventRecycleDetailsHistory history = new LdcEventRecycleDetailsHistory();
        history.setFillCount(fillCount);
        history.setLdcEventId(details.getLdcEventId());
        history.setCategory(details.getCategory());
        history.setSubClass(details.getSubClass());
        history.setLossForm(details.getLossForm());
        history.setOrgId(details.getOrgId());
        history.setDepartNo(details.getDepartNo());
        history.setSubjectCode(details.getSubjectCode());
        history.setSubjectName(details.getSubjectName());
        history.setCurrency(details.getCurrency());
        history.setAmount(details.getAmount());
        history.setExchangeRate(details.getExchangeRate());
        history.setCnyAmount(details.getCnyAmount());
        history.setPostingDate(details.getPostingDate());
        history.setExplanation(details.getExplanation());
        history.setDocument(details.getDocument());
        history.setRelIds(details.getRelIds());
        history.setManualRelIds(details.getManualRelIds());
        history.setCreateBy(details.getCreateBy());
        history.setCreateTime(details.getCreateTime());
        List<String> addClueIdList = new ArrayList<>();
        if ("1".equals(operateType)) {
            if (StringUtils.isNotBlank(details.getRelIds())) {
                addClueIdList.addAll(Arrays.asList(details.getRelIds().split(",")));
            }
            if (StringUtils.isNotBlank(details.getManualRelIds())) {
                addClueIdList.addAll(Arrays.asList(details.getManualRelIds().split(",")));
            }
            history.setAddClueId(String.join(",", addClueIdList));
        } else if ("3".equals(operateType)) {
            if (StringUtils.isNotBlank(details.getRelIds())) {
                addClueIdList.addAll(Arrays.asList(details.getRelIds().split(",")));
            }
            if (StringUtils.isNotBlank(details.getManualRelIds())) {
                addClueIdList.addAll(Arrays.asList(details.getManualRelIds().split(",")));
            }
            history.setDeleteClueId(String.join(",", addClueIdList));
        } else {

        }
        history.setOperateType(operateType);
        history.setDetailId(details.getId());
        history.setOperateTime(manage.getApplyTime());
        addHistoryList.add(history);
    }

    private String getNodeRole(String status, String processFlag) {
        String nodeRole = "";
        if ("1".equals(processFlag)) {
            if ("2".equals(status)) {
                nodeRole = "fh_cfshg";
            } else if ("3".equals(status)) {
                nodeRole = "fhfzr_cfshg";
            } else if ("5".equals(status)) {
                nodeRole = "zh_ldc_shg";
            } else if ("14".equals(status)) {
                nodeRole = "nkhgfzr_ldc_shg";
            }
        } else if ("2".equals(processFlag)) {
            if ("4".equals(status)) {
                nodeRole = "zhgbm_cfshg";
            } else if ("5".equals(status)) {
                nodeRole = "zh_ldc_shg";
            } else if ("14".equals(status)) {
                nodeRole = "nkhgfzr_ldc_shg";
            }
        } else if ("3".equals(processFlag)) {
            if ("5".equals(status)) {
                nodeRole = "zh_ldc_shg";
            } else if ("14".equals(status)) {
                nodeRole = "nkhgfzr_ldc_shg";
            }
        } else if ("4".equals(processFlag)) {
            if ("15".equals(status)) {
                nodeRole = "zgshgfzr_cfshg";
            } else if ("16".equals(status)) {
                nodeRole = "zgsfzr_cfshg";
            } else if ("5".equals(status)) {
                nodeRole = "zh_ldc_shg";
            } else if ("14".equals(status)) {
                nodeRole = "nkhgfzr_ldc_shg";
            }
        } else if ("5".equals(processFlag)) {
            if ("17".equals(status)) {
                nodeRole = "czyhzb_cfshg";
            } else if ("18".equals(status)) {
                nodeRole = "czyhzbhgfzr_cfshg";
            } else if ("19".equals(status)) {
                nodeRole = "czzbfzr_cfshg";
            } else if ("5".equals(status)) {
                nodeRole = "zh_ldc_shg";
            } else if ("14".equals(status)) {
                nodeRole = "nkhgfzr_ldc_shg";
            }
        } else if ("6".equals(processFlag)) {
            nodeRole = "zh_ldc_shg";
        } else if ("7".equals(processFlag)) {
            if ("3".equals(status)) {
                nodeRole = "fhfzr_cfshg";
            } else if ("5".equals(status)) {
                nodeRole = "zh_ldc_shg";
            } else if ("14".equals(status)) {
                nodeRole = "nkhgfzr_ldc_shg";
            }
        }
        return nodeRole;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditManageBack(LdcEventManage ldcEventManage) throws Exception {
        // 获取人员角色
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 获取原数据信息
        LdcEventManage manage = ldcEventManageMapper.selectById(ldcEventManage.getId());
        String processFlag = manage.getProcessFlag();
        String status = manage.getStatus();
        String opinionType = ldcEventManage.getOpinionType();
        if (StringUtils.isBlank(opinionType)) {
            throw new Exception("当审核退回时，意见类型不能为空！");
        }
        if (StringUtils.isBlank(ldcEventManage.getAuditOpinion())) {
            throw new Exception("当审核退回时，审核意见不能为空！");
        }

        String opinionTypeName = "";
        if ("1".equals(opinionType) || "2".equals(opinionType)) {
            String nextStatus = getBackToFillStatus(processFlag, status);
            if ("1".equals(opinionType)) {
                opinionTypeName = "退回修改";
            } else {
                opinionTypeName = "终止报送";
            }
            if (StringUtils.isBlank(nextStatus)) {
                throw new Exception("未找到下一步流程节点");
            }
            ldcEventManage.setStatus(nextStatus);
        } else if ("3".equals(opinionType)) {
            opinionTypeName = "不同意作废";
            // 状态还原
            String statusOld = manage.getStatusOld();
            ldcEventManage.setStatus(statusOld);
            ldcEventManage.setIsNullify("0");
            ldcEventManage.setOpinionType("0");
        }

        // 放入上一环节审核人信息
        ldcEventManage.setPreviousAuditState(opinionTypeName);
        ldcEventManage.setPreviousAuditUser(sysUser.getUsername());
        ldcEventManage.setPreviousAuditUserName(sysUser.getRealname());
        ldcEventManage.setPreviousAuditOpinion(ldcEventManage.getAuditOpinion());

        ldcEventManage.setReachTime(DateUtils.now());
        ldcEventManageMapper.updateById(ldcEventManage);

        // 根据当前节点状态和流程获取流程节点
        String nodeRole = getNodeRole(status, processFlag);
        // 入库提交记录
        LdcEventHistory history = new LdcEventHistory();
        history.setEventId(ldcEventManage.getId());
        history.setNodeRole(nodeRole);
        history.setStatus(status);
        history.setOperateDepart(sysUser.getOrgId());
        history.setOperateTime(new Date());
        history.setOperateUser(sysUser.getUsername());
        history.setOperateType("审核退回：" + opinionTypeName);
        history.setOperateOpinion(ldcEventManage.getAuditOpinion());
        history.setIsAudit("1");
        history.setFillCount(manage.getFillCount());
        historyMapper.insert(history);
    }

    @Override
    public Result<LdcEventManage> judgeCanMerge(LdcEventManage ldcEventManage) throws Exception {
        // 需要判断有没有合并权限，因为数据最后可以开放查询权限给不同部门的审核岗 总行-LDC审核岗、分行操风审核岗

        // 获取人员角色
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> roleList = sysUserRoleMapper.getRoleCodeByUserId(sysUser.getId());
        if (!roleList.contains("zh_ldc_shg") && !roleList.contains("fh_cfshg")) {
            throw new Exception("您没有权限进行合并！");
        }

        LdcEventManage result = new LdcEventManage();

        List<String> ids = ldcEventManage.getIds();

        StringBuffer stringBufferDescription = new StringBuffer();
        StringBuffer reasonBuffer = new StringBuffer();
        String lineSeparator = System.lineSeparator();

        Date initialHappenDateNow = DateUtils.getDate();
        Date findDateNow = DateUtils.getDate();
        Date firstEntryDateNow = null;

        // 定义机构1
        Set<String> organSet1 = new HashSet<>();
        // 定义机构2
        Set<String> organSet2 = new HashSet<>();
        // 定义发生机构
        Set<String> happenOrganSet = new HashSet<>();
        // 定义发生部门
        Set<String> happenDepartSet = new HashSet<>();
        // 定义总损失金额
        BigDecimal totalLossMoneyNow = new BigDecimal(0);
        // 定义已确认的回收总金额
        BigDecimal cfmdRecycleMoneyNow = new BigDecimal(0);
        // 定义净损失金额
        BigDecimal netLossMoneyNow = new BigDecimal(0);
        // 定义已确认的保险赔付
        BigDecimal cfmdIndemnityNow = new BigDecimal(0);
        // 定义事件严重度分级
        String severityClassificationNow = "0";
        // 定义是否纳入计量
        String isIntoMeteringNow = "0";
        // 定义关联台账主键
        Set relLedgerIdSet = new HashSet();
        // 定义关联台账编号
        Set relLedgerCodeSet = new HashSet();
        // 定义发生操作风险事件的主要流程
        Set mainProcessSet = new HashSet();


        // 定义入账线索关联过来的id
        Set<String> manageTemporaryEntryId = new LinkedHashSet<>();
        // 定义关联的入账线索
        Set<String> manageRelIds = new LinkedHashSet<>();
        // 定义关联的手工新增的入账线索
        Set<String> manageManualIds = new LinkedHashSet<>();


        // 定义最大的节点层级
        String nodeLevelNow = "1";
        // 定义第一层节点的Id
        List<String> mergerRootIds = new ArrayList<>();

        List<LdcRuleRel> interiorAllList = new ArrayList<>();
        List<LdcRuleRel> externalAllList = new ArrayList<>();
        
        for (int i = 0; i < ids.size(); i++) {
            String id = ids.get(i);
            LdcEventManage manage = ldcEventManageMapper.selectById(id);
            if (manage.getOpinionType().equals("2")) {
                return Result.error(manage.getEventName() + "的审核意见类型为终止报送，不允许合并");
            }
            if (manage.getIsNullify().equals("1")) {
                return Result.error(manage.getEventName() + "处于废止流程，不允许合并");
            }
            String eventDescrption = manage.getEventDescrption();
            String eventReasonDescription = manage.getEventReasonDescription();
            stringBufferDescription.append(eventDescrption);
            reasonBuffer.append(eventReasonDescription);

            if (i != ids.size() - 1) {
                stringBufferDescription.append(lineSeparator);
                reasonBuffer.append(lineSeparator);
            }
            // 判断事件初始发生日期，取被合并事件该字段最早的日期
            Date initialHappenDate = manage.getInitialHappenDate();
            initialHappenDateNow = initialHappenDate.before(initialHappenDateNow) ? initialHappenDate : initialHappenDateNow;
            // 事件发现日期，取被合并事件该字段最早的日期
            Date findDate = manage.getFindDate();
            findDateNow = findDate.before(findDateNow) ? findDate : findDateNow;
            // 首笔损失入账(或确认)日期
            Date firstEntryDate = manage.getFirstEntryDate();
            if (firstEntryDateNow == null) {
                firstEntryDateNow = firstEntryDate;
            }
            if (firstEntryDateNow != null && firstEntryDate != null) {
                firstEntryDateNow = firstEntryDateNow.before(firstEntryDate) ? firstEntryDateNow : firstEntryDate;
            }
            String fillOrgan = manage.getFillOrgan();// 可能原本就是多个
            String fillOrgans[] = fillOrgan.split(",");
            for (String organ : fillOrgans) {
                String deptFlag = iLdcEntryClueService.findDeptFlag(organ);
                if (deptFlag.equals("1")) {
                    organSet1.add(organ);
                } else if (deptFlag.equals("2")) {
                    organSet2.add(organ);
                }
            }
            // 发生机构
            String happenOrgan[] = manage.getHappenOrgan().split(",");
            happenOrganSet.addAll(Arrays.asList(happenOrgan));

            // 发生部门
            String happenDepart[] = manage.getHappenDepart().split(",");
            happenDepartSet.addAll(Arrays.asList(happenDepart));

            // 总损失金额
            BigDecimal totalLossMoney = manage.getTotalLossMoney();
            totalLossMoneyNow = totalLossMoneyNow.add(totalLossMoney);

            // 已确认的回收总金额
            BigDecimal cfmdRecycleMoney = manage.getCfmdRecycleMoney();
            cfmdRecycleMoneyNow = cfmdRecycleMoneyNow.add(cfmdRecycleMoney);

            // 净损失金额
            BigDecimal netLossMoney = manage.getNetLossMoney();
            netLossMoneyNow = netLossMoneyNow.add(netLossMoney);

            // 其中：已确认的保险赔付
            BigDecimal cfmdIndemnity = manage.getCfmdIndemnity();
            cfmdIndemnityNow = cfmdIndemnityNow.add(cfmdIndemnity);

            // 事件严重度分级,放入最高值即可
            String severityClassification = manage.getSeverityClassification();
            if (Integer.parseInt(severityClassificationNow) <= Integer.parseInt(severityClassification)) {
                severityClassificationNow = severityClassification;
            }

            // 是否纳入计量(TODO:其实是要重新计算的，这个直接取值，以后再改)
            String isIntoMetering = manage.getIsIntoMetering();
            if ("1".equals(isIntoMetering)) {
                isIntoMeteringNow = isIntoMetering;
            }

            // 关联台账类型
            Map<String,Object> queryMap = new HashMap<>();
            queryMap.put("event_id", manage.getId());
            List<LdcLedgerEvent> eventList = ledgerEventMapper.selectByMap(queryMap);
            for (LdcLedgerEvent event : eventList) {
                relLedgerIdSet.add(event.getLedgerId());
            }
//            String relLedgerId = manage.getRelLedgerId();
//            if (StringUtils.isNotBlank(relLedgerId)) {
//                relLedgerIdSet.addAll(Arrays.asList(relLedgerId.split(",")));
//            }

//            String relLedgerType = manage.getRelLedgerType();
//            if (StringUtils.isNotBlank(relLedgerType)) {
//                relLedgerTypeSet.addAll(Arrays.asList(relLedgerType.split(",")));
//            }
//            // 关联台账编号
//            String relLedgerCode = manage.getRelLedgerCode();
//            if (StringUtils.isNotBlank(relLedgerCode)) {
//                relLedgerCodeSet.addAll(Arrays.asList(relLedgerCode.split(",")));
//            }

            // 发生操作风险事件的主要流程
            String mainProcess = manage.getMainProcess();
            if (StringUtils.isNotBlank(mainProcess)) {
                mainProcessSet.addAll(Arrays.asList(mainProcess.split(",")));
            }

            // 获取最大的节点层级
            String nodeLevel = manage.getNodeLevel();
            if ("1".equals(nodeLevel)) {
                mergerRootIds.add(id);
            } else {
                mergerRootIds.addAll(Arrays.asList(manage.getMergerRootId().split(",")));
            }
            if (Integer.parseInt(nodeLevelNow) <= Integer.parseInt(nodeLevel)) {
                nodeLevelNow = nodeLevel;
            }
            // 获取内规
            List<LdcRuleRel> interiorList = getInteriorList(Arrays.asList(id.split(",")));
            interiorAllList.addAll(interiorList);
            // 获取外规
            List<LdcRuleRel> externalList = getExternalList(Arrays.asList(id.split(",")));
            externalAllList.addAll(externalList);
        }

        // 合并损失回收明细
        List<LdcEventRecycleDetails> mergerDetailList = new ArrayList<>();
        List<LdcEventRecycleDetails> detailsList = detailsMapper.selectByManageIds(ids);
        // 先根据category：类别进行分类
        if (detailsList != null && !detailsList.isEmpty()) {
            Map<String, List<LdcEventRecycleDetails>> detailCategoryMap = detailsList.stream().collect(Collectors.groupingBy(o -> o.getCategory()));
            detailCategoryMap.forEach((category, categoryList) -> {
                if ("1".equals(category)) {
                    // 如果是损失/成本
                    // 根据损失形态再次分类
                    Map<String, List<LdcEventRecycleDetails>> lossFormMap = categoryList.stream().collect(Collectors.groupingBy(o -> o.getLossForm()));

                    summaryRecycle(lossFormMap, category,
                            manageTemporaryEntryId, manageRelIds, manageRelIds, manageManualIds,
                            mergerDetailList);
                } else {
                    // 如果是回收
                    // 根据subClass子类重新分类
                    Map<String, List<LdcEventRecycleDetails>> lossFormMap = categoryList.stream().collect(Collectors.groupingBy(o -> o.getSubClass()));
                    summaryRecycle(lossFormMap, category,
                            manageTemporaryEntryId, manageRelIds, manageRelIds, manageManualIds,
                            mergerDetailList);
                }
            });
        }

        // 合并附件
        List<LdcEventDocument> insetDocumentList = new ArrayList<>();
        List<LdcEventDocument> documentList = documentMapper.selectByMainIds(ids);
        if (documentList != null && !documentList.isEmpty()) {
            Map<String, List<LdcEventDocument>> documentMap = documentList.stream().collect(Collectors.groupingBy(o -> o.getDocumentType()));
            documentMap.forEach((documentType, documentSingleList) -> {
                LdcEventDocument document = new LdcEventDocument();
                document.setDocumentType(documentType);
                // 定义所有的文件名
                LinkedList<String> documentAllName = new LinkedList<>();
                StringBuffer buffer = new StringBuffer();
                for (LdcEventDocument documentSingle : documentSingleList) {
                    String[] documents = documentSingle.getDocumentName().split(",");
                    documentAllName.addAll(Arrays.asList(documents));
                    buffer.append(documentSingle.getDocumentExplain());
                    buffer.append(lineSeparator);
                }
                document.setDocumentName(String.join(",", documentAllName));
                document.setDocumentExplain(buffer.toString());
                document.setState("1");
                insetDocumentList.add(document);
            });
        }
        result.setDocumentList(insetDocumentList);

        // 根据填报机构获取编号-fill_depart
        // 后续放入公共方法
        String serialIndex = getEventCodeByOrgId(sysUser.getOrgId());

        result.setSerialIndex(serialIndex);
        result.setEventCode(sysUser.getOrgCode() + serialIndex);
        result.setEventDescrption(stringBufferDescription.toString());
        result.setEventReasonDescription(reasonBuffer.toString());
        result.setInitialHappenDate(initialHappenDateNow);
        result.setFindDate(findDateNow);
        if (organSet1.size() > 0) {
            result.setFillOrgan(String.join(",", organSet1));
        } else {
            if (organSet2.size() > 0) {
                result.setFillOrgan(String.join(",", organSet2));
            } else {
                // 查询本用户（应该不可能为空，先不做查询）
            }
        }
        result.setFillDepart(sysUser.getOrgId());
        result.setHappenOrgan(String.join(",", happenOrganSet));
        result.setHappenDepart(String.join(",", happenDepartSet));
        result.setFirstEntryDate(firstEntryDateNow);
        result.setTotalLossMoney(totalLossMoneyNow);
        result.setCfmdRecycleMoney(cfmdRecycleMoneyNow);
        result.setNetLossMoney(netLossMoneyNow);
        result.setCfmdIndemnity(cfmdIndemnityNow);
        result.setSeverityClassification(severityClassificationNow);
        result.setIsIntoMetering(isIntoMeteringNow);
        result.setRelLedgerId(String.join(",", relLedgerIdSet));
        //result.setRelLedgerCode(String.join(",", relLedgerCodeSet));
        result.setMainProcess(String.join(",", mainProcessSet));

        result.setMergeState("3");
        // 放入子节点
        result.setMergeId(String.join(",", ids));
        // 由于是合并项，展示在全部事件中
        result.setIsHidden("0");
        result.setIsShowPage1("1");

        // 判断流程类型
        if (roleList.contains("zh_ldc_shg")) {
            result.setProcessFlag("6");// 只需要提交给内控合规负责人LDC审核岗
        } else {
            result.setProcessFlag("7");// 为了退回到分行操风审核岗
        }

        // 因为是合并，也会在已合并页面展示
        result.setIsShowPage2("1");
        // 放入最底层的节点
        result.setMergerRootId(String.join(",", mergerRootIds));
        // 放入层级
        result.setNodeLevel((Integer.parseInt(nodeLevelNow) + 1) + "");
        result.setIsParticipateProcess("1");


        result.setTemporaryEntryId(String.join(",", manageTemporaryEntryId));
        result.setRelIds(String.join(",", manageRelIds));
        result.setManualRelIds(String.join(",", manageManualIds));
        result.setDetailsList(mergerDetailList);
        if (mergerDetailList.size()>0) {
            result.setIsLossEvent("1");
        }
        result.setFillCount(1);
        result.setIsRemove("0");
        result.setExternalList(externalAllList);
        result.setInteriorList(interiorAllList);
        return Result.ok(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<LdcEventManage> cancelMerge(LdcEventManage ldcEventManage) {
        LdcEventManage manage = ldcEventManageMapper.selectById(ldcEventManage.getId());
        String parentId = manage.getParentId();
        if (StringUtils.isNotBlank(parentId)) {
            return Result.error("该损失事件不允许取消合并，请选择最上级数据");
        }
        // 获取其子节点
        String mergeIds[] = manage.getMergeId().split(",");
        for (String mergeId : mergeIds) {
            LdcEventManage updateManage = new LdcEventManage();
            updateManage.setId(mergeId);
            // 设置走流程
            updateManage.setIsParticipateProcess("1");
            LdcEventManage sonManage = ldcEventManageMapper.selectById(mergeId);
            // 判断子节点是否是第一层
            String nodeLevel = sonManage.getNodeLevel();
            if (nodeLevel.equals("1")) {
                // 设置页面展示
                updateManage.setIsShowPage1("1");
                updateManage.setIsShowPage3("0");
                updateManage.setIsShowPage2("0");
                // 设置合并状态
                updateManage.setMergeState("1");
            } else {
                // 设置页面展示
                updateManage.setIsShowPage1("1");
                updateManage.setIsShowPage3("0");
                updateManage.setIsShowPage2("1");
                // 设置合并状态
                updateManage.setMergeState("3");
            }
            ldcEventManageMapper.updateById(updateManage);
        }
        // 本身数据不再走流程，且设置为隐藏
        ldcEventManage.setIsParticipateProcess("0");
        ldcEventManage.setIsDelete("1");
        ldcEventManage.setIsShowPage2("0");
        ldcEventManage.setIsShowPage1("0");
        ldcEventManageMapper.updateById(ldcEventManage);

        return Result.ok("取消合并成功！");
    }

    @Override
    public Result<LdcEventManage> getEventCode() {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String serialIndex = getEventCodeByOrgId(sysUser.getOrgId());
        LdcEventManage manage = new LdcEventManage();
        manage.setSerialIndex(serialIndex);
        manage.setEventCode(sysUser.getOrgCode() + serialIndex);
        return Result.ok(manage);
    }


    @Override
    public Page<LdcEventManage> queryAuditedList(Page<LdcEventManage> page, LdcEventManage ldcEventManage) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        ldcEventManage.setOperateUser(sysUser.getUsername());
        List<LdcEventManage> list = ldcEventManageMapper.queryAuditedList(page, ldcEventManage);
        return page.setRecords(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditWithdraw(LdcEventManage ldcEventManage) throws Exception {
        List<String> ids = ldcEventManage.getIds();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        for (String id : ids) {
            LdcEventManage updateManage = new LdcEventManage();
            LdcEventManage manage = ldcEventManageMapper.selectById(id);
            // 根据角色，当前数据的状态，流程判断审批是否可以撤回
            String processFlag = manage.getProcessFlag();
            String status = manage.getStatus();
            String canWithdraw = judgeCanAuditWithdraw(processFlag, status);
            if ("0".equals(canWithdraw)) {
                throw new Exception(manage.getEventName() + "不允许撤回！");
            }
            // 更新状态值
            updateManage.setId(id);
            updateManage.setStatus(canWithdraw);

            LdcEventHistory eventHistory = historyMapper.selectPreviousAuditHistory(id, canWithdraw, manage.getFillCount());

            if (eventHistory != null ) {
                String operateUser = eventHistory.getOperateUser();
                SysUser userByName = sysUserMapper.getUserByNameAll(operateUser);
                updateManage.setReachTime(DateUtils.formatDate(eventHistory.getOperateTime(), "yyyy-MM-dd HH:mm:ss"));
                updateManage.setPreviousAuditUser(eventHistory.getOperateUser());
                updateManage.setPreviousAuditState(eventHistory.getStatus());
                updateManage.setPreviousAuditOpinion(eventHistory.getOperateOpinion());
                updateManage.setPreviousAuditUserName(userByName.getRealname());
            } else {
                updateManage.setReachTime(manage.getApplyTime());
                updateManage.setPreviousAuditUser("");
                updateManage.setPreviousAuditState("");
                updateManage.setPreviousAuditOpinion("");
                updateManage.setPreviousAuditUserName("");
            }
            ldcEventManageMapper.updateById(updateManage);

            // 根据当前节点状态和流程获取流程节点
            String nodeRole = getNodeRole(canWithdraw, processFlag);
            // 入库提交记录
            LdcEventHistory history = new LdcEventHistory();
            history.setEventId(ldcEventManage.getId());
            history.setNodeRole(nodeRole);
            history.setStatus("-1");
            history.setOperateDepart(sysUser.getOrgId());
            history.setOperateTime(new Date());
            history.setOperateUser(sysUser.getUsername());
            history.setOperateType("审核撤回");
            historyMapper.insert(history);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusToUpdate(LdcEventManage ldcEventManage) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        ldcEventManage.setStatus("11");
        ldcEventManage.setIsUpdateProcess("1");

        ldcEventManageMapper.updateById(ldcEventManage);
        // 入库提交记录
        LdcEventHistory history = new LdcEventHistory();
        history.setEventId(ldcEventManage.getId());
        history.setNodeRole(ldcEventManage.getNodeRole());
        history.setStatus("11");
        history.setOperateDepart(sysUser.getOrgId());
        history.setOperateTime(new Date());
        history.setOperateUser(sysUser.getUsername());
        history.setOperateType("置为更新中");
        historyMapper.insert(history);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> batchRepeal(LdcEventManage ldcEventManage) throws Exception {
        List<String> returnEventName = new ArrayList<>();
        // 判断角色是否正确
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> roleList = sysUserRoleMapper.getRoleCodeByUserId(sysUser.getId());
        String processFlag = "";
        String nodeRole = "";
        if (roleList.contains("fh_cfzg")) {
            processFlag = "1";
            nodeRole = "fh_cfzg";
        }else if (roleList.contains("fhbm_cfglg")) {
            processFlag = "1";
            nodeRole = "fhbm_cfglg";
        }else if (roleList.contains("zhgbm_cfglh")) {
            processFlag = "2";
            nodeRole = "zhgbm_cfglh";
        }else if (roleList.contains("zh_ldc_glg")) {
            processFlag = "3";
            nodeRole = "zh_ldc_glg";
        }else if (roleList.contains("zgs_cfzg")) {
            processFlag = "4";
            nodeRole = "zgs_cfzg";
        }else if (roleList.contains("czyh_cfzg")) {
            processFlag = "5";
            nodeRole = "czyh_cfzg";
        }else if (roleList.contains("zh_ldc_shg")) {
            processFlag = "6";
            nodeRole = "zh_ldc_shg";
        } else if (roleList.contains("fh_cfshg")) {
            processFlag = "7";
            nodeRole = "fh_cfshg";
        }
        if (StringUtils.isBlank(processFlag)) {
            throw new Exception("您没有废止权限废止");
        }
        List<String> ids = ldcEventManage.getIds();
        // 定义所有的已关联的入账线索ID
        // List<String> clueIdAllList = new ArrayList<>();
        for (String id : ids) {
            // 定义删除状态
            String isDeleteRel = "0";
            LdcEventManage manage = ldcEventManageMapper.selectById(id);
            if (!manage.getProcessFlag().equals(processFlag)) {
                throw new Exception("您没有权限废止" + manage.getEventName());
            }
            if ("12".equals(manage.getStatus())) {
                throw new Exception(manage.getEventName() + "已废止，请勿重复废止");
            }
            // 入库提交记录
            LdcEventHistory history = new LdcEventHistory();
            // 如果是审批退回，且审核意见为中止流程，则直接废止
            String opinionType = manage.getOpinionType();
            if ("2".equals(opinionType)) {
                LdcEventManage updateMange = new LdcEventManage();
                updateMange.setId(id);
                updateMange.setIsNullify("1");
                updateMange.setStatus("12");
                updateMange.setIsParticipateProcess("0");
                ldcEventManageMapper.updateById(updateMange);
                isDeleteRel = "1";
            } else {
                // 判断状态是不是最终状态,如果是最终状态，允许作废，需要重新走审批流程
                if ("6".equals(manage.getStatus())) {
                    String isNullify = manage.getIsNullify();
                    if (StringUtils.isNotBlank(isNullify)) {
                        if ("1".equals(isNullify)) {
                            throw new Exception(manage.getEventName() + "已提交废止，请勿重复废止");
                        }
                    }

                    LdcEventManage updateMange = new LdcEventManage();
                    updateMange.setId(id);
                    updateMange.setIsNullify("1");
                    updateMange.setStatus(getStatusSecondStep(processFlag));
                    // 需要记录当前状态，以便审核时选择不允许废止，进行数据回滚
                    updateMange.setStatusOld(manage.getStatus());
                    ldcEventManageMapper.updateById(updateMange);
                    returnEventName.add(manage.getEventName());
                } else if ("1".equals(manage.getStatus())) {
                    // 如果是草稿状态，直接废止
                    LdcEventManage updateMange = new LdcEventManage();
                    updateMange.setId(id);
                    updateMange.setIsNullify("1");
                    updateMange.setStatus("12");
                    updateMange.setIsParticipateProcess("0");
                    ldcEventManageMapper.updateById(updateMange);
                    isDeleteRel = "1";
                } else {
                    throw new Exception(manage.getEventName() + "不允许废止");
                }
            }
            // 查到所有ldc关联入账线索数据
            List<String> clueIds = clueRelMapper.selectClueIdList(id);
//            if (clueIds != null && !clueIds.isEmpty()) {
//                clueIdAllList.addAll(clueIds);
//            }
            // 判断是否删除
//            if ("1".equals(isDeleteRel)) {
//
//            } else {
//
//                // 将删除状态置为 1，如果废止审核通过则全部删除，否则回滚
//                // clueRelMapper.upIsDeleteByEventId(id);
//            }


            history.setEventId(id);
            history.setNodeRole(nodeRole);
            history.setStatus("12");
            history.setOperateDepart(sysUser.getOrgId());
            history.setOperateTime(new Date());
            history.setOperateUser(sysUser.getUsername());
            history.setOperateType("废止事件");
            history.setIsAudit("1");
            historyMapper.insert(history);

            // 沟通后放在最后审批通过删除
            if (clueIds.size() > 0 && "1".equals(isDeleteRel)) {
                // 删除
                Map<String, Object> deleteMap = new HashMap<>();
                deleteMap.put("event_id", id);
                clueRelMapper.deleteByMap(deleteMap);

                // 取出不存在于关联关系表中的入账线索
                List<LdcEntryClue> entryClueList = iLdcEntryClueService.selectNotInRelList(clueIds);

                if (entryClueList != null && !entryClueList.isEmpty()) {
                    for (LdcEntryClue ldcEntryClue : entryClueList) {
                        // 更新状态为待反馈
                        LdcEntryClue clue = new LdcEntryClue();
                        clue.setId(ldcEntryClue.getId());
                        clue.setClueStatus("9");
                        iLdcEntryClueService.updateById(clue);
                        // 入库操作记录表
                        LdcEntryClueHistory clueHistory = new LdcEntryClueHistory();
                        clueHistory.setEntryClueId(ldcEntryClue.getId());
                        String description = sysUser.getUsername() + "在ldc管理模块将损失事件["+ldcEventManage.getEventName()+"]作废，解除关联入账线索";
                        clueHistory.setProcessDescribe(description);
                        ldcEntryClueHistoryMapper.insert(clueHistory);
                    }
                }
            }
        }
        if (returnEventName.size() > 0) {
            return Result.OK("操作成功，[" + String.join(",", returnEventName) + "]已提交废止审核流程");
        } else {
            return Result.OK("操作成功");
        }
    }

    @Override
    public void downloadZip(String id, HttpServletResponse response) throws Exception{
        LdcEventManage manage = ldcEventManageMapper.selectById(id);
        // 获取附件
        List<String> filesPath = new ArrayList<>();
        List<LdcEventDocument> ldcEventDocuments = documentMapper.selectByMainId(id);
        for (LdcEventDocument document : ldcEventDocuments) {
            String documentName = document.getDocumentName();
            if (StringUtils.isNotBlank(documentName)) {
                String[] documentNames = documentName.split(",");
                for (String name : documentNames) {
                    String filePath = uploadpath + File.separator + name;
                    filesPath.add(filePath);
                }
            }
        }
        // 拿到损失事件明细的附件
        List<LdcEventRecycleDetails> detailsList = detailsMapper.selectByManageIds(Arrays.asList(id.split(",")));
        for (LdcEventRecycleDetails details : detailsList) {
            String document = details.getDocument();
            if (StringUtils.isNotBlank(document)) {
                String[] documentNames = document.split(",");
                for (String name : documentNames) {
                    String filePath = uploadpath + File.separator + name;
                    filesPath.add(filePath);
                }
            }
        }
        if (filesPath.size() == 0){
            throw new Exception("没有附件可下载");
        }
        FileDownloadUtils.downloadFileMulti(response, filesPath, manage.getEventName());
    }

    @Override
    public IPage<LdcEventManage> selectEventPageList(Page<LdcEventManage> page,
                                                     QueryWrapper<LdcEventManage> queryWrapper,
                                                     QueryWrapper<LdcEventManage> queryWrapper2,
                                                     String orgId) {

        queryWrapper2.apply("EXISTS (SELECT 1 FROM ldc_event_depart " +
                "WHERE ldc_event_manage.id = ldc_event_depart.event_id and ldc_event_depart.depart_no = '"+orgId+"')");
        String unionSql = "(" + queryWrapper.getCustomSqlSegment() + " UNION " +
                queryWrapper2.getCustomSqlSegment() + ")";
        System.out.println("unionSql1:" + unionSql);
        unionSql = unionSql.replaceAll("ORDER BY create_time DESC", "");
        System.out.println("unionSql2:" + unionSql);

        QueryWrapper<LdcEventManage> finalWrapper = new QueryWrapper<>();
        finalWrapper.apply("SELECT * FROM " + unionSql);

        return ldcEventManageMapper.selectEventPageList(page, queryWrapper, queryWrapper2, orgId);
    }

    @Override
    public IPage<LdcEventManage> selectEventPageListByParam(Page<LdcEventManage> page,
                                                            List<String> processFlagList,
                                                            LdcEventManage ldcEventManage,
                                                            String orgId,
                                                            List<String> fillDepartList) {

        if (processFlagList == null || processFlagList.isEmpty()) {
            // 只查对口机构是本机构的
            return ldcEventManageMapper.selectEventPageListByDepart(page, ldcEventManage, orgId);
        } else {
            return ldcEventManageMapper.selectEventPageListByParam(page, processFlagList, ldcEventManage, orgId, fillDepartList);
        }
    }

    @Override
    public Result<String> judgeCanOperate(LdcEventManage ldcEventManage, List<String> processFlagList, List<String> fillDepartList) {
        List<String> ids = ldcEventManage.getIds();
        List<String> existsIds = ldcEventManageMapper.judgeCanOperate(ids, processFlagList, fillDepartList);
        if (ids.size() == existsIds.size()) {
            return Result.OK();
        } else {
            List<String> result = ids.stream()
                    .filter(id -> !existsIds.contains(id))
                    .collect(Collectors.toList());
            List<LdcEventManage> ldcEventManages = ldcEventManageMapper.selectBatchIds(result);
            List<String> eventNames = new ArrayList<>();
            for (LdcEventManage manage : ldcEventManages) {
                eventNames.add(manage.getEventName());
            }

            return Result.error("您没有权限操作数据：" + String.join(",", eventNames));
        }
    }

    @Override
    public Result<String> lockEvent(LdcEventManage ldcEventManage) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        int count = ldcEventManageMapper.updateEditStates(ldcEventManage.getId());
        if (count == 1) {
            // 入库提交记录
            LdcEventHistory history = new LdcEventHistory();
            history.setEventId(ldcEventManage.getId());
            history.setNodeRole(ldcEventManage.getNodeRole());
            history.setStatus("27");
            history.setOperateDepart(sysUser.getOrgId());
            history.setOperateTime(new Date());
            history.setOperateUser(sysUser.getUsername());
            history.setOperateType("审核退回修改中");
            historyMapper.insert(history);
        }
        return Result.OK();
    }

    @Override
    public Result<String> judgeSeverityLevel(LdcEventManage ldcEventManage) {
        BigDecimal totalLossMoney = ldcEventManage.getTotalLossMoney();

        // 声誉受损
        String reputationDamage = ldcEventManage.getReputationDamage() == null ? "0" : ldcEventManage.getReputationDamage();
        // 运营中断
        String operationInterruption = ldcEventManage.getOperationInterruption() == null ? "0" : ldcEventManage.getOperationInterruption();
        // 广大客户服务质量
        String customerServiceQuality = ldcEventManage.getCustomerServiceQuality() == null ? "0" : ldcEventManage.getCustomerServiceQuality();
        // 监管行动
        String regulatoryActions = ldcEventManage.getRegulatoryActions() == null ? "0" : ldcEventManage.getRegulatoryActions();
        // 员工安全
        String employeeSafety = ldcEventManage.getEmployeeSafety() == null ? "0" : ldcEventManage.getEmployeeSafety();

        String[] strings = {reputationDamage, operationInterruption, customerServiceQuality, regulatoryActions, employeeSafety};
        String max = strings[0];
        for (int i = 1; i < strings.length; i++) {
            if (strings[i].compareTo(max) > 0) {
                max = strings[i];
            }
        }
        if (max.equals("5")) {
            return Result.ok(max);
        }


        if (totalLossMoney != null) {
            // 获取最大的等级
            List<LdcParamOrGradeCriterionVersion> versionList = gradeCriterionVersionMapper.selectByMap(new HashMap<>());
            String totalLossMoneyLevel = "0";
            for (LdcParamOrGradeCriterionVersion version : versionList) {
                String flagStr = "0";
                String impactFormula = version.getImpactFormula();
                if (StringUtils.isNotBlank(impactFormula)) {
                    impactFormula = impactFormula.trim();
                    String impactFormulas[] = impactFormula.split(",");
                    for (String impactFormulaSingle : impactFormulas) {
                        boolean flag = evaluateCondition(impactFormulaSingle.trim(), totalLossMoney);
                        if (!flag) {
                            flagStr = "1";
                            break;
                        }
                    }
                    if ("0".equals(flagStr)) {
                        // 如果满足条件，后面的不再判断
                        totalLossMoneyLevel = version.getLevel();
                        break;
                    }
                }
            }
            // 进行比较
            String[] strings1 = {reputationDamage, operationInterruption,
                    customerServiceQuality, regulatoryActions, employeeSafety, totalLossMoneyLevel};
            String max1 = strings1[0];
            for (int i = 1; i < strings1.length; i++) {
                if (strings1[i].compareTo(max1) > 0) {
                    max1 = strings1[i];
                }
            }
            if (max1.equals("0")) {
                max1 = null;
            }
            return Result.ok(max1);
        }
        if (max.equals("0")) {
            max = null;
        }
        return Result.ok(max);
    }

    /**
     * TODO:损失事件明细页面点击关联线索时，不能用ldc的manualRelIds，需要改成明细本身的manualRelIds
     * @param ldcEventManage
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> relClueAndEvent(LdcEventManage ldcEventManage) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 定义审核退回的状态
        List<String> auditBackStateList = new ArrayList<>();
        Collections.addAll(auditBackStateList, "7", "8", "10", "20", "9", "21", "22", "23", "24", "25");

        int remindCount = 0;
        List<String> ids = ldcEventManage.getIds();
        List<String> clueIds = ldcEventManage.getClueIds();
        List<String> eventNameList = new ArrayList<>();
        for (String id : ids) {
            LdcEventManage manage = ldcEventManageMapper.selectById(id);
            eventNameList.add(manage.getEventName());
            // 如果状态是草稿、退回修改中、更新中，直接生成一条明细，进行关联
            String status = manage.getStatus();
            if ("1".equals(status) || "27".equals(status) || "11".equals(status)) {

            } else if (auditBackStateList.contains(status)) {
                // 如果是审核退回，状态变更为退回修改中
                int count = ldcEventManageMapper.updateEditStates(id);
                if (count == 1) {
                    // 入库提交记录
                    LdcEventHistory history = new LdcEventHistory();
                    history.setEventId(id);
                    history.setNodeRole(getRole(manage.getProcessFlag()));
                    history.setStatus("27");
                    history.setOperateDepart(sysUser.getOrgId());
                    history.setOperateTime(new Date());
                    history.setOperateUser(sysUser.getUsername());
                    history.setOperateType("审核退回修改中");
                    historyMapper.insert(history);
                    remindCount++;
                }
            } else {
                LdcEventManage updateManage = new LdcEventManage();
                if ("6".equals(status)) {
                    // 如果是最终状态，流程置为更新中，填报次数+1
                    updateManage.setIsUpdateProcess("1");
                } else {
                    // 填报次数不变，不走更新流程
                }
                // 如果是其他状态，变更为更新中
                updateManage.setId(id);
                updateManage.setStatus("11");
                ldcEventManageMapper.updateById(updateManage);

                // 入库提交记录
                LdcEventHistory history = new LdcEventHistory();
                history.setEventId(ldcEventManage.getId());
                history.setNodeRole(ldcEventManage.getNodeRole());
                history.setStatus("11");
                history.setOperateDepart(sysUser.getOrgId());
                history.setOperateTime(new Date());
                history.setOperateUser(sysUser.getUsername());
                history.setOperateType("置为更新中");
                historyMapper.insert(history);
                remindCount++;
            }

            // 生成一条新的损失事件明细
            LdcEventRecycleDetails recycleDetails = new LdcEventRecycleDetails();
            recycleDetails.setLdcEventId(id);
            recycleDetails.setManualRelIds(String.join(",", clueIds));
            detailsMapper.insert(recycleDetails);
            // 入库关联关系表
            for (String clueId : clueIds) {
                LdcEventClueRel clueRel = new LdcEventClueRel();
                clueRel.setEventId(id);
                clueRel.setIsRelState("0");
                clueRel.setSource("1");
                clueRel.setDetailId(recycleDetails.getId());
                clueRel.setClueId(clueId);
                clueRelMapper.insert(clueRel);
            }
        }

        // 变更入账线索状态已关联事件
        for (String id : clueIds) {
            LdcEntryClue ldcEntryClue = new LdcEntryClue();
            ldcEntryClue.setId(id);
            ldcEntryClue.setClueStatus("9");
            iLdcEntryClueService.updateById(ldcEntryClue);
            // 入库历史记录表
            // 入库历史表
            LdcEntryClueHistory ls = new LdcEntryClueHistory();
            ls.setEntryClueId(id);
            String description = sysUser.getUsername() + "关联LDC损失事件【"+String.join(",", eventNameList)+"】";
            ls.setProcessDescribe(description);
            ldcEntryClueHistoryMapper.insert(ls);
        }

        // TODO:huanjing，需要入库首页提醒数量
        return Result.ok("关联成功!");
    }

    @Override
    public Result<List<LdcEventRecycleDetails>> montageRecycleDetail(LdcEventManage ldcEventManage) {
        List<LdcEventRecycleDetails> detailsList = new ArrayList<>();
        List<String> manualRelIdsList = new ArrayList<>();
        List<String> relIdsList = new ArrayList<>();
        // 定义机构
        Set<String> orginSet = new LinkedHashSet<>();
        // 定义部门
        Set<String> departSet = new LinkedHashSet<>();
        Set<Date> firstEntryDateSetDetail = new HashSet<>();

        // 定义科目代码
        Set<String> subjectCodeSet = new LinkedHashSet<>();
        // 定义科目名称
        Set<String> subjectNameSet = new LinkedHashSet<>();
        // 定义币种
        List<String> accountingCurrencySet = new LinkedList<>();
        // 定义币种对人民币汇率
        List<String> exchangeRateSet = new LinkedList<>();
        // 定义记账金额
        BigDecimal amount = new BigDecimal(0);
        // 定义人民币金额
        BigDecimal cnyAmount = new BigDecimal(0);

        String manualRelIds = ldcEventManage.getManualRelIds();
        List<LdcEntryClue> clueList = iLdcEntryClueService.getByIds(Arrays.asList(manualRelIds.split(",")));
        for (LdcEntryClue clue : clueList) {
            if (clue.getClueStatus().equals("12")) {
                manualRelIdsList.add(clue.getId());
            } else {
                relIdsList.add(clue.getId());
            }



            String clueReceptionDept = clue.getClueReceptionDept();
            SysDepart sysDepart = sysDepartService.getById(clueReceptionDept);
            SysDepart parentDepart = sysDepartService.getById(sysDepart.getParentId());

            orginSet.add(parentDepart.getId());
            departSet.add(clueReceptionDept);

            // 获取入账日期
            Date recordedDate = clue.getRecordedDate();
            if (recordedDate != null) {
                firstEntryDateSetDetail.add(recordedDate);
            }

            // 判断记账金额，需要根据记账币种判断，保留两位小数
            String accountingCurrency = clue.getAccountingCurrency();

            BigDecimal accountingAmt = clue.getAccountingAmt();// 记账金额（数据库的）
            // 保留两位小数放入记账金额-需求是不管币种，直接累加即可
            amount = amount.add(accountingAmt);
            //amountString.add(accountingAmt.setScale(2, RoundingMode.HALF_UP) + "");
            // 定义该条入账萧索计算后的人民币金额
            BigDecimal account = new BigDecimal(0);

            if (StringUtils.isBlank(accountingCurrency)) {
                // 直接取值
                account = accountingAmt;
                // 因为没有币种，记入人民币
                accountingCurrencySet.add("CNY");
                exchangeRateSet.add("1");
            } else if ("CNY".equals(accountingCurrency)) {
                account = accountingAmt;
                accountingCurrencySet.add(accountingCurrency);
                exchangeRateSet.add("1");
            } else {
                // 根据币种和入账日期拿到汇率
                if (recordedDate != null) {
                    String exchangeRate = clueService.getExchangeRate(accountingCurrency, recordedDate);
                    if (StringUtils.isBlank(exchangeRate)) {
                        // 表示没有找到汇率，按照人名币计算（TODO：待定讨论）
                        account = accountingAmt;
                        accountingCurrencySet.add(accountingCurrency);
                        exchangeRateSet.add("未知汇率");
                    } else {
                        // 根据汇率计算对应的人民币金额
                        account = accountingAmt.multiply(new BigDecimal(exchangeRate));
                        accountingCurrencySet.add(accountingCurrency);
                        exchangeRateSet.add(exchangeRate);
                    }
                }
            }

            // 获取科目号
            String ledgerAccountCode = clue.getLedgerAccountCode();
            String ledgerAccountName = clue.getLedgerAccountName();
            if (StringUtils.isNotBlank(ledgerAccountCode) && StringUtils.isNotBlank(ledgerAccountName)) {
                subjectCodeSet.add(ledgerAccountCode);
                subjectNameSet.add(ledgerAccountName);
            }

            // 计算人民币金额
            cnyAmount = cnyAmount.add(account);
        }

        LdcEventRecycleDetails details = new LdcEventRecycleDetails();

        if (firstEntryDateSetDetail.size() > 0) {
            Date minDate = Collections.min(firstEntryDateSetDetail);
            details.setPostingDate(minDate);
        }
        details.setManualRelIds(String.join(",", manualRelIdsList));
        details.setRelIds(String.join(",", relIdsList));
        details.setRemoveIds(String.join(",", relIdsList));
        details.setManualRemoveIds(String.join(",", manualRelIdsList));
        details.setCnyAmount(cnyAmount.setScale(2, RoundingMode.HALF_UP).toString());
        details.setSubjectCode(String.join(",", subjectCodeSet));
        details.setSubjectName(String.join(",", subjectNameSet));
        details.setCurrency(accountingCurrencySet.get(0));
        details.setExchangeRate(exchangeRateSet.get(0));
        details.setAmount(amount);
        details.setOrgId(String.join(",", orginSet));
        details.setDepartNo(String.join(",", departSet));
        detailsList.add(details);
        return Result.ok(detailsList);
    }

    @Override
    public void exportXls(HttpServletRequest request,
                          HttpServletResponse response,
                          LdcEventManage ldcEventManage) throws Exception {

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("损失事件管理表");

        // 设置行高
        sheet.createRow(0).setHeightInPoints(20);
        sheet.createRow(1).setHeightInPoints(20);

        // 创建样式
        CellStyle borderedStyle = createHeaderStyle(workbook);
        CellStyle subHeaderStyle = createSubHeaderStyle(workbook);

        // 数据样式
        CellStyle dataStyle = createDataStyle(workbook);

        // 创建合并单元格表头样式
        CellStyle basicInfoStyle = createBasicInfoStyle(workbook);

        // 查询数据
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> roleList = Arrays.asList(sysUser.getRoleCode().split(","));
        String orgId = sysUser.getOrgId();
        List<String> processFlagList = new ArrayList<>();
        List<String> fillDepartList = new ArrayList<>();
        if (roleList.contains("fh_cfzg") || roleList.contains("fhbm_cfglg")) {
            // 只查本机构
            processFlagList.add("1");
            fillDepartList.add(sysUser.getOrgId());
        } else if (roleList.contains("zhgbm_cfglh")) {
            // 只查本机构
            processFlagList.add("2");
            fillDepartList.add(sysUser.getOrgId());
        } else if (roleList.contains("zh_ldc_glg")) {
            // 只查本机构
            processFlagList.add("3");
            fillDepartList.add(sysUser.getOrgId());
        } else if (roleList.contains("zgs_cfzg")) {
            // 只查本机构
            processFlagList.add("4");
            fillDepartList.add(sysUser.getOrgId());
        } else if (roleList.contains("czyh_cfzg")) {
            // 只查本机构
            processFlagList.add("5");
            fillDepartList.add(sysUser.getOrgId());
        } else if (roleList.contains("fh_cfshg")) {
            // 只查本机构和下级机构
            List<String> subDepIdsByDepId = sysDepartService.getSubDepIdsByDepId(orgId);
            processFlagList.add("1");
            processFlagList.add("7");
            fillDepartList.addAll(subDepIdsByDepId);
        } else if (roleList.contains("zh_ldc_shg")) {
            Collections.addAll(processFlagList, "1", "2", "3", "4", "5", "6", "7");
            // 只查本机构和下级机构
            List<String> subDepIdsByDepId = sysDepartService.getSubDepIdsByDepId(orgId);
            fillDepartList.addAll(subDepIdsByDepId);
        }
        List<LdcEventManage> manageList =
                ldcEventManageMapper.selectEventPageListForExport(ldcEventManage, processFlagList, fillDepartList, orgId);

        List<String> eventIds = new ArrayList<>();
        for (LdcEventManage manage : manageList) {
            eventIds.add(manage.getId());
        }

        // 根据manageList查找所有的损失数据明细，再根据损失数据明细查找所有的入账线索
        List<LdcEventRecycleDetails> detailsList = detailsMapper.selectByManageIds(eventIds);


        // 根据manageList查找所有的台账数据
        // 根据manageList查找所有的附件
        List<LdcEventDocument> documentList = new ArrayList<>();
        if (eventIds.size() > 0) {
            documentList = documentMapper.selectByMainIds(eventIds);
        }

        // 根据manageList查找所有的评估计划
        List<RcsaPlanManage> planManageList = planManageMapper.queryPlanListByLdcId(eventIds);
        for (RcsaPlanManage planManage : planManageList) {
            if (planManage.getDelFlag() == 1) {
                planManage.setState("已删除");
            }
        }

        // 查询所有内规
        List<LdcRuleRel> interiorList = getInteriorList(eventIds);
        // 查询所有外规
        List<LdcRuleRel> externalList = getExternalList(eventIds);

        // 查询状态
        List<String> dictCodeList = new ArrayList<>();
        Collections.addAll(dictCodeList, "ldc_manage_status", "ldc_non_financial",
                "ldc_creditRiskRelevant", "ldc_document_type", "ldc_recycle_category", "ldc_recycle_sub_class",
                "ldc_recycle_loss_form");
        List<SysDictItem> sysDictItemList = sysDictItemMapper.getItemByCode(dictCodeList);

        // 查询所有机构
        List<SysDepart> departList = sysDepartService.list();
        // 查询所有人员
        List<SysUser> userList = sysUserMapper.selectByMap(new HashMap<>());

        String columnType = ldcEventManage.getColumnType();
        if (columnType.equals("all")) {
            // 第一级表头合并
            mergeCells(sheet, 0, 1, 0, 11, "基本信息", basicInfoStyle);
            mergeCells(sheet, 0, 1, 12, 16, "时间信息", basicInfoStyle);
            mergeCells(sheet, 0, 0, 17, 38, "损失和影响信息", borderedStyle);
            mergeCells(sheet, 0, 1, 39, 43, "非财务影响信息", basicInfoStyle);
            mergeCells(sheet, 0, 1, 44, 45, "事件分级信息", basicInfoStyle);
            mergeCells(sheet, 0, 1, 46, 51, "事件属性", basicInfoStyle);
            mergeCells(sheet, 0, 1, 52, 58, "机构/人员信息", basicInfoStyle);
            mergeCells(sheet, 0, 1, 59, 60, "关联台账", basicInfoStyle);
            mergeCells(sheet, 0, 1, 61, 61, "事件关联流程信息", basicInfoStyle);
            mergeCells(sheet, 0, 1, 62, 65, "关联触发式评估计划", basicInfoStyle);
            mergeCells(sheet, 0, 1, 66, 74, "事件反映出的问题与整改措施", basicInfoStyle);
            mergeCells(sheet, 0, 1, 75, 83, "事件关联内外规", basicInfoStyle);
            mergeCells(sheet, 0, 1, 84, 90, "合并后/被合并事件", basicInfoStyle);
            mergeCells(sheet, 0, 1, 91, 93, "附件", basicInfoStyle);

            // 第二级表头合并
            mergeCells(sheet, 1, 1, 17, 21, "财务影响信息", basicInfoStyle);
            mergeCells(sheet, 1, 1, 22, 35, "损失/回收明细", basicInfoStyle);
            mergeCells(sheet, 1, 1, 36, 38, "关联入账线索", basicInfoStyle);

            // 第三行详细列名
            Row detailRow = sheet.createRow(2);
            detailRow.setHeightInPoints(80);

            for (int i = 0; i < HEADERS.length; i++) {
                Cell cell = detailRow.createCell(i);
                cell.setCellValue(HEADERS[i]);
                cell.setCellStyle(subHeaderStyle);
                sheet.setColumnWidth(i, Math.max(4000, HEADERS[i].length() * 900)); // 根据文字长度自动调整列宽
                sheet.setDefaultColumnStyle(i, subHeaderStyle);// 设置列为文本格式
            }

            // 第四行示例数据

            int dataIndex = 3;
            for (LdcEventManage manage : manageList) {
                Row dataRow = sheet.createRow(dataIndex);
                dataRow.setHeightInPoints(30);

                // 放入主数据的字段数据
                dataRow.createCell(0).setCellValue("主数据");

                dataRow.createCell(1).setCellValue(manage.getEventCode());
                dataRow.createCell(2).setCellValue(manage.getEventClassifyFirst());
                dataRow.createCell(3).setCellValue(manage.getEventClassifySecond());
                dataRow.createCell(4).setCellValue(manage.getEventName());
                dataRow.createCell(5).setCellValue(manage.getEventDescrption());
                dataRow.createCell(6).setCellValue(manage.getEventReasonDescription());
                dataRow.createCell(7).setCellValue(manage.getFillCount());
                dataRow.createCell(8).setCellValue(manage.getIsEnd().equals("1") ? "是" : "否");
                // 状态
                String status = manage.getStatus();
                String statusName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_manage_status") && o.getItemValue().equals(status)).collect(Collectors.toList()).get(0).getItemText();
                dataRow.createCell(9).setCellValue(statusName);
                dataRow.createCell(10).setCellValue(manage.getIsLossEvent().equals("1") ? "是" : "否");
                dataRow.createCell(11).setCellValue(manage.getIsRemove().equals("1") ? "是" : "否");
                dataRow.createCell(12).setCellValue(DateUtils.formatDate(manage.getFirstFillDate(), "yyyy-MM-dd"));
                dataRow.createCell(13).setCellValue(DateUtils.formatDate(manage.getThisFillDate(), "yyyy-MM-dd"));
                dataRow.createCell(14).setCellValue(DateUtils.formatDate(manage.getInitialHappenDate(), "yyyy-MM-dd"));
                dataRow.createCell(15).setCellValue(DateUtils.formatDate(manage.getFindDate(), "yyyy-MM-dd"));
                dataRow.createCell(16).setCellValue(DateUtils.formatDate(manage.getFirstEntryDate(), "yyyy-MM-dd"));
                dataRow.createCell(17).setCellValue(manage.getMaxEstimateLossMoney() == null ? "" : manage.getMaxEstimateLossMoney().toString());
                dataRow.createCell(18).setCellValue(manage.getTotalLossMoney() == null ? "" : manage.getTotalLossMoney().toString());
                dataRow.createCell(19).setCellValue(manage.getNetLossMoney() == null ? "" : manage.getNetLossMoney().toString());
                dataRow.createCell(20).setCellValue(manage.getCfmdRecycleMoney() == null ? "" : manage.getCfmdRecycleMoney().toString());
                dataRow.createCell(21).setCellValue(manage.getCfmdIndemnity() == null ? "" :manage.getCfmdIndemnity().toString());
                // 22-35 是损失/回收明细，放入明细数据（需要根据入账线索进行合并）
                // 36-38 是关联入账线索，放入明细数据

                // 广大客户服务质量
                String customerServiceQuality = manage.getCustomerServiceQuality();
                String customerServiceQualityName = "";
                if (StringUtils.isNotBlank(customerServiceQuality)) {
                    customerServiceQualityName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_non_financial") && o.getItemValue().equals(customerServiceQuality)).collect(Collectors.toList()).get(0).getItemText();
                }
                dataRow.createCell(39).setCellValue(customerServiceQualityName);
                // 员工安全
                String employeeSafety = manage.getEmployeeSafety();
                String employeeSafetyName = "";
                if (StringUtils.isNotBlank(employeeSafety)) {
                    employeeSafetyName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_non_financial") && o.getItemValue().equals(employeeSafety)).collect(Collectors.toList()).get(0).getItemText();
                }
                dataRow.createCell(40).setCellValue(employeeSafetyName);
                // 运营中断
                String operationInterruption = manage.getOperationInterruption();
                String operationInterruptionName = "";
                if (StringUtils.isNotBlank(operationInterruption)) {
                    operationInterruptionName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_non_financial") && o.getItemValue().equals(operationInterruption)).collect(Collectors.toList()).get(0).getItemText();
                }
                dataRow.createCell(41).setCellValue(operationInterruptionName);
                // 监管行动
                String regulatoryActions = manage.getRegulatoryActions();
                String regulatoryActionsName = "";
                if (StringUtils.isNotBlank(regulatoryActions)) {
                    regulatoryActionsName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_non_financial") && o.getItemValue().equals(regulatoryActions)).collect(Collectors.toList()).get(0).getItemText();
                }
                dataRow.createCell(42).setCellValue(regulatoryActionsName);

                // 声誉受损
                String reputationDamage = manage.getReputationDamage();
                String reputationDamageName = "";
                if (StringUtils.isNotBlank(reputationDamage)) {
                    reputationDamageName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_non_financial") && o.getItemValue().equals(reputationDamage)).collect(Collectors.toList()).get(0).getItemText();
                }
                dataRow.createCell(43).setCellValue(reputationDamageName);
                // 事件严重度分级
                String severityClassification = manage.getSeverityClassification();
                String severityClassificationName = "";
                if (StringUtils.isNotBlank(severityClassification)) {
                    severityClassificationName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_non_financial") && o.getItemValue().equals(severityClassification)).collect(Collectors.toList()).get(0).getItemText();
                }
                dataRow.createCell(44).setCellValue(severityClassificationName);
                dataRow.createCell(45).setCellValue(manage.getIsMajorRiskEvent().equals("1") ? "是" : "否");
                // 主要成因分类
                dataRow.createCell(46).setCellValue(manage.getMainCauseClassification());
                // 损失事件类型（一级）
                dataRow.createCell(47).setCellValue(manage.getEventTypeFirst());
                // 损失事件类型（二级）
                dataRow.createCell(48).setCellValue(manage.getEventTypeSecond());
                // 损失事件类型（三级）
                dataRow.createCell(49).setCellValue(manage.getEventTypeThird());
                // 与信用/市场风险相关
                String creditRiskRelevant = manage.getCreditRiskRelevant();
                String creditRiskRelevantName = "";
                if (StringUtils.isNotBlank(creditRiskRelevant)) {
                    creditRiskRelevantName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_creditRiskRelevant") && o.getItemValue().equals(creditRiskRelevant)).collect(Collectors.toList()).get(0).getItemText();
                }
                dataRow.createCell(50).setCellValue(creditRiskRelevantName);
                // 是否纳入计量
                dataRow.createCell(51).setCellValue(manage.getIsIntoMetering().equals("1") ? "是" : "否");
                // 事件填报机构
                String fillOrganName = departList.stream().filter(o -> o.getId().equals(manage.getFillOrgan())).collect(Collectors.toList()).get(0).getDepartName();
                dataRow.createCell(52).setCellValue(fillOrganName);
                // 事件填报部门
                String fillDepartName = departList.stream().filter(o -> o.getId().equals(manage.getFillDepart())).collect(Collectors.toList()).get(0).getDepartName();
                dataRow.createCell(53).setCellValue(fillDepartName);
                // 事件填报人
                List<SysUser> fillUserList = userList.stream().filter(o -> o.getUsername().equals(manage.getFillUser())).collect(Collectors.toList());
                if (fillUserList == null || fillUserList.isEmpty()) {
                    dataRow.createCell(54).setCellValue(manage.getFillUser());
                } else {
                    dataRow.createCell(54).setCellValue(fillUserList.get(0).getRealname());
                }
                // 填报人联系方式
                dataRow.createCell(55).setCellValue(manage.getFillUserContact());
                // 总行对口管理部门
                List<String> duetManageDepartNameList = new ArrayList<>();
                String duetManageDepart = manage.getDuetManageDepart();
                if (StringUtils.isNotBlank(duetManageDepart)) {
                    String[] duetManageDeparts = duetManageDepart.split(",");
                    for (String departId : duetManageDeparts) {
                        duetManageDepartNameList.add(departList.stream().filter(o -> o.getId().equals(departId)).collect(Collectors.toList()).get(0).getDepartName());
                    }
                }
                dataRow.createCell(56).setCellValue(String.join(",", duetManageDepartNameList));
                // 发生机构
                String happenOrganName = departList.stream().filter(o -> o.getId().equals(manage.getHappenOrgan())).collect(Collectors.toList()).get(0).getDepartName();
                dataRow.createCell(57).setCellValue(happenOrganName);
                // 发生部门
                String happenDepartName = departList.stream().filter(o -> o.getId().equals(manage.getHappenDepart())).collect(Collectors.toList()).get(0).getDepartName();
                dataRow.createCell(58).setCellValue(happenDepartName);
                // 关联台账类型
                dataRow.createCell(59).setCellValue(manage.getRelLedgerType());
                // 关联台账编号
                dataRow.createCell(60).setCellValue(manage.getRelLedgerCode());
                // 发生操作风险事件的主要流程
                dataRow.createCell(61).setCellValue(manage.getMainProcess());
                // 是否发起触发式评估计划
                dataRow.createCell(62).setCellValue(manage.getIsInitiateRcsa().equals("1") ? "是" : "否");
                // 如果发起
                if ("1".equals(manage.getIsInitiateRcsa())) {
                    List<RcsaPlanManage> single = planManageList.stream().filter(o ->
                            StringUtils.isNotBlank(o.getLdcId()) && o.getLdcId().equals(manage.getId())).collect(Collectors.toList());
                    if (single != null && !single.isEmpty()) {
                        dataRow.createCell(63).setCellValue(single.get(0).getPlanCode());
                        dataRow.createCell(64).setCellValue(single.get(0).getPlanTitle());
                        dataRow.createCell(65).setCellValue(single.get(0).getState());
                    }
                }
                // 问题整改
                // 关联内外规 75
                String isViolateRegulations = manage.getIsViolateRegulations();
                dataRow.createCell(75).setCellValue(isViolateRegulations.equals("1") ? "是" : "否");
                if ("1".equals(isViolateRegulations)) {
                    // 内规
                    List<LdcRuleRel> collect = interiorList.stream().filter(o -> StringUtils.isNotBlank(o.getLdcId()) && o.getLdcId().equals(manage.getId())).collect(Collectors.toList());
                    if (collect != null && !collect.isEmpty()) {
                        List<String> numList = new LinkedList<>();
                        List<String> nameList = new LinkedList<>();
                        List<String> deptList = new LinkedList<>();
                        List<String> timelinessList = new LinkedList<>();
                        for (LdcRuleRel ruleRel : collect) {
                            numList.add(ruleRel.getNum());
                            nameList.add(ruleRel.getName());
                            deptList.add(ruleRel.getDeptName() == null ? "空值" : ruleRel.getDeptName());
                            timelinessList.add(ruleRel.getTimelinessName() == null ? "空值" : ruleRel.getTimelinessName());
                        }
                        dataRow.createCell(76).setCellValue(String.join("@@", numList));
                        dataRow.createCell(77).setCellValue(String.join("@@", nameList));
                        dataRow.createCell(78).setCellValue(String.join("@@", deptList));
                        dataRow.createCell(79).setCellValue(String.join("@@", timelinessList));
                    }
                    // 外规
                    List<LdcRuleRel> collect1 = externalList.stream().filter(o -> StringUtils.isNotBlank(o.getLdcId()) && o.getLdcId().equals(manage.getId())).collect(Collectors.toList());
                    if (collect1 != null && !collect1.isEmpty()) {
                        List<String> numList = new LinkedList<>();
                        List<String> nameList = new LinkedList<>();
                        List<String> deptList = new LinkedList<>();
                        List<String> timelinessList = new LinkedList<>();
                        for (LdcRuleRel ruleRel : collect1) {
                            numList.add(ruleRel.getNum());
                            nameList.add(ruleRel.getName());
                            deptList.add(ruleRel.getDeptName() == null ? "空值" : ruleRel.getDeptName());
                            timelinessList.add(ruleRel.getTimelinessName() == null ? "空值" : ruleRel.getTimelinessName());
                        }
                        dataRow.createCell(80).setCellValue(String.join("@@", numList));
                        dataRow.createCell(81).setCellValue(String.join("@@", nameList));
                        dataRow.createCell(82).setCellValue(String.join("@@", deptList));
                        dataRow.createCell(83).setCellValue(String.join("@@", timelinessList));
                    }
                }


                // 损失/成本数据明细
                List<LdcEventRecycleDetails> eventDetailsList = detailsList.stream().filter(o -> o.getLdcEventId().equals(manage.getId())).collect(Collectors.toList());

                if (eventDetailsList != null && !eventDetailsList.isEmpty()) {
                    int detailIndex = 1;
                    for (LdcEventRecycleDetails details : eventDetailsList) {
                        dataIndex = dataIndex + 1;
                        String relIds = details.getRelIds();
                        String manualRelIds = details.getManualRelIds();
                        List<String> relIdsList = new ArrayList<>();
                        List<String> manualRelIdsList = new ArrayList<>();

                        if (StringUtils.isNotBlank(relIds)) {
                            relIdsList = Arrays.asList(relIds.split(","));
                        }
                        if (StringUtils.isNotBlank(manualRelIds)) {
                            manualRelIdsList = Arrays.asList(manualRelIds.split(","));
                        }


                        Row dataRow1 = sheet.createRow(dataIndex);
                        dataRow1.setHeightInPoints(30);
                        dataRow1.createCell(0).setCellValue("明细数据" + (detailIndex + ""));
                        dataRow1.createCell(1).setCellValue(manage.getEventCode());
                        dataRow1.createCell(9).setCellValue(statusName);
                        dataRow1.createCell(11).setCellValue(manage.getIsRemove().equals("1") ? "是" : "否");

                        // 类别
                        String category = details.getCategory();
                        String categoryName = "";
                        if (StringUtils.isNotBlank(category)) {
                            categoryName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_recycle_category") && o.getItemValue().equals(category)).collect(Collectors.toList()).get(0).getItemText();
                        }
                        dataRow1.createCell(22).setCellValue(categoryName);
                        // 子类
                        String subClass = details.getSubClass();
                        String subClassName = "";
                        if (StringUtils.isNotBlank(subClass)) {
                            subClassName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_recycle_sub_class") && o.getItemValue().equals(subClass)).collect(Collectors.toList()).get(0).getItemText();
                        }
                        dataRow1.createCell(23).setCellValue(subClassName);
                        // 损失形态
                        String lossForm = details.getLossForm();
                        String lossFormName = "";
                        if (StringUtils.isNotBlank(lossForm)) {
                            lossFormName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_recycle_loss_form") && o.getItemValue().equals(lossForm)).collect(Collectors.toList()).get(0).getItemText();
                        }
                        dataRow1.createCell(24).setCellValue(lossFormName);
                        // 机构
                        String orgIds[] = details.getOrgId().split(",");
                        List<String> orgId1List = new ArrayList<>();
                        for (String orgId1 : orgIds) {
                            orgId1List.add(departList.stream().filter(o -> o.getId().equals(orgId1)).collect(Collectors.toList()).get(0).getDepartName());
                        }
                        dataRow1.createCell(25).setCellValue(String.join(",", orgId1List));
                        // 部门
                        String departNos[] = details.getDepartNo().split(",");
                        List<String> departNosList = new ArrayList<>();
                        for (String departNo : departNos) {
                            departNosList.add(departList.stream().filter(o -> o.getId().equals(departNo)).collect(Collectors.toList()).get(0).getDepartName().trim());
                        }
                        dataRow1.createCell(26).setCellValue(String.join(",", departNosList));
                        // 会计科目代码
                        dataRow1.createCell(27).setCellValue(details.getSubjectCode());
                        // 会计科目名称
                        dataRow1.createCell(28).setCellValue(details.getSubjectName());
                        // 记账币种
                        dataRow1.createCell(29).setCellValue(details.getCurrency());
                        // 记账金额(元）
                        dataRow1.createCell(30).setCellValue(details.getAmount().toString());
                        // 对人民币汇率
                        dataRow1.createCell(31).setCellValue(details.getExchangeRate().toString());
                        // 人民币金额（元）
                        dataRow1.createCell(32).setCellValue(details.getCnyAmount().toString());
                        // 入账日期
                        dataRow1.createCell(33).setCellValue(DateUtils.formatDate(details.getPostingDate()));
                        // 说明
                        dataRow1.createCell(34).setCellValue(details.getExplanation() == null ? "" : details.getExplanation());
                        // 附件
                        dataRow1.createCell(35).setCellValue(details.getDocument() == null ? "" : details.getDocument());

                        // 查询入账线索
                        List<String> relIdAllList = new ArrayList<>();
                        relIdAllList.addAll(relIdsList);
                        relIdAllList.addAll(manualRelIdsList);
                        if (relIdAllList.size() > 0) {
                            List<String> clueIdList = new ArrayList<>();
                            List<String> codeList = new ArrayList<>();
                            List<String> summaryList = new ArrayList<>();

                            List<LdcEntryClue> clueList = iLdcEntryClueService.getByIds(relIdAllList);
                            for (LdcEntryClue clue : clueList) {
                                clueIdList.add(clue.getId());
                                codeList.add(clue.getIdentificationCode());
                                summaryList.add(clue.getSummary() == null ? "摘要为空" : clue.getSummary());
                            }
                            dataRow1.createCell(36).setCellValue(String.join("@@", clueIdList));
                            dataRow1.createCell(37).setCellValue(String.join("@@", codeList));
                            dataRow1.createCell(38).setCellValue(String.join("@@", summaryList));

                        }
                        // 设置边框
                        for (int i = 0; i < dataRow1.getLastCellNum(); i++) {
                            Cell cell = dataRow1.getCell(i);
                            if (cell != null) {
                                cell.setCellStyle(dataStyle);
                            } else {
                                cell = dataRow1.createCell(i);
                                cell.setCellStyle(dataStyle);
                            }
                        }
                        detailIndex++;
                    }
                }


                // 附件拼接
                List<LdcEventDocument> eventDocument = documentList.stream().filter(o -> o.getEventId().equals(manage.getId())).collect(Collectors.toList());
                if (eventDocument != null && !eventDocument.isEmpty()) {
                    List<String> documentTypeList = new ArrayList<>();
                    List<String> documentExplainList = new ArrayList<>();
                    List<String> documentNameList = new ArrayList<>();

                    for (LdcEventDocument document : eventDocument) {
                        String documentType = document.getDocumentType();
                        List<SysDictItem> documentTypeDict = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_document_type") && o.getItemValue().equals(documentType)).collect(Collectors.toList());
                        if (documentTypeDict != null && !documentTypeDict.isEmpty()) {
                            documentTypeList.add(documentTypeDict.get(0).getItemText());
                        } else {
                            documentTypeList.add(documentType);
                        }

                        documentExplainList.add(document.getDocumentExplain());
                        documentNameList.add(document.getDocumentName());
                        dataRow.createCell(91).setCellValue(String.join("@@", documentTypeList));
                        dataRow.createCell(92).setCellValue(String.join("@@", documentNameList));
                        dataRow.createCell(93).setCellValue(String.join("@@", documentExplainList));
                    }
                }


                // 设置边框
                for (int i = 0; i < dataRow.getLastCellNum(); i++) {
                    Cell cell = dataRow.getCell(i);
                    if (cell != null) {
                        cell.setCellStyle(dataStyle);
                    } else {
                        cell = dataRow.createCell(i);
                        cell.setCellStyle(dataStyle);
                    }
                }
                dataIndex++;
            }
            // 添加下拉选项
            // 是否结束
            addDropDownList(sheet, 8, dataIndex, new String[]{"是", "否"});
            // 是否为损失事件
            addDropDownList(sheet, 10, dataIndex, new String[]{"是", "否"});
            // 是否经监管认可剔除
            addDropDownList(sheet, 11, dataIndex, new String[]{"是", "否"});
            // 类别
            List<String> categoryDict = sysDictItemMapper.selectTextByCode("ldc_recycle_category");
            addDropDownList(sheet, 22, dataIndex, categoryDict.toArray(new String[categoryDict.size()]));
            // 子类
            List<String> subClassDict = sysDictItemMapper.selectTextByCode("ldc_recycle_sub_class");
            addDropDownList(sheet, 23, dataIndex, subClassDict.toArray(new String[subClassDict.size()]));
            // 损失形态
            List<String> lossFormDict = sysDictItemMapper.selectTextByCode("ldc_recycle_loss_form");
            addDropDownList(sheet, 24, dataIndex, lossFormDict.toArray(new String[lossFormDict.size()]));
            // 广大客户服务质量
            List<String> financialDict = sysDictItemMapper.selectTextByCode("ldc_non_financial");
            addDropDownList(sheet, 39, dataIndex, financialDict.toArray(new String[financialDict.size()]));
            // 员工安全
            addDropDownList(sheet, 40, dataIndex, financialDict.toArray(new String[financialDict.size()]));
            // 运营中断
            addDropDownList(sheet, 41, dataIndex, financialDict.toArray(new String[financialDict.size()]));
            // 监管行动
            addDropDownList(sheet, 42, dataIndex, financialDict.toArray(new String[financialDict.size()]));
            // 声誉受损
            addDropDownList(sheet, 43, dataIndex, financialDict.toArray(new String[financialDict.size()]));
            // 事件严重度分级
            addDropDownList(sheet, 44, dataIndex, financialDict.toArray(new String[financialDict.size()]));
            // 是否重大操作风险事件
            addDropDownList(sheet, 45, dataIndex, new String[]{"是", "否"});
            // 主要成因分类
            List<String> causeList = ldcEventManageMapper.getCauseClassification();
            addDropDownList(sheet, 46, dataIndex, causeList.toArray(new String[causeList.size()]));

            // 与信用/市场风险相关
            List<String> relevantList = sysDictItemMapper.selectTextByCode("ldc_creditRiskRelevant");
            addDropDownList(sheet, 50, dataIndex, relevantList.toArray(new String[relevantList.size()]));
            // 是否纳入计量
            addDropDownList(sheet, 51, dataIndex, new String[]{"是", "否"});
            // 是否发起触发式评估计划
            addDropDownList(sheet, 62, dataIndex, new String[]{"是", "否"});
            // 是否录入操作风险管理系统-问题收集模块
            addDropDownList(sheet, 66, dataIndex, new String[]{"是", "否"});
            // 是否违反内外规
            addDropDownList(sheet, 75, dataIndex, new String[]{"是", "否"});
            // 合并状态
            List<String> mergerList = sysDictItemMapper.selectTextByCode("ldc_mergeState");
            addDropDownList(sheet, 82, dataIndex, mergerList.toArray(new String[mergerList.size()]));
            // 附件类型
            List<String> documentTypeList = sysDictItemMapper.selectTextByCode("ldc_document_type");
            addDropDownList(sheet, 89, dataIndex, documentTypeList.toArray(new String[documentTypeList.size()]));
        } else {
            mergeCells(sheet, 0, 1, 0, 7, "基本信息", basicInfoStyle);
            mergeCells(sheet, 0, 1, 8, 9, "时间信息", basicInfoStyle);
            mergeCells(sheet, 0, 0, 10, 23, "损失和影响信息", borderedStyle);
            mergeCells(sheet, 0, 1, 24, 24, "事件分级信息", basicInfoStyle);
            mergeCells(sheet, 0, 1, 25, 27, "事件属性", basicInfoStyle);
            mergeCells(sheet, 0, 1, 28, 29, "机构/人员信息", basicInfoStyle);

            // 第二级表头合并
            mergeCells(sheet, 1, 1, 10, 12, "财务影响信息", basicInfoStyle);
            mergeCells(sheet, 1, 1, 13, 21, "损失/回收明细", basicInfoStyle);
            mergeCells(sheet, 1, 1, 22, 23, "关联入账线索", basicInfoStyle);

            // 第三行详细列名
            Row detailRow = sheet.createRow(2);
            detailRow.setHeightInPoints(80);

            for (int i = 0; i < STREAMLINE_HEADERS.length; i++) {
                Cell cell = detailRow.createCell(i);
                cell.setCellValue(STREAMLINE_HEADERS[i]);
                cell.setCellStyle(subHeaderStyle);
                sheet.setColumnWidth(i, Math.max(4000, STREAMLINE_HEADERS[i].length() * 900)); // 根据文字长度自动调整列宽
                sheet.setDefaultColumnStyle(i, subHeaderStyle);// 设置列为文本格式
            }

            int dataIndex = 3;
            for (LdcEventManage manage : manageList) {
                Row dataRow = sheet.createRow(dataIndex);
                dataRow.setHeightInPoints(30);
                // 放入主数据的字段数据
                dataRow.createCell(0).setCellValue("主数据");

                dataRow.createCell(1).setCellValue(manage.getEventCode());
                dataRow.createCell(2).setCellValue(manage.getEventClassifyFirst());
                dataRow.createCell(3).setCellValue(manage.getEventClassifySecond());
                dataRow.createCell(4).setCellValue(manage.getEventName());
                dataRow.createCell(5).setCellValue(manage.getEventDescrption());
                dataRow.createCell(6).setCellValue(manage.getEventReasonDescription());
                dataRow.createCell(7).setCellValue(manage.getIsEnd().equals("1") ? "是" : "否");
                dataRow.createCell(8).setCellValue(DateUtils.formatDate(manage.getInitialHappenDate(), "yyyy-MM-dd"));
                dataRow.createCell(9).setCellValue(DateUtils.formatDate(manage.getFindDate(), "yyyy-MM-dd"));
                dataRow.createCell(10).setCellValue(manage.getTotalLossMoney().toString());
                dataRow.createCell(11).setCellValue(manage.getNetLossMoney().toString());
                dataRow.createCell(12).setCellValue(manage.getCfmdRecycleMoney().toString());


                // 事件严重度分级
                String severityClassification = manage.getSeverityClassification();
                String severityClassificationName = "";
                if (StringUtils.isNotBlank(severityClassification)) {
                    severityClassificationName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_non_financial") && o.getItemValue().equals(severityClassification)).collect(Collectors.toList()).get(0).getItemText();
                }
                dataRow.createCell(24).setCellValue(severityClassificationName);

                // 损失事件类型（一级）
                dataRow.createCell(25).setCellValue(manage.getEventTypeFirst());
                // 损失事件类型（二级）
                dataRow.createCell(26).setCellValue(manage.getEventTypeSecond());
                // 损失事件类型（三级）
                dataRow.createCell(27).setCellValue(manage.getEventTypeThird());
                // 事件填报机构
                String fillOrganName = departList.stream().filter(o -> o.getId().equals(manage.getFillOrgan())).collect(Collectors.toList()).get(0).getDepartName();
                dataRow.createCell(28).setCellValue(fillOrganName);
                // 事件填报部门
                String fillDepartName = departList.stream().filter(o -> o.getId().equals(manage.getFillDepart())).collect(Collectors.toList()).get(0).getDepartName();
                dataRow.createCell(29).setCellValue(fillDepartName);

                // 损失/成本数据明细
                List<LdcEventRecycleDetails> eventDetailsList = detailsList.stream().filter(o -> o.getLdcEventId().equals(manage.getId())).collect(Collectors.toList());

                if (eventDetailsList != null && !eventDetailsList.isEmpty()) {
                    int detailIndex = 1;
                    for (LdcEventRecycleDetails details : eventDetailsList) {
                        dataIndex = dataIndex + 1;
                        String relIds = details.getRelIds();
                        String manualRelIds = details.getManualRelIds();
                        List<String> relIdsList = new ArrayList<>();
                        List<String> manualRelIdsList = new ArrayList<>();

                        if (StringUtils.isNotBlank(relIds)) {
                            relIdsList = Arrays.asList(relIds.split(","));
                        }
                        if (StringUtils.isNotBlank(manualRelIds)) {
                            manualRelIdsList = Arrays.asList(manualRelIds.split(","));
                        }

                        Row dataRow1 = sheet.createRow(dataIndex);
                        dataRow1.setHeightInPoints(30);
                        dataRow1.createCell(0).setCellValue("明细数据" + (detailIndex + ""));
                        dataRow1.createCell(1).setCellValue(manage.getEventCode());

                        // 类别
                        String category = details.getCategory();
                        String categoryName = "";
                        if (StringUtils.isNotBlank(category)) {
                            categoryName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_recycle_category") && o.getItemValue().equals(category)).collect(Collectors.toList()).get(0).getItemText();
                        }
                        dataRow1.createCell(13).setCellValue(categoryName);
                        // 子类
                        String subClass = details.getSubClass();
                        String subClassName = "";
                        if (StringUtils.isNotBlank(subClass)) {
                            subClassName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_recycle_sub_class") && o.getItemValue().equals(subClass)).collect(Collectors.toList()).get(0).getItemText();
                        }
                        dataRow1.createCell(14).setCellValue(subClassName);
                        // 损失形态
                        String lossForm = details.getLossForm();
                        String lossFormName = "";
                        if (StringUtils.isNotBlank(lossForm)) {
                            lossFormName = sysDictItemList.stream().filter(o -> o.getDictCode().equals("ldc_recycle_loss_form") && o.getItemValue().equals(lossForm)).collect(Collectors.toList()).get(0).getItemText();
                        }
                        dataRow1.createCell(15).setCellValue(lossFormName);
                        // 机构
                        String orgIds[] = details.getOrgId().split(",");
                        List<String> orgId1List = new ArrayList<>();
                        for (String orgId1 : orgIds) {
                            orgId1List.add(departList.stream().filter(o -> o.getId().equals(orgId1)).collect(Collectors.toList()).get(0).getDepartName());
                        }
                        dataRow1.createCell(16).setCellValue(String.join(",", orgId1List));
                        // 部门
                        String departNos[] = details.getDepartNo().split(",");
                        List<String> departNosList = new ArrayList<>();
                        for (String departNo : departNos) {
                            departNosList.add(departList.stream().filter(o -> o.getId().equals(departNo)).collect(Collectors.toList()).get(0).getDepartName().trim());
                        }

                        dataRow1.createCell(17).setCellValue(String.join(",", departNosList));
                        // 会计科目代码
                        dataRow1.createCell(18).setCellValue(details.getSubjectCode());
                        // 会计科目名称
                        dataRow1.createCell(19).setCellValue(details.getSubjectName());
                        // 人民币金额（元）
                        dataRow1.createCell(20).setCellValue(details.getCnyAmount().toString());
                        // 入账日期
                        dataRow1.createCell(21).setCellValue(DateUtils.formatDate(details.getPostingDate()));

                        // 查询入账线索
                        List<String> relIdAllList = new ArrayList<>();
                        relIdAllList.addAll(relIdsList);
                        relIdAllList.addAll(manualRelIdsList);
                        if (relIdAllList.size() > 0) {
                            List<String> clueIdList = new ArrayList<>();
                            List<String> codeList = new ArrayList<>();
                            List<String> summaryList = new ArrayList<>();

                            List<LdcEntryClue> clueList = iLdcEntryClueService.getByIds(relIdAllList);
                            for (LdcEntryClue clue : clueList) {
                                clueIdList.add(clue.getId());
                                codeList.add(clue.getIdentificationCode());
                                summaryList.add(clue.getSummary() == null ? "摘要为空" : clue.getSummary());
                            }
                            dataRow1.createCell(22).setCellValue(String.join("@@", codeList));
                            dataRow1.createCell(23).setCellValue(String.join("@@", summaryList));
                        }
                        // 设置边框
                        for (int i = 0; i < dataRow1.getLastCellNum(); i++) {
                            Cell cell = dataRow1.getCell(i);
                            if (cell != null) {
                                cell.setCellStyle(dataStyle);
                            } else {
                                cell = dataRow1.createCell(i);
                                cell.setCellStyle(dataStyle);
                            }
                        }
                        detailIndex++;
                    }
                }
                // 设置边框
                for (int i = 0; i < dataRow.getLastCellNum(); i++) {
                    Cell cell = dataRow.getCell(i);
                    if (cell != null) {
                        cell.setCellStyle(dataStyle);
                    } else {
                        cell = dataRow.createCell(i);
                        cell.setCellStyle(dataStyle);
                    }
                }
                dataIndex++;
            }
        }

        response.setHeader("Connection", "close");
        response.setHeader("Content-Type",
                "application/vnd.ms-excel;charset=UTF-8");
        OutputStream out = null;
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateFormat = sdf.format(date);
        String fileName = "export-excel" + dateFormat + ".xlsx";
        fileName = URLEncoder.encode(fileName, "utf-8");
        response.setHeader("Content-Disposition", "attachment;filename="
                + fileName);
        try {
            out = response.getOutputStream();
        } catch (IOException e) {
            log.error(e.getMessage(),e);
        }

        if(out != null){
            try {
                workbook.write(out);
                out.flush();
                out.close();
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
    }

    private static void addDropDownList(Sheet sheet, int colIndex, int maxRows, String[] options) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createExplicitListConstraint(options);

        CellRangeAddressList addressList = new CellRangeAddressList(
                3, // 从第4行开始
                maxRows-1, // 最大行数
                colIndex, // 列索引
                colIndex // 同一列
        );

        DataValidation validation = helper.createValidation(constraint, addressList);
        validation.setSuppressDropDownArrow(true);
        sheet.addValidationData(validation);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> importExcel(HttpServletRequest request,
                                 HttpServletResponse response,
                                 Class<LdcEventManageForImport> clazz) throws Exception {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        String processFlag = getProcessFlag();
        if (StringUtils.isBlank(processFlag)) {
            throw new Exception("您没有权限进行操作");
        }
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            List<LdcEventManageForImport> list = ExcelImportUtil.importExcel(file.getInputStream(), clazz, params);
            // 对数据进行校验
            int index = 4;
            LinkedList<String> eventIdSet = new LinkedList<>();
            LinkedList<String> eventNameList = new LinkedList<>();

            for (LdcEventManageForImport manage : list) {
                String dataType = manage.getDataType();
                if (StringUtils.isBlank(dataType)) {
                    throw new Exception("第" + index + "行，数据类型不能为空");
                }
                if (dataType.equals("主数据")) {
                    LdcEventManage insertManage = new LdcEventManage();
                    // 校验事件分类（一级）
                    String eventClassifyFirst = manage.getEventClassifyFirst();
                    if (StringUtils.isBlank(eventClassifyFirst)) {
                        throw new Exception("第" + index + "行，事件分类（一级）不能为空");
                    }
                    insertManage.setEventClassifyFirst(eventClassifyFirst);
                    // 校验事件分类（二级）
                    String eventClassifySecond = manage.getEventClassifySecond();
                    if (StringUtils.isBlank(eventClassifySecond)) {
                        throw new Exception("第" + index + "行，事件分类（二级）不能为空");
                    }
                    insertManage.setEventClassifySecond(eventClassifySecond);
                    // TODO:需要校验是否存在参数表

                    // 校验事件名称
                    String eventName = manage.getEventName();
                    if (StringUtils.isBlank(eventName)) {
                        throw new Exception("第" + index + "行，事件名称不能为空");
                    } else {
                        if (eventName.length() > 100) {
                            throw new Exception("第" + index + "行，事件名称长度不能超过100");
                        }
                    }
                    insertManage.setEventName(eventName);

                    // 校验事件描述
                    String eventDescrption = manage.getEventDescrption();
                    if (StringUtils.isBlank(eventDescrption)) {
                        throw new Exception("第" + index + "行，事件描述不能为空");
                    } else {
                        if (eventDescrption.length() > 5000) {
                            throw new Exception("第" + index + "行，事件描述长度不能超过5000");
                        }
                    }
                    insertManage.setEventDescrption(eventDescrption);

                    // 校验事件原因描述
                    String eventReasonDescription = manage.getEventReasonDescription();
                    if (StringUtils.isBlank(eventReasonDescription)) {
                        throw new Exception("第" + index + "行，事件原因描述不能为空");
                    } else {
                        if (eventReasonDescription.length() > 5000) {
                            throw new Exception("第" + index + "行，事件原因描述长度不能超过5000");
                        }
                    }
                    insertManage.setEventReasonDescription(eventReasonDescription);

                    // 填报次数（默认第一次）
                    Integer fillCount = manage.getFillCount() == null ? 1 : manage.getFillCount();
                    insertManage.setFillCount(fillCount);

                    // 校验是否结束
                    String isEnd = manage.getIsEnd();
                    if (StringUtils.isBlank(isEnd)) {
                        throw new Exception("第" + index + "行，是否结束不能为空");
                    } else {
                        if (!isEnd.equals("1") && !isEnd.equals("0")) {
                            throw new Exception("第" + index + "行，是否结束输入有误，只能输入是/否");
                        }
                    }
                    insertManage.setIsEnd(isEnd);

                    // 状态默认为草稿
                    insertManage.setStatus("1");

                    // 校验是否为损失事件
                    String isLossEvent = manage.getIsLossEvent();
                    if (StringUtils.isBlank(isLossEvent)) {
                        throw new Exception("第" + index + "行，是否为损失事件不能为空");
                    } else {
                        if (!isLossEvent.equals("1") && !isLossEvent.equals("0")) {
                            throw new Exception("第" + index + "行，是否为损失事件输入有误，只能输入是/否");
                        }
                    }
                    insertManage.setIsLossEvent(isLossEvent);

                    // 是否经监管认可剔除-默认是否
                    insertManage.setIsRemove("0");

                    // 校验初次填报日期
                    Date firstFillDate = manage.getFirstFillDate();
                    if (firstFillDate == null) {
                        throw new Exception("第" + index + "行，初次填报日期不能为空");
                    }
                    // 校验本次填报日期
                    Date thisFillDate = manage.getThisFillDate();
                    if (thisFillDate == null) {
                        throw new Exception("第" + index + "行，本次填报日期不能为空");
                    }
                    if (firstFillDate.after(thisFillDate)) {
                        throw new Exception("第" + index + "行，初次填报日期不能大于本次填报日期");
                    }
                    insertManage.setFirstFillDate(firstFillDate);
                    insertManage.setThisFillDate(thisFillDate);

                    // 校验事件初始发生日期
                    Date initialHappenDate = manage.getInitialHappenDate();
                    if (initialHappenDate == null) {
                        throw new Exception("第" + index + "行，事件初始发生日期不能为空");
                    }
                    // 校验事件发现日期
                    Date findDate = manage.getFindDate();
                    if (findDate == null) {
                        throw new Exception("第" + index + "行，事件发现日期不能为空");
                    }
                    if (initialHappenDate.after(findDate)) {
                        throw new Exception("第" + index + "行，事件发现日期不能早于事件初始发生日期");
                    }
                    if (thisFillDate.before(initialHappenDate)) {
                        throw new Exception("第" + index + "行，事件录入日期不应早于事件发生日期");
                    }
                    if (thisFillDate.before(findDate)) {
                        throw new Exception("第" + index + "行，事件录入日期不应早于事件发现日期");
                    }
                    // 校验首笔损失入账(或确认)日期
                    Date firstEntryDate = manage.getFirstEntryDate();
                    if (firstEntryDate == null) {
                        throw new Exception("第" + index + "行，首笔损失入账(或确认)日期不能为空");
                    }
                    if (firstEntryDate.before(initialHappenDate)) {
                        throw new Exception("第" + index + "行，首笔损失入账(或确认)日期不能早于事件初始发生日期");
                    }
                    insertManage.setInitialHappenDate(initialHappenDate);
                    insertManage.setFindDate(findDate);
                    insertManage.setFirstEntryDate(firstEntryDate);

                    // 校验最大预估损失金额
                    BigDecimal maxEstimateLossMoney = manage.getMaxEstimateLossMoney();
                    // 校验总损失金额
                    BigDecimal totalLossMoney = manage.getTotalLossMoney();
                    if (totalLossMoney == null && isLossEvent.equals("1")) {
                        throw new Exception("第" + index + "行，当确认是损失事件时，总损失金额必填");
                    }
                    // 校验净损失金额
                    BigDecimal netLossMoney = manage.getNetLossMoney();
                    if (netLossMoney == null && isLossEvent.equals("1")) {
                        throw new Exception("第" + index + "行，当确认是损失事件时，净损失金额必填");
                    }
                    // 校验已确认的回收总金额
                    BigDecimal cfmdRecycleMoney = manage.getCfmdRecycleMoney();
                    if (cfmdRecycleMoney == null && isLossEvent.equals("1")) {
                        throw new Exception("第" + index + "行，当确认是损失事件时，已确认的回收总金额必填");
                    }
                    // 校验其中：已确认的保险赔付
                    BigDecimal cfmdIndemnity = manage.getCfmdIndemnity();
                    if (cfmdIndemnity == null && isLossEvent.equals("1")) {
                        throw new Exception("第" + index + "行，当确认是损失事件时，其中：已确认的保险赔付必填");
                    }
                    if (isLossEvent.equals("1")) {
                        if (maxEstimateLossMoney.compareTo(totalLossMoney) < 0) {
                            throw new Exception("第" + index + "行，当确认是损失事件时，最大预估损失金额不得小于总损失金额");
                        }
                        if (maxEstimateLossMoney.compareTo(new BigDecimal("10000")) < 0) {
                            throw new Exception("第" + index + "行，当确认是损失事件时，最大预估损失金额不得小于一万元人民币");
                        }
                    }
                    insertManage.setMaxEstimateLossMoney(maxEstimateLossMoney);
                    insertManage.setTotalLossMoney(totalLossMoney);
                    insertManage.setNetLossMoney(netLossMoney);
                    insertManage.setCfmdRecycleMoney(cfmdRecycleMoney);
                    insertManage.setCfmdIndemnity(cfmdIndemnity);

                    // 校验广大客户服务质量
                    String customerServiceQuality = manage.getCustomerServiceQuality();
                    if (isLossEvent.equals("0") && StringUtils.isBlank(customerServiceQuality)) {
                        throw new Exception("第" + index + "行，当不是损失事件时，广大客户服务质量必填");
                    } else {
                        // 校验填写是否正确
                    }
                    insertManage.setCustomerServiceQuality(customerServiceQuality);
                    // 校验员工安全
                    String employeeSafety = manage.getEmployeeSafety();
                    if (isLossEvent.equals("0") && StringUtils.isBlank(employeeSafety)) {
                        throw new Exception("第" + index + "行，当不是损失事件时，员工安全必填");
                    } else {
                        // 校验填写是否正确
                    }
                    insertManage.setEmployeeSafety(employeeSafety);
                    // 校验运营中断
                    String operationInterruption = manage.getOperationInterruption();
                    if (isLossEvent.equals("0") && StringUtils.isBlank(operationInterruption)) {
                        throw new Exception("第" + index + "行，当不是损失事件时，运营中断必填");
                    } else {
                        // 校验填写是否正确
                    }
                    insertManage.setOperationInterruption(operationInterruption);
                    // 校验监管行动
                    String regulatoryActions = manage.getRegulatoryActions();
                    if (isLossEvent.equals("0") && StringUtils.isBlank(regulatoryActions)) {
                        //throw new Exception("第" + index + "行，当不是损失事件时，监管行动必填");
                    } else {
                        // 校验填写是否正确
                    }
                    insertManage.setRegulatoryActions(regulatoryActions);
                    // 校验声誉受损
                    String reputationDamage = manage.getReputationDamage();
                    if (isLossEvent.equals("0") && StringUtils.isBlank(reputationDamage)) {
                        throw new Exception("第" + index + "行，当不是损失事件时，声誉受损必填");
                    } else {
                        // 校验填写是否正确
                    }
                    insertManage.setReputationDamage(reputationDamage);

                    // 校验事件严重度分级
                    String severityClassification = manage.getSeverityClassification();
                    if (StringUtils.isBlank(severityClassification)) {
                        throw new Exception("第" + index + "行，事件严重度分级必填");
                    }
                    insertManage.setSeverityClassification(severityClassification);
                    // 校验是否重大操作风险事件
                    String isMajorRiskEvent = manage.getIsMajorRiskEvent();
                    if (StringUtils.isBlank(isMajorRiskEvent)) {
                        throw new Exception("第" + index + "行，否重大操作风险事件必填");
                    }
                    insertManage.setIsMajorRiskEvent(isMajorRiskEvent);
                    // 校验主要成因分类
                    String mainCauseClassification = manage.getMainCauseClassification();
                    if (StringUtils.isBlank(mainCauseClassification)) {
                        throw new Exception("第" + index + "行，主要成因分类必填");
                    } else {
                        // 校验填写是否正确
                    }
                    insertManage.setMainCauseClassification(mainCauseClassification);
                    // 校验损失事件类型(一级)
                    String eventTypeFirst = manage.getEventTypeFirst();
                    if (StringUtils.isBlank(eventTypeFirst)) {
                        throw new Exception("第" + index + "行，校验损失事件类型(一级)必填");
                    }
                    insertManage.setEventTypeFirst(eventTypeFirst);
                    // 校验损失事件类型(二级)
                    String eventTypeSecond = manage.getEventTypeSecond();
                    if (StringUtils.isBlank(eventTypeSecond)) {
                        throw new Exception("第" + index + "行，校验损失事件类型(二级)必填");
                    }
                    insertManage.setEventTypeSecond(eventTypeSecond);
                    // 校验损失事件类型(三级)
                    String eventTypeThird = manage.getEventTypeThird();
                    if (StringUtils.isBlank(eventTypeThird)) {
                        throw new Exception("第" + index + "行，校验损失事件类型(三级)必填");
                    }
                    insertManage.setEventTypeThird(eventTypeThird);

                    // 校验与信用/市场风险相关
                    String creditRiskRelevant = manage.getCreditRiskRelevant();
                    if (StringUtils.isBlank(creditRiskRelevant)) {
                        throw new Exception("第" + index + "行，与信用/市场风险相关必填");
                    } else {
                        // 校验填写是否正确
                    }
                    insertManage.setCreditRiskRelevant(creditRiskRelevant);

                    // 校验是否纳入计量
                    String isIntoMetering = manage.getIsIntoMetering();
                    if (StringUtils.isBlank(isIntoMetering)) {
                        throw new Exception("第" + index + "行，是否纳入计量必填");
                    } else {
                        if (!isIntoMetering.equals("1") && !isIntoMetering.equals("0")) {
                            throw new Exception("第" + index + "行，是否纳入计量输入有误，只能输入是/否");
                        }
                    }
                    insertManage.setIsIntoMetering(isIntoMetering);

                    // 校验事件填报机构
                    String fillOrgan = manage.getFillOrgan();
                    if (StringUtils.isBlank(fillOrgan)) {
                        throw new Exception("第" + index + "行，事件填报机构必填");
                    } else {
                        // 校验填写是否正确
                    }
                    insertManage.setFillOrgan(fillOrgan);
                    // 校验事件填报部门
                    String fillDepart = manage.getFillDepart();
                    if (StringUtils.isBlank(fillDepart)) {
                        throw new Exception("第" + index + "行，事件填报部门必填");
                    }
                    insertManage.setFillDepart(fillDepart);
                    // 校验事件填报人
                    String fillUser = manage.getFillUser();
                    if (StringUtils.isBlank(fillUser)) {
                        throw new Exception("第" + index + "行，事件填报人必填");
                    }
                    insertManage.setFillUser(fillUser);
                    // 校验填报人联系方式
                    String fillUserContact = manage.getFillUserContact();
                    if (StringUtils.isBlank(fillUserContact)) {
                        throw new Exception("第" + index + "行，填报人联系方式必填");
                    }
                    insertManage.setFillUserContact(fillUserContact);

                    // 校验总行对口管理部门
                    String duetManageDepart = manage.getDuetManageDepart();
                    if (StringUtils.isBlank(duetManageDepart)) {
                        throw new Exception("第" + index + "行，总行对口管理部门必填");
                    } else {
                        // 校验填写是否正确
                    }
                    insertManage.setDuetManageDepart(duetManageDepart);
                    // 校验发生机构
                    String happenOrgan = manage.getHappenOrgan();
                    if (StringUtils.isBlank(happenOrgan)) {
                        throw new Exception("第" + index + "行，发生机构必填");
                    } else {
                        // 校验填写是否正确
                    }
                    insertManage.setHappenOrgan(happenOrgan);

                    // 校验发生部门
                    String happenDepart = manage.getHappenDepart();
                    if (StringUtils.isBlank(happenDepart)) {
                        throw new Exception("第" + index + "行，发生部门必填");
                    } else {
                        // 校验填写是否正确
                    }
                    insertManage.setHappenDepart(happenDepart);

                    // TODO：huanjing 获取关联台账编号 先不做
                    String relLedgerCode = manage.getRelLedgerCode();

                    // TODO：huanjing合并先不做



                    // 获取编号
                    String serialIndex = getEventCodeByOrgId(insertManage.getFillDepart());
                    insertManage.setSerialIndex(serialIndex);
                    SysDepart sysDepart = sysDepartService.getById(insertManage.getFillDepart());
                    if (sysDepart == null ) {
                        throw new Exception("第" + index + "行，填报部门"+insertManage.getFillDepart()+"未找到，请检查");

                    }
                    insertManage.setEventCode(sysDepart.getOrgCode() + serialIndex);
                    // 获取processFlag
                    insertManage.setProcessFlag(processFlag);
                    // 暂定为未合并
                    insertManage.setMergeState("1");
                    // 入库
                    ldcEventManageMapper.insert(insertManage);
                    // 入库操作记录

                    String nodeRole = getRole(insertManage.getProcessFlag());
                    LdcEventHistory history = new LdcEventHistory();
                    history.setEventId(insertManage.getId());
                    history.setNodeRole(nodeRole);
                    history.setStatus(insertManage.getStatus());
                    history.setOperateDepart(sysUser.getOrgId());
                    history.setOperateTime(new Date());
                    history.setOperateUser(sysUser.getUsername());
                    history.setOperateType("创建事件");
                    historyMapper.insert(history);

                    eventIdSet.add(insertManage.getId());
                    eventNameList.add(insertManage.getEventName());
                    // 校验附件类型
                    String documentType = manage.getDocumentType();
                    // 校验附件名称
                    // 校验附件说明（不入库了，万一有空值）
                    String documentExplain = manage.getDocumentExplain();
                    if (StringUtils.isNotBlank(documentType)) {
                        // 遍历
                        String documentTypes[] = documentType.split("@@");
                        for (String type : documentTypes) {
                            LdcEventDocument document = new LdcEventDocument();
                            document.setEventId(insertManage.getId());
                            // TODO：huanjing需要数据库转换
                            document.setDocumentType(type);
                            documentMapper.insert(document);
                        }
                    }
                } else {
                    LdcEventRecycleDetails ldcEventRecycleDetails = new LdcEventRecycleDetails();
                    // 明细数据挂载到上一个数据上
                    // 拿到主数据id
                    String eventId = eventIdSet.get(eventIdSet.size() - 1);
                    String eventName = eventNameList.get(eventNameList.size() - 1);
                    ldcEventRecycleDetails.setLdcEventId(eventId);
                    // 校验类别
                    String category = manage.getCategory();
                    if (StringUtils.isBlank(category)) {
                        throw new Exception("第" + index + "行，类别必填");
                    } else {
                        // 校验填写是否正确
                    }
                    ldcEventRecycleDetails.setCategory(category);
                    // 校验子类
                    if (category.equals("2")) {
                        String subClass = manage.getSubClass();
                        if (StringUtils.isBlank(subClass)) {
                            throw new Exception("第" + index + "行，当类别为回收时，子类必填");
                        }
                        ldcEventRecycleDetails.setSubClass(subClass);
                    } else {
                        String lossForm = manage.getLossForm();
                        if (StringUtils.isBlank(lossForm)) {
                            throw new Exception("第" + index + "行，当类别为损失/成本时，损失形态必填");
                        }
                        ldcEventRecycleDetails.setLossForm(lossForm);
                    }
                    // 校验机构
                    String orgId = manage.getOrgId();
                    if (StringUtils.isBlank(orgId)) {
                        throw new Exception("第" + index + "行，损失/回收明细的机构必填");
                    }
                    ldcEventRecycleDetails.setOrgId(orgId);
                    // 校验部门
                    String departNo = manage.getDepartNo();
                    if (StringUtils.isBlank(departNo)) {
                        throw new Exception("第" + index + "行，损失/回收明细的部门必填");
                    }
                    ldcEventRecycleDetails.setDepartNo(departNo);
                    // 校验会计科目代码
                    String subjectCode = manage.getSubjectCode();
                    if (StringUtils.isBlank(subjectCode)) {
                        throw new Exception("第" + index + "行，损失/回收明细的会计科目代码必填");
                    }
                    ldcEventRecycleDetails.setSubjectCode(subjectCode);
                    // 校验会计科目名称
                    String subjectName = manage.getSubjectName();
                    if (StringUtils.isBlank(subjectName)) {
                        throw new Exception("第" + index + "行，损失/回收明细的会计科目名称必填");
                    }
                    ldcEventRecycleDetails.setSubjectName(subjectName);
                    // 校验记账币种
                    String currency = manage.getCurrency();
                    if (StringUtils.isBlank(currency)) {
                        throw new Exception("第" + index + "行，损失/回收明细的记账币种必填");
                    }
                    ldcEventRecycleDetails.setCurrency(currency);
                    // 校验记账金额(元）
                    BigDecimal amount = manage.getAmount();
                    if (amount == null) {
                        throw new Exception("第" + index + "行，损失/回收明细的记账金额必填");
                    }
                    ldcEventRecycleDetails.setAmount(amount);
                    // 校验对人民币汇率
                    String exchangeRate = manage.getExchangeRate();
                    if (StringUtils.isBlank(exchangeRate)) {
                        throw new Exception("第" + index + "行，损失/回收明细的对人民币汇率必填");
                    }
                    ldcEventRecycleDetails.setExchangeRate(exchangeRate);
                    // 校验人民币金额（元）
                    String cnyAmount = manage.getCnyAmount();
                    if (StringUtils.isBlank(cnyAmount)) {
                        throw new Exception("第" + index + "行，损失/回收明细的人民币金额（元）必填");
                    }
                    ldcEventRecycleDetails.setCnyAmount(cnyAmount);
                    // 校验入账日期
                    Date postingDate = manage.getPostingDate();
                    if (postingDate == null) {
                        throw new Exception("第" + index + "行，损失/回收明细的入账日期必填");
                    }
                    ldcEventRecycleDetails.setPostingDate(postingDate);
                    // 校验说明
                    ldcEventRecycleDetails.setExplanation(manage.getExplanation());
                    // 附件不入库，页面上传


                    // 获取识别码
                    String identificationCode = manage.getIdentificationCode();
                    if (StringUtils.isBlank(identificationCode)) {
                        throw new Exception("第" + index + "行，关联入账线索的识别码必填");
                    }

                    // 获取摘要
                    String summary = manage.getSummary();

                    // 获取数据标记
                    String clueMarks = manage.getClueMark();
                    if (StringUtils.isBlank(clueMarks)) {
                        throw new Exception("第" + index + "行，关联入账线索的数据标记必填");
                    } else {
                        // 定义relIds
                        List<String> relIdsList = new ArrayList<>();
                        List<String> manuIdsList = new ArrayList<>();
                        // 遍历 clueMarks
                        String clueMarkss[] = clueMarks.split("@@");
                        for (String clueMark : clueMarkss) {
                            // 判断入账线索
                            if (clueMark.contains("手工添加")) {

                                // 判断是否存在
                                QueryWrapper queryWrapper = new QueryWrapper();
                                queryWrapper.eq("data_flag", clueMark);
                                List<LdcEntryClue> ldcEntryClueList = iLdcEntryClueService.list(queryWrapper);
                                LdcEntryClue ldcEntryClue = ldcEntryClueList.get(0);
                                // 需要入库线索表
//                                LdcEntryClue ldcEntryClue = new LdcEntryClue();
//                                ldcEntryClue.setClueStatus("12");
//                                ldcEntryClue.setIdentificationCode(identificationCode);
//                                ldcEntryClue.setDept(orgId);
//                                ldcEntryClue.setDepartment(departNo);
//                                ldcEntryClue.setLedgerAccountCode(subjectCode);
//                                ldcEntryClue.setLedgerAccountName(subjectName);
//                                ldcEntryClue.setAccountingCurrency(currency);
//                                ldcEntryClue.setAccountingAmt(amount);
//                                ldcEntryClue.setRecordedDate(postingDate);
//                                ldcEntryClue.setClueReceptionDept(departNo);
//                                ldcEntryClue.setSummary(summary);
//                                iLdcEntryClueService.save(ldcEntryClue);

                                LdcEventClueRel rel = new LdcEventClueRel();
                                rel.setEventId(eventId);
                                rel.setClueId(ldcEntryClue.getId());
                                rel.setIsRelState("1");
                                clueRelMapper.insert(rel);

                                // 入库操作记录表
                                LdcEntryClueHistory clueHistory = new LdcEntryClueHistory();
                                clueHistory.setEntryClueId(ldcEntryClue.getId());
                                String description = sysUser.getUsername() + "在ldc管理模块将损失事件[" + eventName + "]关联入账线索";
                                clueHistory.setProcessDescribe(description);
                                ldcEntryClueHistoryMapper.insert(clueHistory);

                                // 入库明细表
                                manuIdsList.add(ldcEntryClue.getId());

                                // 更新ldc的rel_ids和manual_rel_ids字段
                                LdcEventManage updateManage = ldcEventManageMapper.selectById(eventId);
                                LdcEventManage updateEventManage = new LdcEventManage();
                                updateEventManage.setId(eventId);
                                String manualRelIds = updateManage.getManualRelIds();
                                if (StringUtils.isBlank(manualRelIds)) {
                                    updateEventManage.setManualRelIds(ldcEntryClue.getId());
                                } else {
                                    updateEventManage.setManualRelIds(manualRelIds + "," + ldcEntryClue.getId());
                                }
                                ldcEventManageMapper.updateById(updateEventManage);
                            } else {
                                // 判断是否存在
                                QueryWrapper queryWrapper = new QueryWrapper();
                                queryWrapper.eq("data_flag", clueMark);
                                List<LdcEntryClue> ldcEntryClueList = iLdcEntryClueService.list(queryWrapper);
                                // LdcEntryClue ldcEntryClue = iLdcEntryClueService.getById(clueMark);
                                if (ldcEntryClueList == null) {
                                    throw new Exception("第" + index + "行，关联入账线索数据标记不存在");
                                } else {
                                    LdcEntryClue ldcEntryClue = ldcEntryClueList.get(0);
                                    // 入库关联表
                                    LdcEventClueRel rel = new LdcEventClueRel();
                                    rel.setEventId(eventId);
                                    rel.setClueId(clueMark);
                                    rel.setIsRelState("1");
                                    clueRelMapper.insert(rel);
                                    // 变更状态为已关联事件
                                    if (!ldcEntryClue.getClueStatus().equals("12")) {
                                        LdcEntryClue clue = new LdcEntryClue();
                                        clue.setId(clueMark);
                                        clue.setClueStatus("9");
                                        iLdcEntryClueService.updateById(clue);
                                        // 入库明细表
                                        relIdsList.add(ldcEntryClue.getId());

                                        // 更新ldc的rel_ids和manual_rel_ids字段
                                        LdcEventManage updateManage = ldcEventManageMapper.selectById(eventId);
                                        LdcEventManage updateEventManage = new LdcEventManage();
                                        updateEventManage.setId(eventId);
                                        String relIds = updateManage.getRelIds();
                                        if (StringUtils.isBlank(relIds)) {
                                            updateEventManage.setRelIds(ldcEntryClue.getId());
                                        } else {
                                            updateEventManage.setRelIds(relIds + "," + ldcEntryClue.getId());
                                        }
                                        ldcEventManageMapper.updateById(updateEventManage);
                                    } else {
                                        // 入库明细表
                                        relIdsList.add(ldcEntryClue.getId());
                                        // 更新ldc的rel_ids和manual_rel_ids字段
                                        LdcEventManage updateManage = ldcEventManageMapper.selectById(eventId);
                                        LdcEventManage updateEventManage = new LdcEventManage();
                                        updateEventManage.setId(eventId);
                                        String manualRelIds = updateManage.getManualRelIds();
                                        if (StringUtils.isBlank(manualRelIds)) {
                                            updateEventManage.setManualRelIds(ldcEntryClue.getId());
                                        } else {
                                            updateEventManage.setManualRelIds(manualRelIds + "," + ldcEntryClue.getId());
                                        }
                                        ldcEventManageMapper.updateById(updateEventManage);
                                    }

                                    // 入库操作记录表
                                    LdcEntryClueHistory clueHistory = new LdcEntryClueHistory();
                                    clueHistory.setEntryClueId(clueMark);
                                    String description = sysUser.getUsername() + "在ldc管理模块将损失事件[" + eventName + "]关联入账线索";
                                    clueHistory.setProcessDescribe(description);
                                    ldcEntryClueHistoryMapper.insert(clueHistory);
                                }
                            }
                        }
                        if (manuIdsList.size() > 0) {
                            ldcEventRecycleDetails.setManualRelIds(String.join(",", manuIdsList));
                        }
                        if (relIdsList.size() > 0) {
                            ldcEventRecycleDetails.setRelIds(String.join(",", relIdsList));
                        }
                    }
                    detailsMapper.insert(ldcEventRecycleDetails);
                }
                index++;
            }
        }
        return Result.ok("导入成功");
    }

    @Override
    public Result<String> queryCurrencyRate(LdcEventRecycleDetails detail) {
        String exchangeRate = detailsMapper.queryCurrencyRate(detail);
        if (StringUtils.isNotBlank(exchangeRate)) {
            BigDecimal rate = new BigDecimal(exchangeRate).divide(new BigDecimal("100"));
            return Result.ok(rate.toString());
        } else {
            return Result.ok();
        }

    }

    @Override
    public Map<String, Object> querySubjectList() {
        List<Map<String, Object>> subjectCodeList = ldcEventManageMapper.querySubjectCodeList();
        List<Map<String, Object>> subjectNameList = ldcEventManageMapper.querySubjectNameList();
        Map<String, Object> result = new HashMap<>();
        result.put("subjectCodeList", subjectCodeList);
        result.put("subjectNameList", subjectNameList);
        return result;
    }

    @Override
    public IPage<LdcEventManage> queryLdcRelRcsaList(Page<LdcEventManage> page, LdcEventManage ldcEventManage) {
        Map<String, Object> queryMap = new HashMap<>();
        // 根据planId拿到截止日期
        RcsaPlanManage planManage = planManageMapper.selectById(ldcEventManage.getPlanId());
        Date evaluateEndDate = planManage.getEvaluateEndDate();
        // 根据截止日期拿到一年前的日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(evaluateEndDate);
        calendar.add(Calendar.YEAR, -1);
        Date evaluateStartDate = calendar.getTime();
        // 判断用户是总行用户还是分行
        LoginUser sysUser =(LoginUser) SecurityUtils.getSubject().getPrincipal();
        SysDepart sysDepart = sysDepartService.getById(sysUser.getOrgId());
        if (sysDepart.getDepartName().contains("分行")) {
            queryMap.put("fillDepart", sysUser.getOrgId());
        }

        queryMap.put("matrixName", ldcEventManage.getMatrixName());
        queryMap.put("evaluateEndDate", evaluateEndDate);
        queryMap.put("evaluateStartDate", evaluateStartDate);

        List<LdcEventManage> result = ldcEventManageMapper.queryLdcRelRcsaList(page, queryMap);
        return page.setRecords(result);
    }

    @Override
    public List<LdcRuleRel> getInteriorList(List<String> ldcIdList) {
        List<String> dictCodeList = new ArrayList<>();
        Collections.addAll(dictCodeList, "rule_external_basic_timeliness");
        List<SysDictItem> sysDictItemList = sysDictItemMapper.getItemByCode(dictCodeList);
        // 查询内规列表
        List<RuleSystemReformedAndAbolished> resultList = andAbolishedMapper.queryInterList(ldcIdList);
        if (resultList == null || resultList.isEmpty()) {
            return new ArrayList<>();
        } else {
            List<LdcRuleRel> relList = new ArrayList<>();
            for (RuleSystemReformedAndAbolished abolished : resultList) {
                LdcRuleRel ruleRel = new LdcRuleRel();
                ruleRel.setNum(abolished.getDocumentNumber());
                ruleRel.setName(abolished.getSystemName());
                ruleRel.setDept(abolished.getIssuingDeptOne());
                ruleRel.setDeptName(abolished.getIssuingDeptOneName());
                String timeliness = abolished.getTimeliness();
                ruleRel.setTimeliness(timeliness);
                if (StringUtils.isNotBlank(timeliness)) {
                    List<SysDictItem> collect = sysDictItemList.stream().filter(o -> o.getItemValue().equals(timeliness)).collect(Collectors.toList());
                    if (collect !=null && !collect.isEmpty()) {
                        String itemText = collect.get(0).getItemText();
                        ruleRel.setTimelinessName(itemText);
                    }
                }
                ruleRel.setRuleId(abolished.getId());
                ruleRel.setRuleType("1");
                ruleRel.setLdcId(abolished.getLdcId());
                relList.add(ruleRel);
            }
            return relList;
        }
    }

    @Override
    public List<LdcRuleRel> getExternalList(List<String> ldcIdList) {
        List<RuleExternalBasic> resultList = externalBasicMapper.queryExternalList(ldcIdList);
        if (resultList == null || resultList.isEmpty()) {
            return new ArrayList<>();
        } else {
            List<LdcRuleRel> relList = new ArrayList<>();
            for (RuleExternalBasic basic : resultList) {
                LdcRuleRel ruleRel = new LdcRuleRel();
                ruleRel.setNum(basic.getBasicIssuedNumber());
                ruleRel.setName(basic.getBasicRegulatoryName());
                ruleRel.setDept(basic.getBasicIssuedAgency());
                ruleRel.setDeptName(basic.getBasicIssuedAgency());
                ruleRel.setTimeliness(basic.getBasicTimeliness());
                ruleRel.setTimelinessName(basic.getBasicTimelinessName());
                ruleRel.setRuleId(basic.getId());
                ruleRel.setRuleType("2");
                ruleRel.setLdcId(basic.getLdcId());
                relList.add(ruleRel);
            }
            return relList;
        }
    }

    @Override
    public String judgeLdcRelInfo(RcsaPlanManage planManage) {

        // 获取ldc的主键
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("module_id1", planManage.getId());
        queryWrapper.eq("module_flag", "1");
        ModuleRel moduleRel = moduleRelMapper.selectOne(queryWrapper);
        if (moduleRel == null) {
            return "0";
        } else {
            // 1-合并 2-是否作废流程 2-审核未通过
            String id = moduleRel.getModuleId2();
            LdcEventManage manage = ldcEventManageMapper.selectById(id);
            // 判断是否已被合并
            String mergeState = manage.getMergeState();
            if ("2".equals(mergeState)) {
                return "1";
            }
            if (manage.getIsNullify().equals("1")) {
                if (!manage.getStatus().equals("12")) {
                    return "2";
                } else {
                    return "3";
                }
            }
            if (!manage.getStatus().equals("12")) {
                return "4";
            } else {
                return "0";
            }
        }
    }

    @Override
    public void resetOrder() {
//        List<LdcEventManage> list = ldcEventManageMapper.selectList111();
//        for (LdcEventManage manage : list) {
//            List<String> clueRelIds = new LinkedList<>();
//            List<String> manRelIds = new LinkedList<>();
//
//            List<String> clueIds = new LinkedList<>();
//            String relIds = manage.getRelIds();
//            if (StringUtils.isNotBlank(relIds)) {
//                clueIds.addAll(Arrays.asList(relIds.split(",")));
//            }
//            String manualRelIds = manage.getManualRelIds();
//            if (StringUtils.isNotBlank(manualRelIds)) {
//                clueIds.addAll(Arrays.asList(manualRelIds.split(",")));
//            }
//            QueryWrapper queryWrapper1 = new QueryWrapper();
//            queryWrapper1.in("id", clueIds);
//            List<LdcEntryClue> clueList = iLdcEntryClueService.list(queryWrapper1);
//            for (LdcEntryClue clue : clueList) {
//                String clueStatus = clue.getClueStatus();
//                if (clueStatus.equals("12")) {
//                    manRelIds.add(clue.getId());
//                } else {
//                    clueRelIds.add(clue.getId());
//                }
//            }
//            // 更新
//            LdcEventManage updateManage = new LdcEventManage();
//            updateManage.setId(manage.getId());
//            updateManage.setRelIds(String.join(",", clueRelIds));
//            updateManage.setManualRelIds(String.join(",", manRelIds));
//            ldcEventManageMapper.updateById(updateManage);
//        }

//        List<LdcEventManage> list = ldcEventManageMapper.selectList(new QueryWrapper<>());
//
    }


    private static CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置字体
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short)18);
        return style;
    }

    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置字体
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short)18);
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());

        // 设置背景色
        style.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setFont(font);

        return style;
    }

    private static CellStyle createSubHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);

        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short)18);
        font.setColor(IndexedColors.DARK_RED.getIndex());
        style.setFont(font);

        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        return style;
    }

    private static void mergeCells(Sheet sheet, int firstRow, int lastRow,
                                   int firstCol, int lastCol, String value, CellStyle style) {
        sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));

        // 为合并区域的所有单元格设置边框
        for (int rowNum = firstRow; rowNum <= lastRow; rowNum++) {
            Row row = sheet.getRow(rowNum) == null ? sheet.createRow(rowNum) : sheet.getRow(rowNum);
            for (int colNum = firstCol; colNum <= lastCol; colNum++) {
                Cell cell = row.createCell(colNum);
                cell.setCellStyle(style);
                if (rowNum == firstRow && colNum == firstCol) {
                    cell.setCellValue(value);
                }
            }
        }
    }

    private static CellStyle createBasicInfoStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置字体
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short)18);
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());

        // 设置背景色
        style.setFillForegroundColor(IndexedColors.DARK_TEAL.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setFont(font);

        return style;
    }

    private boolean evaluateCondition(String formula, BigDecimal value) {
        try {
            // 提取运算符和比较值
            String operator = formula.replaceAll("[0-9.]", "").trim();
            BigDecimal formulaValue = new BigDecimal(formula.replaceAll("[^0-9.]", ""));

            // 执行比较运算
            switch(operator) {
                case ">":  return value.compareTo(formulaValue) > 0;
                case "<":  return value.compareTo(formulaValue) < 0;
                case ">=": return value.compareTo(formulaValue) >= 0;
                case "<=": return value.compareTo(formulaValue) <= 0;
                case "==": return value.compareTo(formulaValue) == 0;
                default: throw new IllegalArgumentException("不支持的运算符: " + operator);
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无效的数字格式");
        }
    }

    /**
     * 根据角色，当前数据的状态，流程判断审批是否可以撤回
     * @param processFlag
     * @param status
     * @return
     */
    private String judgeCanAuditWithdraw(String processFlag, String status) {
        String canAuditWithdraw = "0";
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 获取人员角色
        List<String> roleList = sysUserRoleMapper.getRoleCodeByUserId(sysUser.getId());
        if (processFlag.equals("1")) {
            if (roleList.contains("fh_cfshg")) {
                if ("7".equals(status) || "3".equals(status)) {
                    canAuditWithdraw = "2";
                }
            } else if (roleList.contains("fhfzr_cfshg")) {
                if ("8".equals(status) || "5".equals(status)) {
                    canAuditWithdraw = "3";
                }
            } else if (roleList.contains("zh_ldc_shg")) {
                if ("10".equals(status) || "14".equals(status)) {
                    canAuditWithdraw = "5";
                }
            } else if (roleList.contains("nkhgfzr_ldc_shg")) {
                if ("6".equals(status) || "26".equals(status)) {
                    canAuditWithdraw = "14";
                }
            }
        } else if ("2".equals(processFlag)) {
            if (roleList.contains("zhgbm_cfshg")) {
                if ("4".equals(status) || "5".equals(status)) {
                    canAuditWithdraw = "4";
                }
            } else if (roleList.contains("zh_ldc_shg")) {
                if ("10".equals(status) || "14".equals(status)) {
                    canAuditWithdraw = "5";
                }
            } else if (roleList.contains("nkhgfzr_ldc_shg")) {
                if ("6".equals(status) || "26".equals(status)) {
                    canAuditWithdraw = "14";
                }
            }
        } else if ("3".equals(processFlag)) {
            if (roleList.contains("zh_ldc_shg")) {
                if ("10".equals(status) || "14".equals(status)) {
                    canAuditWithdraw = "5";
                }
            } else if (roleList.contains("nkhgfzr_ldc_shg")) {
                if ("6".equals(status) || "26".equals(status)) {
                    canAuditWithdraw = "14";
                }
            }
        } else if ("4".equals(processFlag)) {
            if (roleList.contains("zgshgfzr_cfshg")) {
                if ("16".equals(status) || "21".equals(status)) {
                    canAuditWithdraw = "15";
                }
            } else if (roleList.contains("zgsfzr_cfshg")) {
                if ("5".equals(status) || "22".equals(status)) {
                    canAuditWithdraw = "16";
                }
            } else if (roleList.contains("zh_ldc_shg")) {
                if ("10".equals(status) || "14".equals(status)) {
                    canAuditWithdraw = "5";
                }
            } else if (roleList.contains("nkhgfzr_ldc_shg")) {
                if ("6".equals(status) || "26".equals(status)) {
                    canAuditWithdraw = "14";
                }
            }

        } else if ("5".equals(processFlag)) {
            if (roleList.contains("czyhzb_cfshg")) {
                if ("18".equals(status) || "23".equals(status)) {
                    canAuditWithdraw = "17";
                }
            } else if (roleList.contains("czyhzbhgfzr_cfshg")) {
                if ("19".equals(status) || "24".equals(status)) {
                    canAuditWithdraw = "18";
                }
            } else if (roleList.contains("czzbfzr_cfshg")) {
                if ("5".equals(status) || "25".equals(status)) {
                    canAuditWithdraw = "19";
                }
            } else if (roleList.contains("zh_ldc_shg")) {
                if ("10".equals(status) || "14".equals(status)) {
                    canAuditWithdraw = "5";
                }
            } else if (roleList.contains("nkhgfzr_ldc_shg")) {
                if ("6".equals(status) || "26".equals(status)) {
                    canAuditWithdraw = "14";
                }
            }
        } else if ("6".equals(processFlag)) {
            if (roleList.contains("nkhgfzr_ldc_shg")) {
                if ("6".equals(status) || "26".equals(status)) {
                    canAuditWithdraw = "14";
                }
            }
        } else if ("7".equals(processFlag)) {
            if (roleList.contains("fhfzr_cfshg")) {
                if ("8".equals(status) || "5".equals(status)) {
                    canAuditWithdraw = "3";
                }
            } else if (roleList.contains("zh_ldc_shg")) {
                if ("10".equals(status) || "14".equals(status)) {
                    canAuditWithdraw = "5";
                }
            } else if (roleList.contains("nkhgfzr_ldc_shg")) {
                if ("6".equals(status) || "26".equals(status)) {
                    canAuditWithdraw = "14";
                }
            }
        }
        return canAuditWithdraw;

    }

    public String getEventCodeByOrgId(String orgId) {
        String serialIndex = "";
        String maxSerialIndex = ldcEventManageMapper.getMaxSerialIndex(orgId);
        String nowMonth = DateUtils.formatDate("yyMM");
        if (StringUtils.isNotBlank(maxSerialIndex)) {
            // 判断是否是当前年月
            if (maxSerialIndex.startsWith(nowMonth)) {
                // 取后四位+1，补足四位数
                int index = Integer.parseInt(maxSerialIndex.substring(4,8)) + 1;
                serialIndex = nowMonth + String.format("%04d", index);
            } else {
                serialIndex = nowMonth + "0001";
            }
        } else {
            serialIndex = nowMonth + "0001";
        }
        return serialIndex;
    }


    private void summaryRecycle(Map<String, List<LdcEventRecycleDetails>> lossFormMap, String category, Set<String> manageTemporaryEntryId, Set<String> manageRelIds, Set<String> manageRelIds1, Set<String> manageManualIds, List<LdcEventRecycleDetails> mergerDetailList) {
        String lineSeparator = System.lineSeparator();
        lossFormMap.forEach((lossForm, lossFormList) -> {
            LdcEventRecycleDetails mergerDetail = new LdcEventRecycleDetails();
            mergerDetail.setCategory(category);
            if ("1".equals(category)) {
                mergerDetail.setLossForm(lossForm);
            } else {
                mergerDetail.setSubClass(lossForm);
            }
            Set<String> orgIdSet = new LinkedHashSet<>();
            Set<String> departNoSet = new LinkedHashSet<>();
            Set<String> subjectCodeSet = new LinkedHashSet<>();
            Set<String> subjectNameSet = new LinkedHashSet<>();
            BigDecimal amountAll = new BigDecimal(0);
            BigDecimal cnyAmountAll = new BigDecimal(0);
            Date postingDateNow = DateUtils.getDate();
            StringBuffer stringBuffer = new StringBuffer();
            List<String> documents = new LinkedList<>();
            Set<String> temporaryEntryIds = new LinkedHashSet<>();
            Set<String> relIdsList = new LinkedHashSet<>();
            Set<String> manualRelIdsList = new LinkedHashSet<>();

            for (LdcEventRecycleDetails details : lossFormList) {
                orgIdSet.addAll(Arrays.asList(details.getOrgId().split(",")));
                departNoSet.addAll(Arrays.asList(details.getDepartNo().split(",")));
                subjectCodeSet.addAll(Arrays.asList(details.getSubjectCode().split(",")));
                subjectNameSet.addAll(Arrays.asList(details.getSubjectName().split(",")));
                amountAll = amountAll.add(details.getAmount());
                cnyAmountAll = cnyAmountAll.add(new BigDecimal(details.getCnyAmount()));

                Date postingDate = details.getPostingDate();
                postingDateNow = postingDateNow.before(postingDate) ? postingDateNow : postingDate;
                String explanation = details.getExplanation();
                if (StringUtils.isNotBlank(explanation)) {
                    stringBuffer.append(explanation);
                    stringBuffer.append(lineSeparator);
                }
                String document = details.getDocument();
                if (StringUtils.isNotBlank(document)) {
                    documents.addAll(Arrays.asList(document.split(",")));
                }
                String temporaryEntryId = details.getTemporaryEntryId();
                if (StringUtils.isNotBlank(temporaryEntryId)) {
                    temporaryEntryIds.addAll(Arrays.asList(temporaryEntryId.split(",")));
                    manageTemporaryEntryId.addAll(Arrays.asList(temporaryEntryId.split(",")));
                }
                String relIds = details.getRelIds();
                if (StringUtils.isNotBlank(relIds)) {
                    relIdsList.addAll(Arrays.asList(relIds.split(",")));
                    manageRelIds.addAll(Arrays.asList(relIds.split(",")));
                }
                String manualRelIds = details.getManualRelIds();
                if (StringUtils.isNotBlank(manualRelIds)) {
                    manualRelIdsList.addAll(Arrays.asList(manualRelIds.split(",")));
                    manageManualIds.addAll(Arrays.asList(manualRelIds.split(",")));
                }
            }
            // 放入
            mergerDetail.setOrgId(String.join(",", orgIdSet));
            mergerDetail.setDepartNo(String.join(",", departNoSet));
            mergerDetail.setSubjectName(String.join(",", subjectNameSet));
            mergerDetail.setSubjectCode(String.join(",", subjectCodeSet));
            mergerDetail.setCurrency(lossFormList.get(0).getCurrency());
            mergerDetail.setAmount(amountAll);
            mergerDetail.setExchangeRate(lossFormList.get(0).getExchangeRate());
            mergerDetail.setCnyAmount(cnyAmountAll.toString());
            mergerDetail.setPostingDate(postingDateNow);
            mergerDetail.setExplanation(stringBuffer.toString());
            mergerDetail.setDocument(String.join(",", documents));
            mergerDetail.setTemporaryEntryId(String.join(",", temporaryEntryIds));
            mergerDetail.setRelIds(String.join(",", relIdsList));
            mergerDetail.setManualRelIds(String.join(",", manualRelIdsList));
            mergerDetailList.add(mergerDetail);
        });
    }

    private String getBackToFillStatus(String processFlag, String status) {
        String nextStatus = "";
        if ("1".equals(processFlag)) {// 分行流程
            if ("2".equals(status)) { // 待分行操风审核岗审核
                nextStatus = "7";
            } else if ("3".equals(status)) { // 待分行负责人-操风审核岗审核
                nextStatus = "8";
            } else if ("5".equals(status)) { // 待总行-LDC审核岗审核
                nextStatus = "10";
            } else if ("14".equals(status)) { // 待内控合规负责人-LDC审核岗审核
                nextStatus = "20";
            }
        } else if ("2".equals(processFlag)) { // 总行各部门流程
            if ("4".equals(status)) { // 待总行各部门操风审核岗审核
                nextStatus = "9";
            } else if ("5".equals(status)) { // 待总行-LDC审核岗审核
                nextStatus = "10";
            } else if ("14".equals(status)) {
                nextStatus = "20";
            }
        } else if ("3".equals(processFlag)) { // 总行ldc管理岗流程
            if ("5".equals(status)) {
                nextStatus = "10";
            } else if ("14".equals(status)) {
                nextStatus = "20";
            }
        } else if ("4".equals(processFlag)) {// 子公司流程
            if ("15".equals(status)) { // 待子公司合规负责人-操风审核岗审核
                nextStatus = "21";
            } else if ("16".equals(status)) { // 待子公司负责人-操风审核岗
                nextStatus = "22";
            }  else if ("5".equals(status)) { // 待总行-LDC审核岗审核
                nextStatus = "10";
            } else if ("14".equals(status)) {//
                nextStatus = "20";
            }
        } else if ("5".equals(processFlag)) {// 村镇银行流程
            if ("17".equals(status)) {// 待村镇银行总部-操风审核岗审核
                nextStatus = "23";
            } else if ("18".equals(status)) {// 待村镇银行总部合规负责人-操风审核岗审核
                nextStatus = "24";
            } else if ("19".equals(status)) {//待村镇总部负责人-操风审核岗审核
                nextStatus = "25";
            } else if ("5".equals(status)) { // 待总行-LDC审核岗审核
                nextStatus = "10";
            } else if ("14".equals(status)) {//
                nextStatus = "20";
            }
        } else if ("6".equals(processFlag)) {// 内控合规负责人流程，只退回到ldc总行审核岗（用于合并流程）
            nextStatus = "20";
        } else if ("7".equals(processFlag)) {// 分行操风审核岗流程
            if ("3".equals(status)) { // 待分行负责人-操风审核岗审核
                nextStatus = "8";
            } else if ("5".equals(status)) { // 待总行-LDC审核岗审核
                nextStatus = "10";
            } else if ("14".equals(status)) { // 待内控合规负责人-LDC审核岗审核
                nextStatus = "20";
            }
        }
        return nextStatus;
    }

    private String getStatusNextStep(String processFlag, String status) {
        String nextStatus = "";
        if ("1".equals(processFlag)) {// 分行流程
            if ("2".equals(status)) { // 待分行操风审核岗审核
                nextStatus = "3";
            } else if ("3".equals(status)) { // 待分行负责人-操风审核岗审核
                nextStatus = "5";
            } else if ("5".equals(status)) { // 待总行-LDC审核岗审核
                nextStatus = "14";
            } else if ("14".equals(status)) {
                nextStatus = "6"; // 审核通过(最终状态)
            }
        } else if ("2".equals(processFlag)) { // 总行各部门流程
            if ("4".equals(status)) { // 待总行各部门操风审核岗审核
                nextStatus = "5";
            } else if ("5".equals(status)) { // 待总行-LDC审核岗审核
                nextStatus = "14";
            } else if ("14".equals(status)) {
                nextStatus = "6"; // 审核通过(最终状态)
            }
        } else if ("3".equals(processFlag)) { // 总行ldc管理岗流程
            if ("5".equals(status)) {
                nextStatus = "14";
            } else if ("14".equals(status)) {
                nextStatus = "6"; // 审核通过(最终状态)
            }
        } else if ("4".equals(processFlag)) {// 子公司流程
            if ("15".equals(status)) { // 待子公司合规负责人-操风审核岗审核
                nextStatus = "16";
            } else if ("16".equals(status)) { // 待子公司负责人-操风审核岗
                nextStatus = "5";
            }  else if ("5".equals(status)) { // 待总行-LDC审核岗审核
                nextStatus = "14";
            } else if ("14".equals(status)) {//
                nextStatus = "6";
            }
        } else if ("5".equals(processFlag)) {// 村镇银行流程
            if ("17".equals(status)) {// 待村镇银行总部-操风审核岗审核
                nextStatus = "18";
            } else if ("18".equals(status)) {// 待村镇银行总部合规负责人-操风审核岗审核
                nextStatus = "19";
            } else if ("19".equals(status)) {//待村镇总部负责人-操风审核岗审核
                nextStatus = "5";
            } else if ("5".equals(status)) { // 待总行-LDC审核岗审核
                nextStatus = "14";
            } else if ("14".equals(status)) {//
                nextStatus = "6";
            }
        } else if ("6".equals(processFlag)) {
            if ("14".equals(status)) {
                nextStatus = "6";
            }
        } else if ("7".equals(processFlag)) {
            if ("3".equals(status)) { // 待分行负责人-操风审核岗审核
                nextStatus = "5";
            } else if ("5".equals(status)) { // 待总行-LDC审核岗审核
                nextStatus = "14";
            } else if ("14".equals(status)) {
                nextStatus = "6"; // 审核通过(最终状态)
            }
        }
        return nextStatus;
    }

    /**
     * 判断提交撤回是否可行
     * @param processFlag
     * @param status
     * @return
     */
    private String withdrawToFirst(String processFlag, String status) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 获取人员角色
        List<String> roleList = sysUserRoleMapper.getRoleCodeByUserId(sysUser.getId());
        String canWithdrawToFirst = "0";
        if ("1".equals(processFlag) && "2".equals(status)) {
            if (roleList.contains("fh_cfzg")) {
                canWithdrawToFirst = "fh_cfzg";
            } else if (roleList.contains("fhbm_cfglg")) {
                canWithdrawToFirst = "fhbm_cfglg";
            }
        } else if ("2".equals(processFlag) && "4".equals(status)) {
            if (roleList.contains("zhgbm_cfglh")) {
                canWithdrawToFirst = "zhgbm_cfglh";
            }
        } else if ("3".equals(processFlag) && "5".equals(status)) {
            if (roleList.contains("zh_ldc_glg")) {
                canWithdrawToFirst = "zh_ldc_glg";
            }
        } else if ("4".equals(processFlag) && "15".equals(status)) {
            if (roleList.contains("zgs_cfzg")) {
                canWithdrawToFirst = "zgs_cfzg";
            }
        } else if ("5".equals(processFlag) && "17".equals(status)) {// 村镇银行
            if (roleList.contains("czyh_cfzg")) {
                canWithdrawToFirst = "czyh_cfzg";
            }
        } else if ("6".equals(processFlag) && "14".equals(status)) {
            if (roleList.contains("zh_ldc_shg")) {
                canWithdrawToFirst = "zh_ldc_shg";
            }
        } else if ("7".equals(processFlag) && "3".equals(status)) {
            if (roleList.contains("fh_cfshg")) {
                canWithdrawToFirst = "fh_cfshg";
            }
        }
        return canWithdrawToFirst;
    }

    private String getStatusSecondStep(String processFlag) {
        String status = "1";
        if ("1".equals(processFlag)) {// 分行流程
            status = "2";
        } else if ("2".equals(processFlag)) {// 总行各部门
            status = "4";
        } else if ("3".equals(processFlag)) {// 总行ldc管理
            status = "5";
        } else if ("4".equals(processFlag)) {// 子公司流程
            status = "15";
        } else if ("5".equals(processFlag)) {// 村镇银行
            status = "17";
        } else if ("6".equals(processFlag)) {// 总行-LDC审核岗
            status = "14";
        } else if ("7".equals(processFlag)) {// 分行-操风审核岗
            status = "3";
        }
        return status;
    }

    /**
     * 设置单元格下拉列表
     * @param sheet 工作表对象
     * @param options 下拉选项数组
     * @param firstRow 起始行(0-based)
     * @param lastRow 结束行(0-based)
     * @param colIndex 列索引(0-based)
     */
    private static void setDropdown(Sheet sheet, String[] options,
                                    int firstRow, int lastRow, int colIndex) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createExplicitListConstraint(options);

        // 设置应用范围
        CellRangeAddressList addressList = new CellRangeAddressList(
                firstRow, lastRow, colIndex, colIndex);

        DataValidation validation = helper.createValidation(constraint, addressList);
        validation.setSuppressDropDownArrow(false); // 显示下拉箭头
        validation.setShowErrorBox(true); // 输入错误时显示提示

        sheet.addValidationData(validation);
    }
}
