package com.gientech.rule.assess.year.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.common.process.enitity.CommonProcess;
import com.gientech.common.process.service.ICommonProcessService;
import com.gientech.rule.assess.year.entity.RuleAssessYear;
import com.gientech.rule.assess.year.entity.RuleAssessYearSub;
import com.gientech.rule.assess.year.service.IRuleAssessYearService;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 年度评估任务管理
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-29
 */
@Tag(name = "年度评估任务管理")
@RestController
@RequestMapping("/rule/assess/year/task")
@Slf4j
public class RuleAssessYearController extends JeecgController<RuleAssessYear, IRuleAssessYearService> {

    @Autowired
    private IRuleAssessYearService ruleAssessYearService;

    @Autowired
    private IWorkflowInstanceService instanceService;

    @Autowired
    private IWorkflowTaskService workflowTaskService;

    @Autowired
    private ICommonProcessService processService;

    private final String businessKey = "ruleAssessYear";

    /**
     * 分页列表查询
     *
     * @param ruleAssessYear 实体参数
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @param req 请求参数
     * @return 查询结果
     */
    @GetMapping(value = "/list")
    @Operation(summary = "分页列表查询")
    public Result<IPage<RuleAssessYear>> queryPageList(RuleAssessYear ruleAssessYear,
                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                       HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("assessType", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("taskStatus", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("processStatus", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<RuleAssessYear> queryWrapper = QueryGenerator.initQueryWrapper(ruleAssessYear, req.getParameterMap(), customeRuleMap);
        Page<RuleAssessYear> page = new Page<RuleAssessYear>(pageNo, pageSize);
        IPage<RuleAssessYear> pageList = ruleAssessYearService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 生成任务编号
     *
     * @return 任务编号
     */
    @GetMapping(value = "/generateTaskCode")
    @Operation(summary = "生成任务编号")
    public Result<String> generateTaskCode() {
        try {
            String taskCode = ruleAssessYearService.generateTaskCode();
            return Result.OK("任务编号", taskCode);
        } catch (Exception e) {
            log.error("生成任务编号失败", e);
            return Result.error("生成任务编号失败");
        }
    }

    /**
     * 添加
     *
     * @param ruleAssessYear 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/add")
    @AutoLog(value = "存量制度全面评估任务-添加")
    @RequiresPermissions("rule.assess.year:task:add")
    public Result<String> add(@RequestBody RuleAssessYear ruleAssessYear) {
        try {
            boolean success = ruleAssessYearService.addAssessTask(ruleAssessYear);
            if (success) {
                return Result.OK("添加成功！");
            } else {
                return Result.error("添加失败！");
            }
        } catch (Exception e) {
            log.error("添加评估任务失败", e);
            return Result.error("添加失败：" + e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param ruleAssessYear 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "编辑")
    @AutoLog(value = "存量制度全面评估任务-编辑")
    @RequiresPermissions("rule.assess.year:task:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody RuleAssessYear ruleAssessYear) {
        ruleAssessYearService.updateById(ruleAssessYear);
        return Result.OK("编辑成功!");
    }

    /**
     * 提交审核
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "提交审核")
    @Operation(summary = "提交审核")
    @PostMapping(value = "/submitRequest")
    @RequiresPermissions("rule.assess.year:task:submit")
    public Result<String> submitRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessYear> ruleAssessYearList = service.listByIds(idList);
        for (RuleAssessYear ruleAssessYear : ruleAssessYearList) {
            // 创建审核工作流
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, ruleAssessYear);

            instanceService.createWorkflowInstance(businessKey, ruleAssessYear.getId(), variables);
        }
        processService.saveProcessBatch(businessKey, idList, "提交审核");
        return Result.ok("提交审核成功!");
    }

    /**
     * 审核通过
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "审核通过")
    @Operation(summary = "审核通过")
    @PostMapping(value = "/passRequest")
    @RequiresPermissions("rule.assess.year:task:examine")
    public Result<String> passRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessYear> ruleAssessYearList = service.listByIds(idList);
        for (RuleAssessYear ruleAssessYear : ruleAssessYearList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, ruleAssessYear.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approve", true);
                executeVariables.put(businessKey, ruleAssessYear);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }
        processService.saveProcessBatch(businessKey, idList, "审核通过");
        return Result.ok("审核通过成功!");
    }

    /**
     * 审核退回
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "审核退回")
    @Operation(summary = "审核退回")
    @PostMapping(value = "/givebackRequest")
    @RequiresPermissions("rule.assess.year:task:examine")
    public Result<String> givebackRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessYear> ruleAssessYearList = service.listByIds(idList);
        for (RuleAssessYear ruleAssessYear : ruleAssessYearList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, ruleAssessYear.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approve", false);
                executeVariables.put(businessKey, ruleAssessYear);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }

        processService.saveProcessBatch(businessKey, idList, "审核退回");
        return Result.ok("审核退回成功!");
    }

    /**
     * 处理过程
     *
     * @param id 数据录入对象id
     * @return 处理过程列表
     */
    @AutoLog(value = "处理过程")
    @Operation(summary = "处理过程")
    @GetMapping(value = "/process")
//    @RequiresPermissions("rule.assess.year:task:process")
    public Result<IPage<CommonProcess>> process(@RequestParam(name = "id", required = true) String id) {
        // 借用分页的字典翻译
        IPage<CommonProcess> page = new Page<>();
        page.setRecords(processService.getProcess(businessKey, id));
        return Result.ok(page);
    }
    /**
     * 通过id删除
     *
     * @param id 实体对象主键
     * @return 是否成功
     */
    @Operation(summary = "通过id删除")
    @DeleteMapping(value = "/delete")
    @AutoLog(value = "存量制度全面评估任务-通过id删除")
    @RequiresPermissions("rule.assess.year:task:delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        ruleAssessYearService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 多个实体对象主键
     * @return 是否成功
     */
    @Operation(summary = "批量删除")
    @DeleteMapping(value = "/deleteBatch")
    @AutoLog(value = "存量制度全面评估任务-批量删除")
    @RequiresPermissions("rule.assess.year:task:deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.ruleAssessYearService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id 实体对象主键
     * @return 查询结果
     */
    @GetMapping(value = "/queryById")
    @Operation(summary = "通过id查询")
    public Result<RuleAssessYear> queryById(@RequestParam(name = "id", required = true) String id) {
        RuleAssessYear ruleAssessYear = ruleAssessYearService.getById(id);
        if (ruleAssessYear == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(ruleAssessYear);
    }

    /**
     * 督办评估任务
     *
     * @param ids 任务ID列表
     * @return 是否成功
     */
    @Operation(summary = "督办评估任务")
    @PostMapping(value = "/urgeTasks")
    @AutoLog(value = "年度评估任务-督办")
    @RequiresPermissions("rule.assess.year:task:urge")
    public Result<String> urgeTasks(@RequestParam List<String> ids) {
        try {
            boolean success = ruleAssessYearService.urgeTask(ids);
            if (success) {
                return Result.OK("督办成功！");
            } else {
                return Result.error("督办失败！");
            }
        } catch (Exception e) {
            log.error("督办任务失败", e);
            return Result.error("督办失败：" + e.getMessage());
        }
    }
}
