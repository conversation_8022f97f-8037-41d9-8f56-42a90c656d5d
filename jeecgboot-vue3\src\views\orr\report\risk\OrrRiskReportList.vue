<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam"
              :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="8">
            <a-form-item name="reportName">
              <template #label><span title="报告名称">报告名称</span></template>
              <JInput placeholder="请输入报告名称" v-model:value="queryParam.reportName"/>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item name="reportStatus">
              <template #label><span title="报告状态">报告状态</span></template>
              <j-select-multiple placeholder="请选择报告状态"
                                 v-model:value="queryParam.reportStatus"
                                 dictCode="orr_report_status" allow-clear/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset"
                          style="margin-left: 8px">重置</a-button>
                <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <Icon
                      :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'"/>
                </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="''" @click="handleExamine"
                  preIcon="ant-design:check-outlined"> 提交审核
        </a-button>
        <a-button type="primary" v-auth="''" @click="handleRevoke"
                  preIcon="ant-design:rollback-outlined"> 撤销
        </a-button>
        <a-button type="primary" v-auth="'orr.report.risk:orr_risk_report:add'" @click="handleAdd"
                  preIcon="ant-design:plus-outlined"> 上传
        </a-button>
        <a-button type="primary" v-auth="'orr.report.risk:orr_risk_report:exportXls'"
                  preIcon="ant-design:export-outlined" @click="handleExportXls"> 导出
        </a-button>
        <a-button type="primary" v-auth="'orr.report.risk:orr_risk_report:importExcel'"
                  preIcon="ant-design:import-outlined" @click="toDownloadTemplate"> 下载模板
        </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"/>
      </template>
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <OrrRiskReportModal ref="registerModal" @success="handleSuccess"></OrrRiskReportModal>
    <ProcessModal ref="processRef" :getProcess="getProcess"/>
  </div>
</template>

<script lang="ts" name="orr.report.risk-orrRiskReport" setup>
import {ref, reactive} from 'vue';
import {BasicTable, TableAction} from '/@/components/Table';
import {useListPage} from '/@/hooks/system/useListPage';
import {columns} from './OrrRiskReport.data';
import {list, batchSubmit, batchRevoke, batchQueryById, downloadTemplate, getProcess} from './OrrRiskReport.api';
import OrrRiskReportModal from './components/OrrRiskReportModal.vue'
import {useUserStore} from '/@/store/modules/user';
import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
import JInput from "/@/components/Form/src/jeecg/components/JInput.vue";
import {useMessage} from "@/hooks/web/useMessage";
import {exportToWord} from "@/utils/wordUtils";
import {useGo} from "@/hooks/web/usePage";
import ProcessModal from "@/views/kri/input/components/ProcessModal.vue";

const formRef = ref();
const processRef = ref();
const queryParam = reactive<any>({});
const toggleSearchStatus = ref<boolean>(false);
const registerModal = ref();
const userStore = useUserStore();
const {createMessage} = useMessage();
const go = useGo();
//注册table数据
const {prefixCls, tableContext} = useListPage({
  tableProps: {
    title: '操作风险报告-重大操作风险事件管理',
    api: list,
    columns,
    canResize: false,
    useSearchForm: false,
    actionColumn: {
      width: 180,
      fixed: 'right',
    },
    defSort: {
      column: 'eventDiscoveryDate',
      order: 'desc'
    },
    beforeFetch: async (params) => {
      return Object.assign(params, queryParam);
    },
  },
});
const [registerTable, {
  reload,
  collapseAll,
  updateTableDataRecord,
  findTableDataRecord,
  getDataSource
}, {rowSelection, selectedRowKeys, selectedRows}] = tableContext;
const labelCol = reactive({
  xs: 8,
  sm: 8,
  xl: 8,
  xxl: 6
});
const wrapperCol = reactive({
  xs: 16,
  sm: 16,
});

/**
 * 提交审核
 */
async function handleExamine() {

  let rowKeys = selectedRowKeys.value;

  if (rowKeys.length <= 0) {
    createMessage.warn("请选择报告！");
    return;
  }

  const hasState1Or4 = selectedRows.value.some(item => item.reportStatus != '1' && item.reportStatus != '4');
  if (hasState1Or4) {
    createMessage.warn("仅支持对状态为“草稿”和“审核退回”的报告进行提交审核操作！");
    return;
  }

  await batchSubmit({ids: rowKeys})
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          handleSuccess();
        } else {
          createMessage.warn(res.message);
        }
      })
      .finally(() => {

      })

}

/**
 * 撤销
 */
async function handleRevoke() {

  let rowKeys = selectedRowKeys.value;

  if (rowKeys.length <= 0) {
    createMessage.warn("请选择报告！");
    return;
  }

  const hasState2Or3 = selectedRows.value.some(item => item.reportStatus != '2' && item.reportStatus != '3');
  if (hasState2Or3) {
    createMessage.warn("仅支持对状态为“审核中”和“审核通过”的报告进行撤销操作！");
    return;
  }

  await batchRevoke({ids: rowKeys})
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          handleSuccess();
        } else {
          createMessage.warn(res.message);
        }
      })
      .finally(() => {

      })

}

/**
 * 新增事件
 */
function handleAdd() {
  registerModal.value.disableSubmit = false;
  registerModal.value.add();
}

/**
 * 导出
 */
async function handleExportXls() {

  let rowKeys = selectedRowKeys.value;

  if (rowKeys.length <= 0) {
    createMessage.warn("请选择报告！");
    return;
  }

  // 获取数据并下载
  await batchQueryById({ids: rowKeys})
      .then((res) => {
        if (res.success) {
          const rows = res.result;
          rows.forEach((item) => {
            exportToWord(item.reportName, item.reportHtml);
          })
        }
      })
      .finally(() => {

      })
}

/**
 * 下载模板
 */
async function toDownloadTemplate() {

  // 获取模板并下载
  await downloadTemplate()
      .then((res) => {
        if (res.success) {
          const result = res.result;
          exportToWord(result.reportName, result.reportHtml)
        }
      })
      .finally(() => {

      })
}

/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {

  if (record.reportStatus != '1') {
    createMessage.warn("仅支持对状态为“草稿”的报告进行编辑操作！");
    return;
  }

  go({
    path: '/report/RiskEdit',
    query: {id: record.id}
  });
}

/**
 * 查看
 */
function handleDetail(record: Recordable) {
  go({
    path: '/report/RiskDetail',
    query: {id: record.id}
  });
}

/**
 * 处理过程
 */
function handleProcess(record: Recordable) {
  processRef.value.handleOpen(record.id);
}

/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '查看',
      onClick: handleDetail.bind(null, record),
    },
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      auth: 'orr.report.risk:orr_risk_report:edit'
    },
    {
      label: '处理过程',
      onClick: handleProcess.bind(null, record),
    },
  ];
}

/**
 * 查询
 */
function searchQuery() {
  reload();
}

/**
 * 重置
 */
function searchReset() {
  formRef.value.resetFields();
  selectedRowKeys.value = [];
  //刷新数据
  reload();
}


</script>

<style lang="less" scoped>
.jeecg-basic-table-form-container {
  padding: 0;

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }

  .query-group-cust {
    min-width: 100px !important;
  }

  .query-group-split-cust {
    width: 30px;
    display: inline-block;
    text-align: center
  }

  .ant-form-item:not(.ant-form-item-with-help) {
    margin-bottom: 16px;
    height: 32px;
  }

  :deep(.ant-picker), :deep(.ant-input-number) {
    width: 100%;
  }
}
</style>
