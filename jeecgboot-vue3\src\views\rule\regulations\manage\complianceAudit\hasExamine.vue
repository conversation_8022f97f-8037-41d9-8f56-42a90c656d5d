<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="6">
            <a-form-item name="taskNum">
              <template #label><span title="维护任务编号">维护任务</span></template>
              <a-input placeholder="请输入维护任务编号" v-model:value="queryParam.taskNum" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-form-item name="taskType">
              <template #label><span title="维护任务类型">维护任务</span></template>
              <j-select-multiple
                placeholder="请选择维护任务类型"
                v-model:value="queryParam.taskType"
                dictCode="rule_system_maintenance_tasks_status"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :lg="6">
              <a-form-item name="taskStatus">
                <template #label><span title="维护任务状态">维护任务</span></template>
                <a-input placeholder="请输入维护任务状态" v-model:value="queryParam.taskStatus" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="systemIssuingBody">
                <template #label><span title="制度发文机构">制度发文</span></template>
                <j-select-dept placeholder="请选择制度发文机构" v-model:value="queryParam.systemIssuingBody" checkStrictly allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="issuingDeptOne">
                <template #label><span title="制度发文部门（一级）">制度发文</span></template>
                <j-select-dept placeholder="请选择制度发文部门（一级）" v-model:value="queryParam.issuingDeptOne" checkStrictly allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="issuingDeptTwo">
                <template #label><span title="制度发文部门（二级）">制度发文</span></template>
                <j-select-dept placeholder="请选择制度发文部门（二级）" v-model:value="queryParam.issuingDeptTwo" checkStrictly allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="systemManager">
                <template #label><span title="制度管理人">制度管理</span></template>
                <a-input placeholder="请输入制度管理人" v-model:value="queryParam.systemManager" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="abolitionSystem">
                <template #label><span title="是否同步废止相关制度">是否同步</span></template>
                <j-select-multiple
                  placeholder="请选择是否同步废止相关制度"
                  v-model:value="queryParam.abolitionSystem"
                  dictCode="kri_alert_is_launch_assess"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="abolitionName">
                <template #label><span title="待废止制度名称">待废止制</span></template>
                <a-input placeholder="请输入待废止制度名称" v-model:value="queryParam.abolitionName" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="consumerRights">
                <template #label><span title="是否涉及消费者权益保护">是否涉及</span></template>
                <j-select-multiple
                  placeholder="请选择是否涉及消费者权益保护"
                  v-model:value="queryParam.consumerRights"
                  dictCode="kri_alert_is_launch_assess"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="whtherDraw">
                <template #label><span title="是否需要绘制流程图">是否需要</span></template>
                <j-select-multiple
                  placeholder="请选择是否需要绘制流程图"
                  v-model:value="queryParam.whtherDraw"
                  dictCode="kri_alert_is_launch_assess"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="documentNumber">
                <template #label><span title="制度文号">制度文号</span></template>
                <a-input placeholder="请输入制度文号" v-model:value="queryParam.documentNumber" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="version">
                <template #label><span title="制度版本号">制度版本</span></template>
                <a-input placeholder="请输入制度版本号" v-model:value="queryParam.version" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="systemName">
                <template #label><span title="制度名称">制度名称</span></template>
                <a-input placeholder="请输入制度名称" v-model:value="queryParam.systemName" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="releaseDate">
                <template #label><span title="发布日期">发布日期</span></template>
                <a-date-picker valueFormat="YYYY-MM-DD" placeholder="请选择发布日期" v-model:value="queryParam.releaseDate" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="implementationDate">
                <template #label><span title="实施日期">实施日期</span></template>
                <a-date-picker valueFormat="YYYY-MM-DD" placeholder="请选择实施日期" v-model:value="queryParam.implementationDate" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="systemLevel">
                <template #label><span title="制度层级">制度层级</span></template>
                <j-select-multiple
                  placeholder="请选择制度层级"
                  v-model:value="queryParam.systemLevel"
                  dictCode="rule_system_maintenance_system_level"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="scopeApplication">
                <template #label><span title="适用范围">适用范围</span></template>
                <j-select-multiple
                  placeholder="请选择适用范围"
                  v-model:value="queryParam.scopeApplication"
                  dictCode="rule_system_maintenance_scope_application"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="systemSecrecy">
                <template #label><span title="制度密级">制度密级</span></template>
                <j-select-multiple
                  placeholder="请选择制度密级"
                  v-model:value="queryParam.systemSecrecy"
                  dictCode="rule_system_maintenance_system_secrecy"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template #bodyCell="{ column, record, index, text }">
        <template v-if="column.dataIndex === 'qpprovalProof'">
          <!--文件字段回显插槽-->
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <a-button v-else :ghost="true" type="primary" preIcon="ant-design:download-outlined" size="small" @click="downloadFile(text)"
            >下载</a-button
          >
        </template>
        <template v-if="column.dataIndex === 'dliberativeFile'">
          <!--文件字段回显插槽-->
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <a-button v-else :ghost="true" type="primary" preIcon="ant-design:download-outlined" size="small" @click="downloadFile(text)"
            >下载</a-button
          >
        </template>
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <RuleSystemReformedAndAbolishedModal ref="registerModal" @success="handleSuccess" />
    <ProcessModal ref="processRef" :getProcess="getProcess" />
  </div>
</template>

<script lang="ts" name="rule.manage.reformedAndAbolished-ruleSystemReformedAndAbolished" setup>
  import { ref, reactive } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useUserStore } from '/@/store/modules/user';
  import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
  import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { columns } from './complianceList.data';
  import { examineList, getProcess } from '@/views/rule/regulations/manage/reformedAndAbolished/RuleSystemReformedAndAbolished.api';
  import RuleSystemReformedAndAbolishedModal from '@/views/rule/regulations/manage/reformedAndAbolished/components/RuleSystemReformedAndAbolishedModal.vue';
  import ProcessModal from '@/views/kri/input/components/ProcessModal.vue';
  import { useMessage } from '@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const processRef = ref();
  const userStore = useUserStore();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      api: examineList,
      columns,
      canResize: false,
      useSearchForm: false,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: async (params) => {
        params['isComplete'] = true;
        params['processStatus'] = ['5', '6', '7'];
        return Object.assign(params, queryParam);
      },
    },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] =
    tableContext;
  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '查看',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '处理过程',
        onClick: handleProcess.bind(null, record),
      },
    ];
  }
  /**
   * 处理过程
   * @param record
   */
  function handleProcess(record: Recordable) {
    processRef.value.handleOpen(record.id);
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }
  defineExpose({
    reload,
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
    .ant-form-item:not(.ant-form-item-with-help) {
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),
    :deep(.ant-input-number) {
      width: 100%;
    }
  }
</style>
