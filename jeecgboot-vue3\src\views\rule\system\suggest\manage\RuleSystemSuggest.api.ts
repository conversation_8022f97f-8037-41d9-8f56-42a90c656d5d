import { defHttp } from "/@/utils/http/axios";
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = "/rule/system/suggest/list",
  save = "/rule/system/suggest/add",
  edit = "/rule/system/suggest/edit",
  deleteOne = "/rule/system/suggest/delete",
  deleteBatch = "/rule/system/suggest/deleteBatch",
  importExcel = "/rule/system/suggest/importExcel",
  exportXls = "/rule/system/suggest/exportXls",

  suggestList = "/rule/system/suggest/suggestList",

  feedbackList = "/rule/system/suggest/feedbackList",
  feedback = "/rule/system/suggest/feedback",
  queryByRelateId = "/rule/system/suggest/queryByRelateId",

  submitRequest = '/rule/system/suggest/submitRequest',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 列表接口(建议子表)
 * @param params
 */
export const suggestList = (params) => defHttp.get({ url: Api.suggestList, params });

/**
 * 列表接口(建议子表)
 * @param params
 */
export const feedbackList = (params) => defHttp.get({ url: Api.feedbackList, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: "warning",
    title: "确认删除",
    content: "是否删除选中数据",
    okText: "确认",
    cancelText: "取消",
    onOk: () => {
      return defHttp.delete({
        url: Api.deleteBatch,
        data: params
      }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
};

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 反馈
 * @param params
 */
export const feedback = (params) => {
  return defHttp.post({ url: Api.feedback, params }, { isTransformResponse: false });
};

/**
 * 查询单条记录
 * @param relateId
 */
export const queryByRelateId = (relateId: string) => {
  let params = { relateId: relateId };
  return defHttp.get({ url: Api.queryByRelateId, params });
};

/**
 * 提交审核
 * @param params
 * @param handleSuccess
 */
export const submitRequest = (params: any, handleSuccess: Function) => {
  createConfirm({
    iconType: 'warning',
    title: '确认提交审核',
    content: '是否对选中数据提交审核?',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      return defHttp
        .post(
          {
            url: Api.submitRequest,
            data: params,
          },
          { joinParamsToUrl: true }
        )
        .then(() => {
          handleSuccess();
        });
    },
  });
};
