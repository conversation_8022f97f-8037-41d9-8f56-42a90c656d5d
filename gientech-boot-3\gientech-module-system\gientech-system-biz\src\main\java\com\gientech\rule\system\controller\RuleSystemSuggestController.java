package com.gientech.rule.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.common.process.enitity.CommonProcess;
import com.gientech.common.process.service.ICommonProcessService;
import com.gientech.rule.regulations.institutionSystemPlan.entity.RuleInstitutionSystemManagement;
import com.gientech.rule.system.entity.RuleSystemSuggest;
import com.gientech.rule.system.service.IRuleSystemSuggestService;
import com.gientech.rule.system.vo.RuleSystemSuggestFeedbackVO;
import com.gientech.rule.system.vo.RuleSystemSuggestVO;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.*;

/**
 * 内外规模块-制度建议表
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-16
 */
@Tag(name = "内外规模块-制度建议表")
@RestController
@RequestMapping("/rule/system/suggest")
@Slf4j
public class RuleSystemSuggestController extends JeecgController<RuleSystemSuggest, IRuleSystemSuggestService> {

    private IRuleSystemSuggestService ruleSystemSuggestService;

    @Autowired
    public void setRuleSystemSuggestService(IRuleSystemSuggestService ruleSystemSuggestService) {
        this.ruleSystemSuggestService = ruleSystemSuggestService;
    }

    @Autowired
    private IWorkflowInstanceService instanceService;

    @Autowired
    private IWorkflowTaskService workflowTaskService;

    @Autowired
    private ICommonProcessService processService;

    private final String businessKey = "ruleSystemSuggest";

    /**
     * 分页列表查询
     *
     * @param ruleSystemSuggestVO 实体参数
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @param req 请求参数
     * @return 查询结果
     */
    @GetMapping(value = "/list")
    @Operation(summary = "分页列表查询")
    public Result<IPage<RuleSystemSuggestVO>> queryPageList(RuleSystemSuggestVO ruleSystemSuggestVO,
                                                            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                            HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：IN
        customeRuleMap.put("systemIssuingBody", QueryRuleEnum.IN);
        customeRuleMap.put("issuingDeptOne", QueryRuleEnum.IN);
        customeRuleMap.put("issuingDeptTwo", QueryRuleEnum.IN);
        QueryWrapper<RuleSystemSuggestVO> queryWrapper = QueryGenerator.initQueryWrapper(ruleSystemSuggestVO, req.getParameterMap(), customeRuleMap);
        Page<RuleSystemSuggestVO> page = new Page<>(pageNo, pageSize);
        IPage<RuleSystemSuggestVO> pageList = ruleSystemSuggestService.selectVOPage(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 分页列表查询(建议子表)
     *
     * @param ruleSystemSuggest 实体参数
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @param req 请求参数
     * @return 查询结果
     */
    @GetMapping(value = "/suggestList")
    @Operation(summary = "分页列表查询")
    public Result<IPage<RuleSystemSuggest>> querySuggestList(RuleSystemSuggest ruleSystemSuggest,
                                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                             HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：IN
        customeRuleMap.put("suggestType", QueryRuleEnum.IN);
        QueryWrapper<RuleSystemSuggest> queryWrapper = QueryGenerator.initQueryWrapper(ruleSystemSuggest, req.getParameterMap(), customeRuleMap);
        Page<RuleSystemSuggest> page = new Page<>(pageNo, pageSize);
        IPage<RuleSystemSuggest> pageList = ruleSystemSuggestService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 分页列表查询(反馈)
     *
     * @param ruleSystemSuggest 实体参数
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @param req 请求参数
     * @return 查询结果
     */
    @GetMapping(value = "/feedbackList")
    @Operation(summary = "分页列表查询")
    public Result<IPage<RuleSystemSuggestFeedbackVO>> queryFeedbackList(RuleSystemSuggestFeedbackVO ruleSystemSuggest,
                                                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                                        HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：IN
        customeRuleMap.put("systemIssuingBody", QueryRuleEnum.IN);
        customeRuleMap.put("issuingDeptOne", QueryRuleEnum.IN);
        customeRuleMap.put("issuingDeptTwo", QueryRuleEnum.IN);
        QueryWrapper<RuleSystemSuggestFeedbackVO> queryWrapper = QueryGenerator.initQueryWrapper(ruleSystemSuggest, req.getParameterMap(), customeRuleMap);
        Page<RuleSystemSuggestFeedbackVO> page = new Page<>(pageNo, pageSize);
        IPage<RuleSystemSuggestFeedbackVO> pageList = ruleSystemSuggestService.selectFeedbackVOPage(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 根据制度主键查询建议信息
     *
     * @param relateId 制度主键
     * @return 建议信息
     */
    @GetMapping(value = "/queryByRelateId")
    @Operation(summary = "根据制度主键查询")
    public Result<IPage<RuleSystemSuggest>> querySuggestListByRelateId(@RequestParam(name = "relateId", required = true) String relateId) {
        List<RuleSystemSuggest> ruleSystemSuggests = service.selectByRelateId(relateId);
        Page<RuleSystemSuggest> page = new Page<>();
        page.setRecords(ruleSystemSuggests);
        return Result.OK(page);
    }

    /**
     * 根据制度主键查询建议信息数量
     *
     * @param systemId 制度主键
     * @return 建议信息
     */
    @GetMapping(value = "/countBySystemId")
    @Operation(summary = "根据制度主键查询建议信息数量")
    public Result<Integer> countByRelateId(@RequestParam(name = "systemId", required = true) String systemId) {
        // 获取建议数量
        QueryWrapper<RuleSystemSuggest> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("system_id", systemId);
        long count = service.count(queryWrapper);
        return Result.OK((int) count);
    }

    /**
     * 根据制度建议主键查询关联计划
     *
     * @param suggestId 制度建议主键
     * @return 关联计划
     */
    @GetMapping(value = "/querySMList")
    @Operation(summary = "根据制度建议主键查询关联计划")
    public Result<IPage<RuleInstitutionSystemManagement>> queryInstitutionSystemManagementList(@RequestParam(name = "suggestId", required = true) String suggestId) {
        List<RuleInstitutionSystemManagement> ruleInstitutionSystemManagements = service.selectSystemManagementBySuggestId(suggestId);
        Page<RuleInstitutionSystemManagement> page = new Page<>();
        page.setRecords(ruleInstitutionSystemManagements);
        return Result.OK(page);
    }

    /**
     * 添加
     *
     * @param ruleSystemSuggestVO 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/add")
    @AutoLog(value = "内外规模块-制度建议表-添加")
    @RequiresPermissions("rule.system:rule_system_suggest:add")
    public Result<String> add(@RequestBody RuleSystemSuggestVO ruleSystemSuggestVO) {
        ruleSystemSuggestService.saveVO(ruleSystemSuggestVO);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param ruleSystemSuggestVO 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "编辑")
    @AutoLog(value = "内外规模块-制度建议表-编辑")
    @RequiresPermissions("rule.system:rule_system_suggest:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody RuleSystemSuggestVO ruleSystemSuggestVO) {
        if (ruleSystemSuggestService.updateVO(ruleSystemSuggestVO)) {
            return Result.OK("保存成功!");
        } else {
            return Result.error("保存失败!");
        }
    }

    /**
     * 反馈
     *
     * @param ruleSystemSuggestFeedbackVO 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "反馈")
    @AutoLog(value = "内外规模块-制度建议表-反馈")
    @RequiresPermissions("rule.system:rule_system_suggest:feedback")
    @RequestMapping(value = "/feedback", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> feedback(@RequestBody RuleSystemSuggestFeedbackVO ruleSystemSuggestFeedbackVO) {
        if (ruleSystemSuggestService.feedback(ruleSystemSuggestFeedbackVO)) {
            return Result.OK("反馈成功!");
        } else {
            return Result.error("反馈失败!");
        }
    }

    /**
     * 通过id删除
     *
     * @param id 实体对象主键
     * @return 是否成功
     */
    @Operation(summary = "通过id删除")
    @DeleteMapping(value = "/delete")
    @AutoLog(value = "内外规模块-制度建议表-通过id删除")
    @RequiresPermissions("rule.system:rule_system_suggest:delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        ruleSystemSuggestService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 多个实体对象主键
     * @return 是否成功
     */
    @Operation(summary = "批量删除")
    @DeleteMapping(value = "/deleteBatch")
    @AutoLog(value = "内外规模块-制度建议表-批量删除")
    @RequiresPermissions("rule.system:rule_system_suggest:deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.ruleSystemSuggestService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id 实体对象主键
     * @return 查询结果
     */
    @GetMapping(value = "/queryById")
    @Operation(summary = "通过id查询")
    public Result<RuleSystemSuggest> queryById(@RequestParam(name = "id", required = true) String id) {
        RuleSystemSuggest ruleSystemSuggest = ruleSystemSuggestService.getById(id);
        if (ruleSystemSuggest == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(ruleSystemSuggest);
    }

    /**
     * 导出excel
     *
     * @param request 请求参数
     * @param ruleSystemSuggest 实体表单参数
     */
    @RequestMapping(value = "/exportXls")
    @RequiresPermissions("rule.system:rule_system_suggest:exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RuleSystemSuggest ruleSystemSuggest) {
        return super.exportXls(request, ruleSystemSuggest, RuleSystemSuggest.class, "内外规模块-制度建议表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request 请求参数
     * @return 是否成功
     */
    @RequiresPermissions("rule.system:rule_system_suggest:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RuleSystemSuggest.class);
    }

    /**
     * 提交审核
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "提交审核")
    @Operation(summary = "提交审核")
    @PostMapping(value = "/submitRequest")
    @RequiresPermissions("rule.system:rule_system_suggest:submit")
    public Result<String> submitRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleSystemSuggest> ruleSystemSuggestList = new ArrayList<>();
        for (String relateId : idList) {
            List<RuleSystemSuggest> ruleSystemSuggests = ruleSystemSuggestService.selectByRelateId(relateId);
            ruleSystemSuggestList.addAll(ruleSystemSuggests);
        }
        for (RuleSystemSuggest ruleSystemSuggest : ruleSystemSuggestList) {
            // 创建审核工作流
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, ruleSystemSuggest);

            instanceService.createWorkflowInstance(businessKey, ruleSystemSuggest.getId(), variables);
        }
        processService.saveProcessBatch(businessKey, idList, "提交审核");
        return Result.ok("提交审核成功!");
    }

    /**
     * 审核通过
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "制度试运行评分审核-审核通过")
    @Operation(summary = "审核通过")
    @PostMapping(value = "/passRequest")
    @RequiresPermissions("rule.system:rule_system_suggest:examine")
    public Result<String> passRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleSystemSuggest> ruleSystemSuggestList = new ArrayList<>();
        for (String relateId : idList) {
            List<RuleSystemSuggest> ruleSystemSuggests = ruleSystemSuggestService.selectByRelateId(relateId);
            ruleSystemSuggestList.addAll(ruleSystemSuggests);
        }
        for (RuleSystemSuggest ruleSystemSuggest : ruleSystemSuggestList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, ruleSystemSuggest.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approve", true);
                executeVariables.put(businessKey, ruleSystemSuggest);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }
        processService.saveProcessBatch(businessKey, idList, "审核通过");
        return Result.ok("审核通过成功!");
    }

    /**
     * 审核退回
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "制度试运行评分审核-审核退回")
    @Operation(summary = "审核退回")
    @PostMapping(value = "/givebackRequest")
    @RequiresPermissions("rule.system:rule_system_suggest:examine")
    public Result<String> givebackRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleSystemSuggest> ruleSystemSuggestList = new ArrayList<>();
        for (String relateId : idList) {
            List<RuleSystemSuggest> ruleSystemSuggests = ruleSystemSuggestService.selectByRelateId(relateId);
            ruleSystemSuggestList.addAll(ruleSystemSuggests);
        }
        for (RuleSystemSuggest ruleSystemSuggest : ruleSystemSuggestList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, ruleSystemSuggest.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approve", false);
                executeVariables.put(businessKey, ruleSystemSuggest);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }
        processService.saveProcessBatch(businessKey, idList, "审核退回");
        return Result.ok("审核退回成功!");
    }

    /**
     * 处理过程
     *
     * @param id 子任务id
     * @return 处理过程列表
     */
    @AutoLog(value = "制度试运行评分审核-处理过程")
    @Operation(summary = "处理过程")
    @GetMapping(value = "/process")
//    @RequiresPermissions("rule.system:rule_system_suggest:process")
    public Result<IPage<CommonProcess>> process(@RequestParam(name = "id", required = true) String id) {
        // 借用分页的字典翻译
        IPage<CommonProcess> page = new Page<>();
        page.setRecords(processService.getProcess(businessKey, id));
        return Result.ok(page);
    }
}
