<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item name="systemName">
              <template #label>
                <span title="制度名称">制度名称</span>
              </template>
              <JInput placeholder="请输入" v-model:value="queryParam.systemName" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="systemIssuingBody">
              <template #label>
                <span title="制度发文机构">制度发文机构</span>
              </template>
              <j-select-dept placeholder="请选择" v-model:value="queryParam.systemIssuingBody"
                             checkStrictly allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="issuingDeptOne">
              <template #label>
                <span title="制度发文部门（一级）">制度发文部门（一级）</span>
              </template>
              <j-select-dept placeholder="请选择" v-model:value="queryParam.issuingDeptOne"
                             checkStrictly allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="issuingDeptTwo">
              <template #label>
                <span title="制度发文部门（二级）">制度发文部门（二级）</span>
              </template>
              <j-select-dept placeholder="请选择" v-model:value="queryParam.issuingDeptTwo"
                             checkStrictly allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8"/>
          <a-col :span="8">
            <span style="float: right; overflow: hidden" class="table-page-search-submitButtons">
                <a-button preIcon="ant-design:reload-outlined" @click="searchReset"
                          style="margin-left: 8px">重置</a-button>
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="reload"
                          style="margin-left: 8px">查询</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button
          type="primary"
          v-auth="'rule.assess:rule_assess_pilot_sub:examine'"
          :disabled="selectedRowKeys.length === 0"
          preIcon="ant-design:check-outlined"
          @click="handlePass"
        >
          通过
        </a-button>
        <a-button
          type="default"
          v-auth="'rule.assess:rule_assess_pilot_sub:examine'"
          :disabled="selectedRowKeys.length === 0"
          preIcon="ant-design:export-outlined"
          @click="handleGiveback"
        >
          退回
        </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <ProcessModal ref="processRef" :getProcess="getProcess" />
    <!--    详情页-->
    <RuleSystemSuggestDetail ref="detailModal"></RuleSystemSuggestDetail>
  </div>
</template>

<script lang="ts" name="rule.system-ruleSystemSuggest" setup>
import { ref, reactive } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import { systemColumns } from '../manage/RuleSystemSuggest.data';
import { list } from '../manage/RuleSystemSuggest.api';
import { useUserStore } from '/@/store/modules/user';
import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
import JInput from "@/components/Form/src/jeecg/components/JInput.vue";
import RuleSystemSuggestDetail
  from "@/views/rule/system/suggest/feedback/components/RuleSystemSuggestDetail.vue";
import {
  getProcess
} from "@/views/rule/assess/pilot/score/examine/RuleAssessPilotScoreExamine.api";
import ProcessModal from "@/views/kri/input/components/ProcessModal.vue";
import { useExamine } from "@/hooks/api/useExamine";

const formRef = ref();
const processRef = ref();
const queryParam = reactive<any>({});
const detailModal = ref();
const userStore = useUserStore();
const {passRequest, givebackRequest, getProcess} = useExamine("/rule/system/suggest")

//注册table数据
const { prefixCls, tableContext } = useListPage({
  tableProps: {
    title: '内外规模块-制度建议表',
    api: list,
    columns: systemColumns,
    canResize:false,
    useSearchForm: false,
    actionColumn: {
      width: 120,
      fixed: 'right',
    },
    tableSetting: {
      redo: false,
      size: false,
      setting: false
    },
    beforeFetch: async (params) => {
      return Object.assign(params, queryParam);
    },
  }
});
const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;
const labelCol = reactive({
  xs:24,
  sm:4,
  xl:6,
  xxl:8
});
const wrapperCol = reactive({
  xs: 24,
  sm: 16,
});

/**
 * 详情
 */
function handleDetail(record: Recordable) {
  detailModal.value.handleOpen(record);
}

/**
 * 审核通过
 */
function handlePass() {
  if (selectedRowKeys.value.length === 0) {
    createMessage.warning('请选择要审核的记录!');
    return;
  }
  const ids = selectedRowKeys.value.join(',');
  passRequest({ ids }, handleSuccess);
}

/**
 * 审核退回
 */
function handleGiveback() {
  if (selectedRowKeys.value.length === 0) {
    createMessage.warning('请选择要退回的记录!');
    return;
  }
  const ids = selectedRowKeys.value.join(',');
  givebackRequest({ ids }, handleSuccess);
}

/**
 * 处理过程
 */
function toProcess(record: Recordable) {
  processRef.value.handleOpen(record.id);
}
/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '查看',
      onClick: handleDetail.bind(null, record),
    },
    {
      label: '处理过程',
      onClick: toProcess.bind(null, record),
    },
  ];
}

/**
 * 查询
 */
function searchQuery() {
  reload();
}

/**
 * 重置
 */
function searchReset() {
  formRef.value.resetFields();
  selectedRowKeys.value = [];
  //刷新数据
  reload();
}

defineExpose({
  reload
})
</script>

<style lang="less" scoped>
.jeecg-basic-table-form-container {
  padding: 0;
  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }
  .query-group-cust{
    min-width: 100px !important;
  }
  .query-group-split-cust{
    width: 30px;
    display: inline-block;
    text-align: center
  }
  .ant-form-item:not(.ant-form-item-with-help){
    margin-bottom: 16px;
    height: 32px;
  }
  :deep(.ant-picker),:deep(.ant-input-number){
    width: 100%;
  }
}
</style>
