package com.gientech.rcsa.plan.controller;

import java.util.*;

import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import com.gientech.rcsa.plan.entity.RcsaPlanManage;
import com.gientech.rcsa.plan.service.IRcsaPlanManageService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: RCSA评估计划表
 * @Author: jeecg-boot
 * @Date:   2025-07-15
 * @Version: V1.0
 */
@Tag(name="RCSA评估计划表")
@RestController
@RequestMapping("/rcsa/plan/rcsaPlanManage")
@Slf4j
public class RcsaPlanManageController extends JeecgController<RcsaPlanManage, IRcsaPlanManageService> {
	@Autowired
	private IRcsaPlanManageService rcsaPlanManageService;
	 @Autowired
	 private IWorkflowInstanceService workflowInstanceService;
	 @Autowired
	 private IWorkflowTaskService workflowTaskService;
	 private final String businessKey = "rcsaPlanProcess";
	
	/**
	 * 分页列表查询
	 *
	 * @param rcsaPlanManage
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "RCSA评估计划表-分页列表查询")
	@Operation(summary="RCSA评估计划表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<RcsaPlanManage>> queryPageList(RcsaPlanManage rcsaPlanManage,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("planType", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("state", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<RcsaPlanManage> queryWrapper = QueryGenerator.initQueryWrapper(rcsaPlanManage, req.getParameterMap(),customeRuleMap);

//		List<String> idList = new ArrayList<>();
//		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
//		idList.addAll(
//				workflowTaskService.getWorkflowTaskBusinessIdList(businessKey, "zh_rcsashg", sysUser.getOrgCode(), false)
//		);
//		if (idList.size() > 0) {
//			queryWrapper.in("id", idList);
//		}

		Page<RcsaPlanManage> page = new Page<RcsaPlanManage>(pageNo, pageSize);
		IPage<RcsaPlanManage> pageList = rcsaPlanManageService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param rcsaPlanManage
	 * @return
	 */
	@AutoLog(value = "RCSA评估计划表-添加")
	@Operation(summary="RCSA评估计划表-添加")
	@RequiresPermissions("rcsa.plan:rcsa_plan_manage:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody RcsaPlanManage rcsaPlanManage) {
        try {
            rcsaPlanManageService.add(rcsaPlanManage);
            return Result.OK("新建成功！");
        } catch (Exception e) {
            log.error("RCSA评估计划新建异常！", e);
            return Result.error("新建异常！");
        }
	}
	
	/**
	 *  编辑
	 *
	 * @param rcsaPlanManage
	 * @return
	 */
	@AutoLog(value = "RCSA评估计划表-编辑")
	@Operation(summary="RCSA评估计划表-编辑")
	@RequiresPermissions("rcsa.plan:rcsa_plan_manage:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody RcsaPlanManage rcsaPlanManage) {
		rcsaPlanManageService.updateById(rcsaPlanManage);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "RCSA评估计划表-通过id删除")
	@Operation(summary="RCSA评估计划表-通过id删除")
	@RequiresPermissions("rcsa.plan:rcsa_plan_manage:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		rcsaPlanManageService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "RCSA评估计划表-批量删除")
	@Operation(summary="RCSA评估计划表-批量删除")
	@RequiresPermissions("rcsa.plan:rcsa_plan_manage:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.rcsaPlanManageService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "RCSA评估计划表-通过id查询")
	@Operation(summary="RCSA评估计划表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<RcsaPlanManage> queryById(@RequestParam(name="id",required=true) String id) {
		RcsaPlanManage rcsaPlanManage = rcsaPlanManageService.getById(id);
		if(rcsaPlanManage==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(rcsaPlanManage);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param rcsaPlanManage
    */
    @RequiresPermissions("rcsa.plan:rcsa_plan_manage:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RcsaPlanManage rcsaPlanManage) {
        return super.exportXls(request, rcsaPlanManage, RcsaPlanManage.class, "RCSA评估计划表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("rcsa.plan:rcsa_plan_manage:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RcsaPlanManage.class);
    }

	/**
	 * 提交
	 * @param planManage
	 * @return
	 */
	@AutoLog(value = "RCSA评估计划表-提交")
	@PostMapping(value = "/submit")
	@RequiresPermissions("rcsa.plan:rcsa_plan_manage:submit")
	public Result<String> submit(@RequestBody RcsaPlanManage planManage) {
//		try {
//			rcsaPlanManageService.submit(planManage);
//			return Result.ok("提交成功");
//		} catch (Exception e) {
//			log.error("RCSA评估计划提交异常！", e);
//			return Result.error("提交异常！");
//		}

		Map<String, Object> variables = new HashMap<>();
		variables.put(businessKey, planManage);
		workflowInstanceService.createWorkflowInstance(businessKey, planManage.getId(), variables);
		return Result.OK("提交审核成功!");
	}

	 /**
	  * 审核通过
	  * @param planManage
	  * @return
	  * @throws Exception
	  */
	 @AutoLog(value = "RCSA评估计划表-审核通过")
	 @PostMapping(value = "/auditPass")
	 @RequiresPermissions("rcsa.plan:rcsa_plan_manage:pass")
	 public Result<String> auditPass(@RequestBody RcsaPlanManage planManage) {
//		 try {
//			 rcsaPlanManageService.auditPass(planManage);
//			 return Result.ok("审核通过成功！");
//		 } catch (Exception e) {
//			 log.error("RCSA评估计划审核通过异常！", e);
//			 return Result.error("审核通过异常！");
//		 }
		 RcsaPlanManage plan = rcsaPlanManageService.getById(planManage.getId());
		 WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, plan.getId());
		 if (workflowTask != null) {
			 Map<String, Object> executeVariables = new HashMap<>();
			 executeVariables.put("approval", true);
			 executeVariables.put(businessKey, plan);
			 workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
		 }
//		 processService.saveProcess(businessKey, plan.getId(), "审核通过");
		 return Result.ok("审核通过成功!");

	 }

	 /**
	  * 审核退回
	  * @param planManage
	  * @return
	  * @throws Exception
	  */
	 @AutoLog(value = "RCSA评估计划表-审核退回")
	 @PostMapping(value = "/auditReject")
	 @RequiresPermissions("rcsa.plan:rcsa_plan_manage:reject")
	 public Result<String> auditReject(@RequestBody RcsaPlanManage planManage) {
//		 try {
//			 rcsaPlanManageService.auditReject(planManage);
//			 return Result.ok("审核退回成功！");
//		 } catch (Exception e) {
//			 log.error("RCSA评估计划审核退回异常！", e);
//			 return Result.error("审核退回异常！");
//		 }
		 RcsaPlanManage plan = rcsaPlanManageService.getById(planManage.getId());
		 WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, plan.getId());
		 if (workflowTask != null) {
			 Map<String, Object> executeVariables = new HashMap<>();
			 executeVariables.put("approval", false);
			 executeVariables.put(businessKey, plan);
			 workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
		 }
//		 processService.saveProcess(businessKey, plan.getId(), "审核退回");
		 return Result.ok("审核退回成功!");
	 }

}
