package com.gientech.rule.assess.pilot.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.common.process.enitity.CommonProcess;
import com.gientech.common.process.service.ICommonProcessService;
import com.gientech.rule.assess.pilot.entity.RuleAssessPilotSub;
import com.gientech.rule.assess.pilot.service.IRuleAssessPilotSubDetailService;
import com.gientech.rule.assess.pilot.service.IRuleAssessPilotSubService;
import com.gientech.rule.assess.pilot.vo.RuleAssessPilotSubVO;
import com.gientech.rule.assess.pilot.vo.RuleAssessScoreDetailVO;
import com.gientech.rule.assess.pilot.vo.RuleAssessScoreSubmitVO;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 内外规库管理-制度试运行评估任务-子任务
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-24
 */
@Tag(name = "内外规库管理-制度试运行评估任务-子任务")
@RestController
@RequestMapping("/rule/assess/pilot/score")
@Slf4j
public class RuleAssessPilotSubController extends JeecgController<RuleAssessPilotSub, IRuleAssessPilotSubService> {

    @Autowired
    private IRuleAssessPilotSubService ruleAssessPilotSubService;

    @Autowired
    private IRuleAssessPilotSubDetailService ruleAssessPilotSubDetailService;

    @Autowired
    private IWorkflowInstanceService instanceService;

    @Autowired
    private IWorkflowTaskService workflowTaskService;

    @Autowired
    private ICommonProcessService processService;

    private final String businessKey = "ruleAssessPilotSub";

    /**
     * 分页列表查询
     *
     * @param ruleAssessPilotSubVO 实体参数
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @param req 请求参数
     * @return 查询结果
     */
    @Operation(summary = "分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<RuleAssessPilotSubVO>> queryPageList(RuleAssessPilotSubVO ruleAssessPilotSubVO,
                                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                             HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：IN
        customeRuleMap.put("assessOrgCode", QueryRuleEnum.IN);
        customeRuleMap.put("assessDeptCode", QueryRuleEnum.IN);
        customeRuleMap.put("taskStatus", QueryRuleEnum.IN);
        customeRuleMap.put("processStatus", QueryRuleEnum.IN);
        customeRuleMap.put("assessType", QueryRuleEnum.IN);
        customeRuleMap.put("mainTaskStatus", QueryRuleEnum.IN);
        customeRuleMap.put("mainProcessStatus", QueryRuleEnum.IN);
        QueryWrapper<RuleAssessPilotSubVO> queryWrapper = QueryGenerator.initQueryWrapper(ruleAssessPilotSubVO, req.getParameterMap(), customeRuleMap);
        Page<RuleAssessPilotSubVO> page = new Page<>(pageNo, pageSize);
        IPage<RuleAssessPilotSubVO> pageList = ruleAssessPilotSubService.selectVOPage(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param ruleAssessPilotSub 实体参数
     * @return 是否成功
     */
    @AutoLog(value = "内外规库管理-制度试运行评估任务-子任务-添加")
    @Operation(summary = "添加")
    @RequiresPermissions("rule.assess:rule_assess_pilot_sub:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody RuleAssessPilotSub ruleAssessPilotSub) {
        ruleAssessPilotSubService.save(ruleAssessPilotSub);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param ruleAssessPilotSub 实体参数
     * @return 是否成功
     */
    @AutoLog(value = "内外规库管理-制度试运行评估任务-子任务-编辑")
    @Operation(summary = "编辑")
    @RequiresPermissions("rule.assess:rule_assess_pilot_sub:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody RuleAssessPilotSub ruleAssessPilotSub) {
        ruleAssessPilotSubService.updateById(ruleAssessPilotSub);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id 实体对象主键
     * @return 是否成功
     */
    @AutoLog(value = "内外规库管理-制度试运行评估任务-子任务-通过id删除")
    @Operation(summary = "通过id删除")
    @RequiresPermissions("rule.assess:rule_assess_pilot_sub:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String id) {
        ruleAssessPilotSubService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 多个实体对象主键
     * @return 是否成功
     */
    @AutoLog(value = "内外规库管理-制度试运行评估任务-子任务-批量删除")
    @Operation(summary = "批量删除")
    @RequiresPermissions("rule.assess:rule_assess_pilot_sub:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.ruleAssessPilotSubService.removeByIds(java.util.Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id 实体对象主键
     * @return 查询结果
     */
    @GetMapping(value = "/queryById")
    @Operation(summary = "通过id查询")
    public Result<RuleAssessPilotSub> queryById(@RequestParam(name = "id") String id) {
        RuleAssessPilotSub ruleAssessPilotSub = ruleAssessPilotSubService.getById(id);
        if (ruleAssessPilotSub == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(ruleAssessPilotSub);
    }

    /**
     * 导出excel
     *
     * @param request 请求参数
     * @param ruleAssessPilotSub 实体参数
     */
    @RequiresPermissions("rule.assess:rule_assess_pilot_sub:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RuleAssessPilotSub ruleAssessPilotSub) {
        return super.exportXls(request, ruleAssessPilotSub, RuleAssessPilotSub.class, "内外规库管理-制度试运行评估任务-子任务");
    }

    /**
     * 通过excel导入数据
     *
     * @param request 请求参数
     * @param response 响应参数
     * @return 是否成功
     */
    @RequiresPermissions("rule.assess:rule_assess_pilot_sub:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RuleAssessPilotSub.class);
    }

    // ========== 制度评分相关接口 ==========

    /**
     * 获取制度评分详情
     *
     * @param subId 子任务主键
     * @return 评分详情
     */
    @GetMapping(value = "/scoreDetail")
    @Operation(summary = "获取制度评分详情")
    public Result<RuleAssessScoreDetailVO> getScoreDetail(@RequestParam(name = "subId") String subId) {
        try {
            RuleAssessScoreDetailVO scoreDetail = ruleAssessPilotSubDetailService.getScoreDetail(subId);
            return Result.OK(scoreDetail);
        } catch (Exception e) {
            log.error("获取制度评分详情失败", e);
            return Result.error("获取制度评分详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取文本映射配置
     *
     * @return 文本映射配置
     */
    @GetMapping(value = "/textMappings")
    @Operation(summary = "获取文本映射配置")
    public Result<Map<String, Object>> getTextMappings() {
        try {
            Map<String, Object> mappings = ruleAssessPilotSubDetailService.getTextMappings();
            return Result.OK(mappings);
        } catch (Exception e) {
            log.error("获取文本映射配置失败", e);
            return Result.error("获取文本映射配置失败：" + e.getMessage());
        }
    }

    /**
     * 提交制度评分结果
     *
     * @param submitVO 评分提交数据
     * @return 是否成功
     */
    @PostMapping(value = "/submitScore")
    @Operation(summary = "提交制度评分结果")
    @AutoLog(value = "内外规库管理-制度试运行评估任务-提交评分")
    public Result<String> submitScore(@RequestBody RuleAssessScoreSubmitVO submitVO) {
        try {
            boolean success = ruleAssessPilotSubDetailService.submitScore(submitVO);
            if (success) {
                // 获取子任务信息
                RuleAssessPilotSub subTask = ruleAssessPilotSubService.getById(submitVO.getSubId());
                if (subTask != null) {
                    // 创建工作流流程实例
                    Map<String, Object> variables = new HashMap<>();
                    variables.put(businessKey, subTask);
                    instanceService.createWorkflowInstance(businessKey, subTask.getId(), variables);

                    // 记录处理过程
                    processService.saveProcess(businessKey, subTask.getId(), "提交评分");
                }
                return Result.OK("评分提交成功！");
            } else {
                return Result.error("评分提交失败！");
            }
        } catch (Exception e) {
            log.error("提交制度评分失败", e);
            return Result.error("提交制度评分失败：" + e.getMessage());
        }
    }

    /**
     * 提交审核
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "提交审核")
    @Operation(summary = "提交审核")
    @PostMapping(value = "/submitRequest")
    @RequiresPermissions("rule.assess:rule_assess_pilot_sub:submit")
    public Result<String> submitRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessPilotSub> ruleAssessPilotSubList = service.listByIds(idList);
        for (RuleAssessPilotSub ruleAssessPilotSub : ruleAssessPilotSubList) {
            // 创建审核工作流
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, ruleAssessPilotSub);

            instanceService.createWorkflowInstance(businessKey, ruleAssessPilotSub.getId(), variables);
        }
        processService.saveProcessBatch(businessKey, idList, "提交审核");
        return Result.ok("提交审核成功!");
    }

    /**
     * 审核通过
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "制度试运行评分审核-审核通过")
    @Operation(summary = "审核通过")
    @PostMapping(value = "/passRequest")
    @RequiresPermissions("rule.assess:rule_assess_pilot_sub:examine")
    public Result<String> passRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessPilotSub> subList = ruleAssessPilotSubService.listByIds(idList);
        for (RuleAssessPilotSub sub : subList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, sub.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approve", true);
                executeVariables.put(businessKey, sub);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }
        processService.saveProcessBatch(businessKey, idList, "审核通过");
        return Result.ok("审核通过成功!");
    }

    /**
     * 审核退回
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "制度试运行评分审核-审核退回")
    @Operation(summary = "审核退回")
    @PostMapping(value = "/givebackRequest")
    @RequiresPermissions("rule.assess:rule_assess_pilot_sub:examine")
    public Result<String> givebackRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessPilotSub> subList = ruleAssessPilotSubService.listByIds(idList);
        for (RuleAssessPilotSub sub : subList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, sub.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approve", false);
                executeVariables.put(businessKey, sub);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }
        processService.saveProcessBatch(businessKey, idList, "审核退回");
        return Result.ok("审核退回成功!");
    }

    /**
     * 处理过程
     *
     * @param id 子任务id
     * @return 处理过程列表
     */
    @AutoLog(value = "制度试运行评分审核-处理过程")
    @Operation(summary = "处理过程")
    @GetMapping(value = "/process")
//    @RequiresPermissions("rule.assess:rule_assess_pilot_sub:process")
    public Result<IPage<CommonProcess>> process(@RequestParam(name = "id", required = true) String id) {
        // 借用分页的字典翻译
        IPage<CommonProcess> page = new Page<>();
        page.setRecords(processService.getProcess(businessKey, id));
        return Result.ok(page);
    }
}