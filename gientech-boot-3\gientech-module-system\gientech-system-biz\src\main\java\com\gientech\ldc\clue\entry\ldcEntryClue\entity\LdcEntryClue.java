package com.gientech.ldc.clue.entry.ldcEntryClue.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 入账线索表
 * @Author: jeecg-boot
 * @Date:   2025-04-24
 * @Version: V1.0
 */
@Data
@TableName("ldc_entry_clue")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="入账线索表")
public class LdcEntryClue implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
    /**数据标记*/
    @Excel(name = "数据标记", width = 15)
    @Schema(description = "数据标记")
    private java.lang.String dataFlag;
    /**来源系统*/
    @Excel(name = "来源系统", width = 15)
    @Schema(description = "来源系统")
    private java.lang.String sourceSystem;
	/**识别码*/
	@Excel(name = "识别码", width = 15)
    @Schema(description = "识别码")
    private java.lang.String identificationCode;
	/**机构*/
	@Excel(name = "机构", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Schema(description = "机构")
    private java.lang.String dept;
	/**部门*/
	@Excel(name = "部门", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Schema(description = "部门")
    private java.lang.String department;
	/**会计科目代码*/
	@Excel(name = "会计科目代码", width = 15)
    @Schema(description = "会计科目代码")
    private java.lang.String ledgerAccountCode;
	/**会计科目名称*/
	@Excel(name = "会计科目名称", width = 15)
    @Schema(description = "会计科目名称")
    private java.lang.String ledgerAccountName;
	/**记账币种*/
	@Excel(name = "记账币种", width = 15, dicCode = "currency")
    @Schema(description = "记账币种")
    @Dict(dicCode = "currency")
    private java.lang.String accountingCurrency;
	/**记账金额*/
	@Excel(name = "记账金额", width = 15)
    @Schema(description = "记账金额")
    private java.math.BigDecimal accountingAmt;
	/**入账日期*/
	@Excel(name = "入账日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "入账日期")
    private java.util.Date recordedDate;
	/**线索状态*/
    @Dict(dicCode = "ldc_entry_clue_status")
    @Schema(description = "线索状态")
    private java.lang.String clueStatus;
	/**线索接收机构*/
	@Excel(name = "线索接收机构", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Schema(description = "线索接收机构")
    private java.lang.String clueReceptionDept;
	/**数据获取日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "数据获取日期")
    private java.util.Date dateFetchDate;
	/**摘要*/
	@Excel(name = "摘要", width = 15)
    @Schema(description = "摘要")
    private java.lang.String summary;
	/**关联事件编号*/
    @Schema(description = "关联事件编号")
    private java.lang.String relevancyEventNumbers;
	/**无需关联事件原因*/
    @Schema(description = "无需关联事件原因")
    private java.lang.String noRelevancyEventReason;
	/**反馈人*/
    @Schema(description = "反馈人")
    private java.lang.String feedbackPeople;
	/**反馈日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "反馈日期")
    private java.util.Date feedbookDate;
	/**反馈人联系方式*/
    @Schema(description = "反馈人联系方式")
    private java.lang.String feedbookPeoplePhone;
	/**流程状态*/
    @Schema(description = "流程状态")
    private java.lang.String processStatus;
	/**原数据目标1自动派发2自动归档*/
    @Schema(description = "原数据目标1自动派发2自动归档")
    private java.lang.String rawDataTarget;
	/**反馈状态*/
    @Schema(description = "反馈状态")
    private java.lang.String feesbookStatus;
	/**审核状态*/
    @Schema(description = "审核状态")
    private java.lang.String authStatus;
	/**反馈机构*/
    @Schema(description = "反馈机构")
    private java.lang.String feedbookDept;
    /**审核意见*/
    @Schema(description = "审核意见")
    private java.lang.String reviewOpinions;
    /**审核意见类型*/
    @Schema(description = "审核意见类型")
    private java.lang.String reviewCommentsType;
    /**审核人*/
    @Schema(description = "审核人")
    private java.lang.String reviewPerson;
    /**审核日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "审核日期")
    private java.util.Date reviewDate;
    @TableField(exist = false)
    List<String> ids;
    @TableField(exist = false)
    private String pageFlag;
    @TableField(exist = false)
    private String removeIds;
    @TableField(exist = false)
    private String relIds;
    @TableField(exist = false)
    private String operateTypeName;
    @TableField(exist = false)
    private String detailHistoryId;
}
