import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/rule/assess/year/sub/list',

  submit = '/rule/assess/year/sub/submit',
  getByYearId = '/rule/assess/year/sub/getByYearId',
  queryById = '/rule/assess/year/sub/queryById',
  save = '/rule/assess/year/sub/add',
  edit = '/rule/assess/year/sub/edit',
  deleteOne = '/rule/assess/year/sub/delete',
  deleteBatch = '/rule/assess/year/sub/deleteBatch',
  importExcel = '/rule/assess/year/sub/importExcel',
  exportXls = '/rule/assess/year/sub/exportXls',

  submitRequest = '/rule/assess/year/sub/submitRequest',
  process = '/rule/assess/year/sub/process',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口（支持查询条件）
 * @param params 包含查询条件和分页参数的对象
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 提交子任务评估结果
 * @param subId 子任务ID
 */
export const submitSubTask = (subId: string) =>
  defHttp.post({
    url: Api.submit,
    params: { subId },
  });

/**
 * 根据主任务ID查询子任务列表
 * @param yearId 主任务ID
 */
export const getByYearId = (params: any) =>
  defHttp.get({
    url: Api.getByYearId,
    params,
  });

/**
 * 通过id查询
 * @param id 子任务ID
 */
export const queryById = (id: string) =>
  defHttp.get({
    url: Api.queryById,
    params: { id },
  });

/**
 * 提交审核
 * @param params
 * @param handleSuccess
 */
export const submitRequest = (params: any, handleSuccess: Function) => {
  createConfirm({
    iconType: 'warning',
    title: '确认提交审核',
    content: '是否对选中数据提交审核?',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      return defHttp
        .post(
          {
            url: Api.submitRequest,
            data: params,
          },
          { joinParamsToUrl: true }
        )
        .then(() => {
          handleSuccess();
        });
    },
  });
};

/**
 * 处理过程
 * @param params
 */
export const getProcess = (params: any) => {
  return defHttp.get({ url: Api.process, params }, { isTransformResponse: false, joinParamsToUrl: true });
};
