package com.gientech.rule.assess.trigger.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.common.process.enitity.CommonProcess;
import com.gientech.common.process.service.ICommonProcessService;
import com.gientech.rule.assess.trigger.entity.RuleAssessTrigger;
import com.gientech.rule.assess.trigger.service.IRuleAssessTriggerService;
import com.gientech.rule.assess.trigger.vo.RuleAssessTriggerVO;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 触发式评估任务
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-31
 */
@Tag(name = "触发式评估任务")
@RestController
@RequestMapping("/rule/assess/trigger")
@Slf4j
public class RuleAssessTriggerController extends JeecgController<RuleAssessTrigger, IRuleAssessTriggerService> {

    private IRuleAssessTriggerService ruleAssessTriggerService;

    @Autowired
    public void setRuleAssessTriggerService(IRuleAssessTriggerService ruleAssessTriggerService) {
        this.ruleAssessTriggerService = ruleAssessTriggerService;
    }

    @Autowired
    private IWorkflowInstanceService instanceService;

    @Autowired
    private IWorkflowTaskService workflowTaskService;

    @Autowired
    private ICommonProcessService processService;

    private final String businessKey = "ruleAssessTrigger";

    /**
     * 分页列表查询
     *
     * @param ruleAssessTrigger 实体参数
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @param req 请求参数
     * @return 查询结果
     */
    @GetMapping(value = "/list")
    @Operation(summary = "分页列表查询")
    public Result<IPage<RuleAssessTrigger>> queryPageList(RuleAssessTrigger ruleAssessTrigger,
                                                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                          HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("assessType", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("triggerType", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("triggerReason", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("taskStatus", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("processStatus", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<RuleAssessTrigger> queryWrapper = QueryGenerator.initQueryWrapper(ruleAssessTrigger, req.getParameterMap(), customeRuleMap);
        Page<RuleAssessTrigger> page = new Page<RuleAssessTrigger>(pageNo, pageSize);
        IPage<RuleAssessTrigger> pageList = ruleAssessTriggerService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 分页列表查询VO（包含维护任务信息）
     *
     * @param ruleAssessTrigger 查询条件
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    @GetMapping(value = "/listVO")
    @Operation(summary = "分页列表查询VO")
    public Result<IPage<RuleAssessTriggerVO>> queryPageListVO(RuleAssessTrigger ruleAssessTrigger,
                                                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<RuleAssessTriggerVO> page = new Page<>(pageNo, pageSize);
        IPage<RuleAssessTriggerVO> pageList = ruleAssessTriggerService.queryPageVO(page, ruleAssessTrigger);
        return Result.OK(pageList);
    }

    /**
     * 生成任务编号
     *
     * @return 任务编号
     */
    @GetMapping(value = "/generateTaskCode")
    @Operation(summary = "生成任务编号")
    public Result<String> generateTaskCode() {
        try {
            String taskCode = ruleAssessTriggerService.generateTaskCode();
            return Result.OK("任务编号", taskCode);
        } catch (Exception e) {
            log.error("生成任务编号失败", e);
            return Result.error("生成任务编号失败");
        }
    }

    /**
     * 添加
     *
     * @param ruleAssessTrigger 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/add")
    @AutoLog(value = "触发式评估任务-添加")
    @RequiresPermissions("rule.assess.trigger:rule_assess_trigger:add")
    public Result<String> add(@RequestBody RuleAssessTrigger ruleAssessTrigger) {
        ruleAssessTriggerService.save(ruleAssessTrigger);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param ruleAssessTrigger 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "编辑")
    @AutoLog(value = "触发式评估任务-编辑")
    @RequiresPermissions("rule.assess.trigger:rule_assess_trigger:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody RuleAssessTrigger ruleAssessTrigger) {
        ruleAssessTriggerService.updateById(ruleAssessTrigger);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id 实体对象主键
     * @return 是否成功
     */
    @Operation(summary = "通过id删除")
    @DeleteMapping(value = "/delete")
    @AutoLog(value = "触发式评估任务-通过id删除")
    @RequiresPermissions("rule.assess.trigger:rule_assess_trigger:delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        ruleAssessTriggerService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 多个实体对象主键
     * @return 是否成功
     */
    @Operation(summary = "批量删除")
    @DeleteMapping(value = "/deleteBatch")
    @AutoLog(value = "触发式评估任务-批量删除")
    @RequiresPermissions("rule.assess.trigger:rule_assess_trigger:deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.ruleAssessTriggerService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id 实体对象主键
     * @return 查询结果
     */
    @GetMapping(value = "/queryById")
    @Operation(summary = "通过id查询")
    public Result<RuleAssessTrigger> queryById(@RequestParam(name = "id", required = true) String id) {
        RuleAssessTrigger ruleAssessTrigger = ruleAssessTriggerService.getById(id);
        if (ruleAssessTrigger == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(ruleAssessTrigger);
    }

    /**
     * 导出excel
     *
     * @param request 请求参数
     * @param ruleAssessTrigger 实体表单参数
     */
    @RequestMapping(value = "/exportXls")
    @RequiresPermissions("rule.assess.trigger:rule_assess_trigger:exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RuleAssessTrigger ruleAssessTrigger) {
        return super.exportXls(request, ruleAssessTrigger, RuleAssessTrigger.class, "触发式评估任务");
    }

    /**
     * 通过excel导入数据
     *
     * @param request 请求参数
     * @return 是否成功
     */
    @RequiresPermissions("rule.assess.trigger:rule_assess_trigger:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RuleAssessTrigger.class);
    }

    /**
     * 提交审核
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "提交审核")
    @Operation(summary = "提交审核")
    @PostMapping(value = "/submitRequest")
    @RequiresPermissions("rule.assess.trigger:rule_assess_trigger:submit")
    public Result<String> submitRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessTrigger> ruleAssessTriggerList = ruleAssessTriggerService.listByIds(idList);
        for (RuleAssessTrigger ruleAssessTrigger : ruleAssessTriggerList) {
            // 创建审核工作流
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, ruleAssessTrigger);

            instanceService.createWorkflowInstance(businessKey, ruleAssessTrigger.getId(), variables);
        }
        processService.saveProcessBatch(businessKey, idList, "提交审核");
        return Result.ok("提交审核成功!");
    }

    /**
     * 审核通过
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "制度试运行评分审核-审核通过")
    @Operation(summary = "审核通过")
    @PostMapping(value = "/passRequest")
    @RequiresPermissions("rule.assess.trigger:rule_assess_trigger:examine")
    public Result<String> passRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessTrigger> ruleAssessTriggerList = ruleAssessTriggerService.listByIds(idList);
        for (RuleAssessTrigger ruleAssessTrigger : ruleAssessTriggerList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, ruleAssessTrigger.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approve", true);
                executeVariables.put(businessKey, ruleAssessTrigger);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }
        processService.saveProcessBatch(businessKey, idList, "审核通过");
        return Result.ok("审核通过成功!");
    }

    /**
     * 审核退回
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "制度试运行评分审核-审核退回")
    @Operation(summary = "审核退回")
    @PostMapping(value = "/givebackRequest")
    @RequiresPermissions("rule.assess.trigger:rule_assess_trigger:examine")
    public Result<String> givebackRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessTrigger> ruleAssessTriggerList = ruleAssessTriggerService.listByIds(idList);
        for (RuleAssessTrigger ruleAssessTrigger : ruleAssessTriggerList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, ruleAssessTrigger.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approve", false);
                executeVariables.put(businessKey, ruleAssessTrigger);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }
        processService.saveProcessBatch(businessKey, idList, "审核退回");
        return Result.ok("审核退回成功!");
    }

    /**
     * 处理过程
     *
     * @param id 子任务id
     * @return 处理过程列表
     */
    @AutoLog(value = "制度试运行评分审核-处理过程")
    @Operation(summary = "处理过程")
    @GetMapping(value = "/process")
//    @RequiresPermissions("rule.assess.trigger:rule_assess_trigger:process")
    public Result<IPage<CommonProcess>> process(@RequestParam(name = "id", required = true) String id) {
        // 借用分页的字典翻译
        IPage<CommonProcess> page = new Page<>();
        page.setRecords(processService.getProcess(businessKey, id));
        return Result.ok(page);
    }
}
