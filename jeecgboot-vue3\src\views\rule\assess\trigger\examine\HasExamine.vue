<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol"
              :wrapper-col="wrapperCol">
        <a-row :gutter="128">
          <a-col :span="8">
            <a-form-item name="taskCode">
              <template #label>
                <span title="任务编号">任务编号</span>
              </template>
              <a-input placeholder="请输入" v-model:value="queryParam.taskCode" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="taskName">
              <template #label>
                <span title="任务名制度后评估任务名称">任务名制</span>
              </template>
              <a-input placeholder="请输入" v-model:value="queryParam.taskName" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="assessType">
              <template #label>
                <span title="制度后评估类型">制度后评</span>
              </template>
              <j-select-multiple placeholder="请选择" v-model:value="queryParam.assessType"
                                 dictCode="rule_assess_pilot_assess_type" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="triggerType">
              <template #label>
                <span title="任务触发类型">任务触发</span>
              </template>
              <j-select-multiple placeholder="请选择" v-model:value="queryParam.triggerType"
                                 dictCode="rule_assess_trigger_type" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="triggerReason">
              <template #label>
                <span title="触发原因">触发原因</span>
              </template>
              <j-select-multiple placeholder="请选择" v-model:value="queryParam.triggerReason"
                                 dictCode="rule_assess_trigger_reason" allow-clear />
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :span="8">
              <a-form-item name="endTime">
                <template #label>
                  <span title="评估任务结束时间">评估任务</span>
                </template>
                <a-date-picker valueFormat="YYYY-MM-DD" placeholder="请选择"
                               v-model:value="queryParam.endTime" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="initiateOrgCode">
                <template #label>
                  <span title="任务发起机构">任务发起</span>
                </template>
                <j-select-dept placeholder="请选择" v-model:value="queryParam.initiateOrgCode"
                               checkStrictly allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="initiateDeptCode">
                <template #label>
                  <span title="任务发起部门">任务发起</span>
                </template>
                <j-select-dept placeholder="请选择" v-model:value="queryParam.initiateDeptCode"
                               checkStrictly allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="assessOrgCode">
                <template #label>
                  <span title="评估机构">评估机构</span>
                </template>
                <j-select-dept placeholder="请选择" v-model:value="queryParam.assessOrgCode"
                               checkStrictly allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="assessDeptCode">
                <template #label>
                  <span title="评估部门">评估部门</span>
                </template>
                <j-select-dept placeholder="请选择" v-model:value="queryParam.assessDeptCode"
                               checkStrictly allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="taskStatus">
                <template #label>
                  <span title="任务状态">任务状态</span>
                </template>
                <j-select-multiple
                  placeholder="请选择"
                  v-model:value="queryParam.taskStatus"
                  dictCode="rule_assess_trigger_task_status"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="processStatus">
                <template #label>
                  <span title="流程状态">流程状态</span>
                </template>
                <j-select-multiple
                  placeholder="请选择"
                  v-model:value="queryParam.processStatus"
                  dictCode="rule_assess_trigger_process_status"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </template>
          <a-col :span="8">
            <span style="float: right; overflow: hidden" class="table-page-search-submitButtons">
              <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                {{ toggleSearchStatus ? "收起" : "展开" }}
                <Icon
                  :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
              </a>
              <a-button preIcon="ant-design:reload-outlined" @click="searchReset"
                        style="margin-left: 8px">重置</a-button>
              <a-button type="primary" preIcon="ant-design:search-outlined" @click="reload"
                        style="margin-left: 8px">查询</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <RuleAssessTriggerModal ref="registerModal" @success="handleSuccess" />
    <ProcessModal ref="processRef" :getProcess="getProcess" />
  </div>
</template>

<script lang="ts" name="rule.assess.trigger-ruleAssessTrigger" setup>
import { ref, reactive } from "vue";
import { BasicTable, TableAction } from "/@/components/Table";
import { useListPage } from "/@/hooks/system/useListPage";
import { columns } from "../RuleAssessTrigger.data";
import { listVO } from "../RuleAssessTrigger.api";
import RuleAssessTriggerModal from "../task/components/RuleAssessTriggerModal.vue";
// import { useUserStore } from '/@/store/modules/user';
import JSelectMultiple from "/@/components/Form/src/jeecg/components/JSelectMultiple.vue";
import JSelectDept from "/@/components/Form/src/jeecg/components/JSelectDept.vue";
import {
  getProcess
} from "@/views/rule/assess/pilot/score/examine/RuleAssessPilotScoreExamine.api";
import ProcessModal from "@/views/kri/input/components/ProcessModal.vue";
import { useExamine } from "@/hooks/api/useExamine";

const formRef = ref();
const processRef = ref();
const queryParam = reactive<any>({});
const toggleSearchStatus = ref<boolean>(false);
const registerModal = ref();
const { getProcess } = useExamine("/rule/assess/trigger");
// const userStore = useUserStore();

//注册table数据
const {
  tableContext: [registerTable, { reload }, { rowSelection, selectedRowKeys }]
} = useListPage({
  tableProps: {
    title: "触发式评估任务",
    api: listVO,
    columns,
    canResize: false,
    useSearchForm: false,
    actionColumn: {
      title: "操作",
      width: 120,
      fixed: "right"
    },
    tableSetting: {
      redo: false,
      size: false,
      setting: false
    },
    beforeFetch: async (params) => {
      return Object.assign(params, queryParam);
    }
  }
});
// const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;
const labelCol = reactive({
  xs: 24,
  sm: 4,
  xl: 6,
  xxl: 4
});
const wrapperCol = reactive({
  xs: 24,
  sm: 20
});

/**
 * 详情
 */
function handleDetail(record: Recordable) {
  registerModal.value.disableSubmit = true;
  registerModal.value.edit(record);
}

/**
 * 处理过程
 */
function toProcess(record: Recordable) {
  processRef.value.handleOpen(record.id);
}

/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: "查看",
      onClick: handleDetail.bind(null, record)
    },
    {
      label: "处理过程",
      onClick: toProcess.bind(null, record)
    }
  ];
}

/**
 * 查询
 */
function searchQuery() {
  reload();
}

/**
 * 重置
 */
function searchReset() {
  formRef.value.resetFields();
  selectedRowKeys.value = [];
  //刷新数据
  reload();
}
</script>

<style lang="less" scoped>
.jeecg-basic-table-form-container {
  padding: 0;

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }

  .query-group-cust {
    min-width: 100px !important;
  }

  .query-group-split-cust {
    width: 30px;
    display: inline-block;
    text-align: center;
  }

  .ant-form-item:not(.ant-form-item-with-help) {
    margin-bottom: 16px;
    height: 32px;
  }

  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
}
</style>
