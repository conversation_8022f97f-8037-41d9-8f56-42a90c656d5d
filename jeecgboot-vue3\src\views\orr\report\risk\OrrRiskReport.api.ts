import {defHttp} from '/@/utils/http/axios';
import {useMessage} from "/@/hooks/web/useMessage";

const {createConfirm} = useMessage();

enum Api {
    list = '/orr/report/risk/orrRiskReport/list',
    save = '/orr/report/risk/orrRiskReport/add',
    edit = '/orr/report/risk/orrRiskReport/edit',
    queryById = '/orr/report/risk/orrRiskReport/queryById',
    deleteOne = '/orr/report/risk/orrRiskReport/delete',
    deleteBatch = '/orr/report/risk/orrRiskReport/deleteBatch',
    batchSubmit = '/orr/report/risk/orrRiskReport/batchSubmit',
    batchRevoke = '/orr/report/risk/orrRiskReport/batchRevoke',
    passBatch = '/orr/report/risk/orrRiskReport/passBatch',
    returnBatch = '/orr/report/risk/orrRiskReport/returnBatch',
    importExcel = '/orr/report/risk/orrRiskReport/importExcel',
    exportXls = '/orr/report/risk/orrRiskReport/exportXls',
    batchQueryById = '/orr/report/risk/orrRiskReport/batchQueryById',
    downloadTemplate = '/orr/report/risk/orrRiskReport/downloadTemplate',
    process = '/orr/report/risk/orrRiskReport/process',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({url: Api.list, params});

/**
 * 审核接口
 * @param params
 */
export const processList = (params) => {
    if (params.reportStatus == undefined && params.processKey == 1) {
        params.reportStatus = "2,5";
    } else if (params.reportStatus == undefined && params.processKey == 2) {
        params.reportStatus = "1,3,4";
    }
    return defHttp.get({url: Api.list, params});
};

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
    return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
    });
}

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
    createConfirm({
        iconType: 'warning',
        title: '确认删除',
        content: '是否删除选中数据',
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
            return defHttp.delete({
                url: Api.deleteBatch,
                data: params
            }, {joinParamsToUrl: true}).then(() => {
                handleSuccess();
            });
        }
    });
}

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
    let url = isUpdate ? Api.edit : Api.save;
    return defHttp.post({url: url, params}, {isTransformResponse: false});
}

/**
 * 批量提交
 * @param params
 */
export const batchSubmit = (params) => {
    return defHttp.post({url: Api.batchSubmit, params}, {
        isTransformResponse: false,
        joinParamsToUrl: true
    });
}

/**
 * 批量撤销
 * @param params
 */
export const batchRevoke = (params) => {
    return defHttp.post({url: Api.batchRevoke, params}, {
        isTransformResponse: false,
        joinParamsToUrl: true
    });
}

/**
 * 批量通过
 * @param params
 */
export const passBatch = (params) => {
    return defHttp.post({url: Api.passBatch, params}, {
        isTransformResponse: false,
        joinParamsToUrl: true
    });
}

/**
 * 批量通过
 * @param params
 */
export const returnBatch = (params) => {
    return defHttp.post({url: Api.returnBatch, params}, {
        isTransformResponse: false,
        joinParamsToUrl: true
    });
}

/**
 * 通过id查询
 * @param params
 */
export const queryById = (params) => {
    return defHttp.get({url: Api.queryById, params}, {
        isTransformResponse: false,
        joinParamsToUrl: true
    });
};

/**
 * 通过id批量查询
 * @param params
 */
export const batchQueryById = (params) => defHttp.post({
    url: Api.batchQueryById,
    params
}, {joinParamsToUrl: true, isTransformResponse: false});

/**
 * 下载模板
 * @param params
 */
export const downloadTemplate = () => defHttp.get({
    url: Api.downloadTemplate
}, {joinParamsToUrl: true, isTransformResponse: false});

/**
 * 处理过程
 * @param params
 */
export const getProcess = (params: any) => {
  return defHttp.get({ url: Api.process, params }, {isTransformResponse:false, joinParamsToUrl: true });
};
