package com.gientech.rule.regulations.deptSystemPlan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gientech.rule.manage.reformedAndAbolished.entity.RuleSystemReformedAndAbolished;
import com.gientech.rule.manage.reformedAndAbolished.mapper.RuleSystemReformedAndAbolishedMapper;
import com.gientech.rule.manage.utils.GenerateTaskNum;
import com.gientech.rule.manage.utils.GetVersion;
import com.gientech.rule.regulations.deptSystemPlan.entity.RuleDeptSystemManagement;
import com.gientech.rule.regulations.deptSystemPlan.entity.RuleDeptSystemManagementDetail;
import com.gientech.rule.regulations.deptSystemPlan.entity.RuleDeptSystemManagementDetailHistory;
import com.gientech.rule.regulations.deptSystemPlan.entity.RuleDeptSystemManagementHistory;
import com.gientech.rule.regulations.deptSystemPlan.mapper.RuleDeptSystemManagementDetailHistoryMapper;
import com.gientech.rule.regulations.deptSystemPlan.mapper.RuleDeptSystemManagementDetailMapper;
import com.gientech.rule.regulations.deptSystemPlan.mapper.RuleDeptSystemManagementHistoryMapper;
import com.gientech.rule.regulations.deptSystemPlan.mapper.RuleDeptSystemManagementMapper;
import com.gientech.rule.regulations.deptSystemPlan.service.IRuleDeptSystemManagementService;
import com.gientech.rule.regulations.institutionSystemPlan.entity.RuleInstitutionSystemManagement;
import com.gientech.rule.regulations.institutionSystemPlan.mapper.RuleInstitutionSystemManagementMapper;
import com.gientech.rule.regulations.institutionSystemPlan.service.impl.GetPlanName;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.mapper.SysDepartMapper;
import org.jeecg.modules.system.mapper.SysDictMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 内外规-部门制度规划管理
 * @Author: jeecg-boot
 * @Date:   2025-07-07
 * @Version: V1.0
 */
@Service
public class RuleDeptSystemManagementServiceImpl extends ServiceImpl<RuleDeptSystemManagementMapper, RuleDeptSystemManagement> implements IRuleDeptSystemManagementService {
    @Autowired
    private RuleDeptSystemManagementMapper ruleDeptSystemManagementMapper;
    @Autowired
    private RuleInstitutionSystemManagementMapper ruleInstitutionSystemManagementMapper;
    @Autowired
    private RuleDeptSystemManagementDetailMapper ruleDeptSystemManagementDetailMapper;
    @Autowired
    private RuleDeptSystemManagementHistoryMapper ruleDeptSystemManagementHistoryMapper;
    @Autowired
    private RuleDeptSystemManagementDetailHistoryMapper ruleDeptSystemManagementDetailHistoryMapper;
    @Autowired
    private GetPlanName planName;
    @Autowired
    private SysDepartMapper sysDepartMapper;
    @Autowired
    private SysDictMapper sysDictMapper;
    @Autowired
    private RuleSystemReformedAndAbolishedMapper rrMapper;
    @Autowired
    private GenerateTaskNum generate;
    @Autowired
    private GetVersion version;

    @Override
    public String newsave(RuleDeptSystemManagement ruleDeptSystemManagement) {
        ruleDeptSystemManagement.setTempFlag("2");//临时保存状态1临时2非临时
        ruleDeptSystemManagement.setAssociateFlag("2");//机构默认关联部门关联状态1关联2未关联
        ruleDeptSystemManagement.setPlanStatus("1");
        ruleDeptSystemManagement.setProcessStatus("1");
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        ruleDeptSystemManagement.setPlanDept(sysUser.getOrgCode());
        boolean flag=this.save(ruleDeptSystemManagement);
        if(flag){
            return "保存成功!";
        }else{
            return "保存失败!";
        }
    }

    @Override
    public List<RuleDeptSystemManagement> querydeptSystemList(String relevancyNum) {
        QueryWrapper<RuleDeptSystemManagement> queryWrapper = new QueryWrapper<RuleDeptSystemManagement>();
        queryWrapper.eq("relevancy_num", relevancyNum);
        List<RuleDeptSystemManagement> list = ruleDeptSystemManagementMapper.selectList(queryWrapper);
        if(list != null && list.size() > 0){
            for(RuleDeptSystemManagement ruleDeptSystemManagement : list){
                String level=sysDictMapper.queryDictTextByKey("rule_institutional_system_hierarchy", ruleDeptSystemManagement.getPlanLevel());
                String type=sysDictMapper.queryDictTextByKey("rule_institutional_system_type", ruleDeptSystemManagement.getPlanType());
                ruleDeptSystemManagement.setLevelName(level);
                ruleDeptSystemManagement.setTypeName(type);
                LambdaQueryWrapper<SysDepart> deptWrapper = new LambdaQueryWrapper<>();
                deptWrapper.eq(SysDepart::getOrgCode,ruleDeptSystemManagement.getPlanDept());
                List<SysDepart> deptlist=sysDepartMapper.selectList(deptWrapper);
                if(deptlist.size()>0) {
                    ruleDeptSystemManagement.setDeptName(deptlist.get(0).getDepartName());
                }else{
                    ruleDeptSystemManagement.setDeptName(ruleDeptSystemManagement.getPlanDept());
                }
            }
        }
        return list;
    }

    @Override
    public String associate(String ids, String planNum) {
        List<String> list=Arrays.asList(ids.split(","));
        QueryWrapper<RuleInstitutionSystemManagement> queryWrapper = new QueryWrapper<RuleInstitutionSystemManagement>();
        queryWrapper.eq("plan_num", planNum);
        RuleInstitutionSystemManagement institution=ruleInstitutionSystemManagementMapper.selectOne(queryWrapper);
        int flag=1;
        for(String id:list){
            RuleDeptSystemManagement dept=ruleDeptSystemManagementMapper.selectById(id);
            dept.setAssociateFlag("1");
            dept.setRelevancyNum(planNum);
            dept.setRelevancyId(institution.getId());
            dept.setYear(String.valueOf(institution.getPlanYear().getYear()));
            dept.setPlanStartDate(institution.getPlanStartDate());
            dept.setPlanEndDate(institution.getPlanEndDate());
            dept.setPlanName(institution.getPlanName());
            String year=String.valueOf(institution.getPlanYear().getYear());
            int level=Integer.parseInt(institution.getPlanLevel())+1;
            String planname=planName.getPlanName(year,institution.getPlanType(),String.valueOf(level));
            dept.setPlanName(planname);
            flag=ruleDeptSystemManagementMapper.updateById(dept);
        }
        if(flag==1){
            return "关联成功!";
        }else{
            return "关联失败!";
        }
    }

    //废止
    @Override
    public void abolition(String ids) {
        String[] list= ids.split(",");
        for(String id:list){
            RuleDeptSystemManagement ruleDeptSystemManagement=this.getById(id);
            ruleDeptSystemManagement.setPlanStatus("4");
            this.updateById(ruleDeptSystemManagement);
        }
    }

    //变更
    @Override
    public String alter(RuleDeptSystemManagement ruleDeptSystemManagement) {
        ruleDeptSystemManagement.setPlanStatus("3");
        boolean flag=this.updateById(ruleDeptSystemManagement);
        //先更新主历史表明细
        RuleDeptSystemManagementHistory systemhistory=ConvertSystemDetail.convertToHistory(ruleDeptSystemManagement);
        systemhistory.setId("");
        systemhistory.setHistoryTime(LocalDate.now().toString());
        ruleDeptSystemManagementHistoryMapper.insert(systemhistory);
        //在更新子历史表明细
        QueryWrapper<RuleDeptSystemManagementDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sys_org_code", ruleDeptSystemManagement.getPlanDept());
        queryWrapper.eq("reational_num",ruleDeptSystemManagement.getRelevancyNum());
        queryWrapper.in("delete_status", Arrays.asList("1","2", "3", "4"));//删除状态1正常2待删除3待新增4待修改
        List<RuleDeptSystemManagementDetail> list=ruleDeptSystemManagementDetailMapper.selectList(queryWrapper);
        if(list != null && list.size() > 0){
            //将数据推送到历史表中，分类型进行，只有待新增数据不同推送到历史
            //删除状态1正常2待删除3待新增4待修改
            for(RuleDeptSystemManagementDetail old : list){
                if("1".equals(old.getDeleteStatus())||"2".equals(old.getDeleteStatus())){
                    RuleDeptSystemManagementDetailHistory history=ConvertSystemDetail.convertToHistory(old);
                    history.setId("");
                    history.setHistoryId(systemhistory.getId());
                    history.setHistoryTime(LocalDate.now().toString());
                    ruleDeptSystemManagementDetailHistoryMapper.insert(history);
                } else if ("4".equals(old.getDeleteStatus())) {
                    ruleDeptSystemManagementDetailHistoryMapper.deleteById(old.getId());
                    RuleDeptSystemManagementDetailHistory history=ConvertSystemDetail.convertToHistory(old);
                    history.setHistoryId(systemhistory.getId());
                    history.setId("");
                    history.setHistoryTime(LocalDate.now().toString());
                    history.setDeleteStatus("4");
                    ruleDeptSystemManagementDetailHistoryMapper.insert(history);
                }
                //将明细表中数据都置为正常状态
                old.setDeleteStatus("1");
                ruleDeptSystemManagementDetailMapper.updateById(old);
            }
        }
        return "变更成功";
    }

    @Override
    public void afterSuccess(RuleDeptSystemManagement ruleDeptSystemManagement) {
        //审核通过，则在相应部门生成制度立改废信息,并修改机构制度状态

        QueryWrapper<RuleDeptSystemManagementDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sys_org_code", ruleDeptSystemManagement.getPlanDept());
        queryWrapper.eq("reational_num",ruleDeptSystemManagement.getRelevancyNum());
        queryWrapper.in("delete_status", Arrays.asList("1"));//删除状态1正常2待删除3待新增4待修改
        List<RuleDeptSystemManagementDetail> list=ruleDeptSystemManagementDetailMapper.selectList(queryWrapper);
        //审核成功必然有任务子明明细
        if(list.size()>0){
            for(RuleDeptSystemManagementDetail detail :list){

                String num=generate.generateTaskNum();
                //生成立改废任务
                RuleSystemReformedAndAbolished rr=new RuleSystemReformedAndAbolished();
                rr.setTaskType(detail.getTaskType());//任务类型
                rr.setIssuingDeptOne(ruleDeptSystemManagement.getPlanDept());//
                rr.setSystemName(detail.getTaskName());//制度名称
                rr.setAssociatedPlanNumber(ruleDeptSystemManagement.getRelevancyNum());//关联计划编号
                rr.setAssociatedPlanId(detail.getId());//关联部门明细计划id
                String institution=generate.getParentDepartCode(ruleDeptSystemManagement.getPlanDept());
                rr.setSystemIssuingBody(institution);//制度发文机构
                rr.setTaskNum(num);
                String v=version.Version(rr);
                rr.setVersion(v);//版本号
                rr.setTaskStatus("1");
                rr.setNewRevisionCause(detail.getSafeguardReasons());
                rr.setProcessStatus("1");
                rr.setTimeliness("0");
                rrMapper.insert(rr);
                //更新任务明细状态、任务编号
                detail.setTaskStatus("2");//执行中
                detail.setReationalNum(num);
                detail.setSystemId(rr.getId());
                ruleDeptSystemManagementDetailMapper.updateById(detail);
            }
        }


    }


}
