import {defHttp} from '/@/utils/http/axios';
import {useMessage} from "/@/hooks/web/useMessage";

const {createConfirm} = useMessage();

enum Api {
    list = '/orr/clue/orrReportClue/list',
    save = '/orr/clue/orrReportClue/add',
    edit = '/orr/clue/orrReportClue/edit',
    deleteOne = '/orr/clue/orrReportClue/delete',
    deleteBatch = '/orr/clue/orrReportClue/deleteBatch',
    batchSubmit = '/orr/clue/orrReportClue/batchSubmit',
    batchRevoke = '/orr/clue/orrReportClue/batchRevoke',
    passBatch = '/orr/clue/orrReportClue/passBatch',
    returnBatch = '/orr/clue/orrReportClue/returnBatch',
    queryById = '/orr/clue/orrReportClue/queryById',
    queryByReportId = '/orr/clue/orrReportClue/queryByReportId',
    importExcel = '/orr/clue/orrReportClue/importExcel',
    exportXls = '/orr/clue/orrReportClue/exportXls',
    process = '/orr/clue/orrReportClue/process',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({url: Api.list, params});

/**
 * 列表接口
 * @param params
 */
export const processList = (params) => {
    if (params.clueStatus == undefined && params.processKey == 1) {
        params.clueStatus = "2,5";
    } else if (params.clueStatus == undefined && params.processKey == 2) {
        params.clueStatus = "1,3,4";
    }
    return defHttp.get({url: Api.list, params})
};

/**
 * 通过id查询
 * @param params
 */
export const queryById = (params) => defHttp.get({url: Api.queryById, params}, {
    isTransformResponse: false,
    joinParamsToUrl: true
});

/**
 * 通过reportId查询
 * @param params
 */
export const queryByReportId = (params) => defHttp.get({url: Api.queryByReportId, params}, {
    isTransformResponse: false,
    joinParamsToUrl: true
});

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
    return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
    });
}

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
    createConfirm({
        iconType: 'warning',
        title: '确认删除',
        content: '是否删除选中数据',
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
            return defHttp.delete({
                url: Api.deleteBatch,
                data: params
            }, {joinParamsToUrl: true}).then(() => {
                handleSuccess();
            });
        }
    });
}

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
    let url = isUpdate ? Api.edit : Api.save;
    return defHttp.post({url: url, params}, {isTransformResponse: false});
}

/**
 * 批量提交
 * @param params
 */
export const batchSubmit = (params) => {
    return defHttp.post({url: Api.batchSubmit, params}, {
        isTransformResponse: false,
        joinParamsToUrl: true
    });
}

/**
 * 批量撤销
 * @param params
 */
export const batchRevoke = (params) => {
    return defHttp.post({url: Api.batchRevoke, params}, {
        isTransformResponse: false,
        joinParamsToUrl: true
    });
}

/**
 * 批量通过
 * @param params
 */
export const passBatch = (params) => {
    return defHttp.post({url: Api.passBatch, params}, {
        isTransformResponse: false,
        joinParamsToUrl: true
    });
}

/**
 * 批量退回
 * @param params
 */
export const returnBatch = (params) => {
    return defHttp.post({url: Api.returnBatch, params}, {
        isTransformResponse: false,
        joinParamsToUrl: true
    });
}

/**
 * 处理过程
 * @param params
 */
export const getProcess = (params: any) => {
  return defHttp.get({ url: Api.process, params }, {isTransformResponse:false, joinParamsToUrl: true });
};
