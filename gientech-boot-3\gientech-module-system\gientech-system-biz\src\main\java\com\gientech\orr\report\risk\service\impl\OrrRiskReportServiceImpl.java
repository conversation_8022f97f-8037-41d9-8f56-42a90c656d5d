package com.gientech.orr.report.risk.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gientech.common.process.service.ICommonProcessService;
import com.gientech.kri.date.entity.Weekdays;
import com.gientech.kri.date.mapper.WeekdaysMapper;
import com.gientech.orr.report.risk.entity.OrrRiskReport;
import com.gientech.orr.report.risk.mapper.OrrRiskReportMapper;
import com.gientech.orr.report.risk.service.IOrrRiskReportService;
import com.gientech.orr.report.template.entity.OrrReportTemplate;
import com.gientech.orr.report.template.mapper.OrrReportTemplateMapper;
import com.gientech.orr.schedule.entity.OrrSchedule;
import com.gientech.orr.schedule.mapper.OrrScheduleMapper;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;

/**
 * @Description: 操作风险报告-重大操作风险事件管理
 * @Author: jeecg-boot
 * @Date:   2025-07-16
 * @Version: V1.0
 */
@Service
public class OrrRiskReportServiceImpl extends ServiceImpl<OrrRiskReportMapper, OrrRiskReport> implements IOrrRiskReportService {

    public static final String businessKey = "orrRiskReport";

    @Autowired
    private WeekdaysMapper weekdaysMapper;

    @Autowired
    private OrrScheduleMapper orrScheduleMapper;

    @Autowired
    private OrrRiskReportMapper orrRiskReportMapper;

    @Autowired
    private OrrReportTemplateMapper orrReportTemplateMapper;

    @Autowired
    private IWorkflowInstanceService instanceService;

    @Autowired
    private ICommonProcessService processService;

    @Autowired
    private IWorkflowTaskService workflowTaskService;

    /**
     * 添加
     *
     * @param orrRiskReport
     * @return
     */
    public Result<String> add(OrrRiskReport orrRiskReport) {

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        orrRiskReport.setReportAgencies(sysUser.getOrgCode());
        orrRiskReport.setReportDepartment(sysUser.getOrgCode());

        // 先获取全年的节假日日期
        QueryWrapper<Weekdays> weekdaysQueryWrapper = new QueryWrapper<>();
        weekdaysQueryWrapper.eq("type", "0");
        List<Weekdays> festivalDaysList = weekdaysMapper.selectList(weekdaysQueryWrapper);

        // 再获取报告时限
        QueryWrapper<OrrSchedule> scheduleQueryWrapper = new QueryWrapper<>();
        scheduleQueryWrapper.eq("report_type", "2");
        OrrSchedule orrSchedule = orrScheduleMapper.selectOne(scheduleQueryWrapper);

        // 计算报告截止日期
        Date eventDiscoveryDate = orrRiskReport.getEventDiscoveryDate();
        Date reportDeadlineDate = null;
        if (null != festivalDaysList && !festivalDaysList.isEmpty() && orrSchedule != null) {
            String reportDeadline = orrSchedule.getReportDeadline();
            if (reportDeadline != null && !reportDeadline.isEmpty()) {
                try {
                    int deadlineDays = Integer.parseInt(reportDeadline);
                    reportDeadlineDate = calculateDeadlineDate(eventDiscoveryDate, deadlineDays, festivalDaysList);
                } catch (NumberFormatException e) {
                    log.error("报告截止日期天数格式错误: {}");
                }
            }
        }
        orrRiskReport.setReportDeadlineDate(reportDeadlineDate);

        // 添加数据
        orrRiskReportMapper.insert(orrRiskReport);

        return Result.ok("上传成功！");
    }

    /**
     * 批量提交
     *
     * @param ids
     * @return
     */
    public Result<String> batchSubmit(List<String> ids) {

        List<OrrRiskReport> orrRiskReportList = orrRiskReportMapper.selectBatchIds(ids);

        for (OrrRiskReport orrRiskReport: orrRiskReportList) {
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, orrRiskReport);

            // 开始工作流
            instanceService.createWorkflowInstance(businessKey, orrRiskReport.getId(), variables);
        }

        // 添加处理过程
        processService.saveProcessBatch(businessKey, ids, "提交审核");

        return Result.ok("提交成功，请等待审核！");
    }

    /**
     * 批量撤销
     *
     * @param ids
     * @return
     */
    public Result<String> batchRevoke(List<String> ids) {

        List<OrrRiskReport> orrRiskReportList = orrRiskReportMapper.selectBatchIds(ids);

        for (OrrRiskReport orrRiskReport: orrRiskReportList) {
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, orrRiskReport);
            if ("2".equals(orrRiskReport.getReportStatus())) {
                // 审核中
                WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, orrRiskReport.getId());
                variables.put("revoke", true);
                workflowTaskService.completeTask(workflowTask.getId(), variables);
            }else{
                // 审核通过 开始撤销申请
                instanceService.createWorkflowInstance(businessKey, orrRiskReport.getId(), variables);
            }
        }

        // 添加处理过程
        processService.saveProcessBatch(businessKey, ids, "撤销");

        return Result.ok("撤销成功，请等待审核！");
    }

    /**
     * 批量通过
     *
     * @param ids
     * @return
     */
    public Result<String> passBatch(List<String> ids) {

        List<OrrRiskReport> orrRiskReportList = orrRiskReportMapper.selectBatchIds(ids);

        for (OrrRiskReport orrRiskReport: orrRiskReportList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, orrRiskReport.getId());
            if (workflowTask != null) {
                Map<String, Object> variables = new HashMap<>();
                variables.put("approve", true);
                variables.put(businessKey, orrRiskReport);
                workflowTaskService.completeTask(workflowTask.getId(), variables);
            }
        }

        processService.saveProcessBatch(businessKey, ids, "审核通过");
        return Result.ok("通过成功！");
    }

    /**
     * 批量退回
     *
     * @param ids
     * @return
     */
    public Result<String> returnBatch(List<String> ids) {

        List<OrrRiskReport> orrRiskReportList = orrRiskReportMapper.selectBatchIds(ids);

        for (OrrRiskReport orrRiskReport: orrRiskReportList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, orrRiskReport.getId());
            if (workflowTask != null) {
                Map<String, Object> variables = new HashMap<>();
                variables.put("approve", false);
                variables.put(businessKey, orrRiskReport);
                workflowTaskService.completeTask(workflowTask.getId(), variables);
            }
        }

        processService.saveProcessBatch(businessKey, ids, "审核退回");
        return Result.ok("退回成功！");
    }

    /**
     * 通过ids批量查询查询
     *
     * @param ids
     * @return
     */
    public Result<List<OrrRiskReport>> batchQueryById(List<String> ids) {

        List<OrrRiskReport> orrRiskReportList = orrRiskReportMapper.selectBatchIds(ids);

        return Result.ok(orrRiskReportList);
    }

    /**
     * 下载模板
     *
     * @param
     * @return
     */
    public Result<OrrReportTemplate> downloadTemplate() {

        QueryWrapper<OrrReportTemplate> templateQueryWrapper = new QueryWrapper<>();
        templateQueryWrapper.eq("type", "3");

        OrrReportTemplate orrReportTemplate = orrReportTemplateMapper.selectOne(templateQueryWrapper);

        return Result.ok(orrReportTemplate);
    }

    /**
     * 计算截止日期（排除节假日）
     *
     * @param startDate 开始日期
     * @param deadlineDays 截止天数（工作日）
     * @param festivalDaysList 节假日列表
     * @return 截止日期
     */
    protected Date calculateDeadlineDate(Date startDate, int deadlineDays, List<Weekdays> festivalDaysList) {

        // 将节假日转换为Set以便快速查找
        Set<String> holidaySet = new HashSet<>();
        for (Weekdays holiday : festivalDaysList) {
            holidaySet.add(holiday.getDay());
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(startDate);

        int addedDays = 0;

        // 添加指定数量的工作日
        while (addedDays < deadlineDays) {
            // 移动到下一天
            cal.add(Calendar.DAY_OF_MONTH, 1);

            // 检查是否是工作日（非周末且非节假日）
            if (DateUtils.isWorkDay(cal, holidaySet)) {
                addedDays++;
            }
        }

        return cal.getTime();
    }

}
