<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="RuleSystemReformedAndAbolishedForm">
          <a-row>
            <a-col :span="24">
              <a-form-item
                label="请输入合规意见"
                v-bind="validateInfos.complianceOpinion"
                id="RuleSystemReformedAndAbolishedForm-complianceOpinion"
                name="complianceOpinion"
              >
                <a-input v-model:value="formData.complianceOpinion" placeholder="请输入合规意见" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, defineProps, computed } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { givebackRequest, passRequest } from '@/views/rule/regulations/manage/reformedAndAbolished/RuleSystemReformedAndAbolished.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  import { useUserStore } from '/@/store/modules/user';

  const userStore = useUserStore(); //获取用户信息
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({}) },
    formBpm: { type: Boolean, default: true },
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    ids: '',
    complianceOpinion: '',
    flag: '',
  });

  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  // 动态生成验证规则
  const validatorRules = computed(() => {
    const rules = {
      complianceOpinion: [{ required: true, message: '请输入合规意见!' }],
    };
    return rules;
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });
  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  //编辑
  async function edit(record, flag) {
    formData.complianceOpinion = '';
    formData.flag = flag;
    formData.ids = record;
  }
  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    if ('1' === formData.flag) {
      passRequest({ ids: formData.ids, complianceOpinion: formData.complianceOpinion, complianceFlag: '1' }, handleSuccess);
    } else {
      givebackRequest({ ids: formData.ids, complianceOpinion: formData.complianceOpinion, complianceFlag: '1' }, handleSuccess);
    }
    confirmLoading.value = false;
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    emit('ok');
  }

  defineExpose({
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
