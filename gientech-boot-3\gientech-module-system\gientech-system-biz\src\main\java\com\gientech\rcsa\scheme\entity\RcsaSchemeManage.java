package com.gientech.rcsa.scheme.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: RCSA评估方案表
 * @Author: jeecg-boot
 * @Date:   2025-07-16
 * @Version: V1.0
 */
@Data
@TableName("rcsa_scheme_manage")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="RCSA评估方案表")
public class RcsaSchemeManage implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**计划表主键*/
    @Schema(description = "计划表主键")
    private java.lang.String planId;
	/**计划编号*/
    @Schema(description = "计划编号")
    private java.lang.String planCode;
	/**计划标题*/
	@Excel(name = "计划标题", width = 15)
    @Schema(description = "计划标题")
    private java.lang.String planTitle;
	/**计划类型*/
	@Excel(name = "计划类型", width = 15, dicCode = "rcsa_plan_type")
	@Dict(dicCode = "rcsa_plan_type")
    @Schema(description = "计划类型")
    private java.lang.String planType;
	/**评估截止日期*/
	@Excel(name = "评估截止日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "评估截止日期")
    private java.util.Date evaluateEndDate;
	/**评估部门*/
	@Excel(name = "评估部门", width = 15, dictTable = "sys_depart", dicCode = "id", dicText = "depart_name")
    @Schema(description = "评估部门")
    @Dict(dictTable = "sys_depart", dicCode = "id", dicText = "depart_name")
    private java.lang.String evaluateDepart;
	/**录入人*/
	@Excel(name = "录入人", width = 15)
    @Schema(description = "录入人")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String enterUser;
	/**录入日期*/
	@Excel(name = "录入日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "录入日期")
    private java.util.Date enterDate;
	/**方案标题*/
	@Excel(name = "方案标题", width = 15)
    @Schema(description = "方案标题")
    private java.lang.String schemeTitle;
	/**录入人联系方式*/
	@Excel(name = "录入人联系方式", width = 15)
    @Schema(description = "录入人联系方式")
    private java.lang.String enterContact;
	/**方案编号*/
	@Excel(name = "方案编号", width = 15)
    @Schema(description = "方案编号")
    private java.lang.String schemeCode;
	/**方案描述*/
	@Excel(name = "方案描述", width = 15)
    @Schema(description = "方案描述")
    private java.lang.String schemeDescription;
	/**附件信息*/
	@Excel(name = "附件信息", width = 15)
    @Schema(description = "附件信息")
    private java.lang.String document;
	/**方案状态*/
	@Excel(name = "方案状态", width = 15, dicCode = "rcsa_plan_state")
	@Dict(dicCode = "rcsa_plan_state")
    @Schema(description = "方案状态")
    private java.lang.String schemeState;
	/**生效时间*/
	@Excel(name = "生效时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "生效时间")
    private java.util.Date effectTime;
	/**编号下标*/
	@Excel(name = "编号下标", width = 15)
    @Schema(description = "编号下标")
    private java.lang.String codeIndex;
	/**流程类型 1-牵头部门 2-总行部门 3-分行机构*/
	@Excel(name = "流程类型 1-牵头部门 2-总行部门 3-分行机构", width = 15)
    @Schema(description = "流程类型 1-牵头部门 2-总行部门 3-分行机构")
    private java.lang.String processFlag;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;

    /**牵头部门*/
    private String leadDepart;

    @TableField(exist = false)
    List<RcsaSchemeMatrixRel> matrixRelList;
    @TableField(exist = false)
    private java.lang.String evaluateDepartCode;
}
