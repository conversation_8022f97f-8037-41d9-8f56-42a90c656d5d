package com.gientech.workflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gientech.workflow.dto.WorkflowTaskQueryDTO;
import com.gientech.workflow.dto.WorkflowTaskQueryParam;
import com.gientech.workflow.entity.WorkflowTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据库映射
 *
 * <AUTHOR>
 * @date 2025年05月07日 14:01
 */
public interface WorkflowTaskMapper extends BaseMapper<WorkflowTask> {

    /**
     * 查询工作流任务详细信息
     * 联表查询任务表和实例表，获取完整的任务和业务信息
     *
     * @param param 查询参数
     * @return 工作流任务查询结果列表
     */
    List<WorkflowTaskQueryDTO> selectWorkflowTaskWithInstance(@Param("param") WorkflowTaskQueryParam param);

    /**
     * 查询工作流任务对应的业务ID列表
     * 优化版本，支持单个和批量委托人查询
     *
     * @param param 查询参数
     * @return 业务ID列表
     */
    List<String> selectWorkflowTaskBusinessIds(@Param("param") WorkflowTaskQueryParam param);

    /**
     * 统计工作流任务数量
     *
     * @param param 查询参数
     * @return 任务数量
     */
    Long countWorkflowTask(@Param("param") WorkflowTaskQueryParam param);
}
