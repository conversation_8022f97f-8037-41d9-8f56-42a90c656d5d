package com.gientech.rule.system.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import com.gientech.rule.system.entity.RuleSystemConsultRelate;
import com.gientech.rule.system.service.IRuleSystemConsultRelateService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * 内外规库管理-制度-制度咨询关联表
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-08-08
 */
@Tag(name = "内外规库管理-制度-制度咨询关联表")
@RestController
@RequestMapping("/rule/system/ruleSystemConsultRelate")
@Slf4j
public class RuleSystemConsultRelateController extends JeecgController<RuleSystemConsultRelate, IRuleSystemConsultRelateService> {

    private IRuleSystemConsultRelateService ruleSystemConsultRelateService;

    @Autowired
    public void setRuleSystemConsultRelateService(IRuleSystemConsultRelateService ruleSystemConsultRelateService) {
        this.ruleSystemConsultRelateService = ruleSystemConsultRelateService;
    }

    /**
     * 分页列表查询
     *
     * @param ruleSystemConsultRelate 实体参数
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @param req 请求参数
     * @return 查询结果
     */
    @GetMapping(value = "/list")
    @Operation(summary = "分页列表查询")
    public Result<IPage<RuleSystemConsultRelate>> queryPageList(RuleSystemConsultRelate ruleSystemConsultRelate,
                                                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                          HttpServletRequest req) {
        QueryWrapper<RuleSystemConsultRelate> queryWrapper = QueryGenerator.initQueryWrapper(ruleSystemConsultRelate, req.getParameterMap());
        Page<RuleSystemConsultRelate> page = new Page<RuleSystemConsultRelate>(pageNo, pageSize);
        IPage<RuleSystemConsultRelate> pageList = ruleSystemConsultRelateService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param ruleSystemConsultRelate 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/add")
    @AutoLog(value = "内外规库管理-制度-制度咨询关联表-添加")
    @RequiresPermissions("rule.system:rule_system_consult_relate:add")
    public Result<String> add(@RequestBody RuleSystemConsultRelate ruleSystemConsultRelate) {
        ruleSystemConsultRelateService.save(ruleSystemConsultRelate);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param ruleSystemConsultRelate 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "编辑")
    @AutoLog(value = "内外规库管理-制度-制度咨询关联表-编辑")
    @RequiresPermissions("rule.system:rule_system_consult_relate:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody RuleSystemConsultRelate ruleSystemConsultRelate) {
        ruleSystemConsultRelateService.updateById(ruleSystemConsultRelate);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id 实体对象主键
     * @return 是否成功
     */
    @Operation(summary = "通过id删除")
    @DeleteMapping(value = "/delete")
    @AutoLog(value = "内外规库管理-制度-制度咨询关联表-通过id删除")
    @RequiresPermissions("rule.system:rule_system_consult_relate:delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        ruleSystemConsultRelateService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 多个实体对象主键
     * @return 是否成功
     */
    @Operation(summary = "批量删除")
    @DeleteMapping(value = "/deleteBatch")
    @AutoLog(value = "内外规库管理-制度-制度咨询关联表-批量删除")
    @RequiresPermissions("rule.system:rule_system_consult_relate:deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.ruleSystemConsultRelateService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id 实体对象主键
     * @return 查询结果
     */
    @GetMapping(value = "/queryById")
    @Operation(summary = "通过id查询")
    public Result<RuleSystemConsultRelate> queryById(@RequestParam(name = "id", required = true) String id) {
        RuleSystemConsultRelate ruleSystemConsultRelate = ruleSystemConsultRelateService.getById(id);
        if (ruleSystemConsultRelate == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(ruleSystemConsultRelate);
    }

    /**
     * 导出excel
     *
     * @param request 请求参数
     * @param ruleSystemConsultRelate 实体表单参数
     */
    @RequestMapping(value = "/exportXls")
    @RequiresPermissions("rule.system:rule_system_consult_relate:exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RuleSystemConsultRelate ruleSystemConsultRelate) {
        return super.exportXls(request, ruleSystemConsultRelate, RuleSystemConsultRelate.class, "内外规库管理-制度-制度咨询关联表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request 请求参数
     * @return 是否成功
     */
    @RequiresPermissions("rule.system:rule_system_consult_relate:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RuleSystemConsultRelate.class);
    }

}
