package com.gientech.rule.assess.pilot.updater;

import com.gientech.rule.assess.pilot.entity.RuleAssessPilot;
import com.gientech.rule.assess.pilot.mapper.RuleAssessPilotMapper;
import com.gientech.workflow.updater.BusinessDataUpdater;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2025年08月08日 09:04
 */
@Slf4j
@Service
public class RuleAssessPilotUpdater implements BusinessDataUpdater {

    @Autowired
    private RuleAssessPilotMapper ruleAssessPilotMapper;

    private final String businessKey = "ruleAssessPilot";

    @Override
    public String getBusinessKey() {
        return this.businessKey;
    }

    @Override
    public Class<?> getBusinessDataType() {
        return RuleAssessPilot.class;
    }

    @Override
    public void beforeProcessTask(Map<String, Object> businessData) {
        Object data = businessData.get(businessKey);
        if (data instanceof RuleAssessPilot ruleAssessPilot) {
            String id = ruleAssessPilot.getId();
            String processStatus = ruleAssessPilot.getProcessStatus();

            ruleAssessPilot = ruleAssessPilotMapper.selectById(id);
            ruleAssessPilot.setProcessStatus(processStatus);
            ruleAssessPilotMapper.updateById(ruleAssessPilot);
        } else {
            log.error("{} is not a RuleAssessPilot", data.getClass().getName());
        }
    }
}
