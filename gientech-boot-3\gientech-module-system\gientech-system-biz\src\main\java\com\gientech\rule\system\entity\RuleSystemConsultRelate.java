package com.gientech.rule.system.entity;

import java.io.Serial;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 内外规库管理-制度-制度咨询关联表
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-08-08
 */
@Data
@TableName("rule_system_consult_relate")
@Accessors(chain = true)
@Schema(description="内外规库管理-制度-制度咨询关联表")
public class RuleSystemConsultRelate implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;

    /**制度建议主表*/
    @Excel(name = "制度建议主表", width = 15)
    @Schema(description = "制度建议主表")
    private java.lang.String systemId;

    /**关联时间*/
    @Excel(name = "关联时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "关联时间")
    private java.util.Date relateTime;

    /**删除标记*/
    @Schema(description = "删除标记")
    @TableLogic
    private java.lang.Integer delFlag;

    /**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;

    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;

    /**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;

    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;

    /**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;

}
