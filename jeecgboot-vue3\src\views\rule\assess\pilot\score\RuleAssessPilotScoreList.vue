<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="reload" :model="queryParam" :label-col="labelCol"
              :wrapper-col="wrapperCol">
        <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 128 }">
          <!-- 默认显示的查询条件 -->
          <a-col :span="8">
            <a-form-item name="taskCode">
              <template #label><span title="任务编号">任务编号</span></template>
              <a-input placeholder="请输入任务编号" v-model:value="queryParam.taskCode" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="taskName">
              <template #label><span title="任务名称">任务名称</span></template>
              <a-input placeholder="请输入任务名称" v-model:value="queryParam.taskName" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
              <a-form-item name="assessType">
                <template #label><span title="评估类型">评估类型</span></template>
                <j-dict-select-tag v-model:value="queryParam.assessType" dictCode="rule_assess_pilot_assess_type"
                  placeholder="请选择评估类型" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="endTime">
                <template #label><span title="结束时间">结束时间</span></template>
                <a-date-picker placeholder="请选择结束时间" v-model:value="queryParam.endTime" valueFormat="YYYY-MM-DD"
                  style="width: 100%" allow-clear />
              </a-form-item>
            </a-col>

          <!-- 展开显示的其余查询条件 -->
          <template v-if="toggleSearchStatus">
            <a-col :span="8">
              <a-form-item name="assessOrgCode">
                <template #label><span title="评估机构">评估机构</span></template>
                <j-select-dept v-model:value="queryParam.assessOrgCode" placeholder="请选择评估机构" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="assessDeptCode">
                <template #label><span title="评估部门">评估部门</span></template>
                <j-select-dept v-model:value="queryParam.assessDeptCode" placeholder="请选择评估部门" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="taskStatus">
                <template #label><span title="任务状态">任务状态</span></template>
                <j-dict-select-tag v-model:value="queryParam.taskStatus" dictCode="rule_assess_pilot_sub_task_status"
                  placeholder="请选择任务状态" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="processStatus">
                <template #label><span title="流程状态">流程状态</span></template>
                <j-dict-select-tag v-model:value="queryParam.processStatus" dictCode="rule_assess_pilot_sub_process_status"
                  placeholder="请选择流程状态" allow-clear />
              </a-form-item>
            </a-col>
          </template>

          <!-- 操作按钮区域 -->
          <a-col :span="8">
            <span style="float: right; overflow: hidden" class="table-page-search-submitButtons">
              <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                {{ toggleSearchStatus ? "收起" : "展开" }}
                <Icon
                  :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
              </a>
              <a-button preIcon="ant-design:reload-outlined" @click="searchReset"
                        style="margin-left: 8px">重置</a-button>
              <a-button type="primary" preIcon="ant-design:search-outlined" style="margin-left: 8px"
                        @click="reload">查询</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'rule.assess:rule_assess_pilot_sub:edit'" @click="handleScore"
          :disabled="selectedRowKeys.length === 0" preIcon="ant-design:edit-outlined"> 评估评分
        </a-button>
        <a-button type="default" v-auth="'rule.assess:rule_assess_pilot_sub:submit'" @click="handleSubmit"
          :disabled="selectedRowKeys.length === 0" preIcon="ant-design:check-outlined"> 提交
        </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    
    <!-- 评分弹窗 -->
    <RuleAssessScoreModal @register="registerScoreModal" @success="handleSuccess"></RuleAssessScoreModal>
  </div>
</template>

<script lang="ts" name="rule.assess-ruleAssessPilotScore" setup>
import { reactive, ref, onMounted } from "vue";
import { BasicTable, TableAction } from "/@/components/Table";
import { useListPage } from "/@/hooks/system/useListPage";
import { useModal } from "/@/components/Modal";
import { columns } from "./RuleAssessPilotScore.data";
import { list, submitRequest } from "./RuleAssessPilotScore.api";
import JSelectDept from "/@/components/Form/src/jeecg/components/JSelectDept.vue";
import JDictSelectTag from "/@/components/Form/src/jeecg/components/JDictSelectTag.vue";
import { useMessage } from "@/hooks/web/useMessage";
import RuleAssessScoreModal from "./components/RuleAssessScoreModal.vue";
import { Icon } from "/@/components/Icon";
import { preloadTextMappings } from "./utils/textMappingsCache";

const formRef = ref();
const queryParam = reactive<any>({
  taskCode: "",
  taskName: "",
  assessType: "",
  endTime: "",
  assessOrgCode: "",
  assessDeptCode: "",
  taskStatus: "",
  processStatus: ""
});

// 控制查询条件展开/收起
const toggleSearchStatus = ref<boolean>(false);

//注册model
const [registerScoreModal, { openModal: openScoreModal }] = useModal();
const { createMessage } = useMessage();

//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    title: "内外规库管理-子任务评估评分列表",
    api: list, // 直接使用后端接口
    columns,
    canResize: false,
    useSearchForm: false,
    actionColumn: {
      title: "操作",
      width: 120,
      fixed: "right"
    },
    tableSetting: {
      redo: false,
      size: false,
      setting: false
    },
    beforeFetch: async (params) => {
      // 将查询参数合并到请求参数中
      const mergedParams = Object.assign({}, params);

      // 只添加非空的查询参数
      Object.keys(queryParam).forEach(key => {
        const value = queryParam[key];
        if (value !== null && value !== undefined && value !== '') {
          mergedParams[key] = value;
        }
      });

      return mergedParams;
    }
  }
});

const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

/**
 * 查看详情
 */
function handleDetail(record: Recordable) {
  openScoreModal(true, {
    subId: record.id,
    taskCode: record.taskCode,
    taskName: record.taskName,
    isEditMode: false, // 查看模式，不可编辑
  });
}


/**
 * 子任务评分（可编辑）
 */
function handleScore() {
  if (selectedRows.value.length === 0) {
    createMessage.warning("请选择要评分的子任务!");
    return;
  }

  if (selectedRows.value.length > 1) {
    createMessage.warning("请选择单个子任务进行评分!");
    return;
  }

  // 打开评分弹窗，传入子任务ID，可编辑模式
  const subTask = selectedRows.value[0];
  openScoreModal(true, {
    subId: subTask.id,
    isEditMode: true // 可编辑模式
  });
}

/**
 * 提交
 */
function handleSubmit() {
  let ids: any[] = [];
  let rows = selectedRows.value;
  let findRow = rows.find((row) => {
    return row.taskStatus !== '1' || row.processStatus !== '1';
  });
  if (findRow) {
    createMessage.warning('请选择完成录入的草稿数据!');
    return;
  }
  ids = selectedRowKeys.value;
  submitRequest({ ids: ids }, handleSuccess);
}

/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}

/**
 * 操作栏
 */
function getTableAction(record: Recordable) {
  return [
    {
      label: "查看",
      onClick: handleDetail.bind(null, record)
    }
  ];
}

const labelCol = reactive({
  xs: 24,
  sm: 4,
  xl: 6,
  xxl: 4
});
const wrapperCol = reactive({
  xs: 24,
  sm: 20
});

/**
 * 重置
 */
function searchReset() {
  formRef.value.resetFields();
  selectedRowKeys.value = [];
  //刷新数据
  reload();
}

// 组件挂载时预加载文本映射
onMounted(() => {
  // 异步预加载，不阻塞页面渲染
  preloadTextMappings();
});
</script>

<style lang="less" scoped>
.jeecg-basic-table-form-container {
  padding: 0;

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }

  .query-group-cust {
    min-width: 100px !important;
  }

  .query-group-split-cust {
    width: 30px;
    display: inline-block;
    text-align: center
  }

  .ant-form-item:not(.ant-form-item-with-help) {
    margin-bottom: 16px;
    height: 32px;
  }

  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
}
</style>
