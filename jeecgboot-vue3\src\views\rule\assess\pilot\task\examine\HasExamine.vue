<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="reload" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item name="taskCode">
              <template #label><span title="任务编号">任务编号</span></template>
              <a-input placeholder="请输入任务编号" v-model:value="queryParam.taskCode" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="taskName">
              <template #label><span title="任务名称">任务名称</span></template>
              <a-input placeholder="请输入任务名称" v-model:value="queryParam.taskName" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="assessType">
              <template #label><span title="评估类型">评估类型</span></template>
              <j-dict-select-tag
                v-model:value="queryParam.assessType"
                dictCode="rule_assess_pilot_assess_type"
                placeholder="请选择评估类型"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="endTime">
              <template #label><span title="结束时间">结束时间</span></template>
              <a-date-picker
                placeholder="请选择结束时间"
                v-model:value="queryParam.endTime"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="assessOrgCode">
              <template #label><span title="参与评估机构">参与评估机构</span></template>
              <j-select-dept v-model:value="queryParam.assessOrgCode" placeholder="请选择参与评估机构" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <span style="float: right; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
              <a-button preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 详情/评估结论录入弹窗 -->
    <RuleAssessPilotDetailModal @register="registerDetailModal" @success="handleSuccess" />

    <ProcessModal ref="processRef" :getProcess="getProcess" />
  </div>
</template>

<script lang="ts" name="rule.assess-ruleAssessPilot" setup>
import { reactive, ref, computed } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import { useModal } from '/@/components/Modal';
import RuleAssessPilotDetailModal from '../components/RuleAssessPilotDetailModal.vue';
import { columns } from '../RuleAssessPilot.data';
import { list } from '../RuleAssessPilot.api';
import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
import { useMessage } from '@/hooks/web/useMessage';
import {
  getProcess
} from "@/views/rule/assess/pilot/score/examine/RuleAssessPilotScoreExamine.api";
import ProcessModal from "@/views/kri/input/components/ProcessModal.vue";
import { useExamine } from "@/hooks/api/useExamine";

const formRef = ref();
const queryParam = reactive<any>({
  taskCode: '',
  taskName: '',
  assessType: '',
  endTime: '',
  assessOrgCode: '',
});
//注册model
const [registerModal, { openModal }] = useModal();
const [registerDetailModal, { openModal: openDetailModal }] = useModal();
const { createMessage } = useMessage();
const { getProcess } = useExamine("/rule/assess/pilot/task")

// const userStore = useUserStore();
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    useSearchForm: false,
    actionColumn: {
      title: '操作',
      width: 160,
      fixed: 'right',
    },
    tableSetting: {
      redo: false,
      size: false,
      setting: false,
    },
    beforeFetch: async (params) => {
      return Object.assign(params, queryParam);
    },
  },
});

const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

/**
 * 详情
 */
function handleDetail(record: Recordable) {
  openDetailModal(true, {
    record,
    isEditMode: false, // 查看模式
  });
}

/**
 * 处理过程
 */
function toProcess(record: Recordable) {
  processRef.value.handleOpen(record.id);
}

/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}

/**
 * 操作栏
 */
function getTableAction(record: Recordable) {
  return [
    {
      label: '查看',
      onClick: handleDetail.bind(null, record),
    },
    {
      label: '处理过程',
      onClick: toProcess.bind(null, record),
    },
  ];
}

const labelCol = reactive({
  xs: 24,
  sm: 4,
  xl: 6,
  xxl: 8,
});
const wrapperCol = reactive({
  xs: 24,
  sm: 16,
});

/**
 * 查询
 */
function searchQuery() {
  reload();
}

/**
 * 重置
 */
function searchReset() {
  formRef.value.resetFields();
  selectedRowKeys.value = [];
  //刷新数据
  reload();
}

defineExpose({
  reload
})
</script>
<style lang="less" scoped>
.jeecg-basic-table-form-container {
  padding: 0;

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }

  .query-group-cust {
    min-width: 100px !important;
  }

  .query-group-split-cust {
    width: 30px;
    display: inline-block;
    text-align: center;
  }

  .ant-form-item:not(.ant-form-item-with-help) {
    margin-bottom: 16px;
    height: 32px;
  }

  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
}
</style>
