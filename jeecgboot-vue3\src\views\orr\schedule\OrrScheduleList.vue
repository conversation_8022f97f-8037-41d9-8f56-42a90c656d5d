<template>
  <a-spin :spinning="confirmLoading">
    <div class="p-2">
      <!-- 按钮操作区 -->
      <div class="mb-4" style="margin-top: 10px;">
        <a-button type="primary" v-auth="'orr.schedule:orr_schedule:edit'" @click="handleEdit"
                  preIcon="ant-design:edit-outlined" class="mr-2">
          编辑
        </a-button>
        <a-button  v-auth="'orr.schedule:orr_schedule:add'" @click="handleSave"
                  preIcon="ant-design:plus-outlined">
          保存
        </a-button>
      </div>

      <!-- Ant Design 表格 -->
      <a-table
          :dataSource="dataSource"
          :columns="tableColumns"
          :pagination="false"
          bordered
          size="middle"
      >
        <template #bodyCell="{ column, record, index }">
          <!-- 报告类型列（不可编辑）：添加椭圆样式容器 -->
          <template v-if="column.dataIndex === 'reportType_dictText'">
            <span class="report-type-tag">
              {{ record.reportType_dictText }}
            </span>
          </template>
          <!-- 可编辑列（代码保持不变） -->
          <template v-else-if="editableColumnKeys.includes(column.dataIndex)">
            <div v-if="!editMode" class="non-edit-value">
              {{ record[column.dataIndex] || '-' }}
            </div>
            <div v-else>
              <a-input-number
                  v-model:value="record[column.dataIndex]"
                  :min="1"
                  :step="1"
                  placeholder="请输入正整数"
                  :status="getFieldStatus(record, column.dataIndex)"
                  @change="handleInputChange(record, column.dataIndex)"
              />
              <div v-if="errors[record.id]?.[column.dataIndex]" class="ant-form-item-explain-error">
                {{ errors[record.id][column.dataIndex] }}
              </div>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </a-spin>
</template>

<script lang="ts" setup>
import {ref, computed, onMounted} from 'vue';
import {message} from 'ant-design-vue';
import {list, saveOrUpdate} from './OrrSchedule.api';
import {columns} from './OrrSchedule.data';
import {useMessage} from "@/hooks/web/useMessage";

const {createMessage} = useMessage();
const confirmLoading = ref(false);

// 表格数据
const dataSource = ref<any[]>([]);

// 编辑模式
const editMode = ref(false);

// 错误信息集合 { [rowId]: { [field]: errorMessage } }
const errors = ref<Record<string, Record<string, string>>>({});

// 可编辑的列数据索引
const editableColumnKeys = [
  'reportDeadline',
  'reportBeginsAt',
  'reportDeadlineBefore',
  'reportDeadlineEveryDay'
];

// 完整的表格列
const tableColumns = computed(() => columns);

// 初始化加载数据
onMounted(async () => {
  toInit();
});

async function toInit() {

  confirmLoading.value = true;
  await list({})
      .then((res) => {
        if (res.success) {
          dataSource.value = res.result.records.map(item => {
            const newItem = {...item};
            editableColumnKeys.forEach(key => {
              if (newItem[key] !== null && newItem[key] !== undefined) {
                // 转换字符串数值为数字类型
                if (typeof newItem[key] === 'string') {
                  const num = Number(newItem[key]);
                  if (!isNaN(num)) {
                    newItem[key] = num;
                  }
                }
              }
            });
            return newItem;
          });
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
}

// 获取字段状态
const getFieldStatus = (record, field) => {
  return errors.value[record.id]?.[field] ? 'error' : '';
};

// 输入变化处理
const handleInputChange = (record, field) => {
  validateField(record, field);
};

// 字段验证
const validateField = (record, field) => {
  const value = record[field];
  const fieldErrors = errors.value[record.id] || {};

  // 字段名称映射
  const fieldNames = {
    reportDeadline: '报告时限（工作日）',
    reportBeginsAt: '报告开始第',
    reportDeadlineBefore: '报告时限截止前第',
    reportDeadlineEveryDay: '报告时限截止后每'
  };

  // 关键修改：允许空值（用户未修改时不强制验证）
  if (value === null || value === undefined || value === '') {
    fieldErrors[field] = `${fieldNames[field]}不能为空`;
    errors.value[record.id] = {...fieldErrors};
    return false;
  }

  // 统一处理数字和字符串类型的值
  let numValue = value;
  if (typeof value === 'string') {
    // 尝试转换为数字
    numValue = Number(value);
    // 转换失败则直接报错
    if (isNaN(numValue)) {
      fieldErrors[field] = `${fieldNames[field]}必须为有效数字`;
    }
  }

  // 验证是否为正整数
  if (typeof numValue !== 'number' || !Number.isInteger(numValue) || numValue <= 0) {
    fieldErrors[field] = `${fieldNames[field]}必须为正整数`;
  } else {
    delete fieldErrors[field];
  }

  errors.value[record.id] = {...fieldErrors};
  return !fieldErrors[field];
};

// 编辑按钮
const handleEdit = () => {
  editMode.value = true;
  // 重置错误状态
  dataSource.value.forEach(item => {
    errors.value[item.id] = {};

    // 预先验证所有字段（确保初始值有效）
    editableColumnKeys.forEach(key => {
      validateField(item, key);
    });
  });
};

// 保存按钮
const handleSave = async () => {

  if (!editMode.value) {
    return;
  }

  let hasErrors = false;

  dataSource.value.forEach(record => {
    editableColumnKeys.forEach(key => {
      const valid = validateField(record, key);
      if (!valid) hasErrors = true;
    });
  });

  if (hasErrors) {
    message.error('请检查，有不合法的填写');
    return;
  }

  // 开始调用接口
  confirmLoading.value = true;
  await saveOrUpdate({
    schedules: dataSource.value,
  }, false)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          toInit();
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        editMode.value = false;
        confirmLoading.value = false;
      });
};
</script>

<style lang="less" scoped>
.non-edit-value {
  padding: 5px 0;
}

.ant-form-item-explain-error {
  color: #ff4d4f;
  font-size: 12px;
}

// 新增样式
:deep(.ant-table) {
  border: 1px solid #FFC098;
  border-radius: 4px;
  overflow: hidden;


  .ant-table-thead > tr > th {
    background: linear-gradient(180deg, #FFE0CC  0%, #FFC098 100%);
    border-color: #FFC098;
    color: #E67E22; 
    font-weight: bold;
    text-align: center;
    
    // 报告类型列的表头特殊样式
    &:first-child {
      background: linear-gradient(180deg, #F69502  0%, #EA5C01  100%);
      color: #fff;
    }
  }

  // 表格内容样式
  .ant-table-tbody {
    
    > tr > td:first-child {
      background-color: #FFF5F0;
      color: #000;
      font-weight: 500;
    }

    // 所有单元格边框
    td {
      border-color: #FFC098 !important;
      text-align: center;
    }

    // > tr:hover > td {
    //   background-color: #FFF5F0 !important;
    // }
  }
}

.report-type-tag {
  width: 170px;
  height: 40px;
  border-radius: 20px; 
  border: 1px solid #FF9933; 
  background-color: white; 
  color: #E67E22; 
  font-weight: bold;
  padding: 3px 12px; 
  font-size: 14px; 
  line-height: 30px; 
  display: inline-block; 
}
:deep(.ant-table-tbody > tr > td:first-child) {
  padding: 8px 16px !important;
}
</style>
