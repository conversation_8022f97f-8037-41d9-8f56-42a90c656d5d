package com.gientech.cm.metering.legal.service;

import com.gientech.cm.metering.legal.entity.CmMeteringLegal;
import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;

import java.util.List;
import java.util.Map;

/**
 * @Description: 操作风险资本计量-法人口径
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
public interface ICmMeteringLegalService extends IService<CmMeteringLegal> {

    Result<String> add(CmMeteringLegal cmMeteringLegal);

    Result<Map<String, Object>> getMeteringVersion(CmMeteringLegal cmMeteringLegal);

    Result<String> batchSubmit(List<String> ids) throws Exception;

    Result<String> batchRevoke(List<String> ids);

    Result<String> auditReject(CmMeteringLegal cmMeteringLegal);

    Result<String> auditPass(CmMeteringLegal cmMeteringLegal);

    void exportXls(HttpServletRequest request,
                   HttpServletResponse response,
                   CmMeteringLegal cmMeteringLegal) throws Exception;

    Result<String> reckonMetering(CmMeteringLegal cmMeteringLegal);

    Result<String> reckonMeteringGroup(CmMeteringLegal cmMeteringLegal) throws Exception;

    Result<List<Map<String, Object>>> getGroupSubjectList(CmMeteringLegal cmMeteringLegal, Integer pageNo, Integer pageSize);

    void exportGroupXls(HttpServletRequest request,
                        HttpServletResponse response,
                        CmMeteringLegal cmMeteringLegal) throws Exception;

    Result<String> batchSubmitByWorkflow(List<String> ids);

    Result<String> batchRevokeByWorkflow(List<String> ids);
}
