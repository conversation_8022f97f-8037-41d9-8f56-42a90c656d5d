package com.gientech.ldc.param.regulatory.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import com.gientech.ldc.param.riskCause.entity.RiskCauseVersion;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import com.gientech.ldc.param.regulatory.entity.RegulatoryVersion;
import com.gientech.ldc.param.regulatory.service.IRegulatoryVersionService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 操作风险损失事件类型（监管分类）（正式表）
 * @Author: jeecg-boot
 * @Date:   2025-04-18
 * @Version: V1.0
 */
@Tag(name="操作风险损失事件类型（监管分类）（正式表）")
@RestController
@RequestMapping("/regulatory/regulatoryVersion")
@Slf4j
public class RegulatoryVersionController extends JeecgController<RegulatoryVersion, IRegulatoryVersionService> {
	@Autowired
	private IRegulatoryVersionService regulatoryVersionService;
	
	/**
	 * 分页列表查询
	 *
	 * @param regulatoryVersion
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "操作风险损失事件类型（监管分类）（正式表）-分页列表查询")
	@Operation(summary="操作风险损失事件类型（监管分类）（正式表）-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<RegulatoryVersion>> queryPageList(RegulatoryVersion regulatoryVersion,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<RegulatoryVersion> queryWrapper = QueryGenerator.initQueryWrapper(regulatoryVersion, req.getParameterMap());
		Page<RegulatoryVersion> page = new Page<RegulatoryVersion>(pageNo, pageSize);
		IPage<RegulatoryVersion> pageList = regulatoryVersionService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param regulatoryVersion
	 * @return
	 */
	@AutoLog(value = "操作风险损失事件类型（监管分类）（正式表）-添加")
	@Operation(summary="操作风险损失事件类型（监管分类）（正式表）-添加")
//	@RequiresPermissions("ldc.param.regulatory:ldc_param_or_regulatory_version:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody RegulatoryVersion regulatoryVersion) {

		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String id = loginUser.getId();

		regulatoryVersion.setCreateId(id);
		regulatoryVersionService.save(regulatoryVersion);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param regulatoryVersion
	 * @return
	 */
	@AutoLog(value = "操作风险损失事件类型（监管分类）（正式表）-编辑")
	@Operation(summary="操作风险损失事件类型（监管分类）（正式表）-编辑")
//	@RequiresPermissions("ldc.param.regulatory:ldc_param_or_regulatory_version:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody RegulatoryVersion regulatoryVersion) {

		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String id = loginUser.getId();

		regulatoryVersion.setCreateId(id);
		regulatoryVersionService.updateById(regulatoryVersion);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "操作风险损失事件类型（监管分类）（正式表）-通过id删除")
	@Operation(summary="操作风险损失事件类型（监管分类）（正式表）-通过id删除")
//	@RequiresPermissions("ldc.param.regulatory:ldc_param_or_regulatory_version:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		regulatoryVersionService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "操作风险损失事件类型（监管分类）（正式表）-批量删除")
	@Operation(summary="操作风险损失事件类型（监管分类）（正式表）-批量删除")
//	@RequiresPermissions("ldc.param.regulatory:ldc_param_or_regulatory_version:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.regulatoryVersionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "操作风险损失事件类型（监管分类）（正式表）-通过id查询")
	@Operation(summary="操作风险损失事件类型（监管分类）（正式表）-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<RegulatoryVersion> queryById(@RequestParam(name="id",required=true) String id) {
		RegulatoryVersion regulatoryVersion = regulatoryVersionService.getById(id);
		if(regulatoryVersion==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(regulatoryVersion);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param regulatoryVersion
    */
//    @RequiresPermissions("ldc.param.regulatory:ldc_param_or_regulatory_version:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RegulatoryVersion regulatoryVersion) {
        return super.exportXls(request, regulatoryVersion, RegulatoryVersion.class, "操作风险损失事件类型（监管分类）（正式表）");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
//    @RequiresPermissions("ldc.param.regulatory:ldc_param_or_regulatory_version:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RegulatoryVersion.class);
    }

	 @Operation(summary="操作风险损失事件类型（监管分类）（正式表）-分页列表查询")
	 @GetMapping(value = "/getEventTypeListNoPage")
	 public Result<List<RegulatoryVersion>> getEventTypeListNoPage() {
		 List<RegulatoryVersion> list = regulatoryVersionService.list();
		 return Result.OK(list);
	 }

}
