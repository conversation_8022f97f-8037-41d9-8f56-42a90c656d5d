package com.gientech.rcsa.task.controller;

import java.util.Arrays;
import java.util.List;
import com.gientech.rcsa.task.entity.RcsaTaskManage;
import com.gientech.rcsa.task.service.IRcsaTaskManageService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.gientech.rcsa.task.entity.RcsaTaskBasicInfo;
import com.gientech.rcsa.task.service.IRcsaTaskBasicInfoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: RCSA评估任务信息表
 * @Author: jeecg-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
@Tag(name="RCSA评估任务信息表")
@RestController
@RequestMapping("/rcsa/task/rcsaTaskBasicInfo")
@Slf4j
public class RcsaTaskBasicInfoController extends JeecgController<RcsaTaskBasicInfo, IRcsaTaskBasicInfoService> {
	@Autowired
	private IRcsaTaskBasicInfoService rcsaTaskBasicInfoService;
	@Autowired
	private IRcsaTaskManageService taskManageService;
	
	/**
	 * 分页列表查询
	 *
	 * @param rcsaTaskBasicInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "RCSA评估任务信息表-分页列表查询")
	@Operation(summary="RCSA评估任务信息表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<RcsaTaskBasicInfo>> queryPageList(RcsaTaskBasicInfo rcsaTaskBasicInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<RcsaTaskBasicInfo> queryWrapper = QueryGenerator.initQueryWrapper(rcsaTaskBasicInfo, req.getParameterMap());
		Page<RcsaTaskBasicInfo> page = new Page<RcsaTaskBasicInfo>(pageNo, pageSize);
		IPage<RcsaTaskBasicInfo> pageList = rcsaTaskBasicInfoService.page(page, queryWrapper);

		// 这边要改
		String taskId = rcsaTaskBasicInfo.getTaskId();
		RcsaTaskManage taskManage = taskManageService.getById(taskId);
		List<RcsaTaskBasicInfo> records = pageList.getRecords();
		for (RcsaTaskBasicInfo basicInfo : records) {
			if (!"3".equals(taskManage.getTaskState())) {
				// 需要重新入库状态值
				List<RcsaTaskBasicInfo> resultList = rcsaTaskBasicInfoService.queryInherentRiskLevelHistoryLimit(basicInfo);
				if (resultList != null && !resultList.isEmpty()) {
					basicInfo.setLastEvaluateResult(resultList.get(0).getAdjustedRemainRiskLevel());
				}
			}
		}
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param rcsaTaskBasicInfo
	 * @return
	 */
	@AutoLog(value = "RCSA评估任务信息表-添加")
	@Operation(summary="RCSA评估任务信息表-添加")
	@RequiresPermissions("rcsa.task:rcsa_task_basic_info:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody RcsaTaskBasicInfo rcsaTaskBasicInfo) {
		rcsaTaskBasicInfoService.save(rcsaTaskBasicInfo);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  评估
	 *
	 * @param rcsaTaskBasicInfo
	 * @return
	 */
	@AutoLog(value = "RCSA评估任务信息表-评估")
	@Operation(summary="RCSA评估任务信息表-评估")
	@RequiresPermissions("rcsa.task:rcsa_task_basic_info:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody RcsaTaskBasicInfo rcsaTaskBasicInfo) {
		try {
			rcsaTaskBasicInfoService.evaluate(rcsaTaskBasicInfo);
			return Result.OK("评估成功!");
		} catch (Exception e) {
			log.error("评估异常：", e);
			return Result.error("评估异常");
		}

	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "RCSA评估任务信息表-通过id删除")
	@Operation(summary="RCSA评估任务信息表-通过id删除")
	@RequiresPermissions("rcsa.task:rcsa_task_basic_info:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		rcsaTaskBasicInfoService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "RCSA评估任务信息表-批量删除")
	@Operation(summary="RCSA评估任务信息表-批量删除")
	@RequiresPermissions("rcsa.task:rcsa_task_basic_info:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.rcsaTaskBasicInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "RCSA评估任务信息表-通过id查询")
	@Operation(summary="RCSA评估任务信息表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<RcsaTaskBasicInfo> queryById(@RequestParam(name="id",required=true) String id) {
		RcsaTaskBasicInfo rcsaTaskBasicInfo = rcsaTaskBasicInfoService.getById(id);
		if(rcsaTaskBasicInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(rcsaTaskBasicInfo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param rcsaTaskBasicInfo
    */
    @RequiresPermissions("rcsa.task:rcsa_task_basic_info:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RcsaTaskBasicInfo rcsaTaskBasicInfo) {
        return super.exportXls(request, rcsaTaskBasicInfo, RcsaTaskBasicInfo.class, "RCSA评估任务信息表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("rcsa.task:rcsa_task_basic_info:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RcsaTaskBasicInfo.class);
    }

	@GetMapping(value = "/queryInherentRiskLevelHistory")
	public Result<IPage<RcsaTaskBasicInfo>> queryInherentRiskLevelHistory(RcsaTaskBasicInfo rcsaTaskBasicInfo) {
		Page<RcsaTaskBasicInfo> page = new Page<RcsaTaskBasicInfo>(1, 1);
		IPage<RcsaTaskBasicInfo> pageList = rcsaTaskBasicInfoService.queryInherentRiskLevelHistory(page, rcsaTaskBasicInfo);
		return Result.OK(pageList);
	}

	 @GetMapping(value = "/queryInherentRiskLevelFH")
	 public Result<IPage<RcsaTaskBasicInfo>> queryInherentRiskLevelFH(RcsaTaskBasicInfo rcsaTaskBasicInfo) {
		 Page<RcsaTaskBasicInfo> page = new Page<RcsaTaskBasicInfo>(1, 150);
		 IPage<RcsaTaskBasicInfo> pageList = rcsaTaskBasicInfoService.queryInherentRiskLevelFH(page, rcsaTaskBasicInfo);
		 return Result.OK(pageList);
	 }




}
