import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/rule/assess/year/task/list',
  generateTaskCode = '/rule/assess/year/task/generateTaskCode',
  save = '/rule/assess/year/task/add',
  edit = '/rule/assess/year/task/edit',
  submit = '/rule/assess/year/task/submitTasks',
  urge = '/rule/assess/year/task/urgeTasks',
  deleteOne = '/rule/assess/year/task/delete',
  deleteBatch = '/rule/assess/year/task/deleteBatch',
  queryById = '/rule/assess/year/task/queryById',
  importExcel = '/rule/assess/year/task/importExcel',
  exportXls = '/rule/assess/year/task/exportXls',

  submitRequest = '/rule/assess/year/task/submitRequest',
  process = '/rule/assess/year/task/process',
}

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 生成任务编号
 */
export const generateTaskCode = () => defHttp.get({ url: Api.generateTaskCode }, { isTransformResponse: false });

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 提交评估任务
 * @param ids 任务ID列表
 */
export const submitTasks = (ids: string[], handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认提交',
    content: '是否提交选中评估任务？',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp
        .post(
          {
            url: Api.submit,
            data: { ids },
          },
          { joinParamsToUrl: true }
        )
        .then(() => {
          handleSuccess();
        });
    },
  });
};

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp
        .delete(
          {
            url: Api.deleteBatch,
            data: params,
          },
          { joinParamsToUrl: true }
        )
        .then(() => {
          handleSuccess();
        });
    },
  });
};

/**
 * 通过id查询
 * @param id 任务ID
 */
export const queryById = (id: string) =>
  defHttp.get({
    url: Api.queryById,
    params: { id },
  });

/**
 * 督办评估任务
 * @param ids 任务ID列表
 */
export const urgeTasks = (ids: string[]) =>
  defHttp.post({
    url: Api.urge,
    params: { ids },
  });

/**
 * 提交审核
 * @param params
 * @param handleSuccess
 */
export const submitRequest = (params: any, handleSuccess: Function) => {
  createConfirm({
    iconType: 'warning',
    title: '确认提交审核',
    content: '是否对选中数据提交审核?',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      return defHttp
        .post(
          {
            url: Api.submitRequest,
            data: params,
          },
          { joinParamsToUrl: true }
        )
        .then(() => {
          handleSuccess();
        });
    },
  });
};

/**
 * 处理过程
 * @param params
 */
export const getProcess = (params: any) => {
  return defHttp.get({ url: Api.process, params }, { isTransformResponse: false, joinParamsToUrl: true });
};
