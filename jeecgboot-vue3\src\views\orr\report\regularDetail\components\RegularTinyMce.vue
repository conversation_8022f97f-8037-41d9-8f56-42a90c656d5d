<template>
  <div style="height: 100%; overflow: hidden">
    <editor
        v-model="myValue"
        :init="init"
        :enabled="enabled"
        :id="tinymceId"
    />
    <!-- 线索提示工具 -->
    <div v-if="activeClue"
         class="clue-tooltip"
         :style="tooltipStyle"
         @mouseenter="handleTooltipMouseEnter"
         @mouseleave="handleTooltipMouseLeave">
      <div class="tooltip-section">
        <div class="tooltip-label">反馈部门/机构：</div>
        <div class="tooltip-value">{{ activeClue.clueReceivingDepartment_dictText }}</div>
      </div>
      <div class="tooltip-section">
        <div class="tooltip-label">反馈内容：</div>
        <div class="tooltip-content" v-html="activeClue.clueFeedbackEnter"></div>
      </div>
      <div class="tooltip-footer" @click="copyFeedbackText">
        <!-- <a-button  preIcon="ant-design:copy-outlined" size="small" @click="copyFeedbackText" block>
          复制
        </a-button> -->
       复制<CopyOutlined />
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed, reactive, watch, ref, nextTick, onMounted} from "vue"; //全屏
import * as echarts from 'echarts'
import {saveAs} from 'file-saver';
import {Document, Paragraph, HeadingLevel, ImageRun, Packer, TextRun} from "docx";

import tinymce from "tinymce/tinymce";
import Editor from "@tinymce/tinymce-vue";
import "tinymce/icons/default/icons";
import "tinymce/models/dom"; // 一定要引入
import "tinymce/themes/silver"; // 界面UI主题
import "tinymce/plugins/image";
import "tinymce/plugins/table";
import "tinymce/plugins/lists"; // 列表插件
import "tinymce/plugins/wordcount"; // 文字计数
import "tinymce/plugins/preview"; // 预览
import "tinymce/plugins/emoticons"; // emoji表情
import "tinymce/plugins/emoticons/js/emojis.js"; //必须引入这个文件才有表情图库
import "tinymce/plugins/code"; // 编辑源码
import "tinymce/plugins/link"; // 链接插件
import "tinymce/plugins/advlist"; //高级列表
import "tinymce/plugins/codesample"; //代码示例
import "tinymce/plugins/autoresize"; // 自动调整编辑器大小
import "tinymce/plugins/quickbars"; // 光标处快捷提示
import "tinymce/plugins/nonbreaking"; //插入不间断空格
import "tinymce/plugins/searchreplace"; //查找替换
import "tinymce/plugins/autolink"; //自动链接
import "tinymce/plugins/directionality"; //文字方向
import "tinymce/plugins/visualblocks"; //显示元素范围
import "tinymce/plugins/visualchars"; //显示不可见字符
import "tinymce/plugins/charmap"; // 特殊符号
import "tinymce/plugins/nonbreaking"; //插入不间断空格
import "tinymce/plugins/insertdatetime"; //插入日期时间
import "tinymce/plugins/importcss"; //引入自定义样式的css文件
import "tinymce/plugins/accordion"; // 可折叠数据手风琴模式
import "tinymce/plugins/anchor"; //锚点
import "tinymce/plugins/fullscreen";
import {message} from 'ant-design-vue';
import {useMessage} from "@/hooks/web/useMessage"; // 引入消息提示
import {
  CopyOutlined,
} from '@ant-design/icons-vue';
const {createMessage} = useMessage();
const showChartPopup = ref(false);
const emits = defineEmits(["update:modelValue", "setHtml"]);
const chartInstances = ref([]);
const loading = ref(false);
const tinymceId = ref("vue-tinymce-" + +new Date() + ((Math.random() * 1000).toFixed(0) + ""));
// 添加锚点ID前缀常量
const ANCHOR_PREFIX = "clue-anchor-";
// 添加线索相关状态
const activeClue = ref(null);
const tooltipStyle = ref({});
const hoverTimeout = ref(null);
const isTooltipHovered = ref(false);

// 存储线索列表
const clueList = ref([]);

// 图表配置选项
const chartOptions = ref([
  {
    title: "销售折线图",
    type: "line",
    option: {
      title: {
        text: "销售趋势",
        left: "center"
      },
      xAxis: {
        type: "category",
        data: ["1月", "2月", "3月", "4月", "5月", "6月"]
      },
      yAxis: {
        type: "value"
      },
      series: [
        {
          data: [120, 200, 150, 80, 70, 110],
          type: "line",
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#1890ff'
          },
          itemStyle: {
            color: '#1890ff'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(24, 144, 255, 0.6)'
              }, {
                offset: 1, color: 'rgba(24, 144, 255, 0.1)'
              }]
            }
          }
        }
      ]
    }
  },
  {
    title: "产品柱状图",
    type: "bar",
    option: {
      title: {
        text: "产品销售统计",
        left: "center"
      },
      xAxis: {
        type: "category",
        data: ["产品A", "产品B", "产品C", "产品D", "产品E"]
      },
      yAxis: {
        type: "value"
      },
      series: [
        {
          data: [350, 210, 430, 180, 320],
          type: "bar",
          itemStyle: {
            color: function (params) {
              const colors = ['#1890ff', '#36cfc9', '#ff7a45', '#ffc53d', '#9254de'];
              return colors[params.dataIndex % colors.length];
            }
          }
        }
      ]
    }
  },
  {
    title: "市场份额饼图",
    type: "pie",
    option: {
      title: {
        text: "市场份额分布",
        left: "center"
      },
      tooltip: {
        trigger: "item",
        formatter: "{a} <br/>{b}: {c} ({d}%)"
      },
      series: [
        {
          name: "市场份额",
          type: "pie",
          radius: "50%",
          data: [
            {value: 35, name: "公司A"},
            {value: 25, name: "公司B"},
            {value: 20, name: "公司C"},
            {value: 15, name: "公司D"},
            {value: 5, name: "其他"}
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)"
            }
          }
        }
      ]
    }
  },
  {
    title: "任务完成率",
    type: "gauge",
    option: {
      title: {
        text: "任务完成率",
        left: "center"
      },
      tooltip: {
        formatter: "{a} <br/>{b} : {c}%"
      },
      series: [
        {
          name: "完成率",
          type: "gauge",
          progress: {
            show: true,
            width: 18
          },
          axisLine: {
            lineStyle: {
              width: 18
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            length: 15,
            lineStyle: {
              width: 2,
              color: '#999'
            }
          },
          axisLabel: {
            distance: 25,
            color: '#999',
            fontSize: 12
          },
          anchor: {
            show: true,
            showAbove: true,
            size: 25,
            itemStyle: {
              borderWidth: 10
            }
          },
          detail: {
            valueAnimation: true,
            fontSize: 30,
            offsetCenter: [0, '70%'],
            formatter: '{value}%'
          },
          data: [
            {
              value: 75,
              name: "完成率"
            }
          ]
        }
      ]
    }
  }
]);
const props = defineProps({
  value: {
    type: String,
    default: () => {
      return "";
    },
  },
  baseUrl: {
    type: String,
    default: "",
  },
  enabled: {
    type: Boolean,
    default: true,
  },
  editable_root: {
    type: Boolean,
    default: true,
  },
  plugins: {
    type: [String, Array],
    default: "importcss autoresize searchreplace autolink directionality code visualblocks visualchars fullscreen image link codesample table charmap nonbreaking anchor insertdatetime advlist lists wordcount charmap quickbars emoticons accordion",
  },
  knwlgId: {
    type: String,
  },
  toolbar: {
    type: [String, Array, Boolean],
    default: "undo redo | accordion accordionremove | blocks fontfamily fontsize| bold italic underline strikethrough ltr rtl  | align numlist bullist | link image customimage charts | table | lineheight outdent indent| forecolor backcolor removeformat | charmap emoticons | anchor codesample",
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  minHeight: {
    type: Number,
    default: 630,
  },
});

// 外部传递进来的数据变化
const myValue = computed({
  get() {
    return props.value;
  },
  set(val) {
    emits("update:modelValue", val);
  },
});

// 监听富文本中的数据变化
watch(
    () => myValue.value,
    () => {
      if (tinymce.activeEditor) {
        emits(
            "setHtml",
            tinymce.activeEditor.getContent({format: "text"}),
            myValue.value
        );
      }
    }
);

// 设置编辑器只读模式
watch(
    () => props.readonly,
    (newValue) => {
      nextTick(() => {
        if (tinymce.activeEditor) {
          tinymce.activeEditor.mode.set(newValue ? "readonly" : "design");
          let iframeDom = document.querySelector("iframe");
          if (iframeDom) {
            iframeDom.contentWindow.document.body.style.margin = newValue ? 0 : "16px";
          }
        }
      });
    },
    {immediate: true}
);

// 初始化编辑器
onMounted(() => {
  tinymce.init({
    license_key: 'gpl',
  });
});

// 打开图表配置弹窗
const openChartPopup = () => {
  showChartPopup.value = true;
  nextTick(() => {
    renderCharts();
  });
};

// 渲染所有图表预览
const renderCharts = () => {
  destroyChartInstances();
  chartOptions.value.forEach((chart, index) => {
    const container = document.getElementById(`chartPreview${index}`);
    if (container) {

      container.style.height = "250px";
      container.style.width = "100%";

      const chartInstance = echarts.init(container);
      chartInstances.value[index] = chartInstance;

      setTimeout(() => {
        chartInstance.setOption(chart.option);
        chartInstance.resize();
      }, 50);
    }
  });
};

// 销毁所有图表实例
const destroyChartInstances = () => {
  chartInstances.value.forEach(instance => {
    if (instance && typeof instance.dispose === 'function') {
      instance.dispose();
    }
  });
  chartInstances.value = [];
};

// 定义一个对象 init初始化
const init = reactive({
  setup: function (editor) {
    // 添加自定义图表按钮
    editor.ui.registry.addButton('charts', {
      icon: 'chart-bar',
      tooltip: '插入图表',
      onAction: function () {
        openChartPopup();
      }
    });
  },
  selector: "#" + tinymceId.value,
  language_url: "/tinymce/langs/zh_CN.js",
  language: "zh_CN",
  skin_url: "/tinymce/skins/ui/oxide",
  editable_root: props.editable_root,
  height: 600,
  branding: false,
  promotion: false,
  menubar: "edit view insert format tools table",
  paste_data_images: true,
  image_dimensions: false,
  plugins: props.plugins,
  toolbar: props.toolbar,
  convert_urls: false,
  link_default_target: "_blank",
  link_context_toolbar: true,
  quickbars_insert_toolbar: "image codesample table",
  quickbars_image_toolbar: "alignleft aligncenter alignright | rotateleft rotateright | imageoptions",
  editimage_toolbar: "rotateleft rotateright | flipv fliph | editimage imageoptions",
  font_family_formats: "Arial=arial,helvetica,sans-serif; 宋体=SimSun; 微软雅黑=Microsoft Yahei; Impact=impact,chicago;",
  font_size_formats: "11px 12px 14px 16px 18px 24px 36px 48px 64px 72px",
  image_caption: true,
  editimage_cors_hosts: ["picsum.photos"],
  noneditable_class: "mceNonEditable",
  toolbar_mode: "wrap",
  // content_style: "body { font-family:Helvetica,Arial,sans-serif; font-size:16px }p {margin:3px; line-height:24px;}",
  // 添加clue-anchor样式
  content_style: "body { font-family:Helvetica,Arial,sans-serif; font-size:16px } p {margin:3px; line-height:24px;} .clue-anchor { background-color: yellow;cursor: pointer;position: relative; position: relative; }",
  image_advtab: true,
  importcss_append: true,
  paste_webkit_styles: "all",
  paste_merge_formats: true,
  nonbreaking_force_tab: false,
  paste_auto_cleanup_on_paste: false,
  file_picker_types: "file",
  quickbars_selection_toolbar: "bold italic | quicklink h2 h3 blockquote quickimage quicktable",
  autoresize_bottom_margin: 20,
  min_height: props.minHeight,
  content_css: "/tinymce/skins/content/default/content.css",

  // 关键配置：允许data URL图片
  images_dataimg_filter: function (img) {
    return true; // 允许所有data URL图片
  },

  // 禁用自动清理
  valid_elements: '*[*]',
  valid_children: '+body[style]',
  extended_valid_elements: 'img[src|alt|title|width|height|style]',

  images_upload_handler: function (blobInfo, progress) {
    return new Promise((resolve, reject) => {
      let file = blobInfo.blob();
      if (file.size / 1024 / 1024 > 200) {
        reject({
          message: "上传失败，图片大小请控制在 200M 以内",
          remove: true,
        });
      }
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        resolve(reader.result);
      };
    });
  },
});

/**
 * 设置值
 * @param content
 */
const handleSetContent = (content) => {
  if (tinymce.activeEditor) {
    tinymce.activeEditor.setContent(content);
  }
};

/**
 * 获取值
 * @returns {string}
 */
const handleGetContent = () => {
  if (tinymce.activeEditor) {
    return tinymce.activeEditor.getContent();
  }
  return "";
};

/**
 * 获取选中文字
 */
const handleGetSelectedText = () => {
  if (tinymce.activeEditor) {
    return tinymce.activeEditor.selection.getContent({format: 'text'});
  }
  return '';
};

/**
 * 在当前位置插入锚点
 * @returns {string} 锚点ID
 */
const insertAnchor = () => {
  if (!tinymce.activeEditor) return null;

  const editor = tinymce.activeEditor;
  const id = `${ANCHOR_PREFIX}${Date.now()}-${Math.floor(Math.random() * 10000)}`;

  // 获取选中的内容
  const selectedContent = editor.selection.getContent();

  // 创建包裹选中内容的span元素
  const anchorNode = editor.dom.create('span', {
    id: id,
    'class': 'clue-anchor'
  });

  // 用新创建的span包裹选中的内容
  editor.selection.setContent(anchorNode.outerHTML);

  // 将选中内容放入新创建的span中
  const newAnchor = editor.dom.get(id);
  if (newAnchor) {
    newAnchor.innerHTML = selectedContent;

    // 添加鼠标事件监听
    newAnchor.addEventListener('mouseenter', (e) => handleAnchorHover(e, id));
    newAnchor.addEventListener('mouseleave', () => activeClue.value = null);
  }

  return id;
};

/**
 * 处理锚点悬停事件
 * @param {Event} e - 鼠标事件
 * @param {string} anchorId - 锚点ID
 */
const handleAnchorHover = (e, anchorId) => {
  // 清除之前的超时
  clearTimeout(hoverTimeout.value);

  const clue = clueList.value.find(item => item.anchorId === anchorId);
  if (!clue) return;

  activeClue.value = clue;

  // 更新工具提示位置
  const editorContainer = document.getElementById(tinymceId.value);
  const rect = editorContainer.getBoundingClientRect();

  tooltipStyle.value = {
    top: `${e.clientY - rect.top - 50}px`,
    left: `${e.clientX - rect.left}px`,
    display: 'block'
  };

  // 标记工具提示未被悬停
  isTooltipHovered.value = false;
};

/**
 * 重置工具提示
 */
const resetTooltip = () => {
  // 设置延迟隐藏
  hoverTimeout.value = setTimeout(() => {
    if (!isTooltipHovered.value) {
      activeClue.value = null;
    }
  }, 1000);
};

// 添加提示工具的鼠标事件处理
const handleTooltipMouseEnter = () => {
  isTooltipHovered.value = true;
  clearTimeout(hoverTimeout.value);
};

const handleTooltipMouseLeave = () => {
  isTooltipHovered.value = false;
  activeClue.value = null;
};

/**
 * 复制反馈文本（去除HTML标签）
 */
const copyFeedbackText = () => {
  if (!activeClue.value) return;

  // 创建临时div来提取纯文本
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = activeClue.value.clueFeedbackEnter;
  const plainText = tempDiv.textContent || tempDiv.innerText;

  // 复制到剪贴板
  navigator.clipboard.writeText(plainText).then(() => {
    createMessage.success("反馈内容已复制！");
  }).catch(err => {
    console.error('复制失败:', err);
    createMessage.warning('反馈内容复制失败');
  });
};

/**
 * 高亮显示线索锚点
 * @param {Array} clues - 线索列表
 */
const highlightClueAnchors = (clues) => {
  clueList.value = clues;

  if (!tinymce.activeEditor) return;

  const editor = tinymce.activeEditor;
  const contentDoc = editor.getDoc();

  // 清除之前的事件监听
  const anchors = contentDoc.querySelectorAll('.clue-anchor');
  anchors.forEach(anchor => {
    anchor.removeEventListener('mouseenter', handleAnchorHover);
    anchor.removeEventListener('mouseleave', resetTooltip);
  });

  // 为当前线索的锚点添加事件监听
  clues.forEach(clue => {
    const anchor = contentDoc.getElementById(clue.anchorId);
    if (anchor) {
      // 添加鼠标事件监听
      anchor.addEventListener('mouseenter', (e) => handleAnchorHover(e, clue.anchorId));
      anchor.addEventListener('mouseleave', resetTooltip);
    }
  });
};

defineExpose({
  handleSetContent,
  handleGetContent,
  handleGetSelectedText,
  insertAnchor,
  highlightClueAnchors,
});
</script>

<style lang="scss" scoped>
:deep(.tox-tinymce) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;

  .tox-statusbar {
    display: none;
  }
}

.popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.popup-content {
  background-color: white;
  padding: 25px;
  border-radius: 8px;
  max-width: 900px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25px;
  margin: 20px 0;
}

.chart-item {
  position: relative;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fff;

  &:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
    border-color: #1890ff;
  }
}

.chart-preview {
  height: 250px;
  width: 100%;
}

.chart-title {
  padding: 12px;
  text-align: center;
  font-weight: 600;
  background-color: #f9f9f9;
  border-top: 1px solid #eee;
  color: #333;
}

.chart-checkbox {
  position: absolute;
  top: -290px;
  // right: 15px;
  width: 22px;
  height: 22px;
  border: 2px solid #ccc;
  background-color: white;
  border-radius: 50%;
  z-index: 10;

  &.selected {
    background-color: #1890ff;
    border-color: #1890ff;
    position: relative;
  }

  &.selected::after {
    content: "✓";
    color: white;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 14px;
    font-weight: bold;
  }
}

.popup-footer {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.popup-footer button {
  padding: 10px 25px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  font-size: 15px;
  transition: all 0.3s;
}

.popup-footer button:first-child {
  background-color: #1890ff;
  color: white;

  &:hover {
    background-color: #40a9ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  }
}

.popup-footer button:last-child {
  background-color: #f5f5f5;
  color: #666;

  &:hover {
    background-color: #e6e6e6;
  }
}

h3 {
  margin-top: 0;
  color: #1a3353;
  text-align: center;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
  font-size: 22px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chart-grid {
    grid-template-columns: 1fr;
  }

  .popup-content {
    padding: 15px;
  }

  .chart-preview {
    height: 200px;
  }
}

.debug-container {
  position: absolute;
  top: 44px;
  bottom: 20px;
  left: 410px;
  z-index: 1000;
}

.debug-btn {
  padding: 8px 16px;
  font-size: 14px;
  background-color: white;
  color: black;
  border: none;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background-color: gray;
  }
}

/* 添加线索工具提示样式 */
.clue-tooltip {
  position: absolute;
  z-index: 10000;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 16px;
  max-width: 500px;
  min-width: 300px;
  pointer-events: auto;

  .tooltip-section {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .tooltip-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    font-size: 14px;
  }

  .tooltip-value {
    font-size: 14px;
    color: #555;
    padding-left: 8px;
  }

  .tooltip-content {
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    padding: 10px;
    max-height: 200px;
    overflow-y: auto;
    font-size: 13px;
    line-height: 1.6;
    background-color: #fafafa;

    ::v-deep img {
      max-width: 100%;
    }

    ::v-deep p {
      margin: 5px 0;
    }
  }

  .tooltip-footer {
    margin-top: 12px;
    text-align: left;
    color: orange;
    .ant-btn {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
