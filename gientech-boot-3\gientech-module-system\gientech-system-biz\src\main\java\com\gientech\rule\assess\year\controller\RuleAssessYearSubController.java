package com.gientech.rule.assess.year.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.common.process.enitity.CommonProcess;
import com.gientech.common.process.service.ICommonProcessService;
import com.gientech.rule.assess.year.entity.RuleAssessYearSub;
import com.gientech.rule.assess.year.service.IRuleAssessYearSubService;
import com.gientech.rule.assess.year.vo.RuleAssessYearSubVO;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 存量制度全面评估任务-子任务
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-29
 */
@Tag(name = "存量制度全面评估任务-子任务")
@RestController
@RequestMapping("/rule/assess/year/sub")
@Slf4j
public class RuleAssessYearSubController extends JeecgController<RuleAssessYearSub, IRuleAssessYearSubService> {

    private IRuleAssessYearSubService ruleAssessYearSubService;

    @Autowired
    public void setRuleAssessYearSubService(IRuleAssessYearSubService ruleAssessYearSubService) {
        this.ruleAssessYearSubService = ruleAssessYearSubService;
    }

    @Autowired
    private IWorkflowInstanceService instanceService;

    @Autowired
    private IWorkflowTaskService workflowTaskService;

    @Autowired
    private ICommonProcessService processService;

    private final String businessKey = "ruleAssessYearSub";

    /**
     * 分页列表查询
     *
     * @param ruleAssessYearSub 实体参数
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @param req 请求参数
     * @return 查询结果
     */
    @GetMapping(value = "/list")
    @Operation(summary = "分页列表查询")
    public Result<IPage<RuleAssessYearSubVO>> queryPageList(RuleAssessYearSubVO ruleAssessYearSub,
                                                            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                            HttpServletRequest req) {
        QueryWrapper<RuleAssessYearSubVO> queryWrapper = new QueryWrapper<>();
        if (ruleAssessYearSub.getTaskCode() != null) {
            queryWrapper.like("sub.task_code", ruleAssessYearSub.getTaskCode());
        }
        if (ruleAssessYearSub.getTaskName() != null) {
            queryWrapper.like("main.task_name", ruleAssessYearSub.getTaskName());
        }
        if (ruleAssessYearSub.getEndTime() != null) {
            queryWrapper.eq("main.end_time", ruleAssessYearSub.getEndTime());
        }
        Page<RuleAssessYearSubVO> page = new Page<>(pageNo, pageSize);
        IPage<RuleAssessYearSubVO> pageList = ruleAssessYearSubService.selectVOPage(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 通过id查询
     *
     * @param id 实体对象主键
     * @return 查询结果
     */
    @GetMapping(value = "/queryById")
    @Operation(summary = "通过id查询")
    public Result<RuleAssessYearSub> queryById(@RequestParam(name = "id", required = true) String id) {
        RuleAssessYearSub ruleAssessYearSub = ruleAssessYearSubService.getById(id);
        if (ruleAssessYearSub == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(ruleAssessYearSub);
    }

    /**
     * 提交审核
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "提交审核")
    @Operation(summary = "提交审核")
    @PostMapping(value = "/submitRequest")
    @RequiresPermissions("rule.assess.year:sub:submit")
    public Result<String> submitRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessYearSub> ruleAssessYearSubList = service.listByIds(idList);
        for (RuleAssessYearSub ruleAssessYearSub : ruleAssessYearSubList) {
            // 创建审核工作流
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, ruleAssessYearSub);

            instanceService.createWorkflowInstance(businessKey, ruleAssessYearSub.getId(), variables);
        }
        processService.saveProcessBatch(businessKey, idList, "提交审核");
        return Result.ok("提交审核成功!");
    }
    
    /**
     * 审核通过
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "审核通过")
    @Operation(summary = "审核通过")
    @PostMapping(value = "/passRequest")
    @RequiresPermissions("rule.assess.year:sub:examine")
    public Result<String> passRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessYearSub> ruleAssessYearSubList = service.listByIds(idList);
        for (RuleAssessYearSub ruleAssessYearSub : ruleAssessYearSubList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, ruleAssessYearSub.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approve", true);
                executeVariables.put(businessKey, ruleAssessYearSub);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }
        processService.saveProcessBatch(businessKey, idList, "审核通过");
        return Result.ok("审核通过成功!");
    }

    /**
     * 审核退回
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "审核退回")
    @Operation(summary = "审核退回")
    @PostMapping(value = "/givebackRequest")
    @RequiresPermissions("rule.assess.year:sub:examine")
    public Result<String> givebackRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessYearSub> ruleAssessYearSubList = service.listByIds(idList);
        for (RuleAssessYearSub ruleAssessYearSub : ruleAssessYearSubList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, ruleAssessYearSub.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approve", false);
                executeVariables.put(businessKey, ruleAssessYearSub);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }

        processService.saveProcessBatch(businessKey, idList, "审核退回");
        return Result.ok("审核退回成功!");
    }

    /**
     * 处理过程
     *
     * @param id 数据录入对象id
     * @return 处理过程列表
     */
    @AutoLog(value = "处理过程")
    @Operation(summary = "处理过程")
    @GetMapping(value = "/process")
//    @RequiresPermissions("rule.assess.year:sub:process")
    public Result<IPage<CommonProcess>> process(@RequestParam(name = "id", required = true) String id) {
        // 借用分页的字典翻译
        IPage<CommonProcess> page = new Page<>();
        page.setRecords(processService.getProcess(businessKey, id));
        return Result.ok(page);
    }

    /**
     * 根据主任务ID查询子任务列表（支持分页）
     *
     * @param yearId 主任务ID
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @return 分页结果
     */
    @GetMapping(value = "/getByYearId")
    @Operation(summary = "根据主任务ID查询子任务列表")
    public Result<IPage<RuleAssessYearSub>> getByYearId(@RequestParam String yearId,
                                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<RuleAssessYearSub> pageList = ruleAssessYearSubService.getByYearId(yearId, pageNo, pageSize);
        return Result.OK(pageList);
    }

}
