package com.gientech.ldc.manage.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gientech.ldc.manage.entity.LdcEventManage;
import com.gientech.ldc.manage.entity.LdcEventManageForImport;
import com.gientech.ldc.manage.entity.LdcEventRecycleDetails;
import com.gientech.ldc.manage.entity.LdcRuleRel;
import com.gientech.rcsa.plan.entity.RcsaPlanManage;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;

import java.util.List;
import java.util.Map;

/**
 * @Description: 损失事件管理表
 * @Author: jeecg-boot
 * @Date:   2025-04-29
 * @Version: V1.0
 */
public interface ILdcEventManageService extends IService<LdcEventManage> {

    void add(LdcEventManage ldcEventManage) throws Exception;

    Result<List<LdcEventRecycleDetails>> getRecycleListByManageId(LdcEventManage ldcEventManage);

    void edit(LdcEventManage ldcEventManage) throws Exception;

    void submitFromForm(LdcEventManage ldcEventManage) throws Exception;

    void submitFormList(LdcEventManage ldcEventManage) throws Exception;

    void handleWithdrawToFirst(LdcEventManage ldcEventManage) throws Exception;

    void getAuditListByRole(List<String> statuList, List<String> departList, List<String> processFlagList);

    void auditManagePass(LdcEventManage ldcEventManage) throws Exception;

    void auditManageBack(LdcEventManage ldcEventManage) throws Exception;

    Result<LdcEventManage> judgeCanMerge(LdcEventManage ldcEventManage) throws Exception;

    Result<LdcEventManage> cancelMerge(LdcEventManage ldcEventManage);

    Result<LdcEventManage> getEventCode();

    Page<LdcEventManage> queryAuditedList(Page<LdcEventManage> page, LdcEventManage ldcEventManage);

    void auditWithdraw(LdcEventManage ldcEventManage) throws Exception;

    void updateStatusToUpdate(LdcEventManage ldcEventManage);

    Result<String> batchRepeal(LdcEventManage ldcEventManage) throws Exception;

    void downloadZip(String id, HttpServletResponse response) throws Exception;

    IPage<LdcEventManage> selectEventPageList(Page<LdcEventManage> page, QueryWrapper<LdcEventManage> queryWrapper, QueryWrapper<LdcEventManage> queryWrapper2, String orgId);

    IPage<LdcEventManage> selectEventPageListByParam(Page<LdcEventManage> page,
                                                     List<String> processFlagList,
                                                     LdcEventManage ldcEventManage,
                                                     String orgId,
                                                     List<String> fillDepartList);

    Result<String> judgeCanOperate(LdcEventManage ldcEventManage, List<String> processFlagList, List<String> fillDepartList);

    Result<String> lockEvent(LdcEventManage ldcEventManage);

    Result<String> judgeSeverityLevel(LdcEventManage ldcEventManage);

    Result<String> relClueAndEvent(LdcEventManage ldcEventManage);

    Result<List<LdcEventRecycleDetails>> montageRecycleDetail(LdcEventManage ldcEventManage);

    void exportXls(HttpServletRequest request,
                   HttpServletResponse response, LdcEventManage ldcEventManage) throws Exception;

    Result<?> importExcel(HttpServletRequest request,
                          HttpServletResponse response,
                          Class<LdcEventManageForImport> ldcEventManageClass) throws Exception;

    Result<String> queryCurrencyRate(LdcEventRecycleDetails detail);
    Map<String, Object> querySubjectList();

    IPage<LdcEventManage> queryLdcRelRcsaList(Page<LdcEventManage> page, LdcEventManage ldcEventManage);

    List<LdcRuleRel> getInteriorList(List<String> ldcIdList);

    List<LdcRuleRel> getExternalList(List<String> ldcIdList);

    String judgeLdcRelInfo(RcsaPlanManage planManage);

    void resetOrder();
}
