<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="128">
          <a-col :span="8">
            <a-form-item name="taskCode">
              <template #label>
                <span title="任务编号">任务编号</span>
              </template>
              <a-input placeholder="请输入" v-model:value="queryParam.taskCode" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="taskName">
              <template #label>
                <span title="任务名称">任务名称</span>
              </template>
              <a-input placeholder="请输入" v-model:value="queryParam.taskName" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="endTime">
              <template #label>
                <span title="结束时间">结束时间</span>
              </template>
              <a-date-picker valueFormat="YYYY-MM-DD" placeholder="请选择" v-model:value="queryParam.endTime" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <span style="float: right; overflow: hidden" class="table-page-search-submitButtons">
              <a-button preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
              <a-button type="primary" preIcon="ant-design:search-outlined" @click="reload" style="margin-left: 8px">查询</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAssess" preIcon="ant-design:form-outlined" :disabled="selectedRowKeys.length === 0"
          >存量制度评估
        </a-button>
        <a-button type="default" @click="handleSubmit" preIcon="ant-design:upload-outlined" :disabled="selectedRowKeys.length === 0">提交 </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>

    <!-- 制度评估弹窗 -->
    <SystemListModal ref="systemListModalRef" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { list, submitRequest } from './RuleAssessYearSub.api';
  import SystemListModal from './components/SystemListModal.vue';
  import { listColumns } from '@/views/rule/assess/year/sub/RuleAssessYearSub.data';

  const formRef = ref();
  const queryParam = reactive<any>({});
  const systemListModalRef = ref();
  const { createMessage } = useMessage();

  //注册table数据
  const { tableContext } = useListPage({
    tableProps: {
      title: '存量制度年度评估',
      api: list,
      columns: listColumns,
      canResize: false,
      useSearchForm: false,
      actionColumn: {
        width: 120,
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
      },
      tableSetting: {
        redo: false,
        size: false,
        setting: false,
      },
      beforeFetch: async (params) => {
        Object.assign(params, queryParam);
      },
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;
  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  /**
   * 存量制度评估
   */
  function handleAssess() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请选择要评估的任务');
      return;
    }
    if (selectedRowKeys.value.length > 1) {
      createMessage.warning('请选择一个任务进行评估');
      return;
    }
    let taskRecord = selectedRows.value[0];
    // 获取选中的记录数据
    if (taskRecord) {
      // 打开制度评估弹窗
      systemListModalRef.value.open(taskRecord);
    } else {
      createMessage.error('未找到选中的任务记录');
    }
  }

  /**
   * 提交审核
   */
  async function handleSubmit() {
    let ids: any[] = [];
    let rows = selectedRows.value;
    let findRow = rows.find((row) => {
      return row.taskStatus !== '2' || row.processStatus !== '1';
    });
    if (findRow) {
      createMessage.warning('请选择完成录入的草稿数据!');
      return;
    }
    ids = selectedRowKeys.value;
    submitRequest({ ids: ids }, handleSuccess);
  }

  /**
   * 查看详情
   */
  function handleDetail(record: any) {
    systemListModalRef.value.open(record);
  }

  /**
   * 操作栏
   */
  function getTableAction(record: any) {
    return [
      {
        label: '查看',
        onClick: handleDetail.bind(null, record),
      },
    ];
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }

  /**
   * 操作成功回调
   */
  function handleSuccess() {
    reload();
  }
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
    .ant-form-item:not(.ant-form-item-with-help) {
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),
    :deep(.ant-input-number) {
      width: 100%;
    }
  }
</style>
