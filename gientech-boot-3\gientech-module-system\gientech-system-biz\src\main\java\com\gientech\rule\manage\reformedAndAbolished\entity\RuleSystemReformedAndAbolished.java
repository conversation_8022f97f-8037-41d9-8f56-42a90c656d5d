package com.gientech.rule.manage.reformedAndAbolished.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 内外规-制度立改废任务管理
 * @Author: jeecg-boot
 * @Date:   2025-07-11
 * @Version: V1.0
 */
@Data
@TableName("rule_system_reformed_and_abolished")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="内外规-制度立改废任务管理")
public class RuleSystemReformedAndAbolished implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	/**维护任务编号*/
	@Excel(name = "维护任务编号", width = 15)
    @Schema(description = "维护任务编号")
    private java.lang.String taskNum;
	/**维护任务类型*/
	@Excel(name = "维护任务类型", width = 15, dicCode = "rule_system_maintenance_tasks_type")
	@Dict(dicCode = "rule_system_maintenance_tasks_type")
    @Schema(description = "维护任务类型")
    private java.lang.String taskType;
	/**维护任务状态*/
	@Excel(name = "维护任务状态", width = 15, dicCode = "rule_system_maintenance_tasks_status")
	@Dict(dicCode = "rule_system_maintenance_tasks_status")
    @Schema(description = "维护任务状态")
    private java.lang.String taskStatus;
	/**流程状态*/
	@Excel(name = "流程状态", width = 15, dicCode = "rule_system_maintenance_process_status")
	@Dict(dicCode = "rule_system_maintenance_process_status")
    @Schema(description = "流程状态")
    private java.lang.String processStatus;
	/**新建/修订原因*/
	@Excel(name = "新建/修订原因", width = 15, dicCode = "rule_system_maintenance_new_revision_cause")
	@Dict(dicCode = "rule_system_maintenance_new_revision_cause")
    @Schema(description = "新建/修订原因")
    private java.lang.String newRevisionCause;
	/**是否计划内*/
	@Excel(name = "是否计划内", width = 15, dicCode = "kri_alert_is_launch_assess")
	@Dict(dicCode = "kri_alert_is_launch_assess")
    @Schema(description = "是否计划内")
    private java.lang.String isPlaned;
	/**关联计划编号*/
	@Excel(name = "关联计划编号", width = 15)
    @Schema(description = "关联计划编号")
    private java.lang.String associatedPlanNumber;
	/**关联计划编号*/
	@Excel(name = "关联部门明细计划id", width = 15)
	@Schema(description = "关联部门明细计划id")
	private java.lang.String associatedPlanId;
	/**制度发文机构*/
	@Excel(name = "制度发文机构", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "制度发文机构")
    private java.lang.String systemIssuingBody;
	/**制度发文部门（一级）*/
	@Excel(name = "制度发文部门（一级）", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "制度发文部门（一级）")
    private java.lang.String issuingDeptOne;
	/**制度发文部门（二级）*/
	@Excel(name = "制度发文部门（二级）", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "制度发文部门（二级）")
    private java.lang.String issuingDeptTwo;
	/**制度管理人*/
	@Excel(name = "制度管理人", width = 15)
    @Schema(description = "制度管理人")
    private java.lang.String systemManager;
	/**制度管理人账号*/
	@Excel(name = "制度管理人账号", width = 15)
    @Schema(description = "制度管理人账号")
    private java.lang.String systemManagerUsername;
	/**是否同步废止相关制度*/
	@Excel(name = "是否同步废止相关制度", width = 15, dicCode = "kri_alert_is_launch_assess")
	@Dict(dicCode = "kri_alert_is_launch_assess")
    @Schema(description = "是否同步废止相关制度")
    private java.lang.String abolitionSystem;
	/**待废止制度名称*/
	@Excel(name = "待废止制度名称", width = 15)
    @Schema(description = "待废止制度名称")
    private java.lang.String abolitionName;
	/**是否涉及消费者权益保护*/
	@Excel(name = "是否涉及消费者权益保护", width = 15, dicCode = "kri_alert_is_launch_assess")
	@Dict(dicCode = "kri_alert_is_launch_assess")
    @Schema(description = "是否涉及消费者权益保护")
    private java.lang.String consumerRights;
	/**消保审批证明*/
	@Excel(name = "消保审批证明", width = 15)
    @Schema(description = "消保审批证明")
    private java.lang.String qpprovalProof;
	/**是否需要绘制流程图*/
	@Excel(name = "是否需要绘制流程图", width = 15, dicCode = "kri_alert_is_launch_assess")
	@Dict(dicCode = "kri_alert_is_launch_assess")
    @Schema(description = "是否需要绘制流程图")
    private java.lang.String whtherDraw;
	/**制度文号*/
	@Excel(name = "制度文号", width = 15)
    @Schema(description = "制度文号")
    private java.lang.String documentNumber;
	/**制度版本号*/
	@Excel(name = "制度版本号", width = 15)
    @Schema(description = "制度版本号")
    private java.lang.String version;
	/**制度名称*/
	@Excel(name = "制度名称", width = 15)
    @Schema(description = "制度名称")
    private java.lang.String systemName;
	/**发布日期*/
	@Excel(name = "发布日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "发布日期")
    private java.util.Date releaseDate;
	/**实施日期*/
	@Excel(name = "实施日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "实施日期")
    private java.util.Date implementationDate;
	/**制度层级*/
	@Excel(name = "制度层级", width = 15, dicCode = "rule_system_maintenance_system_level")
	@Dict(dicCode = "rule_system_maintenance_system_level")
    @Schema(description = "制度层级")
    private java.lang.String systemLevel;
	/**适用范围*/
	@Excel(name = "适用范围", width = 15, dicCode = "rule_system_maintenance_scope_application")
	@Dict(dicCode = "rule_system_maintenance_scope_application")
    @Schema(description = "适用范围")
    private java.lang.String scopeApplication;
	/**制度密级*/
	@Excel(name = "制度密级", width = 15, dicCode = "rule_system_maintenance_system_secrecy")
	@Dict(dicCode = "rule_system_maintenance_system_secrecy")
    @Schema(description = "制度密级")
    private java.lang.String systemSecrecy;
	/**是否需要征求意见*/
	@Excel(name = "是否需要征求意见", width = 15, dicCode = "kri_alert_is_launch_assess")
	@Dict(dicCode = "kri_alert_is_launch_assess")
    @Schema(description = "是否需要征求意见")
    private java.lang.String askComments;
	/**是否需要上会*/
	@Excel(name = "是否需要上会", width = 15, dicCode = "kri_alert_is_launch_assess")
	@Dict(dicCode = "kri_alert_is_launch_assess")
    @Schema(description = "是否需要上会")
    private java.lang.String isMeeting;
	/**制度审议机构*/
	@Excel(name = "制度审议机构", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "制度审议机构")
    private java.lang.String dliberativeBody;
	/**审议结论*/
	@Excel(name = "审议结论", width = 15, dicCode = "rule_system_maintenance_dliberative_conclusion")
	@Dict(dicCode = "rule_system_maintenance_dliberative_conclusion")
    @Schema(description = "审议结论")
    private java.lang.String dliberativeConclusion;
	/**审议通过时间*/
	@Excel(name = "审议通过时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "审议通过时间")
    private java.util.Date dliberativePassTime;
	/**审议记录文档*/
	@Excel(name = "审议记录文档", width = 15)
    @Schema(description = "审议记录文档")
    private java.lang.String dliberativeFile;
	/**交接原因*/
	@Excel(name = "交接原因", width = 15)
	@Schema(description = "交接原因")
	private java.lang.String handoverReason;
	/**待交接制度名称*/
	@Excel(name = "待交接制度名称", width = 15)
	@Schema(description = "待交接制度名称")
	private java.lang.String handoverAme;
	/**交接后制度发文机构*/
	@Excel(name = "交接后制度发文机构", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
	@Schema(description = "交接后制度发文机构")
	private java.lang.String afterIssuingBody;
	/**交接后制度发文部门(一级)*/
	@Excel(name = "交接后制度发文部门(一级)", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
	@Schema(description = "交接后制度发文部门(一级)")
	private java.lang.String afterIssuingDeptOne;
	/**交接后制度发文部门(二级)*/
	@Excel(name = "交接后制度发文部门(二级)", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
	@Schema(description = "交接后制度发文部门(二级)")
	private java.lang.String afterIssuingDeptTwo;
	/**时效性*/
	@Excel(name = "时效性", width = 15)
	@Schema(description = "时效性0未生效1现行有效，2已废止")
	private java.lang.String timeliness;
	/**合规审核意见*/
	@Excel(name = "合规审核意见", width = 15)
	@Schema(description = "合规审核意见")
	private java.lang.String complianceOpinion;
	/**合规审核/审批意见*/
	@Excel(name = "合规审核/审批意见", width = 15)
	@Schema(description = "合规审核/审批意见")
	private java.lang.String complianceAuditOpinion;
	@Schema(description = "待交接ids")
	private String handoverIds;
	@TableField(exist = false)
	@Schema(description = "制度修订")
	private List<RuleSystemReformedAndAbolishedAmendment> amendments;
	@TableField(exist = false)
	@Schema(description = "征求意见")
	private List<RuleSystemReformedAndAbolishedAsk> measures;
	@TableField(exist = false)
	@Schema(description = "附件id")
	private String fileIds;
	@TableField(exist = false)
	@Schema(description = "关联外规")
	private List<RuleSystemReformedAndAbolishedExternal> exteriors;
	@TableField(exist = false)
	@Schema(description = "关联内规")
	private List<RuleSystemReformedAndAbolishedInterior> interiors;
	@TableField(exist = false)
	@Schema(description = "废止信息ids")
	private String abolitionIds;
	@TableField(exist = false)
	@Schema(description = "交接信息")
	private List<Connect> connects;
	@TableField(exist = false)
	@Schema(description = "交接信息")
	private List<Publish> publishs;
	@TableField(exist = false)
	private String ruleId;
	@TableField(exist = false)
	private String timelinessName;
	@TableField(exist = false)
	private String ldcId;
	@TableField(exist = false)
	private String issuingDeptOneName;
}
