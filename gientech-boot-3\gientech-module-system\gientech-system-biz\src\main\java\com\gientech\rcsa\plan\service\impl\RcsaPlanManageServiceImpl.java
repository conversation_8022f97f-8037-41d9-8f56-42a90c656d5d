package com.gientech.rcsa.plan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gientech.rcsa.history.entity.RcsaPlanManageHistory;
import com.gientech.rcsa.history.mapper.RcsaPlanManageHistoryMapper;
import com.gientech.rcsa.plan.entity.RcsaPlanManage;
import com.gientech.rcsa.plan.mapper.RcsaPlanManageMapper;
import com.gientech.rcsa.plan.service.IRcsaPlanManageService;
import com.gientech.rcsa.scheme.entity.RcsaSchemeManage;
import com.gientech.rcsa.scheme.entity.RcsaSchemeMatrixRel;
import com.gientech.rcsa.scheme.mapper.RcsaSchemeManageMapper;
import com.gientech.rcsa.scheme.mapper.RcsaSchemeMatrixRelMapper;
import com.gientech.sr.matrix.entity.SrRiskControlMatrix;
import com.gientech.sr.matrix.mapper.SrRiskControlMatrixMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.mapper.SysDepartMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Description: RCSA评估计划表
 * @Author: jeecg-boot
 * @Date:   2025-07-15
 * @Version: V1.0
 */
@Service
public class RcsaPlanManageServiceImpl extends ServiceImpl<RcsaPlanManageMapper, RcsaPlanManage> implements IRcsaPlanManageService {

    @Autowired
    private RcsaPlanManageMapper planManageMapper;
    @Autowired
    private RcsaPlanManageHistoryMapper historyMapper;
    @Autowired
    private SysDepartMapper departMapper;
    @Autowired
    private SrRiskControlMatrixMapper matrixMapper;
    @Autowired
    private RcsaSchemeManageMapper schemeManageMapper;
    @Autowired
    private RcsaSchemeMatrixRelMapper relMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(RcsaPlanManage rcsaPlanManage) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        // 生成编号
        String year = DateUtils.formatDate(new Date(), "yyyy");
        String code = planManageMapper.getMaxCodeIndex(year);
        String codeIndex = "01";
        if (StringUtils.isNotBlank(code)) {
            codeIndex = String.format("%02d", (Integer.parseInt(code) + 1));
        }
        rcsaPlanManage.setPlanCode(year+codeIndex);
        rcsaPlanManage.setCodeIndex(codeIndex);
        planManageMapper.insert(rcsaPlanManage);

        // 入库操作记录表
        RcsaPlanManageHistory history = new RcsaPlanManageHistory();
        history.setPlanManageId(rcsaPlanManage.getId());
        history.setIsAudit("0");
        history.setOperateTime(new Date());
        history.setState("1");
        history.setOperateUser(userName);
        history.setOperateType("新建");
        history.setOperateDepart(sysUser.getOrgId());
        historyMapper.insert(history);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submit(RcsaPlanManage planManage) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();

        planManage.setState("2");
        planManageMapper.updateById(planManage);
        // 入库操作记录表
        RcsaPlanManageHistory history = new RcsaPlanManageHistory();
        history.setPlanManageId(planManage.getId());
        history.setIsAudit("0");
        history.setOperateTime(new Date());
        history.setState("2");
        history.setOperateUser(userName);
        history.setOperateType("提交审核");
        history.setOperateDepart(sysUser.getOrgId());
        historyMapper.insert(history);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditPass(RcsaPlanManage planManage) throws Exception {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        planManage.setState("3");
        planManage.setEffectTime(new Date());
        planManageMapper.updateById(planManage);
        // 入库操作记录表
        RcsaPlanManageHistory history = new RcsaPlanManageHistory();
        history.setPlanManageId(planManage.getId());
        history.setIsAudit("1");
        history.setOperateTime(new Date());
        history.setState("3");
        history.setOperateUser(userName);
        history.setOperateType("审核通过");
        history.setOperateDepart(sysUser.getOrgId());
        historyMapper.insert(history);
        // 根据评估部门生成评估方案
        RcsaPlanManage manage = planManageMapper.selectById(planManage.getId());
        String evaluateDeparts[] = manage.getEvaluateDepart().split(",");
        // 获取内控合规部的主键
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("depart_name", "内控合规部");
        queryWrapper.eq("del_flag", "0");
        SysDepart leaderDepart = departMapper.selectOne(queryWrapper);
        int index = 1;
        for (String evaluateDepart : evaluateDeparts) {
            // 需要剔除父节点
            SysDepart sysDepart = departMapper.selectById(evaluateDepart);
            if (StringUtils.isBlank(sysDepart.getParentId())) {
                continue;
            }
            RcsaSchemeManage schemeManage = new RcsaSchemeManage();
            schemeManage.setPlanId(manage.getId());
            schemeManage.setPlanTitle(manage.getPlanTitle());
            schemeManage.setPlanType(manage.getPlanType());
            schemeManage.setPlanCode(manage.getPlanCode());
            schemeManage.setEvaluateEndDate(manage.getEvaluateEndDate());
            schemeManage.setEvaluateDepart(evaluateDepart);
            schemeManage.setLeadDepart(leaderDepart.getId());

            if (sysDepart.getDepartName().equals("内控合规部")) {
                schemeManage.setProcessFlag("1");
            } else if (sysDepart.getDepartName().endsWith("分行")) {
                schemeManage.setProcessFlag("3");
            } else {
                schemeManage.setProcessFlag("2");
            }
            // 生成方案编号
            String schemeCode = manage.getPlanCode() + "FA" + String.format("%02d", index);
            schemeManage.setSchemeCode(schemeCode);
            index++;

            schemeManageMapper.insert(schemeManage);
            // 判断是否是定期式，如果是，则查询本部门是否重要流程”为“是”的矩阵，且状态为启用
            if ("1".equals(manage.getPlanType())) {
                List<SrRiskControlMatrix> matrixList = matrixMapper.queryMatrixList(evaluateDepart, "1");
                int matrixIndex = 1;
                for (SrRiskControlMatrix matrix : matrixList) {
                    RcsaSchemeMatrixRel rel = new RcsaSchemeMatrixRel();
                    rel.setMatrixId(matrix.getId());
                    rel.setPlanId(planManage.getId());
                    rel.setSchemeId(schemeManage.getId());
                    rel.setIsEdit("0");
                    rel.setMatrixNum(matrix.getMatrixNum());
                    rel.setMatrixName(matrix.getMatrixName());
                    rel.setMatrixType("2");
                    rel.setSortNum(matrixIndex);
                    relMapper.insert(rel);
                    matrixIndex++;
                }
            } else {
                // 如果是触发式，就根据流程名称生成唯一的评估信息
                String matrixNames[] = manage.getMatrixName().split(",");
                for (String matrixName : matrixNames) {
                    SrRiskControlMatrix matrix = matrixMapper.queryMatrixInfoByName(matrixName);
                    if (matrix == null) {
                        throw new Exception("矩阵" + matrixName + "不存在，请检查");
                    }
                    RcsaSchemeMatrixRel rel = new RcsaSchemeMatrixRel();
                    rel.setMatrixId(matrix.getId());
                    rel.setPlanId(planManage.getId());
                    rel.setSchemeId(schemeManage.getId());
                    rel.setIsEdit("0");
                    rel.setMatrixNum(matrix.getMatrixNum());
                    rel.setMatrixName(matrix.getMatrixName());
                    rel.setMatrixType("2");
                    rel.setSortNum(1);
                    relMapper.insert(rel);
                }

                if (!"1".equals(manage.getRelModule())) {
                    // 拿到相关数据关联的东西
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditReject(RcsaPlanManage planManage) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        planManage.setState("4");
        planManageMapper.updateById(planManage);
        // 入库操作记录表
        RcsaPlanManageHistory history = new RcsaPlanManageHistory();
        history.setPlanManageId(planManage.getId());
        history.setIsAudit("1");
        history.setOperateTime(new Date());
        history.setState("4");
        history.setOperateUser(userName);
        history.setOperateType("审核退回");
        history.setOperateDepart(sysUser.getOrgId());
        historyMapper.insert(history);
    }
}
