<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="128">
          <a-col :span="8">
            <a-form-item name="taskCode">
              <template #label>
                <span title="任务编号">任务编号</span>
              </template>
              <JInput placeholder="请输入" v-model:value="queryParam.taskCode" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="taskName">
              <template #label>
                <span title="任务名称">任务名称</span>
              </template>
              <JInput placeholder="请输入" v-model:value="queryParam.taskName" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="taskLevel">
              <template #label>
                <span title="任务层级">任务层级</span>
              </template>
              <j-select-multiple placeholder="请选择" v-model:value="queryParam.taskLevel" dictCode="rule_assess_year_level" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="assessType">
              <template #label>
                <span title="评估类型">评估类型</span>
              </template>
              <j-select-multiple placeholder="请选择" v-model:value="queryParam.assessType" dictCode="rule_assess_pilot_assess_type" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="endTime">
              <template #label>
                <span title="结束时间">结束时间</span>
              </template>
              <a-date-picker valueFormat="YYYY-MM-DD" placeholder="请选择" v-model:value="queryParam.endTime" allow-clear />
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :span="8">
              <a-form-item name="assessOrgCode">
                <template #label>
                  <span title="参与评估机构/部门">参与评估机构/部门</span>
                </template>
                <j-select-dept placeholder="请选择" v-model:value="queryParam.assessOrgCode" checkStrictly allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="taskStatus">
                <template #label>
                  <span title="任务状态">任务状态</span>
                </template>
                <j-select-multiple placeholder="请选择" v-model:value="queryParam.taskStatus" dictCode="rule_assess_year_task_status" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="processStatus">
                <template #label>
                  <span title="流程状态">流程状态</span>
                </template>
                <j-select-multiple
                  placeholder="请选择"
                  v-model:value="queryParam.processStatus"
                  dictCode="rule_assess_pilot_process_status"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </template>
          <a-col :span="8">
            <span style="float: right; overflow: hidden" class="table-page-search-submitButtons">
              <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
              </a>
              <a-button preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
              <a-button type="primary" preIcon="ant-design:search-outlined" @click="reload" style="margin-left: 8px">查询</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'rule.assess.year:task:examine'" preIcon="ant-design:check-outlined" @click="handlePass">
          审核通过
        </a-button>
        <a-button type="default" v-auth="'rule.assess.year:task:examine'" preIcon="ant-design:export-outlined" @click="handleGiveback">
          审核退回
        </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 详情弹窗 -->
    <RuleAssessYearDetailModal ref="detailModalRef" @success="handleSuccess" />
    <ProcessModal ref="processRef" :getProcess="getProcess" />
  </div>
</template>

<script lang="ts" name="rule.assess.year-ruleAssessYear" setup>
  import { ref, reactive } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns } from '../RuleAssessYear.data';
  import { list } from '../RuleAssessYear.api';
  import RuleAssessYearDetailModal from '../components/RuleAssessYearDetailModal.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
  import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
  import { JInput } from '@/components/Form';
  import ProcessModal from '@/views/kri/input/components/ProcessModal.vue';
  import { useExamine } from '@/hooks/api/useExamine';

  const formRef = ref();
  const processRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const detailModalRef = ref();
  const { createMessage } = useMessage();
  const { passRequest, givebackRequest, getProcess } = useExamine('/rule/assess/year/task');

  //注册table数据
  const { tableContext } = useListPage({
    tableProps: {
      title: '存量制度全面评估任务',
      api: list,
      columns,
      canResize: false,
      useSearchForm: false,
      actionColumn: {
        width: 120,
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
      },
      tableSetting: {
        redo: false,
        size: false,
        setting: false,
      },
      beforeFetch: async (params) => {
        return Object.assign(params, queryParam);
      },
    },
  });
  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;
  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  /**
   * 详情
   */
  function handleDetail(record: any) {
    detailModalRef.value.open(record);
  }

  /**
   * 审核通过
   */
  async function handlePass() {
    let ids: any[] = [];
    ids = selectedRowKeys.value;
    if (ids.length === 0) {
      createMessage.warning('请选择数据再进行操作!');
      return;
    }
    passRequest({ ids: ids }, handleSuccess);
  }

  /**
   * 审核退回
   */
  async function handleGiveback() {
    let ids: any[] = [];
    ids = selectedRowKeys.value;
    if (ids.length === 0) {
      createMessage.warning('请选择数据再进行操作!');
      return;
    }
    givebackRequest({ ids: ids }, handleSuccess);
  }

  /**
   * 处理过程
   * @param record
   */
  function handleProcess(record: Recordable) {
    processRef.value.handleOpen(record.id);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record: any) {
    return [
      {
        label: '查看',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '处理过程',
        onClick: handleProcess.bind(null, record),
      },
    ];
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }

  defineExpose({
    reload,
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
    .ant-form-item:not(.ant-form-item-with-help) {
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),
    :deep(.ant-input-number) {
      width: 100%;
    }
  }
</style>
