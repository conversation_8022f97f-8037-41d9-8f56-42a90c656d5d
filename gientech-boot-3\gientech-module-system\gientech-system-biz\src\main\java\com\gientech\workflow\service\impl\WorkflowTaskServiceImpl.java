package com.gientech.workflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gientech.workflow.cache.WorkflowDefineCache;
import com.gientech.workflow.define.WorkflowDefine;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.dto.WorkflowTaskQueryDTO;
import com.gientech.workflow.dto.WorkflowTaskQueryParam;
import com.gientech.workflow.entity.InstanceStatus;
import com.gientech.workflow.entity.TaskStatus;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.mapper.WorkflowInstanceMapper;
import com.gientech.workflow.mapper.WorkflowTaskMapper;
import com.gientech.workflow.parser.NodeParseUtil;
import com.gientech.workflow.service.IWorkflowTaskService;
import com.gientech.workflow.strategy.NodeExecutionResult;
import com.gientech.workflow.strategy.NodeExecutionStrategyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 工作流任务 服务实现
 *
 * <AUTHOR>
 * @date 2025年05月08日 13:47
 */
@Slf4j
@Service
public class WorkflowTaskServiceImpl extends ServiceImpl<WorkflowTaskMapper, WorkflowTask> implements IWorkflowTaskService {

    /**
     * 节点处理
     */
    private NodeParseUtil nodeParseUtil;

    /**
     * 流程定义缓存
     */
    private WorkflowDefineCache workflowDefineCache;

    /**
     * 流程实例-持久层接口
     */
    private WorkflowInstanceMapper instanceMapper;

    /**
     * 节点执行策略管理器
     */
    private NodeExecutionStrategyManager nodeExecutionStrategyManager;

    @Autowired
    public void setNodeParseUtil(NodeParseUtil nodeParseUtil) {
        this.nodeParseUtil = nodeParseUtil;
    }

    @Autowired
    public void setWorkflowDefineCache(WorkflowDefineCache workflowDefineCache) {
        this.workflowDefineCache = workflowDefineCache;
    }

    @Autowired
    public void setInstanceMapper(WorkflowInstanceMapper instanceMapper) {
        this.instanceMapper = instanceMapper;
    }

    @Autowired
    public void setNodeExecutionStrategyManager(NodeExecutionStrategyManager nodeExecutionStrategyManager) {
        this.nodeExecutionStrategyManager = nodeExecutionStrategyManager;
    }

    /**
     * 根据角色、机构查询 某业务下的当前工作流任务
     *
     * @param businessKey 业务key
     * @param assignee 委托人
     * @param assigneeOrgCode 委托人机构
     * @return 工作流任务
     */
    @Override
    public List<WorkflowTask> getWorkflowTask(String businessKey, String assignee, String assigneeOrgCode) {
        // 获取流程定义
        WorkflowDefine define = workflowDefineCache.get(businessKey);
        // 查询实例
        LambdaQueryWrapper<WorkflowInstance> instanceQueryWrapper = new LambdaQueryWrapper<>();
        instanceQueryWrapper.eq(WorkflowInstance::getWorkflowDefineId, define.getId());
        instanceQueryWrapper.eq(WorkflowInstance::getStatus, InstanceStatus.processing);
        List<WorkflowInstance> workflowInstances = instanceMapper.selectList(instanceQueryWrapper);
        List<String> instanceIdList = workflowInstances.stream().map(WorkflowInstance::getId).toList();
        // 查询任务
        LambdaQueryWrapper<WorkflowTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WorkflowTask::getWorkflowInstanceId, instanceIdList);
        queryWrapper.eq(WorkflowTask::getAssignee, assignee);
        queryWrapper.eq(WorkflowTask::getAssigneeOrgCode, assigneeOrgCode);
        // 任务状态
        queryWrapper.eq(WorkflowTask::getStatus, TaskStatus.created);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 根据角色、机构查询 某业务下的当前工作流任务对应业务id
     *
     * @param businessKey 业务key
     * @param assignee 委托人
     * @param assigneeOrgCode 委托人机构
     * @return 工作流任务
     */
    @Override
    public List<String> getWorkflowTaskBusinessIdList(String businessKey, String assignee, String assigneeOrgCode, boolean isComplete) {
        List<String> businessIdList = new ArrayList<>();

        // 获取流程定义
        WorkflowDefine define = workflowDefineCache.get(businessKey);
        // 查询实例
        LambdaQueryWrapper<WorkflowInstance> instanceQueryWrapper = new LambdaQueryWrapper<>();
        instanceQueryWrapper.eq(WorkflowInstance::getWorkflowDefineId, define.getId());
//        instanceQueryWrapper.eq(WorkflowInstance::getStatus, InstanceStatus.processing);
        List<WorkflowInstance> workflowInstances = instanceMapper.selectList(instanceQueryWrapper);

        // 查询是否存在对应任务
        for (WorkflowInstance workflowInstance : workflowInstances) {
            LambdaQueryWrapper<WorkflowTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkflowTask::getAssignee, assignee);
            queryWrapper.eq(WorkflowTask::getAssigneeOrgCode, assigneeOrgCode);
            // 任务状态
            if (isComplete) {
                queryWrapper.eq(WorkflowTask::getStatus, TaskStatus.completed);
            } else {
                queryWrapper.eq(WorkflowTask::getStatus, TaskStatus.created);
            }
            queryWrapper.eq(WorkflowTask::getWorkflowInstanceId, workflowInstance.getId());

            if (baseMapper.exists(queryWrapper)) {
                businessIdList.add(workflowInstance.getBusinessId());
            }
        }

        return businessIdList;
    }

    /**
     * 查询指定业务数据的当前工作流任务
     *
     * @param businessKey 业务key
     * @param businessId 业务主键
     * @return 工作流任务
     */
    @Override
    public WorkflowTask getWorkflowTask(String businessKey, String businessId) {
        // 获取流程定义
        WorkflowDefine workflowDefine = workflowDefineCache.get(businessKey);
        // 查询工作流实例
        LambdaQueryWrapper<WorkflowInstance> instanceQueryWrapper = new LambdaQueryWrapper<>();
        instanceQueryWrapper.eq(WorkflowInstance::getWorkflowDefineId, workflowDefine.getId());
        instanceQueryWrapper.eq(WorkflowInstance::getBusinessId, businessId);
        instanceQueryWrapper.eq(WorkflowInstance::getStatus, InstanceStatus.processing);
        WorkflowInstance instance = instanceMapper.selectOne(instanceQueryWrapper);
        if (instance == null)
            return null;
        // 查询工作流任务
        LambdaQueryWrapper<WorkflowTask> taskQueryWrapper = new LambdaQueryWrapper<>();
        taskQueryWrapper.eq(WorkflowTask::getWorkflowInstanceId, instance.getId());
        taskQueryWrapper.eq(WorkflowTask::getStatus, TaskStatus.created);
        return baseMapper.selectOne(taskQueryWrapper);
    }

    /**
     * 完成工作流任务
     * 使用新的策略模式重构，消除重复代码
     *
     * @param taskId 任务Id
     * @param variables 参数 通常为Map存储的业务对象 businessKey->业务对象以及一个跳转参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeTask(String taskId, Map<String, Object> variables) {
        // 查询任务、实例、定义
        WorkflowTask task = baseMapper.selectById(taskId);
        WorkflowInstance instance = instanceMapper.selectById(task.getWorkflowInstanceId());
        WorkflowDefine define = workflowDefineCache.getByDefineId(instance.getWorkflowDefineId());

        // 合并业务变量
        Map<String, Object> instanceVariables = instance.getVariables();
        instanceVariables.putAll(variables);
        log.info("合并后的变量: {}", instanceVariables.toString());

        // 获取下一个节点
        WorkflowNode nextNode = nodeParseUtil.getNextNode(define, instance.getCurrentNodeId(), instanceVariables);
        if (nextNode == null) {
            log.error("完成任务时,获取到的下一个节点为空");
            log.error("当前任务:{}", task.toString());
            log.error("当前实例:{}", instance.toString());
            log.error("当前定义:{}", define.toString());
            throw new RuntimeException("Next node is null");
        }

        // 标记当前任务为完成
        task.setStatus(TaskStatus.completed);
        baseMapper.updateById(task);

        // 更新实例当前节点
        instance.setCurrentNodeId(nextNode.getId());
        instanceMapper.updateById(instance);

        // 使用策略模式执行下一个节点
        NodeExecutionResult result = nodeExecutionStrategyManager.executeNode(instance, nextNode, define, instanceVariables, task);

        if (!result.isSuccess()) {
            log.error("执行下一个节点失败: {}", result.getErrorMessage());
            throw new RuntimeException("执行下一个节点失败: " + result.getErrorMessage());
        }

        // 处理执行结果
        if (result.getInstanceStatus() != null) {
            instance.setStatus(result.getInstanceStatus());
            instanceMapper.updateById(instance);
        }

        if (result.getNewTask() != null) {
            baseMapper.insert(result.getNewTask());
        }

        log.info("任务完成并流转成功: {}", result.getDescription());
    }

    /**
     * 查询工作流任务详细信息（优化版本）
     *
     * @param param 查询参数
     * @return 工作流任务查询结果列表
     */
    @Override
    public List<WorkflowTaskQueryDTO> getWorkflowTaskWithInstance(WorkflowTaskQueryParam param) {
        return baseMapper.selectWorkflowTaskWithInstance(param);
    }

    /**
     * 查询工作流任务对应的业务ID列表（优化版本）
     * 支持单个和批量委托人查询
     *
     * @param param 查询参数
     * @return 业务ID列表
     */
    @Override
    public List<String> getWorkflowTaskBusinessIdListOptimized(WorkflowTaskQueryParam param) {
        return baseMapper.selectWorkflowTaskBusinessIds(param);
    }

    /**
     * 统计工作流任务数量
     *
     * @param param 查询参数
     * @return 任务数量
     */
    @Override
    public Long countWorkflowTask(WorkflowTaskQueryParam param) {
        return baseMapper.countWorkflowTask(param);
    }
}
