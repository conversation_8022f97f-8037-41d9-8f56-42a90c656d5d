package com.gientech.ldc.manage.controller;

import java.util.*;

import com.gientech.ldc.manage.entity.LdcEventManage;
import com.gientech.ldc.manage.entity.LdcEventManageForImport;
import com.gientech.ldc.manage.entity.LdcEventRecycleDetails;
import com.gientech.ldc.manage.entity.LdcRuleRel;
import com.gientech.ldc.manage.service.ILdcEventManageService;
import com.gientech.rcsa.plan.entity.RcsaPlanManage;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.service.ISysDepartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 损失事件管理表
 * @Author: jeecg-boot
 * @Date:   2025-04-29
 * @Version: V1.0
 */
@Tag(name="损失事件管理表")
@RestController
@RequestMapping("/manage/ldcEventManage")
@Slf4j
public class LdcEventManageController extends JeecgController<LdcEventManage, ILdcEventManageService> {
	@Autowired
	private ILdcEventManageService ldcEventManageService;
	@Autowired
	private ISysDepartService sysDepartService;
	
	/**
	 * 分页列表查询
	 *
	 * @param ldcEventManage
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "损失事件管理表-分页列表查询")
	@Operation(summary="损失事件管理表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<LdcEventManage>> queryPageList(LdcEventManage ldcEventManage,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {

		// ldcEventManageService.resetOrder();
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("eventClassifyFirst", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("eventClassifySecond", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("isEnd", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("isLossEvent", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("isRemove", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<LdcEventManage> queryWrapper = QueryGenerator.initQueryWrapper(ldcEventManage, req.getParameterMap(),customeRuleMap);

		// 添加权限控制，根据角色控制
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		List<String> roleList = Arrays.asList(sysUser.getRoleCode().split(","));
		String orgId = sysUser.getOrgId();
		List<String> processFlagList = new ArrayList<>();
		List<String> fillDepartList = new ArrayList<>();

		if (roleList.contains("fh_cfzg") || roleList.contains("fhbm_cfglg")) {
			queryWrapper.eq("process_flag", "1");
			// 只查本机构
			queryWrapper.eq("fill_depart", sysUser.getOrgId());
			processFlagList.add("1");
			fillDepartList.add(sysUser.getOrgId());
		} else if (roleList.contains("zhgbm_cfglh")) {
			queryWrapper.eq("process_flag", "2");
			// 只查本机构
			queryWrapper.eq("fill_depart", sysUser.getOrgId());
			processFlagList.add("2");
			fillDepartList.add(sysUser.getOrgId());
		} else if (roleList.contains("zh_ldc_glg")) {
			queryWrapper.eq("process_flag", "3");
			// 只查本机构
			queryWrapper.eq("fill_depart", sysUser.getOrgId());
			processFlagList.add("3");
			fillDepartList.add(sysUser.getOrgId());
		} else if (roleList.contains("zgs_cfzg")) {
			queryWrapper.eq("process_flag", "4");
			// 只查本机构
			queryWrapper.eq("fill_depart", sysUser.getOrgId());
			processFlagList.add("4");
			fillDepartList.add(sysUser.getOrgId());
		} else if (roleList.contains("czyh_cfzg")) {
			queryWrapper.eq("process_flag", "5");
			// 只查本机构
			queryWrapper.eq("fill_depart", sysUser.getOrgId());
			processFlagList.add("5");
			fillDepartList.add(sysUser.getOrgId());
		} else if (roleList.contains("fh_cfshg")) {
			queryWrapper.in("process_flag", Collections.addAll(processFlagList, "1", "7"));
			// 只查本机构和下级机构
			List<String> subDepIdsByDepId = sysDepartService.getSubDepIdsByDepId(orgId);
			queryWrapper.in("fill_depart", subDepIdsByDepId);
			fillDepartList.addAll(subDepIdsByDepId);
		} else if (roleList.contains("zh_ldc_shg")) {
			queryWrapper.in("process_flag", Collections.addAll(processFlagList, "1", "2", "3", "4", "5", "6", "7"));
			// 只查本机构和下级机构
			List<String> subDepIdsByDepId = sysDepartService.getSubDepIdsByDepId(orgId);
			queryWrapper.in("fill_depart", subDepIdsByDepId);
			fillDepartList.addAll(subDepIdsByDepId);
		}

		Page<LdcEventManage> page = new Page<LdcEventManage>(pageNo, pageSize);
		// IPage<LdcEventManage> pageList = ldcEventManageService.page(page, queryWrapper);

		IPage<LdcEventManage> pageList =
				ldcEventManageService.selectEventPageListByParam(
						page, processFlagList, ldcEventManage, orgId, fillDepartList);

		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param ldcEventManage
	 * @return
	 */
	@AutoLog(value = "损失事件管理表-添加")
	@Operation(summary="损失事件管理表-添加")
	@RequiresPermissions("manage:ldc_event_manage:add")
	@PostMapping(value = "/add")
	public Result<LdcEventManage> add(@RequestBody LdcEventManage ldcEventManage) throws Exception{
//		try {
//			ldcEventManageService.add(ldcEventManage);
//			return Result.OK("新建成功！");
//		} catch (Exception e) {
//			log.error("损失事件管理表-新建异常");
//			return Result.error("新建异常");
//		}
		ldcEventManageService.add(ldcEventManage);
		return Result.OK(ldcEventManage);
	}
	
	/**
	 *  编辑
	 *
	 * @param ldcEventManage
	 * @return
	 */
	@AutoLog(value = "损失事件管理表-编辑")
	@Operation(summary="损失事件管理表-编辑")
	@RequiresPermissions("manage:ldc_event_manage:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<LdcEventManage> edit(@RequestBody LdcEventManage ldcEventManage) throws Exception {


		ldcEventManageService.edit(ldcEventManage);
		return Result.OK(ldcEventManage);
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "损失事件管理表-通过id删除")
	@Operation(summary="损失事件管理表-通过id删除")
	@RequiresPermissions("manage:ldc_event_manage:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		ldcEventManageService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "损失事件管理表-批量删除")
	@Operation(summary="损失事件管理表-批量删除")
	@RequiresPermissions("manage:ldc_event_manage:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.ldcEventManageService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "损失事件管理表-通过id查询")
	@Operation(summary="损失事件管理表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<LdcEventManage> queryById(@RequestParam(name="id",required=true) String id) {
		LdcEventManage ldcEventManage = ldcEventManageService.getById(id);
		if(ldcEventManage==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(ldcEventManage);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param ldcEventManage
    */
    @RequiresPermissions("manage:ldc_event_manage:exportXls")
    @RequestMapping(value = "/exportXls")
    public void exportXls(HttpServletRequest request,
						  HttpServletResponse response, LdcEventManage ldcEventManage) throws Exception {

		ldcEventManageService.exportXls(request, response, ldcEventManage);
        //return super.exportXls(request, ldcEventManage, LdcEventManage.class, "损失事件管理表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
	@RequiresPermissions("manage:ldc_event_manage:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {

		// 重写导入功能
		return ldcEventManageService.importExcel(request, response, LdcEventManageForImport.class);

        //return super.importExcel(request, response, LdcEventManage.class);
    }

	@Operation(summary="LDC损失回收明细-获取明细信息")
	@PostMapping(value = "/getRecycleListByManageId")
	public Result<List<LdcEventRecycleDetails>> getRecycleListByManageId(@RequestBody LdcEventManage ldcEventManage) {
		return ldcEventManageService.getRecycleListByManageId(ldcEventManage);
	}
	@AutoLog(value = "损失事件管理表-表单中提交")
	@Operation(summary="损失事件管理表-表单中提交")
	@PostMapping(value = "/submitFromForm")
	public Result<String> submitFromForm(@RequestBody LdcEventManage ldcEventManage) throws Exception {
		ldcEventManageService.submitFromForm(ldcEventManage);
		return Result.OK("提交成功");
	}

	@AutoLog(value = "损失事件管理表-列表提交")
	@Operation(summary="损失事件管理表-列表提交")
	@PostMapping(value = "/submitFormList")
	public Result<String> submitFormList(@RequestBody LdcEventManage ldcEventManage) throws Exception {
		ldcEventManageService.submitFormList(ldcEventManage);
		return Result.OK("提交成功");
	}

	@AutoLog(value = "损失事件管理表-提交撤回")
	@Operation(summary="损失事件管理表-提交撤回")
	@PostMapping(value = "/handleWithdrawToFirst")
	public Result<String> handleWithdrawToFirst(@RequestBody LdcEventManage ldcEventManage) throws Exception {
		ldcEventManageService.handleWithdrawToFirst(ldcEventManage);
		return Result.OK("撤回成功");
	}

	@AutoLog(value = "损失事件管理表-待审核列表查询")
	@Operation(summary="损失事件管理表-待审核列表查询")
	@GetMapping(value = "/queryAuditList")
	public Result<IPage<LdcEventManage>> queryAuditList(LdcEventManage ldcEventManage,
														@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
														@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
														HttpServletRequest req) throws Exception {
		// 自定义查询规则
		Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
		// 自定义多选的查询规则为：LIKE_WITH_OR
		customeRuleMap.put("eventClassifyFirst", QueryRuleEnum.LIKE_WITH_OR);
		customeRuleMap.put("eventClassifySecond", QueryRuleEnum.LIKE_WITH_OR);
		customeRuleMap.put("isEnd", QueryRuleEnum.LIKE_WITH_OR);
		customeRuleMap.put("isLossEvent", QueryRuleEnum.LIKE_WITH_OR);
		customeRuleMap.put("isRemove", QueryRuleEnum.LIKE_WITH_OR);
		QueryWrapper<LdcEventManage> queryWrapper = QueryGenerator.initQueryWrapper(ldcEventManage, req.getParameterMap(),customeRuleMap);
		// 根据角色判断状态值
		List<String> statuList = new ArrayList<>();
		List<String> departList = new ArrayList<>();
		List<String> processFlagList = new ArrayList<>();
		ldcEventManageService.getAuditListByRole(statuList, departList, processFlagList);
		if (statuList.size() == 0) {
			throw new Exception("您没有权限审核！");
		}
		queryWrapper.in("status", statuList);
		queryWrapper.in("fill_depart", departList);
		queryWrapper.in("process_flag", processFlagList);

		Page<LdcEventManage> page = new Page<LdcEventManage>(pageNo, pageSize);
		IPage<LdcEventManage> pageList = ldcEventManageService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	@AutoLog(value = "损失事件管理表-审核通过")
	@Operation(summary="损失事件管理表-审核通过")
	@PostMapping(value = "/auditManagePass")
	public Result<String> auditManagePass(@RequestBody LdcEventManage ldcEventManage){
		try {
			ldcEventManageService.auditManagePass(ldcEventManage);
			return Result.OK("审核通过成功");
		} catch (Exception e) {
			log.error("审核通过异常：", e);
			return Result.error("审核通过异常");
		}
	}

	@AutoLog(value = "损失事件管理表-审核退回")
	@Operation(summary="损失事件管理表-审核退回")
	@PostMapping(value = "/auditManageBack")
	public Result<String> auditManageBack(@RequestBody LdcEventManage ldcEventManage){
		try {
			ldcEventManageService.auditManageBack(ldcEventManage);
			return Result.OK("审核退回成功");
		} catch (Exception e) {
			log.error("审核退回异常：", e);
			return Result.error("审核退回异常");
		}
	}

	@AutoLog(value = "损失事件管理表-判断是否可以进行合并")
	@Operation(summary="损失事件管理表-判断是否可以进行合并")
	@PostMapping(value = "/judgeCanMerge")
	public Result<LdcEventManage> judgeCanMerge(@RequestBody LdcEventManage ldcEventManage){
		try {
			return ldcEventManageService.judgeCanMerge(ldcEventManage);
		} catch (Exception e) {
			log.error("合并异常：", e);
			return Result.error("合并异常");
		}
	}

	@AutoLog(value = "损失事件管理表-取消合并")
	@Operation(summary="损失事件管理表-取消合并")
	@PostMapping(value = "/cancelMerge")
	public Result<LdcEventManage> cancelMerge(@RequestBody LdcEventManage ldcEventManage){
		try {
			return ldcEventManageService.cancelMerge(ldcEventManage);
		} catch (Exception e) {
			log.error("取消合并异常：", e);
			return Result.error("取消合并异常");
		}
	}

	@Operation(summary="损失事件管理表-获取事件编号")
	@PostMapping(value = "/getEventCode")
	public Result<LdcEventManage> getEventCode() {
		try {
			return ldcEventManageService.getEventCode();
		} catch (Exception e) {
			log.error("获取事件编号异常：", e);
			return Result.error("获取事件编号异常");
		}
	}

	@Operation(summary="损失事件管理表-分页列表查询")
	@GetMapping(value = "/queryAuditedList")
	public Result<IPage<LdcEventManage>> queryAuditedList(LdcEventManage ldcEventManage,
														@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
														@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
														HttpServletRequest req) {

		Page<LdcEventManage> page = new Page<LdcEventManage>(pageNo, pageSize);
		IPage<LdcEventManage> pageList = ldcEventManageService.queryAuditedList(page, ldcEventManage);
		return Result.OK(pageList);
	}


	@Operation(summary="损失事件管理表-审核撤回")
	@PostMapping(value = "/auditWithdraw")
	public Result<String> auditWithdraw(@RequestBody LdcEventManage ldcEventManage) throws Exception {
		ldcEventManageService.auditWithdraw(ldcEventManage);
		return Result.OK("撤回成功");
	}

	@Operation(summary="损失事件管理表-置为更新状态")
	@PostMapping(value = "/updateStatusToUpdate")
	public Result<String> updateStatusToUpdate(@RequestBody LdcEventManage ldcEventManage) throws Exception {
		ldcEventManageService.updateStatusToUpdate(ldcEventManage);
		return Result.OK("置为更新中状态成功");
	}


	@Operation(summary="损失事件管理表-置为更新状态")
	@PostMapping(value = "/batchRepeal")
	public Result<String> batchRepeal(@RequestBody LdcEventManage ldcEventManage) throws Exception {
		return ldcEventManageService.batchRepeal(ldcEventManage);
		// return Result.OK("废止成功");
	}

	@Operation(summary="损失事件管理表-根据Id查询数据")
	@GetMapping(value = "/queryListById")
	public Result<IPage<LdcEventManage>> queryListById(LdcEventManage ldcEventManage,
													   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		QueryWrapper<LdcEventManage> queryWrapper = new QueryWrapper<>();
		queryWrapper.in("id", ldcEventManage.getIds());
		Page<LdcEventManage> page = new Page<LdcEventManage>(pageNo, pageSize);

		IPage<LdcEventManage> pageList = ldcEventManageService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	@Operation(summary="损失事件管理表-文件下载")
	@RequestMapping(value = "/downloadZip", method = RequestMethod.GET)
	public void downloadZip(HttpServletRequest request, HttpServletResponse response) throws Exception{
		String id = request.getParameter("id");
		ldcEventManageService.downloadZip(id, response);
	}


	@Operation(summary="损失事件管理表-判断是否有权限对数据进行操作")
	@PostMapping(value = "/judgeCanOperate")
	public Result<String> judgeCanOperate(@RequestBody LdcEventManage ldcEventManage) throws Exception {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		List<String> roleList = Arrays.asList(sysUser.getRoleCode().split(","));
		String orgId = sysUser.getOrgId();
		List<String> processFlagList = new ArrayList<>();
		List<String> fillDepartList = new ArrayList<>();

		if (roleList.contains("fh_cfzg") || roleList.contains("fhbm_cfglg")) {
			// 只查本机构
			processFlagList.add("1");
			fillDepartList.add(sysUser.getOrgId());
		} else if (roleList.contains("zhgbm_cfglh")) {
			// 只查本机构
			processFlagList.add("2");
			fillDepartList.add(sysUser.getOrgId());
		} else if (roleList.contains("zh_ldc_glg")) {
			// 只查本机构
			processFlagList.add("3");
			fillDepartList.add(sysUser.getOrgId());
		} else if (roleList.contains("zgs_cfzg")) {
			// 只查本机构
			processFlagList.add("4");
			fillDepartList.add(sysUser.getOrgId());
		} else if (roleList.contains("czyh_cfzg")) {
			// 只查本机构
			processFlagList.add("5");
			fillDepartList.add(sysUser.getOrgId());
		} else if (roleList.contains("fh_cfshg")) {
			// 只查本机构和下级机构
			List<String> subDepIdsByDepId = sysDepartService.getSubDepIdsByDepId(orgId);
			fillDepartList.addAll(subDepIdsByDepId);
		} else if (roleList.contains("zh_ldc_shg")) {
			// 只查本机构和下级机构
			List<String> subDepIdsByDepId = sysDepartService.getSubDepIdsByDepId(orgId);
			fillDepartList.addAll(subDepIdsByDepId);
		} else {
			throw new Exception("您没有权限进行操作");
		}
		return ldcEventManageService.judgeCanOperate(ldcEventManage, processFlagList, fillDepartList);
	}


	@Operation(summary="锁定损失事件，防止修改时被审批人撤回")
	@PostMapping(value = "/lockEvent") 
	public Result<String> lockEvent(@RequestBody LdcEventManage ldcEventManage) throws Exception {
		return ldcEventManageService.lockEvent(ldcEventManage);
	}

	@Operation(summary="获取事件严重度分级")
	@PostMapping(value = "/judgeSeverityLevel")
	public Result<String> judgeSeverityLevel(@RequestBody LdcEventManage ldcEventManage) throws Exception {
		return ldcEventManageService.judgeSeverityLevel(ldcEventManage);
	}

	@Operation(summary="损失事件管理表-")
	@GetMapping(value = "/queryEventByFillDepartList") 
	public Result<IPage<LdcEventManage>> queryEventByFillDepartList(LdcEventManage ldcEventManage,
																	 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
																	 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
																	 HttpServletRequest req) {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String orgId = sysUser.getOrgId();

		QueryWrapper<LdcEventManage> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("fill_depart", orgId);
//		String notExistsSql = "select 1 from ldc_event_clue_rel where ldc_event_clue_rel.event_id = ldc_event_manage.id";
//		queryWrapper.notExists(notExistsSql);
		Page<LdcEventManage> page = new Page<LdcEventManage>(pageNo, pageSize);
		IPage<LdcEventManage> pageList = ldcEventManageService.page(page, queryWrapper);
		return Result.OK(pageList);
	}


	@Operation(summary="入账线索关联LDC损失事件")
	@PostMapping(value = "/relClueAndEvent")
	public Result<String> relClueAndEvent(@RequestBody LdcEventManage ldcEventManage) throws Exception {
		return ldcEventManageService.relClueAndEvent(ldcEventManage);
	}

	@Operation(summary="LDC损失回收明细-入账线索填报拼接")
	@PostMapping(value = "/montageRecycleDetail")
	public Result<List<LdcEventRecycleDetails>> montageRecycleDetail(@RequestBody LdcEventManage ldcEventManage) {
		return ldcEventManageService.montageRecycleDetail(ldcEventManage);
	}

	@PostMapping(value = "/queryCurrencyRate")
	public Result<String> queryCurrencyRate(@RequestBody LdcEventRecycleDetails detail) {
		return ldcEventManageService.queryCurrencyRate(detail);
	}

	@PostMapping(value = "/querySubjectList")
	public Result<Map<String, Object>> querySubjectList() {
		Map<String, Object> result = ldcEventManageService.querySubjectList();
		return Result.ok(result);
	}

	@Operation(summary="损失事件管理表-分页列表查询")
	@GetMapping(value = "/queryLdcRelRcsaList")
	public Result<IPage<LdcEventManage>> queryLdcRelRcsaList(LdcEventManage ldcEventManage,
														   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
														   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
														   HttpServletRequest req) {

		Page<LdcEventManage> page = new Page<LdcEventManage>(pageNo, pageSize);
		IPage<LdcEventManage> pageList = ldcEventManageService.queryLdcRelRcsaList(page, ldcEventManage);
		return Result.OK(pageList);
	}


	@PostMapping(value = "/queryRuleRelList")
	public Result<Map<String, Object>> queryRuleRelList(@RequestBody LdcEventManage ldcEventManage) {
		List<LdcRuleRel> interiorList = ldcEventManageService.getInteriorList(Arrays.asList(ldcEventManage.getId().split(",")));
		List<LdcRuleRel> externalList = ldcEventManageService.getExternalList(Arrays.asList(ldcEventManage.getId().split(",")));
		Map<String, Object> result = new HashMap<>();
		result.put("interiorList", interiorList);
		result.put("externalList", externalList);
		return Result.ok(result);
	}

	 @PostMapping(value = "/judgeLdcRelInfo")
	 public Result<String> judgeLdcRelInfo(@RequestBody RcsaPlanManage planManage) {
		String state = ldcEventManageService.judgeLdcRelInfo(planManage);
		return Result.ok(state);
	 }




}
