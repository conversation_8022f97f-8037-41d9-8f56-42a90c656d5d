<template>
  <a-modal v-model:open="visible" :okButtonProps="{ class: { 'jee-hidden': true } }" title="重大操作风险认定标准" cancel-text="关闭">
    <a-table ref="table" :columns="columns" :pagination="false" :data-source="tableData1" bordered size="middle" rowKey="id"> </a-table>
  </a-modal>

  <a-modal style="width: 1000px" v-model:open="visible2" :okButtonProps="{ class: { 'jee-hidden': true } }" title="关联台账" cancel-text="关闭">
    <a-table
      ref="table"
      :columns="columnsTZ"
      :pagination="false"
      :data-source="tableData2"
      bordered
      size="middle"
      :scroll="{ x: 'calc(700px + 50%)' }"
      rowKey="id"
    >
    </a-table>
  </a-modal>

  <a-modal style="width: 1000px" v-model:open="visible3" :okButtonProps="{ class: { 'jee-hidden': true } }" title="事件历史版本" cancel-text="关闭">

    <!-- <a-timeline mode="left">
			<a-timeline-item 
			v-for="(item, index) in timelineData" 
			:key="index"
			:color="item.color || 'red'">
			<span slot="label" style="font-weight: bold;">{{ item.time }}</span>
			<div style="background-color: #fff6f0;">
				<strong style="font-weight: 800; font-size: 16px; ">{{ item.title }}</strong>
				<p style="margin-top: 8px;">{{ item.content }}</p>
			</div>
			</a-timeline-item>
		</a-timeline> -->
    <!-- <a-table ref="table" 
		  	:columns="columnsEventName" 
			:pagination="false"
			:data-source="tableData3" bordered size="middle" 
			:scroll="{ x: 'calc(700px + 50%)'}" 
			rowKey="id">
        </a-table> -->

    <!-- <div class="timeline">
      <h2>事件相关历史版本</h2>
       <div v-for="(event, index) in tableData3" :key="index" class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-content">
          <div class="event-header">
            <span class="event-time">{{ event.createTime }}</span>
            <span class="event-name">事件名称{{ event.eventName }}</span>
          </div>
          <div class="event-description">
            <h3>事件描述：</h3>
            <p>{{ event.eventDescrption }}</p>
          </div>
          <div class="event-reason">
            <h3>事件原因描述：</h3>
            <p>{{ event.eventReasonDescription }}</p>
          </div>
        </div>
      </div>
    </div> -->

<!-- 时间轴 -->


    <!-- <LdcEcharts /> -->

   <div class="event-timeline">
    <div v-for="(event, index) in tableData3" :key="index" class="event-item">
        
      <div class="event-header" >
        <span class="event-time" >{{ event.createTime }}</span>
        <span class="event-name">{{ event.eventName }}</span>
      </div>
      <div class="event-content">
        <div class="event-description">
          <p><span>事件描述：</span>{{ event.eventDescrption }}</p>
        </div>
        <div class="event-reason">
          <p><span>事件原因描述：</span>{{ event.eventReasonDescription }}</p>
        </div>
      </div>
    </div>
  </div>
  </a-modal>

  <!-- 锚点导航 -->
  <div class="page-container" style="background-color: #fff6f0">
    <div style="text-align: center">
      <span style="font-weight: bold; font-size: 28px; font-family: '黑体', sans-serif">操作风险损失事件记录表</span>
      <div style="display: flex; justify-content: center" v-if="isShowButton">
        <a-button type="primary" preIcon="ant-design:left-outlined" @click="returnFirst"> 返回</a-button>
        <a-button type="default" style="margin-left: 20px" preIcon="ant-design:edit-outlined" @click="continueEdit">编辑</a-button>
      </div>
    </div>
    <div class="scroll-wrapper" style="background-color: #fff6f0; margin-top: 5px">
      <a-menu mode="horizontal" calss="scroll-menu">
        <a-menu-item v-for="item in anchorItems" :key="item.id" :key-path="item.id">
          <a :href="`#${item.id}`" @click.prevent="scrollToSection(item.id)">{{ item.title }}</a>
        </a-menu-item>
      </a-menu>
    </div>

    <!-- <JFormContainer >
			<template #detail> -->
    <div class="form-container" style="background-color: #fff6f0">
      <a-spin :spinning="confirmLoading" :disabled="isDisabled">
        <a-form ref="formRef" :model="formData" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="LdcEventManageForm">
          <a-card title="基本信息" :id="anchorItems[0].id" class="content-section">
            <a-row>
              <a-col :span="12">
                <a-form-item label="事件编号" v-bind="validateInfos.eventCode" id="LdcEventManageForm-eventCode" name="eventCode">
                  <a-input v-model:value="formData.eventCode" placeholder="请输入事件编号" disabled allow-clear></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="事件分类(一级)"
                  v-bind="validateInfos.eventClassifyFirst"
                  id="LdcEventManageForm-eventClassifyFirst"
                  name="eventClassifyFirst"
                >
                  <!-- <j-dict-select-tag v-model:value="formData.eventClassifyFirst" dictCode=""
										placeholder="请选择事件分类(一级)" disabled allow-clear /> -->
                  <a-select disabled v-model:value="formData.eventClassifyFirst" placeholder="请选择事件分类(一级)" allow-clear>
                    <a-select-option v-for="item in eventClassifyFirstList" :key="item.eventLevelOne" :value="item.eventLevelOne">
                      {{ item.eventLevelOne }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="事件分类(二级)"
                  v-bind="validateInfos.eventClassifySecond"
                  id="LdcEventManageForm-eventClassifySecond"
                  name="eventClassifySecond"
                >
                  <!-- <j-dict-select-tag v-model:value="formData.eventClassifySecond" dictCode=""
										placeholder="请选择事件分类(二级)" allow-clear /> -->
                  <a-select v-model:value="formData.eventClassifySecond" placeholder="请选择事件分类(二级)" allow-clear>
                    <a-select-option
                      v-for="item in eventClassifySecondList"
                      :key="item.eventLevelOne + '-' + item.eventLevelTwo"
                      :value="item.eventLevelOne + '-' + item.eventLevelTwo"
                      @click="getSecondInfo(item)"
                    >
                      {{ item.eventLevelOne + '-' + item.eventLevelTwo }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="事件名称"
                  v-bind="validateInfos.eventName"
                  id="LdcEventManageForm-eventName"
                  name="eventName"
                  :tooltip="nameStandard"
                >
                  <a-input
                    v-if="isShowName1"
                    v-model:value="formData.eventName"
                    placeholder="请输入事件名称，字数不超过100个汉字"
                    allow-clear
                  ></a-input>
                  <div v-if="isShowName2" style="display: flex; align-items: center; gap: 8px">
                    <a-input v-model:value="formData.eventName" placeholder="请输入事件名称，字数不超过100个汉字" allow-clear></a-input>
                    <a-button style="flex-shrink: 0" type="primary" @click="showNameHistory"> 查看历史版本</a-button>
                  </div>
                </a-form-item>
              </a-col>

              <a-col :span="12">
                <a-form-item
                  label="事件描述"
                  v-bind="validateInfos.eventDescrption"
                  id="LdcEventManageForm-eventDescrption"
                  name="eventDescrption"
                  :tooltip="describeCriteria"
                >
                  <a-textarea v-if="isShowName1" v-model:value="formData.eventDescrption" :rows="4" placeholder="请输入事件描述" />

                  <div v-if="isShowName2" style="display: flex; align-items: center; gap: 8px">
                    <a-textarea v-if="isShowName2" v-model:value="formData.eventDescrption" :rows="4" placeholder="请输入事件描述" />
                    <a-button style="flex-shrink: 0" type="primary" @click="showNameHistory"> 查看历史版本</a-button>
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="事件原因描述"
                  v-bind="validateInfos.eventReasonDescription"
                  id="LdcEventManageForm-eventReasonDescription"
                  name="eventReasonDescription"
                  :tooltip="eventCauseStandard"
                >
                  <a-textarea v-if="isShowName1" v-model:value="formData.eventReasonDescription" :rows="4" placeholder="请输入事件原因描述" />
                  <!-- v-if="isShowName2"  -->
                  <div v-if="isShowName2" style="display: flex; align-items: center; gap: 8px">
                    <a-textarea v-model:value="formData.eventReasonDescription" :rows="4" placeholder="请输入事件原因描述" />
                    <a-button style="flex-shrink: 0" type="primary" @click="showNameHistory"> 查看历史版本</a-button>
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="第几次填报" v-bind="validateInfos.fillCount" id="LdcEventManageForm-fillCount" name="fillCount">
                  <a-input-number disabled v-model:value="fillCount" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="是否结束"
                  v-bind="validateInfos.isEnd"
                  id="LdcEventManageForm-isEnd"
                  name="isEnd"
                  :tooltip="eventEndstutsCognizance"
                >
                  <j-dict-select-tag v-model:value="formData.isEnd" dictCode="whether" placeholder="请选择是否结束" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="状态" id="LdcEventManageForm-status_dictText" name="status_dictText">
                  <a-input v-model:value="formData.status_dictText" disabled allow-clear></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="是否为损失事件" v-bind="validateInfos.isLossEvent" id="LdcEventManageForm-isLossEvent" name="isLossEvent">
                  <j-dict-select-tag
                    :disabled="isShowEvent"
                    v-model:value="formData.isLossEvent"
                    dictCode="whether"
                    placeholder="请选择是否为损失事件"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12" v-if="isZHSHG">
                <a-form-item label="是否经监管认可剔除" v-bind="validateInfos.isRemove" id="LdcEventManageForm-isRemove" name="isRemove">
                  <j-dict-select-tag v-model:value="formData.isRemove" dictCode="whether" placeholder="请选择是否经监管认可剔除" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
          <a-card title="时间信息" :id="anchorItems[1].id" class="!mt-5">
            <a-row>
              <a-col :span="12">
                <a-form-item label="初次填报日期" v-bind="validateInfos.firstFillDate" id="LdcEventManageForm-firstFillDate" name="firstFillDate">
                  <a-date-picker
                    placeholder="请选择初次填报日期"
                    v-model:value="formData.firstFillDate"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                    allow-clear
                    disabled
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="本次填报日期" v-bind="validateInfos.thisFillDate" id="LdcEventManageForm-thisFillDate" name="thisFillDate">
                  <a-date-picker
                    placeholder="请选择本次填报日期"
                    v-model:value="formData.thisFillDate"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                    allow-clear
                    disabled
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="事件初始发生日期"
                  v-bind="validateInfos.initialHappenDate"
                  id="LdcEventManageForm-initialHappenDate"
                  name="initialHappenDate"
                  :tooltip="eventFirstDate"
                >
                  <a-date-picker
                    placeholder="请选择事件初始发生日期"
                    v-model:value="formData.initialHappenDate"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="事件发现日期"
                  v-bind="validateInfos.findDate"
                  id="LdcEventManageForm-findDate"
                  name="findDate"
                  :tooltip="eventFoundDate"
                >
                  <a-date-picker
                    placeholder="请选择事件发现日期"
                    v-model:value="formData.findDate"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="首笔损失入账(或确认)日期"
                  v-bind="validateInfos.firstEntryDate"
                  id="LdcEventManageForm-firstEntryDate"
                  name="firstEntryDate"
                >
                  <a-date-picker
                    placeholder="请选择首笔损失入账(或确认)日期"
                    v-model:value="formData.firstEntryDate"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                    allow-clear
                    disabled
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
          <a-card title="机构/人员信息" class="!mt-5" :id="anchorItems[2].id">
            <a-row>
              <a-col :span="12">
                <a-form-item label="事件填报机构" v-bind="validateInfos.fillOrgan" id="LdcEventManageForm-fillOrgan" name="fillOrgan">
                  <j-select-dept v-model:value="formData.fillOrgan" :multiple="true" checkStrictly allow-clear disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="事件填报部门" v-bind="validateInfos.fillDepart" id="LdcEventManageForm-fillDepart" name="fillDepart">
                  <j-select-dept v-model:value="formData.fillDepart" :multiple="true" checkStrictly allow-clear disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="事件填报人" v-bind="validateInfos.fillUser" id="LdcEventManageForm-fillUser" name="fillUser">
                  <j-select-user v-model:value="formData.fillUser" allow-clear disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="填报人联系方式"
                  v-bind="validateInfos.fillUserContact"
                  id="LdcEventManageForm-fillUserContact"
                  name="fillUserContact"
                >
                  <a-input v-model:value="formData.fillUserContact" placeholder="请输入填报人联系方式" allow-clear></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="总行对口管理部门"
                  v-bind="validateInfos.duetManageDepart"
                  id="LdcEventManageForm-duetManageDepart"
                  name="duetManageDepart"
                >
                  <j-select-dept v-model:value="formData.duetManageDepart" :multiple="true" checkStrictly allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="发生机构" v-bind="validateInfos.happenOrgan" id="LdcEventManageForm-happenOrgan" name="happenOrgan">
                  <j-select-dept v-model:value="formData.happenOrgan" :multiple="true" checkStrictly allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="发生部门" v-bind="validateInfos.happenDepart" id="LdcEventManageForm-happenDepart" name="happenDepart">
                  <j-select-dept v-model:value="formData.happenDepart" :multiple="true" checkStrictly allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
          <a-card title="损失和影响信息" class="!mt-5" :id="anchorItems[3].id">
            <legend>财务影响信息</legend>
            <a-row>
              <a-col :span="12">
                <a-form-item
                  label="最大预估损失金额"
                  v-bind="validateInfos.maxEstimateLossMoney"
                  id="LdcEventManageForm-maxEstimateLossMoney"
                  name="maxEstimateLossMoney"
                >
                  <a-input-number v-model:value="formData.maxEstimateLossMoney" placeholder="请输入最大预估损失金额" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="总损失金额" v-bind="validateInfos.totalLossMoney" id="LdcEventManageForm-totalLossMoney" name="totalLossMoney">
                  <a-input-number v-model:value="formData.totalLossMoney" placeholder="请输入总损失金额" style="width: 100%" disabled @change="getSeverityLevel"/>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="已确认的回收总金额"
                  v-bind="validateInfos.cfmdRecycleMoney"
                  id="LdcEventManageForm-cfmdRecycleMoney"
                  name="cfmdRecycleMoney"
                >
                  <a-input-number v-model:value="formData.cfmdRecycleMoney" placeholder="请输入已确认的回收总金额" style="width: 100%" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="净损失金额" v-bind="validateInfos.netLossMoney" id="LdcEventManageForm-netLossMoney" name="netLossMoney">
                  <a-input-number v-model:value="formData.netLossMoney" placeholder="请输入净损失金额" style="width: 100%" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="其中：已确认的保险赔付"
                  v-bind="validateInfos.cfmdIndemnity"
                  id="LdcEventManageForm-cfmdIndemnity"
                  name="cfmdIndemnity"
                >
                  <a-input-number v-model:value="formData.cfmdIndemnity" placeholder="请输入其中：已确认的保险赔付" style="width: 100%" disabled />
                </a-form-item>
              </a-col>
            </a-row>
            <legend>损失回收明细</legend>
            <a-tabs v-model:activeKey="activeKey" animated style="overflow: hidden">
              <a-tab-pane key="recycleDetails" :forceRender="true">
                <j-vxe-table
                  :keep-source="true"
                  resizable
                  ref="ldcEventRecycleDetailsTableRef"
                  :loading="recycleDetailsTable.loading"
                  :columns="recycleDetailsColumns"
                  :dataSource="recycleDetailsTable.dataSource"
                  :height="200"
                  :rowNumber="true"
                  :rowSelection="true"
                  :toolbar="true"
                  @removed="handleDeleteDetail"
                  @added="handleAddDetail"
                  @edit-closed="handleEditClosed"
                >
                <!-- 其他列定义 -->
                <template #toolbarSuffix>
                  <a-button v-if="isShowRecycle" type="primary" preIcon="ant-design:zoom-in-outlined" @click="showRecycleHistory">历史损失明细查看</a-button>
                </template>
                  <template #myAction="record">
                    <!-- <a @click="configIndex(record)">删除</a> -->
                    <!-- <a-divider type="vertical" /> -->
                    <a @click="relEntryClue(record)">关联入账线索</a>
                    <a-divider type="vertical" />
                    <!-- <a disabled @click="configIndex(record)">修改</a>
                    <a-divider type="vertical" />-->
									  <a @click="clueRelList(record)">入账线索详情</a>
                  </template>
                </j-vxe-table>
              </a-tab-pane>
            </a-tabs>
          </a-card>
          <a-card title="非财务影响信息" class="!mt-5" :id="anchorItems[4].id">
            <a-row>
              <a-col :span="12">
                <a-form-item
                  label="声誉受损"
                  v-bind="validateInfos.reputationDamage"
                  id="LdcEventManageForm-reputationDamage"
                  name="reputationDamage"
                >
                  <j-dict-select-tag
                    v-model:value="formData.reputationDamage"
                    dictCode="ldc_non_financial"
                    placeholder="请选择声誉受损"
                    allow-clear
                    @change="getSeverityLevel"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="运营中断"
                  v-bind="validateInfos.operationInterruption"
                  id="LdcEventManageForm-operationInterruption"
                  name="operationInterruption"
                >
                  <j-dict-select-tag
                    v-model:value="formData.operationInterruption"
                    dictCode="ldc_non_financial"
                    placeholder="请选择运营中断"
                    allow-clear
                    @change="getSeverityLevel"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="广大客户服务质量"
                  v-bind="validateInfos.customerServiceQuality"
                  id="LdcEventManageForm-customerServiceQuality"
                  name="customerServiceQuality"
                >
                  <j-dict-select-tag
                    v-model:value="formData.customerServiceQuality"
                    dictCode="ldc_non_financial"
                    placeholder="请选择广大客户服务质量"
                    allow-clear
                    @change="getSeverityLevel"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="监管行动"
                  v-bind="validateInfos.regulatoryActions"
                  id="LdcEventManageForm-regulatoryActions"
                  name="regulatoryActions"
                >
                  <j-dict-select-tag
                    v-model:value="formData.regulatoryActions"
                    dictCode="ldc_non_financial"
                    placeholder="请选择监管行动"
                    allow-clear
                    @change="getSeverityLevel"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="员工安全" v-bind="validateInfos.employeeSafety" id="LdcEventManageForm-employeeSafety" name="employeeSafety">
                  <j-dict-select-tag v-model:value="formData.employeeSafety" dictCode="ldc_non_financial" placeholder="请选择员工安全" allow-clear @change="getSeverityLevel"/>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
          <a-card title="事件分级信息" class="!mt-5" :id="anchorItems[5].id">
            <a-row>
              <a-col :span="12">
                <a-form-item
                  label="事件严重度分级"
                  v-bind="validateInfos.severityClassification"
                  id="LdcEventManageForm-severityClassification"
                  name="severityClassification"
                >
                  <j-dict-select-tag
                    v-model:value="formData.severityClassification"
                    dictCode="ldc_non_financial"
                    placeholder="请选择事件严重度分级"
                    allow-clear
                    disabled
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  v-bind="validateInfos.isMajorRiskEvent"
                  id="LdcEventManageForm-isMajorRiskEvent"
                  name="isMajorRiskEvent"
                  style="width: 120%"
                >
                  <template #label>
                    <a @click="showImportOrec">是否重大操作风险事件</a>
                  </template>
                  <j-dict-select-tag
                    type="radio"
                    v-model:value="formData.isMajorRiskEvent"
                    dictCode="whether"
                    placeholder="请选择是否重大操作风险事件"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
          <a-card title="事件属性" class="!mt-5" :id="anchorItems[6].id">
            <a-row>
              <a-col :span="12">
                <a-form-item
                  label="主要成因分类"
                  v-bind="validateInfos.mainCauseClassification"
                  id="LdcEventManageForm-mainCauseClassification"
                  name="mainCauseClassification"
                >
                <div style="display: flex; align-items: center; gap: 8px">
                    <a-input disabled style="flex: 1" v-model:value="formData.mainCauseClassification" placeholder="请选择主要成因分类"></a-input>
                  <a-button style="flex-shrink: 0" type="primary" @click="chooseMainCause"> 选择</a-button>
                </div>
                  <!-- <j-dict-select-tag v-model:value="formData.mainCauseClassification" dictCode="" placeholder="请选择主要成因分类" allow-clear /> -->
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="损失事件类型(一级)"
                  v-bind="validateInfos.eventTypeFirst"
                  id="LdcEventManageForm-eventTypeFirst"
                  name="eventTypeFirst"
                >
                  <div style="display: flex; align-items: center; gap: 8px">
                    <a-input disabled style="flex: 1" v-model:value="formData.eventTypeFirst" placeholder="请选择损失事件类型(一级)"></a-input>
                    <a-button style="flex-shrink: 0" type="primary" @click="chooseRegulatory"> 选择</a-button>
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="与信用/市场风险相关"
                  v-bind="validateInfos.creditRiskRelevant"
                  id="LdcEventManageForm-creditRiskRelevant"
                  name="creditRiskRelevant"
                >
                  <j-dict-select-tag
                    v-model:value="formData.creditRiskRelevant"
                    dictCode="ldc_creditRiskRelevant"
                    placeholder="请选择与信用/市场风险相关"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="损失事件类型(二级)"
                  v-bind="validateInfos.eventTypeSecond"
                  id="LdcEventManageForm-eventTypeSecond"
                  name="eventTypeSecond"
                >
                  <!-- <j-dict-select-tag v-model:value="formData.eventTypeSecond" dictCode=""
										placeholder="请选择损失事件类型(二级)" allow-clear /> -->
                  <div style="display: flex; align-items: center; gap: 8px">
                    <a-input disabled style="flex: 1" v-model:value="formData.eventTypeSecond" placeholder="请选择损失事件类型(二级)"></a-input>
                    <a-button style="flex-shrink: 0" type="primary" @click="chooseRegulatory"> 选择</a-button>
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="是否纳入计量" v-bind="validateInfos.isIntoMetering" id="LdcEventManageForm-isIntoMetering" name="isIntoMetering">
                  <j-dict-select-tag
                    v-model:value="formData.isIntoMetering"
                    dictCode="whether"
                    placeholder="请选择是否纳入计量"
                    disabled
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="损失事件类型(三级)"
                  v-bind="validateInfos.eventTypeThird"
                  id="LdcEventManageForm-eventTypeThird"
                  name="eventTypeThird"
                >
                  <!-- <j-dict-select-tag v-model:value="formData.eventTypeThird" dictCode=""
										placeholder="请选择损失事件类型(三级)" allow-clear /> -->
                  <div style="display: flex; align-items: center; gap: 8px">
                    <a-input disabled style="flex: 1" v-model:value="formData.eventTypeThird" placeholder="请选择损失事件类型(三级)"></a-input>
                    <a-button style="flex-shrink: 0" type="primary" @click="chooseRegulatory"> 选择</a-button>
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
          <a-card title="关联台账" class="!mt-5" :id="anchorItems[7].id">
            <a-row>
              <a-col :span="12">
                <a-form-item id="LdcEventManageForm-relLedgerType" name="relLedgerType">
                  <!-- <j-dict-select-tag disabled placeholder="请选择" v-model:value="formData.relLedgerType"
										dictCode="ldc_ledger_clue_type" allow-clear /> -->
                  <template #label>
                    <a @click="showGLTZ">关联台账类型</a>
                  </template>
                  <a-input disabled v-model:value="formData.relLedgerType" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item v-bind="validateInfos.relLedgerCode" id="LdcEventManageForm-relLedgerCode" name="relLedgerCode">
                  <template #label>
                    <a @click="showGLTZ">关联台账编号</a>
                  </template>
                  <a-input v-model:value="formData.relLedgerCode" disabled></a-input>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
          <a-card title="事件关联RCSA" class="!mt-5" :id="anchorItems[8].id">
            <a-row>
              <a-col :span="12">
                <a-form-item
                  label="发生操作风险事件的主要流程"
                  v-bind="validateInfos.mainProcess"
                  id="LdcEventManageForm-mainProcess"
                  name="mainProcess"
                  style="width: 180%"
                >
                  <a-select mode="multiple" v-model:value="formData.mainProcesses" placeholder="请选择发生操作风险事件的主要流程" allow-clear >
                    <a-select-option v-for="item in mainProcessList" :key="item.matrixName" :value="item.matrixName">
                      {{item.matrixName}}
                    </a-select-option>
                  </a-select>
                
                  <!-- <j-select-multiple
                    type="list_multi"
                    v-model:value="formData.mainProcess"
                    dictCode=""
                    placeholder="请选择发生操作风险事件的主要流程"
                    :triggerChange="false"
                    style="width: 50%"
                  /> -->
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item
                  label="是否发起触发式评估计划"
                  v-bind="validateInfos.isInitiateRcsa"
                  id="LdcEventManageForm-isInitiateRcsa"
                  name="isInitiateRcsa"
                  style="width: 160%"
                >
                  <j-dict-select-tag
                    type="radio"
                    v-model:value="formData.isInitiateRcsa"
                    dictCode="whether"
                    placeholder="请选择是否发起触发式评估计划"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-for="item in rcsaRelInfoList">
              <a-col :span="8">
                <a-form-item label="计划编号">
                  <a-input disabled v-model:value="item.planCode" allow-clear></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="计划名称">
                  <a-input disabled v-model:value="item.planTitle" allow-clear></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="计划状态">
                  <a-input disabled v-model:value="item.stateName" allow-clear></a-input>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
          <a-card title="事件反映出的问题与整改措施" class="!mt-5" :id="anchorItems[9].id">
            <a-row>
              <a-col :span="24">
                <a-form-item
                  label="是否录入操作风险管理系统-问题收集模块"
                  v-bind="validateInfos.isIntoHistory"
                  id="LdcEventManageForm-isIntoHistory"
                  name="isIntoHistory"
                  style="width: 120%"
                >
                  <j-dict-select-tag
                    type="radio"
                    v-model:value="formData.isIntoHistory"
                    dictCode="whether"
                    placeholder="请选择是否录入操作风险管理系统-问题收集模块"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item label="问题编号" v-bind="validateInfos.historyCode" id="LdcEventManageForm-historyCode" name="historyCode">
                  <a-input v-model:value="formData.historyCode" placeholder="请输入问题编号" allow-clear></a-input>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>

          <a-card title="事件关联内外规" class="!mt-5" :id="anchorItems[10].id">
            <a-row>
              <a-col :span="12">
                <a-form-item
                  label="是否违反内外规"
                  v-bind="validateInfos.isViolateRegulations"
                  id="LdcEventManageForm-isViolateRegulations"
                  name="isViolateRegulations"
                >
                  <j-dict-select-tag
                    type="radio"
                    v-model:value="formData.isViolateRegulations"
                    dictCode="whether"
                    placeholder="请选择是否违反内外规"
                    allow-clear
                    @change="getIsViolateRegulations"
                    :disabled="isDisabledRegulations"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row >
              <a-col style="color: orange; display: flex; font-weight: 800">
              |
                <a-col style="color: black; padding-left: 5px"> 关联外规 </a-col>
              </a-col>
              <div style="margin: 10px; background: #fff; padding: 10px; border: 1px solid #e8e8e8">
                <div style="margin-bottom: 10px">
                  <a-button type="primary" @click="addExternalLawRow" style="margin-right: 8px"> <PlusCircleOutlined /> 新增 </a-button>
                  <a-button type="primary" danger @click="deleteExternalLawRows" :disabled="selectedExternalLawKeys.length === 0">
                    <MinusCircleOutlined /> 删除
                  </a-button>
                </div>
                <a-table
                  :dataSource="ruleExternalLawDetailTable.dataSource"
                  :columns="externalLawColumns"
                  :rowKey="(record, index) => index"
                  :rowSelection="{ selectedRowKeys: selectedExternalLawKeys, onChange: onExternalLawSelectChange }"
                  bordered
                  size="small"
                >
                  <template #bodyCell="{ column, record, index }">
                    <template v-if="column.dataIndex === 'num'">
                      <a-input
                        v-model:value="record[column.dataIndex]"
                        :placeholder="`请输入${column.title}`"
                        @click="openExteriorList(index, column.dataIndex, $event)"
                      />
                    </template>
                  </template>
                </a-table>
              </div>
              <!-- 关联内规 - 替换为自定义表格 -->
              <a-col style="color: orange; display: flex; font-weight: 800">
              |
                <a-col style="color: black; padding-left: 5px"> 关联内规 </a-col>
              </a-col>
              <div style="margin: 10px; background: #fff; padding: 10px; border: 1px solid #e8e8e8">
                <div style="margin-bottom: 10px">
                  <a-button type="primary" @click="addExternalInternalRow" style="margin-right: 8px"> <PlusCircleOutlined /> 新增 </a-button>
                  <a-button type="primary" danger @click="deleteExternalInternalRows" :disabled="selectedExternalInternalKeys.length === 0">
                    <MinusCircleOutlined /> 删除
                  </a-button>
                </div>
                <a-table
                  :dataSource="ruleExternalInternalDetailTable.dataSource"
                  :columns="externalInternalColumns"
                  :rowKey="(record, index) => index"
                  :rowSelection="{ selectedRowKeys: selectedExternalInternalKeys, onChange: onExternalInternalSelectChange }"
                  bordered
                  size="small"
                >
                  <template #bodyCell="{ column, record, index }">
                    <!-- 关联内规类别 -->
                    <template v-if="column.dataIndex === 'num'">
                      <a-input
                        v-model:value="record[column.dataIndex]"
                        :placeholder="`请输入${column.title}`"
                        @click="openInteriorList(index, column.dataIndex, $event)"
                      />
                    </template>
                  </template>
                </a-table>
              </div>
            </a-row>
          </a-card>

          <a-card title="事件合并" class="!mt-5" :id="anchorItems[11].id">
            <a-row>
              <a-col :span="12">
                <a-form-item label="合并状态" v-bind="validateInfos.mergeState" id="LdcEventManageForm-mergeState" name="mergeState">
                  <j-dict-select-tag
                    v-model:value="formData.mergeState"
                    dictCode="ldc_mergeState"
                    placeholder="请选择合并状态"
                    disabled
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>

          <a-card title="附件" class="!mt-5" :id="anchorItems[12].id">
            <j-vxe-table
              :keep-source="true"
              resizable
              :disabledRows="{state: '1'}"
              ref="ldcEventDocumentTableRef"
              :loading="ldcEventDocumentTable.loading"
              :columns="ldcEventDocumentTable.columns"
              :dataSource="ldcEventDocumentTable.dataSource"
              :height="340"
              :rowNumber="true"
              :rowSelection="true"
              :toolbar="true"
            />
            <!-- <a-button type="primary" onClick="addNewDocument" >新增</a-button>
						<a-table ref="table" 
							:columns="columns" 
							:pagination="false"
							:data-source="tableData1" bordered size="middle" 
							rowKey="id">
						</a-table> -->
          </a-card>
          <!-- <a-row>

						<a-col :span="12">
							<a-form-item label="关联制度主键" v-bind="validateInfos.regulationId" id="LdcEventManageForm-regulationId" name="regulationId">
								<a-input v-model:value="formData.regulationId" placeholder="请输入关联制度主键"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						
          </a-row> -->
          <!--<div style="margin: auto;  margin-left: 36%; margin-top: 20px;">
						<a-button preIcon="ant-design:save-outlined" type="primary" @click="save">保存</a-button>
						<a-button preIcon="ant-design:check-outlined" style="margin-left: 5px;" type="default" @click="submit"> 提交</a-button>
						<a-button preIcon="ant-design:redo-outlined" style="margin-left: 5px;" type="default" @click="reset"> 重置</a-button>
						<a-button preIcon="ant-design:close-outlined" style="margin-left: 5px;" type="default" @click="cancel"> 取消</a-button>
					</div>-->
        </a-form>
      </a-spin>
    </div>
    <div class="button-container" style="background-color: #fff6f0; margin: auto; text-align: center; margin-top: 20px">
      <a-button preIcon="ant-design:save-outlined" type="primary" @click="save" v-if="isShowSaveButton">保存</a-button>
      <a-button preIcon="ant-design:check-outlined" style="margin-left: 5px" type="default" @click="submit"> 提交</a-button>
      <a-button preIcon="ant-design:redo-outlined" style="margin-left: 5px" type="default" @click="reset"> 重置</a-button>
      <a-button preIcon="ant-design:close-outlined" style="margin-left: 5px" type="default" @click="cancel"> 取消</a-button>
    </div>
    <!-- </template>
		</JFormContainer> -->
  </div>
  <LdcClueModal ref="registerModal" @success="getResult"></LdcClueModal>
  <RegulatoryVersionListModal ref="registerModal2" @success="getRegulatory" />
  <LdcEventRecycleDetailsHistoryListModal ref="registerModal3"></LdcEventRecycleDetailsHistoryListModal>
  <RiskCauseVersionListModal ref="registerModal4" @success="getRiskCase"></RiskCauseVersionListModal>
  <LdcClueDetailModal ref="registerModal5"></LdcClueDetailModal>
  <ExteriorsList ref="exteriorsRefModel" @associate-data="handleEexteriorData" />
  <InteriorsList ref="interiorsRefModel" @associate-data="handleInteriorData" />


</template>

<script lang="ts" setup>
import { LeftCircleOutlined, RightCircleOutlined } from '@ant-design/icons-vue';
import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted, onBeforeMount, watchEffect } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';
import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
import JSelectUser from '/@/components/Form/src/jeecg/components/JSelectUser.vue';
import { getValueType } from '/@/utils';
import {
  saveOrUpdate,
  queryAllEventClassifyList,
  getMyOrganDepartInfo,
  getRecycleListByManageId,
  submitFromForm,
  getEventCode,
  queryLdcEventDocumentListByMainId,
  queryLedgerList,
  queryEventNameHistory,
  judgeSeverityLevel,
  queryModuleRelInfo,
  queryCurrencyRate,
  querySubjectList,
  queryRuleRelList,
} from '../LdcEventManage.api';
import { Form, message } from 'ant-design-vue';
import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
import { useUserStore } from '/@/store/modules/user';
import dayjs from 'dayjs';
// import {recycleDetailsColumns} from '../LdcEventManage.data';
import { useValidateAntFormAndTable } from '/@/hooks/system/useJvxeMethods';
import { JVxeTypes, JVxeColumn, JVxeLinkageConfig } from '/@/components/jeecg/JVxeTable/types';
import LdcClueModal from './LdcClueModal.vue';
// import LdcEcharts from '../LdcEcharts.vue';
import { columns } from '../../param/importorec/LdcParamOrImportOrecVersion.data';
import { getListNoPage } from '../../param/importorec/LdcParamOrImportOrecVersion.api';
import { CodeSandboxCircleFilled } from '@ant-design/icons-vue';
import RegulatoryVersionListModal from '../../param/regulatory/components/RegulatoryVersionListModal.vue';
import { Modal } from 'ant-design-vue';
import { columnsTZ } from '@/views/ldc/clue/ledger/LedgerClue.data';
import { columnsEventName } from '../LdcEventManage.data';
import LdcEventRecycleDetailsHistoryListModal from '../../detailsHistory/LdcEventRecycleDetailsHistoryListModal.vue';
import RiskCauseVersionListModal from '../../param/riskCause/RiskCauseVersionListModal.vue';
import LdcClueDetailModal from './LdcClueDetailModal.vue';
import { queryMatrixList } from '../../../rcsa/scheme/RcsaSchemeManage.api';
import ExteriorsList from '@/views/rule/regulations/manage/reformedAndAbolished/components/external/ExteriorsList.vue';
import InteriorsList from '@/views/rule/regulations/manage/reformedAndAbolished/components/interior/InteriorList.vue';


const isDisabledRegulations = ref<boolean>(false);
const mainProcessList = ref([]);
const rcsaRelInfoList = ref([]);
const subjectCodeList = ref([]);
const subjectNameList = ref([]);

const events = [
  {
    time: '2025-04-03 12:00',
    name: '事件名称：2025年XX分行人民银行【XX】反洗钱处罚事件',
    description:
      '事件描述：我行xx客户，于20xx年x月x日在我行办理贸易融资业务，金额xx元，后于20xx年x月x日该客户用该笔资金偿还我行于20xx年x月x日向其签发的银行承兑汇票，共计xx元。xx监管机构于20xx年x月x日对我行xx分行开展xx专项检查，共发现问题xx笔（可将问题举例说明一下），我行因违反《xxx》（外规名称）第xx条，xx监管于xx日向我行开具《xxx》处罚通知，罚款金额共计RMB xx元，我行于xx月xx日收到处罚决定书，目前我行该笔处罚已于20xx年x月x日入账，该笔事件已结束。',
    reason:
      '事件原因描述：我行xx员工在开展xx业务时在xx环节/流程，未对企业通过关联交易虚构贸易背景进行融资套现的风险进行严格审核。未发现借款人通过多家关联企业之间的关联交易虚增销售收入，造成借款企业融资交易背景虚假，企业贷款资金用途不真实，贷款资金易被挪用。',
  },
  // {
  //   time: '2025-04-03 12:00',
  //   name: '事件名称：2025年XX分行人民银行【XX】反洗钱处罚事件',
  //   description: '事件描述：我行xx客户，于20xx年x月x日在我行办理贸易融资业务，金额xx元，后于20xx年x月x日该客户用该笔资金偿还我行于20xx年x月x日向其签发的银行承兑汇票，共计xx元。xx监管机构于20xx年x月x日对我行xx分行开展xx专项检查，共发现问题xx笔（可将问题举例说明一下），我行因违反《xxx》（外规名称）第xx条，xx监管于xx日向我行开具《xxx》处罚通知，罚款金额共计RMB xx元，我行于xx月xx日收到处罚决定书，目前我行该笔处罚已于20xx年x月x日入账，该笔事件已结束。',
  //   reason: '事件原因描述：我行xx员工在开展xx业务时在xx环节/流程，未对企业通过关联交易虚构贸易背景进行融资套现的风险进行严格审核。未发现借款人通过多家关联企业之间的关联交易虚增销售收入，造成借款企业融资交易背景虚假，企业贷款资金用途不真实，贷款资金易被挪用。'
  // },
];

const ldcEventDocumentColumns = ref<JVxeColumn[]>([
  {
    title: '附件类型',
    key: 'documentType',
    type: JVxeTypes.select,
    options: [],
    dictCode: 'ldc_document_type',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '附件说明',
    key: 'documentExplain',
    type: JVxeTypes.textarea,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '附件名称',
    key: 'documentName',
    type: JVxeTypes.file,
    token: true,
    responseName: 'message',
    width: '200px',
    placeholder: '请选择文件',
    defaultValue: '',
  },
]);

const activeKeyDocument = ref('ldcEventDocument');
const ldcEventDocumentTableRef = ref();
const ldcEventDocumentTable = reactive<Record<string, any>>({
  loading: false,
  columns: ldcEventDocumentColumns,
  dataSource: [],
});

const registerModal = ref();
const registerModal2 = ref();
const registerModal3 = ref();
const registerModal4 = ref();
const registerModal5 = ref();
const recycleDetailsColumns = ref<JVxeColumn[]>([
  {
    title: '损失事件主键',
    key: 'ldcEventId',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    visible: false,
  },
  {
    title: '类别',
    key: 'category',
    type: JVxeTypes.select,
    options: [],
    dictCode: 'ldc_recycle_category',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '子类',
    key: 'subClass',
    type: JVxeTypes.select,
    options: [],
    dictCode: 'ldc_recycle_sub_class',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '损失形态',
    key: 'lossForm',
    type: JVxeTypes.select,
    options: [],
    dictCode: 'ldc_recycle_loss_form',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '机构',
    key: 'orgId',
    type: JVxeTypes.departSelect,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '部门',
    key: 'departNo',
    type: JVxeTypes.departSelect,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '科目代码',
    key: 'subjectCode',
    type: JVxeTypes.select,
    options: subjectCodeList,
    //dict: "external_data.zzxt_cux_fnd_all_account_v,account_name as text,account_code as value",
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    allowSearch: true,
  },
  {
    title: '科目名称',
    key: 'subjectName',
    type: JVxeTypes.select,
    options: subjectNameList,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '记账币种',
    key: 'currency',
    type: JVxeTypes.select,
    options: [],
    dictCode: 'currency',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '记账金额',
    key: 'amount',
    type: JVxeTypes.inputNumber,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '对人民币汇率',
    key: 'exchangeRate',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '人民币金额（元）',
    key: 'cnyAmount',
    type: JVxeTypes.normal,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '入账日期',
    key: 'postingDate',
    type: JVxeTypes.normal,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '说明',
    key: 'explanation',
    type: JVxeTypes.textarea,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '附件',
    key: 'document',
    type: JVxeTypes.file,
    token: true,
    responseName: 'message',
    width: '200px',
    placeholder: '请选择文件',
    defaultValue: '',
  },
  {
    title: '操作',
    key: 'action',
    type: JVxeTypes.slot,
    fixed: 'right',
    minWidth: 300,
    align: 'center',
    slotName: 'myAction',
  },
]);

const activeKey = ref('recycleDetails');
const ldcEventRecycleDetailsTableRef = ref();
const recycleDetailsTable = reactive<Record<string, any>>({
  loading: false,
  columns: recycleDetailsColumns,
  dataSource: [],
});

// 定义锚点数据
const anchorItems = ref([
  {
    id: 'section1',
    title: '基本信息',
  },
  {
    id: 'section2',
    title: '时间信息',
  },
  {
    id: 'section3',
    title: '机构/人员信息',
  },
  {
    id: 'section4',
    title: '损失和影响信息',
  },
  {
    id: 'section5',
    title: '非财务影响信息',
  },
  {
    id: 'section6',
    title: '事件分段信息',
  },
  {
    id: 'section7',
    title: '事件属性',
  },
  {
    id: 'section8',
    title: '关联台账',
  },
  {
    id: 'section9',
    title: '事件关联RCSA',
  },
  {
    id: 'section10',
    title: '事件反映出的问题与整改措施',
  },
  {
    id: 'section11',
    title: '事件关联内外规',
  },
  {
    id: 'section12',
    title: '事件合并',
  },
  {
    id: 'section13',
    title: '附件',
  },
]);

// 滚动到指定锚点的方法
const scrollToSection = (id: string) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ block: 'start', behavior: 'smooth' });
  }
};
function getContainer() {
  // 给组件指定渲染的容器，解决锚点不会随页面滚动而移动的问题
  return document.querySelector('.form-wrapper');
}
/** Anchor 锚点形式点击锚点 */
function handleClickAnchor(e, link) {
  // 阻止点击的默认事件修改路由
  e.preventDefault();
  if (link.href) {
    const element = document.querySelector(link.href);
    element && element.scrollIntoView({ block: 'start', behavior: 'smooth', alignToTop: 'false' });
  }
}
const props = defineProps({
  formDisabled: { type: Boolean, default: false },
  formData: { type: Object, default: () => ({}) },
  formBpm: { type: Boolean, default: true },
  operateFlag: { type: String },
  manageInfo: { type: Object },
});

// let propsData = defineProps(['manageInfo']);

// console.log('propsData.manageInfo', propsData.manageInfo);

const userStore = useUserStore();
const eventClassifySecondList = ref([]);
const eventClassifyFirstList = ref([]);
const nameStandard = ref('命名规范');
const describeCriteria = ref('事件描述规则');
const eventCauseStandard = ref('事件原因描述规则');
const eventEndstutsCognizance = ref('事件结束状态认定规则');
const eventFirstDate = ref('事件初始发生日期认定规则');
const eventFoundDate = ref('事件发现发生日期认定规则');
const isShowEvent = ref<boolean>(false);
const isZHSHG = ref<boolean>(false);
const visible = ref<boolean>(false);
const tableData1 = ref([]);
const isShowButton = ref<boolean>(false);
const isShowSaveButton = ref<boolean>(true);
const isDisabled = ref<boolean>(false);
const isShowName1 = ref<boolean>(false);
const isShowName2 = ref<boolean>(false);
const visible2 = ref<boolean>(false);
const tableData2 = ref([]);
const ledgerType_dictTexts = ref([]);
const ledgerNumbers = ref([]);
const relLedgerIds = ref([]);
const visible3 = ref<boolean>(false);
const tableData3 = ref([]);
const isShowRecycle = ref<boolean>(false);

const formRef = ref();
const useForm = Form.useForm;
const emit = defineEmits(['register', 'ok', 'manageBack']);
const fillCount = ref(1);
const formData = reactive<Record<string, any>>({
  state: '',
  id: '',
  eventCode: '',
  eventClassifyFirst: '',
  eventClassifySecond: '',
  eventName: '',
  eventDescrption: '',
  eventReasonDescription: '',
  // fillCount: 1,
  isEnd: '',
  isLossEvent: '',
  isRemove: '0',
  firstFillDate: '',
  thisFillDate: '',
  initialHappenDate: '',
  findDate: '',
  firstEntryDate: '',
  fillOrgan: '',
  fillDepart: '',
  fillUser: '',
  fillUserContact: '',
  duetManageDepart: '',
  happenOrgan: '',
  happenDepart: '',
  maxEstimateLossMoney: undefined,
  totalLossMoney: undefined,
  cfmdRecycleMoney: undefined,
  netLossMoney: undefined,
  cfmdIndemnity: undefined,
  reputationDamage: '',
  operationInterruption: '',
  customerServiceQuality: '',
  regulatoryActions: '',
  employeeSafety: '',
  severityClassification: '',
  isMajorRiskEvent: '',
  mainCauseClassification: '',
  eventTypeFirst: '',
  eventTypeSecond: '',
  eventTypeThird: '',
  creditRiskRelevant: '',
  isIntoMetering: '0',
  relLedgerType: '',
  relLedgerCode: '',
  mainProcess: '',
  mainProcesses: [],
  isInitiateRcsa: '',
  rcsaPlanCode: '',
  rcsaPlanName: '',
  rcsaPlanState: '',
  isIntoHistory: '',
  historyCode: '',
  isViolateRegulations: '',
  regulationId: '',
  mergeState: '1',
  status_dictText: '草稿',
  temporaryEntryId: '',
  serialIndex: '',
  relLedgerId: '',
  isUpdateProcess: '',
});
const { createMessage } = useMessage();
const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 7 } });
const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 14 } });

const confirmLoading = ref<boolean>(false);
//表单验证
const validatorRules = reactive({

  reputationDamage: [{required: computed(() => formData.isLossEvent != null && formData.isLossEvent == '0'), message: '请选择声誉受损!'},],
  operationInterruption: [{required: computed(() => formData.isLossEvent != null && formData.isLossEvent == '0'), message: '请选择运营中断!'},],
  customerServiceQuality: [{required: computed(() => formData.isLossEvent != null && formData.isLossEvent == '0'), message: '请选择广大客户服务质量!'},],
  regulatoryActions: [{required: computed(() => formData.isLossEvent != null && formData.isLossEvent == '0'), message: '请选择监管行动!'},],
  employeeSafety: [{required: computed(() => formData.isLossEvent != null && formData.isLossEvent == '0'), message: '请选择员工安全!'},],

  eventCode: [{ required: true, message: '请输入事件编号!' }],
  eventClassifyFirst: [{ required: true, message: '请输入事件分类(一级)!' }],
  eventClassifySecond: [{ required: true, message: '请输入事件分类(二级)!' }],
  eventName: [
    { required: true, message: '请输入事件名称!' },
    { pattern: /^.{6,16}$/, message: '请输入6到16位任意字符!' },
  ],
  eventDescrption: [{ required: true, message: '请输入事件描述!' }],
  eventReasonDescription: [{ required: true, message: '请输入事件原因描述!' }],
  fillCount: [{ required: true, message: '请输入第几次填报!' }],
  isEnd: [{ required: true, message: '请输入是否结束!' }],
  isLossEvent: [{ required: true, message: '请输入是否为损失事件!' }],
  isRemove: [{ required: true, message: '请输入是否经监管认可剔除!' }],
  firstFillDate: [{ required: true, message: '请输入初次填报日期!' }],
  thisFillDate: [{ required: true, message: '请输入本次填报日期!' }],
  initialHappenDate: [{ required: true, message: '请输入事件初始发生日期!' }],
  findDate: [{ required: true, message: '请输入事件发现日期!' }],
  fillOrgan: [{ required: true, message: '请输入事件填报机构!' }],
  fillDepart: [{ required: true, message: '请输入事件填报部门!' }],
  fillUser: [{ required: true, message: '请输入事件填报人!' }],
  fillUserContact: [{ required: true, message: '请输入填报人联系方式!' }],
  duetManageDepart: [{ required: true, message: '请输入总行对口管理部门!' }],
  happenOrgan: [{ required: true, message: '请输入发生机构!' }],
  happenDepart: [{ required: true, message: '请输入发生部门!' }],
  totalLossMoney: [{ required: true, message: '请输入总损失金额!' }],
  cfmdRecycleMoney: [{ required: true, message: '请输入已确认的回收总金额!' }],
  netLossMoney: [{ required: true, message: '请输入净损失金额!' }],
  cfmdIndemnity: [{ required: true, message: '请输入其中：已确认的保险赔付!' }],
  //severityClassification: [{ required: true, message: '请输入事件严重度分级!' }],
  isMajorRiskEvent: [{ required: true, message: '请输入是否重大操作风险事件!' }],
  mainCauseClassification: [{ required: true, message: '请输入主要成因分类!' },],
  eventTypeFirst: [{ required: true, message: '请输入损失事件类型(一级)!' }],
  eventTypeSecond: [{ required: true, message: '请输入损失事件类型(二级)!' }],
  eventTypeThird: [{ required: true, message: '请输入损失事件类型(三级)!' }],
  creditRiskRelevant: [{ required: true, message: '请输入与信用/市场风险相关!' }],
  isIntoMetering: [{ required: true, message: '请输入是否纳入计量!' }],
  mainProcess: [{ required: true, message: '请输入发生操作风险事件的主要流程!' },],
  isInitiateRcsa: [{ required: true, message: '请输入是否发起触发式评估计划!' }],
  isIntoHistory: [{ required: true, message: '请输入是否录入操作风险管理系统-问题收集模块!' }],
  isViolateRegulations: [{ required: true, message: '请输入是否违反内外规!' }],
  mergeState: [{ required: true, message: '请输入合并状态!' }],
});
const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

// 表单禁用
const disabled = computed(() => {
  if (props.formBpm === true) {
    if (props.formData.disabled === false) {
      return false;
    } else {
      return true;
    }
  }
  return props.formDisabled;
});

/**
 * 新增
 */
function add() {
  edit({});
}

/**
 * 编辑
 */
function edit(record) {
  nextTick(() => {
    resetFields();
    const tmpData = {};
    Object.keys(formData).forEach((key) => {
      if (record.hasOwnProperty(key)) {
        tmpData[key] = record[key];
      }
    });
    //赋值
    Object.assign(formData, tmpData);
  });
}

const { getSubFormAndTableData, transformData } = useValidateAntFormAndTable(activeKey, {
  ldcEventRecycleDetails: ldcEventRecycleDetailsTableRef,
  ldcEventDocument: ldcEventDocumentTableRef,
});

/**
 * 保存数据
 */
async function save() {
  if (formData.isViolateRegulations == '1') {
    if (ruleExternalInternalDetailTable.dataSource.length == 0 && ruleExternalLawDetailTable.dataSource == 0) {
      message.warning("当违反内外规时，关联外规或者关联内规必选");
      return;
    }
  }

  const subData = await getSubFormAndTableData();
  const list = subData.ldcEventRecycleDetailsList;
  const list2 = subData.ldcEventDocumentList;
  try {
    // 触发表单验证
    await validate();
  } catch ({ errorFields }) {
    if (errorFields) {
      const firstField = errorFields[0];
      if (firstField) {
        formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
      }
    }
    return Promise.reject(errorFields);
  }

  // 手工校验
  if (new Date(formData.thisFillDate) < new Date(formData.initialHappenDate)) {
    message.warning('事件初始发生日期不能晚于本次填报日期');
    return;
  }
  if (new Date(formData.thisFillDate) < new Date(formData.findDate)) {
    message.warning('事件初始发生日期不能晚于事件发现日期');
    return;
  }
  if (new Date(formData.initialHappenDate) > new Date(formData.findDate)) {
    message.warning('事件初始发生日期不能晚于事件发现日期');
    return;
  }

  if (new Date(formData.firstEntryDate) > new Date(formData.initialHappenDate)) {
    message.warning('首笔损失入账(或确认)日期不能早于事件初始发生日期');
    return;
  }
  if (formData.maxEstimateLossMoney != null) {
    if (formData.maxEstimateLossMoney < formData.totalLossMoney) {
      message.warning('最大预估损失金额不得小于总损失金额');
      return;
    }
  }
  if (formData.isLossEvent == '1') {
    if (formData.maxEstimateLossMoney != null && formData.maxEstimateLossMoney < 10000) {
      message.warning('存在损失事件时，最大预估损失金额不得小于一万元人民币');
      return;
    }
  }
  if (formData.isEnd == '1' && formData.isLossEvent == '1' && (list == null || list.length == 0)) {
    message.warning('事件已结束，请添加损失明细');
    return;
  }
  if (formData.isLossEvent == '0') {
    if (formData.reputationDamage == null || formData.reputationDamage == '') {
      message.warning('不为损失事件时，非财务影响声誉受损必填');
      return;
    }
    if (formData.operationInterruption == null || formData.operationInterruption == '') {
      message.warning('不为损失事件时，非财务影响运营中断必填');
      return;
    }
    if (formData.customerServiceQuality == null || formData.customerServiceQuality == '') {
      message.warning('不为损失事件时，非财务影响广大客户服务质量必填');
      return;
    }
    if (formData.regulatoryActions == null || formData.regulatoryActions == '') {
      message.warning('不为损失事件时，非财务影响监管行动必填');
      return;
    }
    if (formData.employeeSafety == null || formData.employeeSafety == '') {
      message.warning('不为损失事件时，非财务影响员工安全必填');
      return;
    }
  }

  //时间格式化
  let model = formData;
  //循环数据
  for (let data in model) {
    //如果该数据是数组并且是字符串类型
    if (model[data] instanceof Array) {
      let valueType = getValueType(formRef.value.getProps, data);
      //如果是字符串类型的需要变成以逗号分割的字符串
      if (valueType === 'string') {
        model[data] = model[data].join(',');
      }
    }
  }
  confirmLoading.value = true;

  model.detailsList = list;
  model.documentList = list2;
  const isUpdate = ref<boolean>(false);
  if (model.id) {
    isUpdate.value = true;
  }
  if (props.operateFlag == '3') {
    model.beCopyId = beCopyId.value;
  }
  model.mainProcess = model.mainProcesses;
  model.interiorList = ruleExternalInternalDetailTable.dataSource;
  model.externalList = ruleExternalLawDetailTable.dataSource;
  await saveOrUpdate(model, isUpdate.value).then((res) => {
    if (res.success) {
      createMessage.success('保存成功');
      isShowButton.value = true;
      isShowSaveButton.value = false;
      formData.id = res.result.id;
      recycleDetailsTable.dataSource = res.result.detailsList;
      queryRcsaList(res.result);
    } else {
      createMessage.warning(res.message);
      confirmLoading.value = false;
    }
  });
  // .finally(() => {
  // 	confirmLoading.value = false;
  // });
}

async function queryRcsaList(record) {
  // 查询关联的RCSA
  let rcsaParam = {
    'moduleId2': record.id,
    'moduleFlag': '1',
  }
  const rcsaList = await queryModuleRelInfo(rcsaParam);
  rcsaRelInfoList.value = rcsaList.result;
}

async function submit() {
  if (formData.isViolateRegulations == '1') {
    if (ruleExternalInternalDetailTable.dataSource.length == 0 && ruleExternalLawDetailTable.dataSource == 0) {
      message.warning("当违反内外规时，关联外规或者关联内规必选");
      return;
    }
  }

  const subData = await getSubFormAndTableData();
  const list = subData.ldcEventRecycleDetailsList;
  const list2 = subData.ldcEventDocumentList;
  try {
    // 触发表单验证
    await validate();
  } catch ({ errorFields }) {
    if (errorFields) {
      const firstField = errorFields[0];
      if (firstField) {
        formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
      }
    }
    return Promise.reject(errorFields);
  }

  // 手工校验
  if (new Date(formData.thisFillDate) < new Date(formData.initialHappenDate)) {
    message.warning('事件初始发生日期不能晚于本次填报日期');
    return;
  }
  if (new Date(formData.thisFillDate) < new Date(formData.findDate)) {
    message.warning('事件初始发生日期不能晚于事件发现日期');
    return;
  }
  if (new Date(formData.initialHappenDate) > new Date(formData.findDate)) {
    message.warning('事件初始发生日期不能晚于事件发现日期');
    return;
  }

  if (new Date(formData.firstEntryDate) > new Date(formData.initialHappenDate)) {
    message.warning('首笔损失入账(或确认)日期不能早于事件初始发生日期');
    return;
  }
  if (formData.maxEstimateLossMoney != null) {
    if (formData.maxEstimateLossMoney < formData.totalLossMoney) {
      message.warning('最大预估损失金额不得小于总损失金额');
      return;
    }
  }
  if (formData.isLossEvent == '1') {
    if (formData.maxEstimateLossMoney != null && formData.maxEstimateLossMoney < 10000) {
      message.warning('存在损失事件时，最大预估损失金额不得小于一万元人民币');
      return;
    }
  }
  if (formData.isEnd == '1' && formData.isLossEvent == '1' && (list == null || list.length == 0)) {
    message.warning('事件已结束，请添加损失明细');
    return;
  }
  if (formData.isLossEvent == '0') {
    if (formData.reputationDamage == null || formData.reputationDamage == '') {
      message.warning('不为损失事件时，非财务影响声誉受损必填');
      return;
    }
    if (formData.operationInterruption == null || formData.operationInterruption == '') {
      message.warning('不为损失事件时，非财务影响运营中断必填');
      return;
    }
    if (formData.customerServiceQuality == null || formData.customerServiceQuality == '') {
      message.warning('不为损失事件时，非财务影响广大客户服务质量必填');
      return;
    }
    if (formData.regulatoryActions == null || formData.regulatoryActions == '') {
      message.warning('不为损失事件时，非财务影响监管行动必填');
      return;
    }
    if (formData.employeeSafety == null || formData.employeeSafety == '') {
      message.warning('不为损失事件时，非财务影响员工安全必填');
      return;
    }
  }


  confirmLoading.value = true;
  //时间格式化
  let model = formData;
  //循环数据
  for (let data in model) {
    //如果该数据是数组并且是字符串类型
    if (model[data] instanceof Array) {
      let valueType = getValueType(formRef.value.getProps, data);
      //如果是字符串类型的需要变成以逗号分割的字符串
      if (valueType === 'string') {
        model[data] = model[data].join(',');
      }
    }
  }

  model.detailsList = list;
  model.documentList = list2;

  if (props.operateFlag == '3') {
    model.beCopyId = beCopyId.value;
  }
  model.mainProcess = model.mainProcesses;
  model.interiorList = ruleExternalInternalDetailTable.dataSource;
  model.externalList = ruleExternalLawDetailTable.dataSource;

  const res = await submitFromForm(model);
  if (res.success) {
    message.success(res.message);
    emit('manageBack');
  } else {
    message.warn(res.message);
  }
  confirmLoading.value = false;
}

// 重置
function reset() {
  Modal.confirm({
    title: '确认重置',
    content: '是否确认将已填写的内容进行清空？',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      // 确认回调
      formRef.value.resetFields();
      getManageInfo();
      // 损失事件置空
      recycleDetailsTable.dataSource = [];
    },
    onCancel() {
      // 取消回调
      console.log('取消重置操作');
    },
  });
}

// 取消
function cancel() {
  Modal.confirm({
    title: '确认重置',
    content: '还未保存当前操作，是否继续取消？',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      // 确认回调
      emit('manageBack');
    },
    onCancel() {
      // 取消回调
      console.log('取消重置操作');
    },
  });
}
computed: {
  // 计算属性：如果state为0，则表格被禁用
  // isTableDisabled() {
  //   return formData.state === 0;
  // }
}

/**
 * 提交数据
 */
async function submitForm() {
  try {
    // 触发表单验证
    await validate();
  } catch ({ errorFields }) {
    if (errorFields) {
      const firstField = errorFields[0];
      if (firstField) {
        formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
      }
    }
    return Promise.reject(errorFields);
  }
  confirmLoading.value = true;
  const isUpdate = ref<boolean>(false);
  //时间格式化
  let model = formData;
  if (model.id) {
    isUpdate.value = true;
  }
  //循环数据
  for (let data in model) {
    //如果该数据是数组并且是字符串类型
    if (model[data] instanceof Array) {
      let valueType = getValueType(formRef.value.getProps, data);
      //如果是字符串类型的需要变成以逗号分割的字符串
      if (valueType === 'string') {
        model[data] = model[data].join(',');
      }
    }
  }
  await saveOrUpdate(model, isUpdate.value)
    .then((res) => {
      if (res.success) {
        createMessage.success(res.message);
        emit('ok');
      } else {
        createMessage.warning(res.message);
      }
    })
    .finally(() => {
      confirmLoading.value = false;
    });
}

onBeforeMount(async () => {
  getManageInfo();
});

const beCopyId = ref('');

async function getManageInfo() {
  confirmLoading.value = true;
  isDisabledRegulations.value = false;
  const res = await queryAllEventClassifyList();
  console.log('huanjing', res)
  eventClassifySecondList.value = res;
  eventClassifyFirstList.value = res;

  // 查询科目号
  const subjectList = await querySubjectList();
  subjectCodeList.value = subjectList.result.subjectCodeList;
  subjectNameList.value = subjectList.result.subjectNameList;

  // 查询本部门的主要流程
  let param3 = {
    'evaluateDepart': '',
  }
  const res3 = await queryMatrixList(param3);
  mainProcessList.value = res3;

  // 判断用户角色
  // 判断当前用户有没有权限
  if (userStore.getUserInfo.roleList.includes('zh_ldc_shg')) {
    isZHSHG.value = true;
  } else {
    isZHSHG.value = false;
  }

  // 查询2-1.5操作风险原因分类(待定)
  // const result = await queryRiskCaseList();
  // if (result.success) {

  // }

  // 获取本人的机构和部门
  let param = {};
  const res2 = await getMyOrganDepartInfo(param);
  if (res2.success) {
    formData.fillOrgan = res2.result.fillOrgan;
    formData.fillDepart = res2.result.fillDepart;
  }

  // 获取操作风险损失事件分类 一级分类
  // const res3 = await get

  // 只有新增的时候才需要回显

  if (props.operateFlag == '1') {
    formData.fillUserContact = userStore.getUserInfo.telephone;
    // 新增默认初次填报日期为当前日期
    formData.firstFillDate = dayjs().format('YYYY-MM-DD');
    formData.thisFillDate = dayjs().format('YYYY-MM-DD');
    // 填入填报人
    formData.fillUser = userStore.getUserInfo.username;
    // 获取编号
    const res = await getEventCode();
    if (res.success) {
      formData.serialIndex = res.result.serialIndex;
      formData.eventCode = res.result.eventCode;
    } else {
      message.error(res.message);
      confirmLoading.value = false;
      return;
    }
    isShowName1.value = true;
    isShowName2.value = false;
  } else {
    let record = props.manageInfo;

    // 查询关联内外规
    let ruleParam = {
      'id': record.id
    }
    const ruleResult = await queryRuleRelList(ruleParam);
    ruleExternalInternalDetailTable.dataSource = ruleResult.result.interiorList;// 内规
    ruleExternalLawDetailTable.dataSource = ruleResult.result.externalList;// 外规

    // 查询关联的RCSA
    let rcsaParam = {
      'moduleId2': record.id,
      'moduleFlag': '1',
    }
    const rcsaList = await queryModuleRelInfo(rcsaParam);
    rcsaRelInfoList.value = rcsaList.result;

    if (record.isUpdateProcess == '1') {
      isShowName1.value = false;
      isShowName2.value = true;
      fillCount.value = record.fillCount + 1;
      isShowRecycle.value = true;
    } else {
      isShowName1.value = true;
      isShowName2.value = false;
      isShowRecycle.value = false;
    }

    // 查询关联台账
    let param2 = {
      eventId: record.id,
      pageNo: 1,
      pageSize: 300,
    };
    const res6 = await queryLedgerList(param2);
    tableData2.value = res6.records;


    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if (record.hasOwnProperty(key)) {
          tmpData[key] = record[key];
        }
      });
      //赋值
      Object.assign(formData, tmpData);
      if (props.operateFlag == '3') {
        // 表示复制新增，需要将id置为空
        beCopyId.value = record.id;
        formData.id = '';
        formData.firstFillDate = dayjs().format('YYYY-MM-DD');
        formData.thisFillDate = dayjs().format('YYYY-MM-DD');
        // 填入填报人
        formData.fillUser = userStore.getUserInfo.username;
      }
      if (record.mainProcess != null && record.mainProcess != '') {
        formData.mainProcesses = record.mainProcess.split(',');
      }
      // if (rcsaList.success) {
      //   formData.rcsaPlanCode = rcsaList.result.planCode;
      //   formData.rcsaPlanName = rcsaList.result.planTitle;
      //   formData.rcsaPlanState = rcsaList.result.stateName;
      // }

      if (tableData2.value == null || tableData2.value.length == 0) {
        formData.relLedgerType == '';
        formData.relLedgerCode == '';
        formData.relLedgerId == '';
      } else {
        tableData2.value.forEach((item, index) => {
          // 如果包含行政处罚，是否违反内外规为是，不可修改
          if(item.ledgerType_dictText.indexOf('行政处罚')) {
            formData.isViolateRegulations = '1';
            isDisabledRegulations.value = true;
          }
          if (ledgerType_dictTexts.value.indexOf(item.ledgerType_dictText) > -1) {
          } else {
            ledgerType_dictTexts.value.push(item.ledgerType_dictText);
          }
          ledgerNumbers.value.push(item.ledgerNumber);
          relLedgerIds.value.push(item.id);
        });
        formData.relLedgerType = ledgerType_dictTexts.value.join(',');
        formData.relLedgerCode = ledgerNumbers.value.join(',');
        formData.relLedgerId = relLedgerIds.value.join(',');
      }
    });
    if ((record.isLossEvent = '1')) {
      isShowEvent.value = true;
    }
    // 根据LDC主键查找损失回收明细
    const res4 = await getRecycleListByManageId(record);
    recycleDetailsTable.dataSource = res4.result;
    if (props.operateFlag == '3') {
      // 表示复制新增，需要将损失回收明细id置为空
      recycleDetailsTable.dataSource.forEach((item) => {
        item.id = null;
        item.ldcEventId = '';
      });
    }

    // 查询附件
    const res5 = await queryLdcEventDocumentListByMainId(record.id);
    ldcEventDocumentTable.dataSource = res5;
  }
  confirmLoading.value = false;
}

function getSecondInfo(item) {
  formData.eventClassifyFirst = item.eventLevelOne;
  nameStandard.value = '命名规范:' + item.namingConventions + '\n' + '名称示例:' + item.exampleName;
  describeCriteria.value = '描述标准:' + item.describeCriteria + '\n' + '规范性描述示例:' + item.specificationExamples;
  eventCauseStandard.value = '事件原因描述标准:' + item.eventCauseStandard + '\n' + '事件原因描述示例:' + item.eventCauseExample;
  eventEndstutsCognizance.value = item.eventEndstutsCognizance;
  eventFirstDate.value = item.eventFirstDate;
  eventFoundDate.value = item.eventFoundDate;
}

function validateField2(row) {}

const currentRowIndex = ref(null);
const currentRow = ref({});
async function relEntryClue(record) {  
  currentRowIndex.value = record.rowIndex;
  let row = record.row;
  if (row.category == null || row.category == '') {
    message.warning("请先选择类别，再关联入账线索");
    return;
  }
  const subData = await getSubFormAndTableData();
  const list = subData.ldcEventRecycleDetailsList;
  const removeIds = ref([]);
  const manualRemoveIds = ref([]);
  // 遍历list, 得到已经关联的数据（排除本身）
  list.forEach((item, index) => {
    if (currentRowIndex.value != index) {
      if (item.relIds != null && item.relIds != '' && item.relIds.length > 0) {
        item.relIds.split(',').forEach((item) => {
          removeIds.value.push(item);
        });
      }

      if (item.manualRelIds != null && item.manualRelIds != '' && item.manualRelIds.length > 0) {
        item.manualRelIds.split(',').forEach((item) => {
          manualRemoveIds.value.push(item);
        });
      }
    }
  });
  row.firstEntryDateOld = formData.firstEntryDate;
  row.removeIds = removeIds.value.join(',');
  row.manualRemoveIds = manualRemoveIds.value.join(',');
  row.eventManageId = formData.id;
  currentRow.value = row;
  registerModal.value.showClue(row);
}

let firstEntryDateCom = '';
async function getResult(relIds, manualRelIds, detail) {
  if (currentRow != null && currentRow != '') {
    if (relIds != null && relIds.length > 0) {
      currentRow.value.relIds = relIds.join(',');
    }
    if (manualRelIds != null && manualRelIds.length > 0) {
      currentRow.value.manualRelIds = manualRelIds.join(',');
    }
    currentRow.value.orgId = detail.orginSet;
    currentRow.value.departNo = detail.departSet;
    currentRow.value.subjectCode = detail.subjectCodeSet;
    currentRow.value.subjectName = detail.subjectNameSet;
    currentRow.value.amount = detail.amountString;
    currentRow.value.currency = detail.accountingCurrencySet;

    currentRow.value.exchangeRate = detail.exchangeRateSet;
    currentRow.value.cnyAmount = detail.cnyAmount;
    currentRow.value.totalLossMoney = detail.totalLossMoney;
    currentRow.value.cfmdRecycleMoney = detail.cfmdRecycleMoney;
    currentRow.value.cfmdIndemnity = detail.cfmdIndemnity;
    currentRow.value.postingDate = detail.firstEntryDateDetail;
  }
  // 计算总金额
  const subData = await getSubFormAndTableData();
  const list = subData.ldcEventRecycleDetailsList;
  // 定义总损失金额
  let totalLossMoney = 0;
  let cfmdRecycleMoney = 0;
  let cfmdIndemnity = 0;

  if (list != null && list.length > 0) {
    isShowEvent.value = true;
    formData.isLossEvent = '1';
  }
  list.forEach((item) => {
    let category = item.category;
    let subClass = item.subClass;
    if (category == '1') {
      if (item.cnyAmount != null) {
        totalLossMoney = totalLossMoney + Number(item.cnyAmount);
      }
      // if (formData.firstEntryDate == null || formData.firstEntryDate == '') {
      // 	firstEntryDateCom = detail.firstEntryDate;
      // } else {
      // 	const date1 = new Date(firstEntryDateCom);
      // 	const date2 = new Date(detail.firstEntryDate);
      // 	if(date1 > date2) {
      // 		firstEntryDateCom = detail.firstEntryDate;
      // 	}
      // }
    } else if (category == '2') {
      if (item.cnyAmount != null) {
        cfmdRecycleMoney = cfmdRecycleMoney + Number(item.cnyAmount);
        if (subClass != null && subClass != '' && subClass == '3') {
          cfmdIndemnity = cfmdIndemnity + Number(item.cnyAmount);
        }
      }
    }
  });
  formData.totalLossMoney = totalLossMoney;
  formData.cfmdRecycleMoney = cfmdRecycleMoney;
  formData.netLossMoney = totalLossMoney - cfmdRecycleMoney;
  formData.cfmdIndemnity = cfmdIndemnity;
  formData.firstEntryDate = detail.firstEntryDate;
  // 判断事件严重程度
  getSeverityLevel();
}

async function showImportOrec() {
  const res = await getListNoPage();
  tableData1.value = res;
  visible.value = true;
}

function chooseRegulatory() {
  registerModal2.value.choose();
}

function chooseMainCause() {
  registerModal4.value.choose();
}

function getRegulatory(regulatory) {
  formData.eventTypeFirst = regulatory.firstLevelName;
  formData.eventTypeSecond = regulatory.secondLevelName;
  formData.eventTypeThird = regulatory.threeLevelName;
}

function getRiskCase(riskCase) {
  formData.mainCauseClassification = riskCase.firstLevelName;
}

async function handleDeleteDetail() {
  const subData = await getSubFormAndTableData();
  const list = subData.ldcEventRecycleDetailsList;
  // 定义总损失金额
  let totalLossMoney = 0;
  let cfmdRecycleMoney = 0;
  let cfmdIndemnity = 0;

  if (list != null && list.length > 0) {
    isShowEvent.value = true;
    formData.isLossEvent = '1';
    list.forEach((item) => {
      let category = item.category;
      let subClass = item.subClass;
      if (category == '1') {
        if (item.cnyAmount != null) {
          totalLossMoney = totalLossMoney + Number(item.cnyAmount);
        }
      } else if (category == '2') {
        if (item.cnyAmount != null) {
          cfmdRecycleMoney = cfmdRecycleMoney + Number(item.cnyAmount);
          if (subClass != null && subClass != '' && subClass == '3') {
            cfmdIndemnity = cfmdIndemnity + Number(item.cnyAmount);
          }
        }
      }
    });
  } else {
    isShowEvent.value = false;
    formData.isLossEvent = '';
  }
  // 重新遍历，判断字段的值以及是否可以修改
  formData.totalLossMoney = totalLossMoney;
  formData.cfmdRecycleMoney = cfmdRecycleMoney;
  formData.netLossMoney = totalLossMoney - cfmdRecycleMoney;
  formData.cfmdIndemnity = cfmdIndemnity;
  currentRow.value = {};
  currentRowIndex.value = null;
  //formData.firstEntryDate = detail.firstEntryDate;
  // 判断事件严重程度
  getSeverityLevel();
}

async function handleAddDetail() {
  const subData = await getSubFormAndTableData();
  const list = subData.ldcEventRecycleDetailsList;
  if (list != null && list.length > 0) {
    isShowEvent.value = true;
    formData.isLossEvent = '1';
  } else {
    isShowEvent.value = false;
    formData.isLossEvent = '';
  }
}

async function handleEditClosed(event) {

  // 获得变动的、字段
  let column = event.column.key;

  // 币种变更或者入账日期变更
  if (column == 'currency' || column == 'postingDate') {
    // 查一下汇率
    if (event.row.currency != null && event.row.currency != ''
      && event.row.postingDate != null && event.row.postingDate != ''
    ) {
      let param = {
        'currency': event.row.currency,
        'postingDate': event.row.postingDate,
      }
      const res = await queryCurrencyRate(param);
      if (res.success) {
        event.row.exchangeRate = res.result;
      }
    }
  }

  if (column == 'subjectCode') {
    for (let i=0; i < subjectCodeList.value.length; i++) {
      let record = subjectCodeList.value[i];
      if (event.row.subjectCode == record.value) {
        
        event.row.subjectName = record.text.split("_")[1];
        return;
      }
    }
  }

  if (column == 'subjectName') {
    for (let i=0; i < subjectNameList.value.length; i++) {
      let record = subjectNameList.value[i];
      if (event.row.subjectName == record.value) {
        event.row.subjectCode = record.text.split("_")[0];
        return;
      }
    }
  }
  const subData = await getSubFormAndTableData();
  const list = subData.ldcEventRecycleDetailsList;
  //const list = recycleDetailsTable.dataSource;

  // 定义总损失金额
  let totalLossMoney = 0;
  let cfmdRecycleMoney = 0;
  let cfmdIndemnity = 0;

  
  // 重新计算金额
  let id = event.row.id;  
  list.forEach((item) => {
    let category = item.category;
    let subClass = item.subClass;
    if (column == 'amount' || column == 'exchangeRate' || column == 'currency' || column == 'postingDate') {
        if (item.amount != null && item.amount != '' && item.exchangeRate != null && item.exchangeRate != '') {
          let cnyAmount = Number(item.amount) * Number(item.exchangeRate);
          cnyAmount = Number(cnyAmount).toFixed(2);
          if (category == '1') {
            if (cnyAmount != null) {
              totalLossMoney = Number(totalLossMoney) + Number(cnyAmount);
            }
          } else if (category == '2') {
            if (cnyAmount != null) {
              cfmdRecycleMoney = Number(cfmdRecycleMoney) + Number(cnyAmount);
              if (subClass != null && subClass != '' && subClass == '3') {
                cfmdIndemnity = Number(cfmdIndemnity) + Number(cnyAmount);
              }
            }
          }
          event.row.cnyAmount = cnyAmount;
          item.cnyAmount = cnyAmount;
        }
    } else if (column == 'category' || column == 'subClass' || column == 'lossForm') {
      if (category == '1') {
        if (item.cnyAmount != null) {
          totalLossMoney = Number(totalLossMoney) + Number(item.cnyAmount);
        }
      } else if (category == '2') {
            if (item.cnyAmount != null) {
              cfmdRecycleMoney = Number(cfmdRecycleMoney) + Number(item.cnyAmount);
              if (subClass != null && subClass != '' && subClass == '3') {
                cfmdIndemnity = Number(cfmdIndemnity) + Number(item.cnyAmount);
              }
            }
          }
    }
  });
  recycleDetailsTable.dataSource = list;
  formData.totalLossMoney = totalLossMoney.toFixed(2);
  formData.cfmdRecycleMoney = cfmdRecycleMoney.toFixed(2);
  formData.netLossMoney = totalLossMoney - cfmdRecycleMoney;
  formData.cfmdIndemnity = cfmdIndemnity;
  if (formData.totalLossMoney != null && formData.totalLossMoney > 0) {
    getSeverityLevel();
  }
  
}

function returnFirst() {
  emit('manageBack');
}

function continueEdit() {
  confirmLoading.value = false;
  isShowSaveButton.value = true;
}

async function showGLTZ() {
  visible2.value = true;
}

async function showNameHistory() {
  let param = {
    eventId: formData.id,
    pageNo: 1,
    pageSize: 100,
  };
  const res = await queryEventNameHistory(param);
  tableData3.value = res.records;
  visible3.value = true;
}

function showRecycleHistory() {
  registerModal3.value.viewHistory(formData.id);
}

async function getSeverityLevel() {
  let param = {
    'reputationDamage': formData.reputationDamage,
    'operationInterruption': formData.operationInterruption,
    'customerServiceQuality': formData.customerServiceQuality,
    'regulatoryActions': formData.regulatoryActions,
    'employeeSafety': formData.employeeSafety,
    'totalLossMoney': formData.totalLossMoney,
  }
  const res = await judgeSeverityLevel(param);
  if (res.success) {
    formData.severityClassification = res.result;
  } else {
    message.warning("判断事件严重度分级异常");
  }
}

function clueRelList(record) {
	registerModal5.value.showClue(record);
}
// 外规内容
const ruleExternalLawDetailTable = reactive<Record<string, any>>({
  loading: false,
  dataSource: [],
});

// 外规表格操作
const addExternalLawRow = () => {
  ruleExternalLawDetailTable.dataSource.push({
    ruleId: '',
    issuedNumber: '',
    name: '',
    domain: '',
    timeliness: '',
    releaseDate: '',
    implementationDate: '',
  });
};
// 外规表格选择
const selectedExternalLawKeys = ref([]);
const deleteExternalLawRows = () => {
  // 按索引从大到小删除，避免索引变化
  selectedExternalLawKeys.value
    .sort((a, b) => b - a)
    .forEach((index) => {
      ruleExternalLawDetailTable.dataSource.splice(index, 1);
    });
  selectedExternalLawKeys.value = [];
};

  // 定义内外规表格列
  const externalLawColumns = ref([
    { title: '外规文号', dataIndex: 'num', width: '200px' },
    { title: '外规名称', dataIndex: 'name', width: '200px' },
    { title: '发文机构', dataIndex: 'dept', width: '200px' },
    { title: '时效性', dataIndex: 'timeliness', width: '200px' },
  ]);
  const onExternalLawSelectChange = (selectedKeys) => {
    selectedExternalLawKeys.value = selectedKeys;
  };

  // 外规类型选项
  const externalTypeOptions = ref([]);
  // 添加当前行索引的引用
  const currentExteriorsRowIndex = ref<number | null>(null);
  // 打开外规关联弹窗事件
  const exteriorsRefModel = ref();
  function openExteriorList(index, field, event) {
    // 记录当前行索引
    currentExteriorsRowIndex.value = index;
    exteriorsRefModel.value.show();
    ruleExternalLawDetailTable.dataSource[index][field] = event.target.value;
  }

  // 处理关联外规数据事件
  function handleEexteriorData(record) {
    const rowIndex = currentExteriorsRowIndex.value;
    ruleExternalLawDetailTable.dataSource[rowIndex] = {
      ...ruleExternalLawDetailTable.dataSource[rowIndex], // 保留原有数据
      num: record.basicIssuedNumber || '',
      name: record.basicRegulatoryName || '',
      dept: record.basicIssuedAgency || '',
      timeliness: record.basicTimeliness_dictText || '',
      ruleId: record.id || '',
    };
    ruleExternalLawDetailTable.dataSource = [...ruleExternalLawDetailTable.dataSource];
    // 重置当前索引
    currentExteriorsRowIndex.value = null;
  }

  // 内规表格操作
  const addExternalInternalRow = () => {
    ruleExternalInternalDetailTable.dataSource.push({
      ruleId: '',
      num: '',
      name: '',
      dept: '',
      deptOne: '',
      implementationDate: '',
    });
  };

  const deleteExternalInternalRows = () => {
    selectedExternalInternalKeys.value
      .sort((a, b) => b - a)
      .forEach((index) => {
        ruleExternalInternalDetailTable.dataSource.splice(index, 1);
      });
    selectedExternalInternalKeys.value = [];
  };

  // 内规表格选择
  const selectedExternalInternalKeys = ref([]);
  const onExternalInternalSelectChange = (selectedKeys) => {
    selectedExternalInternalKeys.value = selectedKeys;
  };

  // 内规内容
  const ruleExternalInternalDetailTable = reactive<Record<string, any>>({
    loading: false,
    dataSource: [],
  });

  const externalInternalColumns = ref([
    { title: '制度文号', dataIndex: 'num', width: '200px' },
    { title: '制度名称', dataIndex: 'name', width: '200px' },
    { title: '发文机构', dataIndex: 'dept', width: '200px' },
    { title: '发文部门', dataIndex: 'deptOne', width: '200px' },
    { title: '实施日期', dataIndex: 'implementationDate', width: '200px' },
  ]);

  // 添加当前行索引的引用
  const currentInternalRowIndex = ref<number | null>(null);
  // 打开外规关联弹窗事件
  const interiorsRefModel = ref();
  function openInteriorList(index, field, event) {
    // 确保组件实例存在
    if (!interiorsRefModel.value) {
      console.error('InteriorsList component not mounted');
      return;
    }
    currentInternalRowIndex.value = index;
    // 添加延迟确保DOM更新
    setTimeout(() => {
      interiorsRefModel.value.show();
    }, 50);
  }
  // 处理关联数据事件
  function handleInteriorData(record) {
    if (currentInternalRowIndex.value !== null) {
      const rowIndex = currentInternalRowIndex.value;
      if (ruleExternalInternalDetailTable.dataSource && rowIndex < ruleExternalInternalDetailTable.dataSource.length) {
        ruleExternalInternalDetailTable.dataSource[rowIndex] = {
          ...ruleExternalInternalDetailTable.dataSource[rowIndex],
          num: record.documentNumber || '',
          name: record.systemName || '',
          dept: record.systemIssuingBody || '',
          deptOne: record.issuingDeptOne || '',
          implementationDate: record.implementationDate || '',
          ruleId: record.id || '',
        };
        ruleExternalInternalDetailTable.dataSource = [...ruleExternalInternalDetailTable.dataSource];
      }
      currentInternalRowIndex.value = null;
    }
  }

  function getIsViolateRegulations(val) {
    if (val == '0') {
      ruleExternalInternalDetailTable.dataSource = [];
      ruleExternalLawDetailTable.dataSource = [];

    }
  }
  


  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
.antd-modal-form {
  padding: 14px;
}

.page-container {
  display: flex;
  flex-direction: column;
  height: 75vh; /* 使容器高度占满视口高度 */
  overflow: hidden; /* 防止外层容器出现滚动条 */
}

.form-container {
  flex: 1; /* 使表单容器占据剩余空间 */
  overflow-x: hidden; /* 允许表单容器在垂直方向上滚动 */
  padding: 16px; /* 添加一些内边距以便更好地显示表单 */
}

.button-container {
  position: sticky; /* 使用粘性定位 */
  bottom: 0; /* 固定在页面底部 */
  background-color: #fff; /* 确保按钮背景与页面底部颜色一致（如果需要） */
  //padding: 16px; /* 添加一些内边距以便更好地显示按钮 */
  text-align: right; /* 将按钮对齐到右侧（可选） */
}

/* 滚动容器 */
.scroll-wrapper {
  overflow-x: auto;
  scrollbar-width: none; /* 隐藏 Firefox 滚动条 */
  -ms-overflow-style: none; /* 隐藏 IE 滚动条 */
  &::-webkit-scrollbar {
    display: none;
  } /* 隐藏 Chrome 滚动条 */
  padding: 0 32px; /* 两侧留出按钮空间 */
}

/* 菜单项间距优化 */
.scroll-wrapper.scroll-menu {
  white-space: nowrap;
  .ant-menu-item {
    padding: 0 24px; /* 增大间距便于滚动 */
  }
}
.event-timeline {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.event-item {
  /* margin-bottom: 20px; */
  border-left: 2px solid #f90;
  padding-left: 20px;
  position: relative;
}

.event-item::before {
  content: '';
  position: absolute;
  left: -6px; /* 调整为-11px以确保圆点与线对齐 */
  top: 5px; /* 调整为5px以确保圆点与线在垂直方向上对齐 */
  width: 10px;
  height: 10px;
  background-color: #fff;
  border: solid 3px #f90;
  border-radius: 50%;
}

.event-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.event-time {
  color: #f90;
  font-weight: bold;
  margin-right: 10px;
    position: absolute; /* 将时间绝对定位到左侧 */
  left: -160px; /* 根据时间宽度调整 */
  top: 0;
  width: 140px; /* 固定宽度保持对齐 */
  color: #f90;
  font-weight: bold;
  text-align: right; /* 时间右对齐 */
}

.event-name {
  font-weight: bold;
}

.event-content {
  background-color: #FEF7EE;
  padding: 10px;
  border-radius: 5px;
}

.event-description h3,
.event-reason h3 {
  margin-top: 0;
  margin-bottom: 5px;
  color: #333;
}
</style>
