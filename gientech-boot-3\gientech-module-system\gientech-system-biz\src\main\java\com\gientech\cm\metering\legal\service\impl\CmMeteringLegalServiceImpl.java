package com.gientech.cm.metering.legal.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.cm.basicdata.group.asset.entity.CmBasicGroupAssetData;
import com.gientech.cm.basicdata.group.profit.entity.CmBasicGroupProfit;
import com.gientech.cm.basicdata.group.profit.mapper.CmBasicGroupProfitMapper;
import com.gientech.cm.basicdata.legal.asset.entity.CmBasicInterBearAsset;
import com.gientech.cm.basicdata.legal.asset.mapper.CmBasicInterBearAssetMapper;
import com.gientech.cm.basicdata.legal.dividend.entity.CmBasicCompanyDividendRemove;
import com.gientech.cm.basicdata.legal.dividend.mapper.CmBasicCompanyDividendRemoveMapper;
import com.gientech.cm.basicdata.legal.loss.entity.CmBasicLegalLoss;
import com.gientech.cm.basicdata.legal.loss.mapper.CmBasicLegalLossMapper;
import com.gientech.cm.basicdata.legal.profit.entity.CmBasicLegalProfitResults;
import com.gientech.cm.basicdata.legal.profit.mapper.CmBasicLegalProfitResultsMapper;
import com.gientech.cm.metering.config.entity.CmWeightedAssetsCalculationConfig;
import com.gientech.cm.metering.config.entity.CmWeightedAssetsCalculationConfigGroup;
import com.gientech.cm.metering.config.service.ICmWeightedAssetsCalculationConfigGroupService;
import com.gientech.cm.metering.config.service.ICmWeightedAssetsCalculationConfigService;
import com.gientech.cm.metering.history.entity.CmMeteringLegalHistory;
import com.gientech.cm.metering.history.mapper.CmMeteringLegalHistoryMapper;
import com.gientech.cm.metering.legal.entity.CmMeteringLegal;
import com.gientech.cm.metering.legal.mapper.CmMeteringLegalMapper;
import com.gientech.cm.metering.legal.service.ICmMeteringLegalService;
import com.gientech.cm.metering.legal.vo.CmVersionCompany;
import com.gientech.cm.metering.rel.entity.*;
import com.gientech.cm.metering.rel.mapper.*;
import com.gientech.cm.metering.rel.service.*;
import com.gientech.cm.rules.organization.entity.CmRulesOrganization;
import com.gientech.cm.rules.organization.mapper.CmRulesOrganizationMapper;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.entity.SysCategory;
import org.jeecg.modules.system.mapper.SysCategoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 操作风险资本计量-法人口径
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Service
public class CmMeteringLegalServiceImpl extends ServiceImpl<CmMeteringLegalMapper, CmMeteringLegal> implements ICmMeteringLegalService {

    @Autowired
    private CmMeteringLegalMapper cmMeteringLegalMapper;
    @Autowired
    private CmRulesOrganizationMapper cmRulesOrganizationMapper;
    @Autowired
    private ICmWeightedAssetsCalculationConfigService configService;
    @Autowired
    private ICmWeightedAssetsCalculationConfigGroupService configGroupService;
    @Autowired
    private CmMeteringLegalHistoryMapper legalHistoryMapper;
    @Autowired
    private ICmMeteringTableRelService relService;
    @Autowired
    private CmBasicInterBearAssetMapper assetMapper;
    @Autowired
    private ICmSubjectMappingResultService iMappingResultService;
    @Autowired
    private SysCategoryMapper sysCategoryMapper;
    @Autowired
    private CmBasicCompanyDividendRemoveMapper dividendRemoveMapper;
    @Autowired
    private CmBasicLegalProfitResultsMapper profitResultsMapper;
    @Autowired
    private CmBasicInterBearAssetResultMapper assetResultMapper;
    @Autowired
    private CmBasicCompanyDividendRemoveResultMapper removeResultMapper;
    @Autowired
    private CmMeteringProfitResultMapper profitResultMapper;
    @Autowired
    private ICmWeightedAssetsCalculationConfigResultService resultService;
    @Autowired
    private CmRulesOrganizationMapper organizationMapper;
    @Autowired
    private ICmSubjectMappingGroupResultService mappingGroupResultService;
    @Autowired
    private CmMeteringOrgRelMapper orgRelMapper;
    @Autowired
    private CmBasicLegalLossMapper legalLossMapper;
    @Autowired
    private CmBasicLegalLossResultMapper legalLossResultMapper;
    @Autowired
    private CmAssetGroupResultMapper assetGroupResultMapper;
    @Autowired
    private CmDividendGroupResultMapper dividendGroupResultMapper;
    @Autowired
    private CmBasicGroupProfitMapper basicGroupProfitMapper;
    @Autowired
    private CmMeteringProfitGroupResultMapper profitGroupResultMapper;
    @Autowired
    private CmBasicGroupLossResultMapper groupLossResultMapper;
    @Autowired
    private ICmCalculationConfigGroupResultService groupResultService;
    @Autowired
    private IWorkflowInstanceService workflowInstanceService;
    @Autowired
    private IWorkflowTaskService workflowTaskService;
    private final String businessKey = "meteringProcess";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> add(CmMeteringLegal cmMeteringLegal) {

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();

        String isLossDataCheck = "";
        String legalDepart = "";

        String meteringFlag = cmMeteringLegal.getMeteringFlag();
        String meteringFlagName = "";
        // 查询法人的机构号
        Map<String, Object> organMap = new HashMap<>();
        organMap.put("del_flag", 0);
        organMap.put("org_state", "2");
        if ("2".equals(meteringFlag)) {
            organMap.put("org_type", "2");
            List<CmRulesOrganization> cmRulesOrganizations = cmRulesOrganizationMapper.selectByMap(organMap);
            if (cmRulesOrganizations == null || cmRulesOrganizations.isEmpty()) {
                return Result.error("不存在生效的法人机构，请检查");
            }
            isLossDataCheck = cmRulesOrganizations.get(0).getIsLossDataCheck();
            legalDepart = cmRulesOrganizations.get(0).getOrgCode();
            meteringFlagName = "法人";
        } else {
            organMap.put("org_type", "1");
            List<CmRulesOrganization> cmRulesOrganizations = cmRulesOrganizationMapper.selectByMap(organMap);
            if (cmRulesOrganizations == null || cmRulesOrganizations.isEmpty()) {
                return Result.error("不存在生效的集团，请检查");
            }
            // 需求中放在点击测算时进行同步
            // isLossDataCheck = cmRulesOrganizations.get(0).getIsLossDataCheck();
            legalDepart = cmRulesOrganizations.get(0).getOrgCode();
            meteringFlagName = "集团";
        }

        String version = "RWA" + meteringFlagName + cmMeteringLegal.getMeteringYear() + cmMeteringLegal.getAuditVersion() + "V";
        BigDecimal versionNum = new BigDecimal("1.0");
        // 根据version查询当前最大的版本数字
        String maxVersionNum = cmMeteringLegalMapper.getMaxVersionNum(version);
        if (StringUtils.isNotBlank(maxVersionNum)) {
            versionNum = new BigDecimal(maxVersionNum).add(new BigDecimal("0.1"));
        }
        String versionNumStr = versionNum.toString();
        version = version + versionNumStr;
        cmMeteringLegal.setVersion(version);
        cmMeteringLegal.setIsLossDataCheck(isLossDataCheck);
        cmMeteringLegal.setLegalDepart(legalDepart);
        cmMeteringLegal.setVersionNum(versionNum);
        cmMeteringLegalMapper.insert(cmMeteringLegal);

        CmMeteringLegalHistory history = new CmMeteringLegalHistory();
        history.setMeteringLegalId(cmMeteringLegal.getId());
        history.setState("1");
        history.setOperateUser(userName);
        history.setOperateDepart(sysUser.getOrgId());
        history.setOperateTime(new Date());
        history.setOperateType("新增");
        history.setOperateRole("总行操作资本计量岗");
        legalHistoryMapper.insert(history);
        return Result.ok("新增成功！");
    }

    @Override
    public Result<Map<String, Object>> getMeteringVersion(CmMeteringLegal cmMeteringLegal) {

        cmMeteringLegal = cmMeteringLegalMapper.selectById(cmMeteringLegal.getId());
        String isLossDataCheck = "";
        if (cmMeteringLegal.getMeteringFlag().equals("1")) {
            QueryWrapper jtWrapper = new QueryWrapper();
            jtWrapper.eq("org_type", "1");
            jtWrapper.eq("org_state", "2");
            jtWrapper.eq("del_flag", 0);
            CmRulesOrganization organization = organizationMapper.selectOne(jtWrapper);
            if (organization == null) {
                return Result.error("集团验收状态为空，请检查！");
            }
            isLossDataCheck = organization.getIsLossDataCheck();
            if (StringUtils.isBlank(isLossDataCheck)) {
                return Result.error("集团未生效，请检查！");
            }
        }
        Map<String, Object> result = getCMVersionByMetering(cmMeteringLegal, "0");
        // 获取数据（未测算前数据为空）
        String state = cmMeteringLegal.getState();
        if (state.equals("1") || state.equals("2")) {
            getReportList(result, cmMeteringLegal.getMeteringFlag(), isLossDataCheck);
        } else {
            getReportDataList(result, cmMeteringLegal);
        }
        return Result.ok(result);
    }

    private void getReportDataList(Map<String, Object> result, CmMeteringLegal cmMeteringLegal) {
        // 获取操纵风险加权资产测算结果表
        getListByMeteringId(result, cmMeteringLegal);

        // 获取会计科目映射结果表数据
        getMappingByMeteringId(result, cmMeteringLegal);

        // 获取生息资产结果表
        getAssetByMeteringId(result, cmMeteringLegal);

        // 获取股利收入结果表
        getDividendByMeteringId(result, cmMeteringLegal);

        // 获取交易账簿净损益结果
        getProfitByMeteringId(result, cmMeteringLegal);

        // 获取损失数据统计表
        getLossDataByMeteringId(result, cmMeteringLegal);

    }

    /**
     * 获取损失数据结果表
     * @param result
     * @param cmMeteringLegal
     */
    private void getLossDataByMeteringId(Map<String, Object> result, CmMeteringLegal cmMeteringLegal) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("metering_id", cmMeteringLegal.getId());
        if (cmMeteringLegal.getMeteringFlag().equals("2")) {
            CmBasicLegalLossResult lossResult = legalLossResultMapper.selectOne(wrapper);
            result.put("lossDataList", lossResult);
        } else {
            wrapper.orderByAsc("sort_num");
            List<CmBasicGroupLossResult> lossResult = groupLossResultMapper.selectList(wrapper);
            result.put("lossDataList", lossResult);
        }
    }

    /**
     * 获取交易账簿净损益结果
     * @param result
     * @param cmMeteringLegal
     */
    private void getProfitByMeteringId(Map<String, Object> result, CmMeteringLegal cmMeteringLegal) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("metering_id", cmMeteringLegal.getId());
        wrapper.orderByAsc("sort_num");
        if (cmMeteringLegal.getMeteringFlag().equals("2")) {
            List<CmMeteringProfitResult> accountBookList = profitResultMapper.selectList(wrapper);
            if (accountBookList == null || accountBookList.isEmpty()) {
                accountBookList = new ArrayList<>();
            }
            result.put("accountBookList", accountBookList);
        } else {
            List<CmMeteringProfitGroupResult> accountBookList = profitGroupResultMapper.selectList(wrapper);
            if (accountBookList == null || accountBookList.isEmpty()) {
                accountBookList = new ArrayList<>();
            }
            result.put("accountBookList", accountBookList);
        }
    }

    /**
     * 获取股利收入结果表
     * @param result
     * @param cmMeteringLegal
     */
    private void getDividendByMeteringId(Map<String, Object> result, CmMeteringLegal cmMeteringLegal) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("metering_id", cmMeteringLegal.getId());
        wrapper.orderByAsc("sort_num");
        if (cmMeteringLegal.getMeteringFlag().equals("2")) {
            List<CmBasicCompanyDividendRemoveResult> dividendList = removeResultMapper.selectList(wrapper);
            if (dividendList == null || dividendList.isEmpty()) {
                dividendList = new ArrayList<>();
            }
            result.put("dividendList", dividendList);
        } else {
            List<CmDividendGroupResult> dividendList = dividendGroupResultMapper.selectList(wrapper);
            if (dividendList == null || dividendList.isEmpty()) {
                dividendList = new ArrayList<>();
            }
            result.put("dividendList", dividendList);
        }
    }

    /**
     * 获取生息资产结果表
     * @param result
     * @param cmMeteringLegal
     */
    private void getAssetByMeteringId(Map<String, Object> result, CmMeteringLegal cmMeteringLegal) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("metering_id", cmMeteringLegal.getId());
        if (cmMeteringLegal.getMeteringFlag().equals("2")) {
            List<CmBasicInterBearAssetResult> assetResultList = assetResultMapper.selectList(wrapper);
            if (assetResultList == null || assetResultList.isEmpty()) {
                assetResultList = new ArrayList<>();
            }
            result.put("assetResultList", assetResultList);
        } else {
            wrapper.orderByAsc("sort_num");
            List<CmAssetGroupResult> assetResultList = assetGroupResultMapper.selectList(wrapper);
            if (assetResultList == null || assetResultList.isEmpty()) {
                assetResultList = new ArrayList<>();
            }
            result.put("assetResultList", assetResultList);
        }

    }

    /**
     * 获取会计科目映射结果表数据
     * @param result
     * @param cmMeteringLegal
     */
    private void getMappingByMeteringId(Map<String, Object> result, CmMeteringLegal cmMeteringLegal) {
        List<SysCategory> categoryList = sysCategoryMapper.selectList(new QueryWrapper<>());
        if (cmMeteringLegal.getMeteringFlag().equals("2")) {
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("metering_id", cmMeteringLegal.getId());
            List<CmSubjectMappingResult> mappingResultList = iMappingResultService.list(wrapper);
            for (CmSubjectMappingResult mappingResult : mappingResultList) {
                if (StringUtils.isNotBlank(mappingResult.getIsFitBi())) {
                    if ("1".equals(mappingResult.getIsFitBi())) {
                        mappingResult.setIsFitBiName("是");
                    } else {
                        mappingResult.setIsFitBiName("否");
                    }
                }
                if (StringUtils.isNotBlank(mappingResult.getCorrespondBiSubject())) {
                    String name = categoryList.stream().filter(o -> o.getId().equals(mappingResult.getCorrespondBiSubject())).collect(Collectors.toList()).get(0).getName();
                    mappingResult.setCorrespondBiSubjectName(name);
                }
                if (StringUtils.isNotBlank(mappingResult.getCorrespondBiSubitem())) {
                    String name = categoryList.stream().filter(o -> o.getId().equals(mappingResult.getCorrespondBiSubitem())).collect(Collectors.toList()).get(0).getName();
                    mappingResult.setCorrespondBiSubitemName(name);
                }
                if (StringUtils.isNotBlank(mappingResult.getCorrespondBiTypicalSubitem())) {
                    String name = categoryList.stream().filter(o -> o.getId().equals(mappingResult.getCorrespondBiTypicalSubitem())).collect(Collectors.toList()).get(0).getName();
                    mappingResult.setCorrespondBiTypicalSubitemName(name);
                }
            }
            result.put("mappingResultList", mappingResultList);
        }
    }

    /**
     * 获取操纵风险加权资产测算结果表
     * @param result
     * @param cmMeteringLegal
     */
    private void getListByMeteringId(Map<String, Object> result, CmMeteringLegal cmMeteringLegal) {
        if (cmMeteringLegal.getMeteringFlag().equals("2")) {
            List<CmWeightedAssetsCalculationConfigResult> configList = resultService.selectListByMeteringId(cmMeteringLegal.getId(), false, "0");
            result.put("weightedAssets", configList);
        } else {
            // 查询配置表Excel测算下的三条子节点的id
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("pid", "0");
            CmWeightedAssetsCalculationConfigGroup configGroupParent = configGroupService.getOne(wrapper);
//            // 根据groupResultParent拿到3条子数据
//            QueryWrapper wrapper2 = new QueryWrapper();
//            wrapper2.eq("pid", configGroupParent.getId());
//            List<CmWeightedAssetsCalculationConfigGroup> list = configGroupService.list(wrapper2);
//            List<String> pidList = new ArrayList<>();
//            for (CmWeightedAssetsCalculationConfigGroup configGroup : list) {
//                pidList.add(configGroup.getId());
//            }
//            // 根据meteringId和list的id拿到真正的pid
//            QueryWrapper wrapper3 = new QueryWrapper();
//            wrapper2.eq("metering_id", cmMeteringLegal.getId());
//            wrapper2.in("pid", pidList);
//            CmCalculationConfigGroupResult parentResult = groupResultService.getOne(wrapper2);
//            if (parentResult == null) {
//                result.put("weightedAssets", new ArrayList<>());
//            } else {
//                List<CmCalculationConfigGroupResult> configList = groupResultService.selectListByMeteringId(cmMeteringLegal.getId(), false, parentResult.getConfigId());
//                result.put("weightedAssets", configList);
//            }
            List<CmCalculationConfigGroupResult> configList = groupResultService.selectListByMeteringId(cmMeteringLegal.getId(), false, configGroupParent.getId());
            result.put("weightedAssets", configList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> batchSubmit(List<String> ids) throws Exception {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        for (String id : ids) {
            CmMeteringLegal cmMeteringLegal = cmMeteringLegalMapper.selectById(id);
            // 判断有没有“待复核”、“待审核”或“审核通过”的数据,排除本身
            int inProcessCount = cmMeteringLegalMapper.getInProcessCount(cmMeteringLegal);
            if (inProcessCount > 0) {
                return Result.error("当前计量年度【"+cmMeteringLegal.getMeteringYear()+"】审计版本【"+cmMeteringLegal.getAuditVersion()+"】下已有处于审批流或审核通过的版本，请勿重复操作");
            }
        }
        Date nowDate = new Date();
        for (String id : ids) {
            CmMeteringLegal updateMetering = new CmMeteringLegal();
            updateMetering.setId(id);
            updateMetering.setState("4");
            updateMetering.setReachTime(nowDate);
            updateMetering.setSubmitTime(nowDate);
            updateMetering.setSubmitUser(userName);
            updateMetering.setSubmitUserDepart(sysUser.getOrgId());
            cmMeteringLegalMapper.updateById(updateMetering);
            CmMeteringLegalHistory history = new CmMeteringLegalHistory();
            history.setMeteringLegalId(id);
            history.setState(updateMetering.getState());
            history.setOperateUser(userName);
            history.setOperateDepart(sysUser.getOrgId());
            history.setOperateTime(new Date());
            history.setOperateType("提交");
            history.setOperateRole("总行操作资本计量岗");
            legalHistoryMapper.insert(history);
        }
        return Result.ok("提交成功！");
    }

    @Transactional(rollbackFor = Exception.class)
    public Result<String> submit(CmMeteringLegal cmMeteringLegal) throws Exception {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        Date nowDate = new Date();
        CmMeteringLegal updateMetering = new CmMeteringLegal();
        updateMetering.setId(cmMeteringLegal.getId());
        updateMetering.setState("4");
        updateMetering.setReachTime(nowDate);
        updateMetering.setSubmitTime(nowDate);
        updateMetering.setSubmitUser(userName);
        updateMetering.setSubmitUserDepart(sysUser.getOrgId());
        cmMeteringLegalMapper.updateById(updateMetering);
        CmMeteringLegalHistory history = new CmMeteringLegalHistory();
        history.setMeteringLegalId(cmMeteringLegal.getId());
        history.setState(updateMetering.getState());
        history.setOperateUser(userName);
        history.setOperateDepart(sysUser.getOrgId());
        history.setOperateTime(new Date());
        history.setOperateType("提交");
        history.setOperateRole("总行操作资本计量岗");
        legalHistoryMapper.insert(history);
        return Result.ok("提交成功！");
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> batchRevoke(List<String> ids) {
        List<CmMeteringLegal> updateList = new ArrayList<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        for (String id : ids) {
            CmMeteringLegal cmMeteringLegal = cmMeteringLegalMapper.selectById(id);
            CmMeteringLegal updateMetering = new CmMeteringLegal();
            updateMetering.setId(id);
            String state = cmMeteringLegal.getState();
            if ("4".equals(state)) {
                // 3
                updateMetering.setState("3");
            } else if ("6".equals(state)) {
                // 8
                updateMetering.setState("8");
            } else {
                return Result.error(cmMeteringLegal.getVersion() + "当前状态不允许撤销");
            }
            updateList.add(updateMetering);
        }
        for (CmMeteringLegal updateMetering : updateList) {
            cmMeteringLegalMapper.updateById(updateMetering);
            // 入库日志表
            CmMeteringLegalHistory history = new CmMeteringLegalHistory();
            history.setMeteringLegalId(updateMetering.getId());
            history.setState(updateMetering.getState());
            history.setOperateUser(userName);
            history.setOperateDepart(sysUser.getOrgId());
            history.setOperateTime(new Date());
            history.setOperateType("撤销");
            history.setOperateRole("总行操作资本计量岗");
            legalHistoryMapper.insert(history);
        }
        return Result.ok("撤销成功！");
    }

    @Transactional(rollbackFor = Exception.class)
    public Result<String> revoke(CmMeteringLegal cmMeteringLegal) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        String state = cmMeteringLegal.getState();
        CmMeteringLegal updateMetering = new CmMeteringLegal();
        updateMetering.setId(cmMeteringLegal.getId());
        if ("4".equals(state)) {
            // 3
            updateMetering.setState("3");
        } else if ("6".equals(state)) {
            // 8
            updateMetering.setState("8");
        } else {
            return Result.error(cmMeteringLegal.getVersion() + "当前状态不允许撤销");
        }
        cmMeteringLegalMapper.updateById(updateMetering);
        // 入库日志表
        CmMeteringLegalHistory history = new CmMeteringLegalHistory();
        history.setMeteringLegalId(updateMetering.getId());
        history.setState(updateMetering.getState());
        history.setOperateUser(userName);
        history.setOperateDepart(sysUser.getOrgId());
        history.setOperateTime(new Date());
        history.setOperateType("撤销");
        history.setOperateRole("总行操作资本计量岗");
        legalHistoryMapper.insert(history);
        return Result.ok("撤销成功！");
    }

    @Override
    public Result<String> auditReject(CmMeteringLegal cmMeteringLegal) {

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        Date nowDate = new Date();
        // 获取处理意见
        String auditOpinion = cmMeteringLegal.getAuditOpinion();

        CmMeteringLegal legal = cmMeteringLegalMapper.selectById(cmMeteringLegal.getId());
        // 定义下一步节点的流程状态
        String nextState = "";
        // 定义历史表下一节点的状态
        String nextStateHistory = "";
        // 定义操作类型
        String operateType = "";
        // 定义处理的角色
        String operateRole = "";
        // 判断是撤销退回还是正常退回
        String state = legal.getState();
        if ("4".equals(state) || "5".equals(state)) {
            // 正常流程
            nextState = "7";
            if ("4".equals(state)) {
                operateType = "复核退回";
                operateRole = "总行操风资本计量复核岗";
                nextStateHistory = "7";// 复核退回
            } else {
                operateType = "审核退回";
                operateRole = "总行操风资本计量审核岗";
                nextStateHistory = "11";// 审核退回
            }
        } else if ("8".equals(state) || "9".equals(state)) {
            // 撤销流程
            nextState = "6";
            if ("8".equals(state)) {
                operateType = "撤销复核退回";
                operateRole = "总行操风资本计量复核岗";
                nextStateHistory = "12";// 撤销复核退回
            } else {
                operateType = "撤销审核退回";
                operateRole = "总行操风资本计量审核岗";
                nextStateHistory = "13";// 撤销审核退回
            }
        } else {
            return Result.error("当前状态不允许退回");
        }
        // 更新
        cmMeteringLegal.setReachTime(nowDate);
        cmMeteringLegal.setState(nextState);
        cmMeteringLegal.setIsAudited("1");
        cmMeteringLegalMapper.updateById(cmMeteringLegal);

        // 入库日志表
        CmMeteringLegalHistory history = new CmMeteringLegalHistory();
        history.setMeteringLegalId(cmMeteringLegal.getId());
        history.setState(nextStateHistory);
        history.setOperateUser(userName);
        history.setOperateDepart(sysUser.getOrgId());
        history.setOperateTime(nowDate);
        history.setOperateType(operateType);
        history.setOperateRole(operateRole);
        history.setOperateOpinion(auditOpinion);
        history.setIsAudit("1");
        legalHistoryMapper.insert(history);

        return Result.ok("退回成功！");

    }

    @Override
    public Result<String> auditPass(CmMeteringLegal cmMeteringLegal) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        Date nowDate = new Date();
        // 获取处理意见
        String auditOpinion = cmMeteringLegal.getAuditOpinion();

        CmMeteringLegal legal = cmMeteringLegalMapper.selectById(cmMeteringLegal.getId());
        // 定义下一步节点的流程状态
        String nextState = "";
        // 定义操作类型
        String operateType = "";
        // 定义处理的角色
        String operateRole = "";
        // 判断是撤销退回还是正常退回
        String state = legal.getState();
        if ("4".equals(state)) {
            // 正常流程
            operateRole = "总行操风资本计量复核岗";
            nextState = "5";
            operateType = "复核通过";
        } else if ("5".equals(state)) {
            // 正常流程
            operateRole = "总行操风资本计量审核岗";
            nextState = "6";
            operateType = "审核通过";
            cmMeteringLegal.setEffectiveTime(nowDate);
        } else if ("8".equals(state)) {
            // 撤销流程
            operateRole = "总行操风资本计量复核岗";
            nextState = "9";
            operateType = "撤销复核通过";
        } else if ("9".equals(state)) {
            // 撤销流程
            operateRole = "总行操风资本计量审核岗";
            nextState = "10";
            operateType = "撤销审核通过";
        } else {
            return Result.error("当前状态不允许审核");
        }
        // 更新
        cmMeteringLegal.setReachTime(nowDate);
        cmMeteringLegal.setState(nextState);
        cmMeteringLegal.setIsAudited("1");
        cmMeteringLegalMapper.updateById(cmMeteringLegal);

        // 入库日志表
        CmMeteringLegalHistory history = new CmMeteringLegalHistory();
        history.setMeteringLegalId(cmMeteringLegal.getId());
        history.setState(nextState);
        history.setOperateUser(userName);
        history.setOperateDepart(sysUser.getOrgId());
        history.setOperateTime(nowDate);
        history.setOperateType(operateType);
        history.setOperateRole(operateRole);
        history.setOperateOpinion(auditOpinion);
        history.setIsAudit("1");
        legalHistoryMapper.insert(history);

        return Result.ok(operateType + "成功！");
    }

    @Override
    public void exportXls(HttpServletRequest request,
                          HttpServletResponse response,
                          CmMeteringLegal cmMeteringLegal) throws Exception {

        // 查询法人的层级
        QueryWrapper<CmRulesOrganization> orgWrapper = new QueryWrapper<>();
        orgWrapper.eq("org_type", "2");
        orgWrapper.eq("del_flag", 0);
        orgWrapper.eq("org_state", "2");
        CmRulesOrganization organization = organizationMapper.selectOne(orgWrapper);
        if (organization == null) {
            throw new Exception("法人未生效，请检查！");
        }
        cmMeteringLegal = cmMeteringLegalMapper.selectById(cmMeteringLegal.getId());
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 创建第一个sheet页
        Sheet sheet1 = workbook.createSheet("操作风险加权资产测算表");
        Sheet sheet2 = workbook.createSheet("会计科目映射表");
        Sheet sheet3 = workbook.createSheet("生息资产统计表");
        Sheet sheet4 = workbook.createSheet("股利收入统计表");
        Sheet sheet5 = workbook.createSheet("交易账簿净损益统计表");
        XSSFSheet sheet6 = workbook.createSheet("损失数据统计表");

        // 设置主标题样式
        CellStyle tableStyle = createTableStyle(workbook);
        // 设置合并单元格样式
        CellStyle titleStyle = createTitleStyle(workbook);
        // 设置常规单元格样式(左上角)
        CellStyle normalStyle1 = createNormalStyle(workbook,
                "0", "center", null, "5", "5", "1", "1", null);
        // 设置常规单元格样式(上)
        CellStyle normalStyle2 = createNormalStyle(workbook,
                "0", "center", null, "5", "1", "1", "1", null);
        // 设置常规单元格样式(右上角)
        CellStyle normalStyle3 = createNormalStyle(workbook,
                "0", "center", null, "5", "1", "1", "5", null);
        // 设置常规单元格样式(左)
        CellStyle normalStyle4 = createNormalStyle(workbook,
                "0", "center", null, "1", "5", "1", "1", null);
        // 设置常规单元格样式(左下角)
        CellStyle normalStyle5 = createNormalStyle(workbook,
                "0", "center", null, "1", "5", "5", "1", null);
        // 设置常规单元格样式(下)
        CellStyle normalStyle6 = createNormalStyle(workbook,
                "1", "left", null, "1", "1", "5", "1", null);
        // 设置常规单元格样式(右)
        CellStyle normalStyle7 = createNormalStyle(workbook,
                "0", "center", null, "1", "1", "1", "5", null);
        // 设置常规单元格样式(右下角)
        CellStyle normalStyle8 = createNormalStyle(workbook,
                "0", "center", null, "1", "1", "5", "5", null);

        // 设置常规单元格样式(无背景颜色)
        CellStyle normalStyle9 = createNormalStyle(workbook,
                "0", "center", null, "1", "1", "1", "1", null);

        // 设置常规单元格样式(黄色)
        CellStyle normalStyle10 = createNormalStyle(workbook,
                "0", "right", "13", "1", "1", "1", "1", null);

        // 设置常规单元格样式(灰色)
        CellStyle normalStyle11 = createNormalStyle(workbook,
                "0", "center", "22", "1", "1", "1", "1", null);
        // 设置常规单元格样式(紫色)
        CellStyle normalStyle12 = createNormalStyle(workbook,
                "0", "right", "46", "1", "1", "1", "1", null);

        // 设置黄色最外层
        CellStyle normalStyle13 = createNormalStyle(workbook,
                "0", "right", "13", "1", "1", "1", "5", null);

        // 设置字体加粗(用于项目列一级标题)
        CellStyle normalStyle14 = createNormalStyle(workbook,
                "1", "left", null, "1", "1", "1", "5", null);
        // 设置字体加粗(用于项目列二级标题)
        CellStyle normalStyle15 = createNormalStyle(workbook,
                "1", "left", null, "1", "1", "1", "5", "1");
        // 设置字体加粗(用于项目列三级标题)
        CellStyle normalStyle16 = createNormalStyle(workbook,
                "1", "left", null, "1", "1", "1", "5", "2");
        // 设置字体加粗(用于项目列四级标题)
        CellStyle normalStyle17 = createNormalStyle(workbook,
                "0", "left", null, "1", "1", "1", "5", "3");
        // 设置三级标题字体不加粗
        CellStyle normalStyle18 = createNormalStyle(workbook,
                "0", "left", null, "1", "1", "1", "5", "2");

        // 放入sheet1的数据
        // 合并列
        mergeCells(sheet1, 3, 3, 1, 5, "法人操作风险加权资产测算表", tableStyle);
        mergeCells(sheet1, 5, 6, 1, 1, "序号", normalStyle1);
        mergeCells(sheet1, 5, 6, 2, 2, "项目", normalStyle2);
        // 放入列表名称
        Row detailRow5 = sheet1.getRow(5);
        detailRow5.setHeightInPoints(30);
        Cell cell1 = detailRow5.createCell(3);
        cell1.setCellValue("A");
        cell1.setCellStyle(normalStyle2);
        Cell cell2 = detailRow5.createCell(4);
        cell2.setCellValue("B");
        cell2.setCellStyle(normalStyle2);
        Cell cell3 = detailRow5.createCell(5);
        cell3.setCellValue("C");
        cell3.setCellStyle(normalStyle3);

        Row detailRow6 = sheet1.getRow(6);
        detailRow6.setHeightInPoints(30);
        Cell cell4 = detailRow6.createCell(3);
        cell4.setCellValue("最近第一年");
        cell4.setCellStyle(normalStyle9);
        Cell cell5 = detailRow6.createCell(4);
        cell5.setCellValue("最近第二年");
        cell5.setCellStyle(normalStyle9);
        Cell cell6 = detailRow6.createCell(5);
        cell6.setCellValue("最近第三年");
        cell6.setCellStyle(normalStyle7);

        // 设置宽度
        sheet1.setColumnWidth(1, 2000);
        sheet1.setColumnWidth(2, 9000);
        sheet1.setColumnWidth(3, 9000);
        sheet1.setColumnWidth(4, 9000);
        sheet1.setColumnWidth(5, 9000);

        // 放入数据
        Map<String, Object> result = new HashMap<>();
//        getReportDataList(result, cmMeteringLegalMapper.selectById(cmMeteringLegal.getId()));
//        List<CmWeightedAssetsCalculationConfig> configList = (List<CmWeightedAssetsCalculationConfig>) result.get("weightedAssets");

//        QueryWrapper<CmWeightedAssetsCalculationConfig> wrapper = new QueryWrapper<>();
//        wrapper.ne("sort_num", "0");
//        wrapper.orderByAsc("sort_num");
//        List<CmWeightedAssetsCalculationConfig> configLists = configService.list(wrapper);
        QueryWrapper<CmWeightedAssetsCalculationConfigResult> wrapper = new QueryWrapper();
        wrapper.ne("sort_num", "0");
        wrapper.eq("metering_id", cmMeteringLegal.getId());
        wrapper.orderByAsc("sort_num");
        List<CmWeightedAssetsCalculationConfigResult> configLists = resultService.list(wrapper);
        int sheet1DataIndex = 7;
        // 灰色
        List<Integer> indexList = new ArrayList<>();
        Collections.addAll(indexList, 0,1,2,7,12,15,16,17,18,19,20,21);
        // 紫色
        List<Integer> indexList2 = new ArrayList<>();
        Collections.addAll(indexList2, 1,2,7,12,15,16,18,19,21);
        // 全部黄色列
        List<Integer> indexList3 = new ArrayList<>();
        Collections.addAll(indexList3, 3,4,5,6,8,9,10,11,13,14);
        // 单独黄色列
        List<Integer> indexList4 = new ArrayList<>();
        Collections.addAll(indexList3, 17, 20);
        for (int i = 0; i< configLists.size(); i++) {
//            CmWeightedAssetsCalculationConfig config = configLists.get(i);
            CmWeightedAssetsCalculationConfigResult config = configLists.get(i);
            Row dataRow = sheet1.createRow(sheet1DataIndex);
            dataRow.setHeightInPoints(30);
            // 序号列
            Cell cell7 = dataRow.createCell(1);
            // 项目列
            Cell cell8 = dataRow.createCell(2);
            // 最近第一年
            Cell cell9 = dataRow.createCell(3);
            // 最近第二年
            Cell cell10 = dataRow.createCell(4);
            // 最近第三年
            Cell cell11 = dataRow.createCell(5);

            // 放入序号
            cell7.setCellValue(config.getSerialNumber() == null || config.getSerialNumber().equals("null") ? "" : config.getSerialNumber());
            // 放入项目
            cell8.setCellValue(config.getProjectName() == null || config.getProjectName().equals("null") ? "" : config.getProjectName());
            // 放入最近第一年
            cell9.setCellValue(config.getFirstValue() + "");
            // 放入最近第二年
            cell10.setCellValue(config.getSecondValue() + "");
            // 放入最近第三年
            cell11.setCellValue(config.getThirdValue() + "");

            // 如果是最后一行
            if (i + 1 == configLists.size()) {
                cell7.setCellStyle(normalStyle5);
                cell8.setCellStyle(normalStyle6);
                cell9.setCellStyle(createNormalStyle(workbook,
                        "0", "right", "46", "1", "1", "5", "1", null));

                cell10.setCellStyle(createNormalStyle(workbook,
                        "0", "center", "22", "1", "1", "5", "1", null));
                cell10.setCellValue("");
                cell11.setCellStyle(createNormalStyle(workbook,
                        "0", "center", "22", "1", "1", "5", "5", null));
                cell11.setCellValue("");
            } else {
                cell7.setCellStyle(normalStyle4);
                if (config.getTitleLevel().equals("1")) {
                    cell8.setCellStyle(normalStyle14);
                } else if (config.getTitleLevel().equals("2")) {
                    cell8.setCellStyle(normalStyle15);
                } else if (config.getTitleLevel().equals("3")) {
                    if (i==17 || i==19 || i==20) {
                        cell8.setCellStyle(normalStyle18);
                    } else {
                        cell8.setCellStyle(normalStyle16);
                    }
                } else {
                    cell8.setCellStyle(normalStyle17);
                }
            }
            // 设置紫色列
            if (indexList2.contains(i)) {
                cell9.setCellStyle(normalStyle12);
            }
            // 设置黄色列
            if (indexList3.contains(i)) {
                cell9.setCellStyle(normalStyle10);
                cell10.setCellStyle(normalStyle10);
                cell11.setCellStyle(normalStyle13);
            }
            if (indexList4.contains(i)) {
                cell9.setCellStyle(normalStyle10);
            }

            if (i == 0) {
                cell9.setCellStyle(normalStyle11);
                cell9.setCellValue("");
            }

            // 设置单元格为灰色
            if (indexList.contains(i)) {
                cell10.setCellValue("");
                cell10.setCellStyle(normalStyle11);
                cell11.setCellStyle(createNormalStyle(workbook,
                        "0", "right", "22", "1", "1", "1", "5", null));
                cell11.setCellValue("");
            }
            sheet1DataIndex++;
        }

        // sheet2
        int ledgerAccount = Integer.parseInt(organization.getLedgerAccount());

        CellStyle sheet2Style1 = createNormalStyle2(workbook,
                "1", "center", "23", "1", "1", "1", "1", null);
        CellStyle sheet2Style2 = createNormalStyle2(workbook,
                "1", "center", "48", "1", "1", "1", "1", null);
        CellStyle sheet2Style3 = createNormalStyle2(workbook,
                "1", "center", "60", "1", "1", "1", "1", null);

        CellStyle sheet2Style4 = createNormalStyle2(workbook,
                "1", "center", "8", "1", "1", "1", "1", null);

        mergeCells(sheet2, 3, 3, 1, ledgerAccount*2+8, "法人会计科目映射表", tableStyle);
        mergeCells(sheet2, 5, 5, 1, ledgerAccount*2, "会计科目基本信息", sheet2Style1);
        mergeCells(sheet2, 5, 5, ledgerAccount*2+1, ledgerAccount*2+3, "G04映射关系", sheet2Style2);
        mergeCells(sheet2, 5, 5, ledgerAccount*2+4, ledgerAccount*2+7, "BI映射关系", sheet2Style3);
        mergeCells(sheet2, 5, 6, ledgerAccount*2+8, ledgerAccount*2+8, "科目余额（借正贷负/万元）", sheet2Style4);

        // 获取第7行
        Row sheet2Row6 = sheet2.getRow(6) == null ? sheet2.createRow(6) : sheet2.getRow(6);


        if (ledgerAccount == 5) {
            createSheet2Row(sheet2Row6, 10, "五级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 9, "五级科目代码", sheet2Style1);
            createSheet2Row(sheet2Row6, 8, "四级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 7, "四级科目代码", sheet2Style1);
            createSheet2Row(sheet2Row6, 6, "三级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 5, "三级科目代码", sheet2Style1);
            createSheet2Row(sheet2Row6, 4, "二级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 3, "二级科目代码", sheet2Style1);
            createSheet2Row(sheet2Row6, 2, "一级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 1, "一级科目代码", sheet2Style1);
        } else if (ledgerAccount == 4) {
            createSheet2Row(sheet2Row6, 8, "四级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 7, "四级科目代码", sheet2Style1);
            createSheet2Row(sheet2Row6, 6, "三级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 5, "三级科目代码", sheet2Style1);
            createSheet2Row(sheet2Row6, 4, "二级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 3, "二级科目代码", sheet2Style1);
            createSheet2Row(sheet2Row6, 2, "一级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 1, "一级科目代码", sheet2Style1);
        } else if (ledgerAccount == 3) {
            createSheet2Row(sheet2Row6, 6, "三级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 5, "三级科目代码", sheet2Style1);
            createSheet2Row(sheet2Row6, 4, "二级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 3, "二级科目代码", sheet2Style1);
            createSheet2Row(sheet2Row6, 2, "一级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 1, "一级科目代码", sheet2Style1);
        } else if (ledgerAccount == 2) {
            createSheet2Row(sheet2Row6, 4, "二级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 3, "二级科目代码", sheet2Style1);
            createSheet2Row(sheet2Row6, 2, "一级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 1, "一级科目代码", sheet2Style1);
        } else if (ledgerAccount == 1) {
            createSheet2Row(sheet2Row6, 2, "一级科目名称", sheet2Style1);
            createSheet2Row(sheet2Row6, 1, "一级科目代码", sheet2Style1);
        }



//        Cell sheetCell1 = sheet2Row6.createCell(1);
//        sheetCell1.setCellValue("一级科目代码");
//        sheetCell1.setCellStyle(sheet2Style1);
//        Cell sheetCell2 = sheet2Row6.createCell(2);
//        sheetCell2.setCellValue("一级科目名称");
//        sheetCell2.setCellStyle(sheet2Style1);
//        Cell sheetCell3 = sheet2Row6.createCell(3);
//        sheetCell3.setCellValue("二级科目代码");
//        sheetCell3.setCellStyle(sheet2Style1);
//        Cell sheetCell4 = sheet2Row6.createCell(4);
//        sheetCell4.setCellValue("二级科目名称");
//        sheetCell4.setCellStyle(sheet2Style1);
//        Cell sheetCell5 = sheet2Row6.createCell(5);
//        sheetCell5.setCellValue("三级科目代码");
//        sheetCell5.setCellStyle(sheet2Style1);

        Cell sheetCell7 = sheet2Row6.createCell(ledgerAccount*2+1);
        sheetCell7.setCellValue("G04一级项目");
        sheetCell7.setCellStyle(sheet2Style2);
        Cell sheetCell8 = sheet2Row6.createCell(ledgerAccount*2+2);
        sheetCell8.setCellValue("G04二级项目");
        sheetCell8.setCellStyle(sheet2Style2);
        Cell sheetCell9 = sheet2Row6.createCell(ledgerAccount*2+3);
        sheetCell9.setCellValue("G04三级项目");
        sheetCell9.setCellStyle(sheet2Style2);
        Cell sheetCell10 = sheet2Row6.createCell(ledgerAccount*2+4);
        sheetCell10.setCellValue("是否符合BI定义");
        sheetCell10.setCellStyle(sheet2Style3);
        Cell sheetCell11 = sheet2Row6.createCell(ledgerAccount*2+5);
        sheetCell11.setCellValue("对应BI项目");
        sheetCell11.setCellStyle(sheet2Style3);
        Cell sheetCell12 = sheet2Row6.createCell(ledgerAccount*2+6);
        sheetCell12.setCellValue("对应BI子项");
        sheetCell12.setCellStyle(sheet2Style3);
        Cell sheetCell13 = sheet2Row6.createCell(ledgerAccount*2+7);
        sheetCell13.setCellValue("对应BI典型子项");
        sheetCell13.setCellStyle(sheet2Style3);

        // 获取会计科目映射表数据
        getMappingByMeteringId(result, cmMeteringLegal);
        List<CmSubjectMappingResult> mappingResultList = (List) result.get("mappingResultList");

        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        style.setBorderTop(BorderStyle.THIN); // 上边框
        style.setBorderBottom(BorderStyle.THIN); // 下边框
        style.setBorderLeft(BorderStyle.THIN); // 左边框
        style.setBorderRight(BorderStyle.THIN); // 右边框

        int mappingIndex = 7;
        for (CmSubjectMappingResult mappingResult : mappingResultList) {

            Row row = sheet2.createRow(mappingIndex);

            if (ledgerAccount == 5) {
                createSheet2Row(row, 10, mappingResult.getSubjectNameFive(), style);
                createSheet2Row(row, 9, mappingResult.getSubjectCodeFive(), style);
                createSheet2Row(row, 8, mappingResult.getSubjectNameFour(), style);
                createSheet2Row(row, 7, mappingResult.getSubjectCodeFour(), style);
                createSheet2Row(row, 6, mappingResult.getSubjectNameThree(), style);
                createSheet2Row(row, 5, mappingResult.getSubjectCodeThree(), style);
                createSheet2Row(row, 4, mappingResult.getSubjectNameTwo(), style);
                createSheet2Row(row, 3, mappingResult.getSubjectCodeTwo(), style);
                createSheet2Row(row, 2, mappingResult.getSubjectNameOne(), style);
                createSheet2Row(row, 1, mappingResult.getSubjectCodeOne(), style);
            } else if (ledgerAccount == 4) {
                createSheet2Row(row, 8, mappingResult.getSubjectNameFour(), style);
                createSheet2Row(row, 7, mappingResult.getSubjectCodeFour(), style);
                createSheet2Row(row, 6, mappingResult.getSubjectNameThree(), style);
                createSheet2Row(row, 5, mappingResult.getSubjectCodeThree(), style);
                createSheet2Row(row, 4, mappingResult.getSubjectNameTwo(), style);
                createSheet2Row(row, 3, mappingResult.getSubjectCodeTwo(), style);
                createSheet2Row(row, 2, mappingResult.getSubjectNameOne(), style);
                createSheet2Row(row, 1, mappingResult.getSubjectCodeOne(), style);
            } else if (ledgerAccount == 3) {
                createSheet2Row(row, 6, mappingResult.getSubjectNameThree(), style);
                createSheet2Row(row, 5, mappingResult.getSubjectCodeThree(), style);
                createSheet2Row(row, 4, mappingResult.getSubjectNameTwo(), style);
                createSheet2Row(row, 3, mappingResult.getSubjectCodeTwo(), style);
                createSheet2Row(row, 2, mappingResult.getSubjectNameOne(), style);
                createSheet2Row(row, 1, mappingResult.getSubjectCodeOne(), style);
            } else if (ledgerAccount == 2) {
                createSheet2Row(row, 4, mappingResult.getSubjectNameTwo(), style);
                createSheet2Row(row, 3, mappingResult.getSubjectCodeTwo(), style);
                createSheet2Row(row, 2, mappingResult.getSubjectNameOne(), style);
                createSheet2Row(row, 1, mappingResult.getSubjectCodeOne(), style);
            } else if (ledgerAccount == 1) {
                createSheet2Row(row, 2, mappingResult.getSubjectNameOne(), style);
                createSheet2Row(row, 1, mappingResult.getSubjectCodeOne(), style);
            }

//            Cell cellMa1 = row.createCell(1);
//            cellMa1.setCellValue(mappingResult.getSubjectCodeOne());
//            cellMa1.setCellStyle(style);
//            Cell cellMa2 = row.createCell(2);
//            cellMa2.setCellValue(mappingResult.getSubjectNameOne());
//            cellMa2.setCellStyle(style);
//            Cell cellMa3 = row.createCell(3);
//            cellMa3.setCellValue(mappingResult.getSubjectCodeTwo());
//            cellMa3.setCellStyle(style);
//            Cell cellMa4 = row.createCell(4);
//            cellMa4.setCellValue(mappingResult.getSubjectNameTwo());
//            cellMa4.setCellStyle(style);
//            Cell cellMa5 = row.createCell(5);
//            cellMa5.setCellValue(mappingResult.getSubjectCodeThree());
//            cellMa5.setCellStyle(style);
//            Cell cellMa6 = row.createCell(6);
//            cellMa6.setCellValue(mappingResult.getSubjectNameThree());
//            cellMa6.setCellStyle(style);
            Cell cellMa7 = row.createCell(ledgerAccount*2+1);
            cellMa7.setCellValue(mappingResult.getG04ProjectOne());
            cellMa7.setCellStyle(style);
            Cell cellMa8 = row.createCell(ledgerAccount*2+2);
            cellMa8.setCellValue(mappingResult.getG04ProjectTwo());
            cellMa8.setCellStyle(style);
            Cell cellMa9 = row.createCell(ledgerAccount*2+3);
            cellMa9.setCellValue(mappingResult.getG04ProjectThree());
            cellMa9.setCellStyle(style);
            Cell cellMa10 = row.createCell(ledgerAccount*2+4);
            cellMa10.setCellValue(mappingResult.getIsFitBiName());
            cellMa10.setCellStyle(style);
            Cell cellMa11 = row.createCell(ledgerAccount*2+5);
            cellMa11.setCellValue(mappingResult.getCorrespondBiSubjectName());
            cellMa11.setCellStyle(style);
            Cell cellMa12 = row.createCell(ledgerAccount*2+6);
            cellMa12.setCellValue(mappingResult.getCorrespondBiSubitemName());
            cellMa12.setCellStyle(style);
            Cell cellMa13 = row.createCell(ledgerAccount*2+7);
            cellMa13.setCellValue(mappingResult.getCorrespondBiTypicalSubitemName());
            cellMa13.setCellStyle(style);
            Cell cellMa14 = row.createCell(ledgerAccount*2+8);
            cellMa14.setCellValue(mappingResult.getEndBalance() == null ? "" : mappingResult.getEndBalance() + "");
            cellMa14.setCellStyle(style);
            mappingIndex++;
        }

        // 设置宽度

        int legalStartIndex = 1;
        while (legalStartIndex<=ledgerAccount*2) {
            sheet2.setColumnWidth(legalStartIndex, 3000);
            legalStartIndex++;
        }
//        sheet2.setColumnWidth(1, 3000);
//        sheet2.setColumnWidth(2, 3000);
//        sheet2.setColumnWidth(3, 3000);
//        sheet2.setColumnWidth(4, 3000);
//        sheet2.setColumnWidth(5, 3000);
//        sheet2.setColumnWidth(6, 3000);

        sheet2.setColumnWidth(ledgerAccount*2+1, 3000);
        sheet2.setColumnWidth(ledgerAccount*2+2, 3000);
        sheet2.setColumnWidth(ledgerAccount*2+3, 3000);
        sheet2.setColumnWidth(ledgerAccount*2+4, 4000);
        sheet2.setColumnWidth(ledgerAccount*2+5, 3000);
        sheet2.setColumnWidth(ledgerAccount*2+6, 3000);
        sheet2.setColumnWidth(ledgerAccount*2+7, 3500);
        sheet2.setColumnWidth(ledgerAccount*2+8, 8000);

        // sheet3
        CellStyle sheet3Style1 = createNormalStyle(workbook,
                "1", "center", null, "1", "1", "1", "1", null);
        CellStyle sheet3Style2 = createNormalStyle(workbook,
                "1", "center", "22", "1", "1", "1", "1", null);

        CellStyle sheet3Style3 = createNormalStyle(workbook,
                "0", "center", "13", "1", "1", "1", "1", null);

        Row sheet3Row1 = sheet3.createRow(3);
        sheet3Row1.setHeightInPoints(20);
        Cell sheet3Cell1 = sheet3Row1.createCell(1);
        sheet3Cell1.setCellValue("法人生息资产统计表");
        sheet3Cell1.setCellStyle(sheet3Style1);

        Row sheet3Row2 = sheet3.createRow(5);
        Cell sheet3Cell2 = sheet3Row2.createCell(1);
        sheet3Cell2.setCellValue("余额（万元）");
        sheet3Cell2.setCellStyle(sheet3Style2);

        Row sheet3Row3 = sheet3.createRow(6);
        Cell sheet3Cell3 = sheet3Row3.createCell(1);
        sheet3Cell3.setCellStyle(sheet3Style3);

        // 放入数据
        getAssetByMeteringId(result, cmMeteringLegal);
        List<CmBasicInterBearAssetResult> assetResultList = (List) result.get("assetResultList");
        if (assetResultList != null && !assetResultList.isEmpty()) {
            BigDecimal assetAmount = assetResultList.get(0).getAssetAmount();
            sheet3Cell3.setCellValue( assetAmount == null ? "" : assetAmount+"");
        }
        // 设置宽度
        sheet3.setColumnWidth(1, 12000);

        // sheet4
        mergeCells(sheet4, 3, 3, 1, 2, "法人股利收入统计表", tableStyle);

//        Row sheet4Row1 = sheet4.createRow(3);
//        sheet4Row1.setHeightInPoints(20);
//        Cell sheet4Cell1 = sheet4Row1.createCell(1);
//        sheet4Cell1.setCellValue("法人股利收入统计表");
//        sheet4Cell1.setCellStyle(sheet3Style1);

        Row sheet4Row2 = sheet4.createRow(5);
        Cell sheet4Cell2 = sheet4Row2.createCell(1);
        sheet4Cell2.setCellValue("数据来源");
        sheet4Cell2.setCellStyle(sheet3Style2);
        Cell sheet4Cell3 = sheet4Row2.createCell(2);
        sheet4Cell3.setCellValue("金额（借正贷负/万元）");
        sheet4Cell3.setCellStyle(sheet3Style2);


        getDividendByMeteringId(result, cmMeteringLegal);
        List<CmBasicCompanyDividendRemoveResult> dividendList = (List) result.get("dividendList");
        int dividendIndex = 6;
        for (CmBasicCompanyDividendRemoveResult dividendResult : dividendList) {
            Row sheet4Row = sheet4.createRow(dividendIndex);
            sheet4Row.setHeightInPoints(30);
            Cell sheet4Cell4 = sheet4Row.createCell(1);
            sheet4Cell4.setCellValue(dividendResult.getDataSource());
            sheet4Cell4.setCellStyle(normalStyle9);

            Cell sheet4Cell5 = sheet4Row.createCell(2);
            sheet4Cell5.setCellValue(dividendResult.getAmount()==null?"":dividendResult.getAmount()+"");
            sheet4Cell5.setCellStyle(sheet3Style3);
            dividendIndex++;
        }


//        Row sheet4Row3 = sheet4.createRow(6);
//        sheet4Row3.setHeightInPoints(30);
//        Cell sheet4Cell4 = sheet4Row3.createCell(1);
//        sheet4Cell4.setCellValue("会计科目映射表");
//        sheet4Cell4.setCellStyle(normalStyle9);
//        Cell sheet4Cell5 = sheet4Row3.createCell(2);
//        sheet4Cell5.setCellValue("0");
//        sheet4Cell5.setCellStyle(sheet3Style3);
//
//        Row sheet4Row4 = sheet4.createRow(7);
//        sheet4Row4.setHeightInPoints(30);
//        Cell sheet4Cell6 = sheet4Row4.createCell(1);
//        sheet4Cell6.setCellValue("子公司股利剔除项");
//        sheet4Cell6.setCellStyle(normalStyle9);
//        Cell sheet4Cell7 = sheet4Row4.createCell(2);
//        sheet4Cell7.setCellValue("0");
//        sheet4Cell7.setCellStyle(sheet3Style3);
//
//        Row sheet4Row5 = sheet4.createRow(8);
//        sheet4Row5.setHeightInPoints(30);
//        Cell sheet4Cell8 = sheet4Row5.createCell(1);
//        sheet4Cell8.setCellValue("合计");
//        sheet4Cell8.setCellStyle(normalStyle9);
//        Cell sheet4Cell9 = sheet4Row5.createCell(2);
//        sheet4Cell9.setCellValue("0");
//        sheet4Cell9.setCellStyle(sheet3Style3);

        // 设置宽度
        sheet4.setColumnWidth(1, 8000);
        sheet4.setColumnWidth(2, 12000);

        // sheet5
        mergeCells(sheet5, 3, 3, 1, 2, "法人交易账簿净损益统计表", tableStyle);

        Row sheet5Row1 = sheet5.createRow(5);
        sheet5Row1.setHeightInPoints(30);
        Cell sheet5Cell1 = sheet5Row1.createCell(1);
        sheet5Cell1.setCellValue("数据来源");
        sheet5Cell1.setCellStyle(sheet3Style2);
        Cell sheet5Cell2 = sheet5Row1.createCell(2);
        sheet5Cell2.setCellValue("金额（借正贷负/万元）");
        sheet5Cell2.setCellStyle(sheet3Style2);


        getProfitByMeteringId(result, cmMeteringLegal);
        List<CmMeteringProfitResult> accountBookList = (List) result.get("accountBookList");
        int accountIndex = 6;
        for (CmMeteringProfitResult profitResult : accountBookList) {
            Row sheet5Row2 = sheet5.createRow(accountIndex);
            sheet5Row2.setHeightInPoints(30);
            Cell sheet5Cell3 = sheet5Row2.createCell(1);
            sheet5Cell3.setCellValue(profitResult.getDataSource());
            sheet5Cell3.setCellStyle(normalStyle9);

            Cell sheet5Cell4 = sheet5Row2.createCell(2);
            sheet5Cell4.setCellValue(profitResult.getAmount()==null?"":profitResult.getAmount()+"");
            sheet5Cell4.setCellStyle(sheet3Style3);
            accountIndex++;
        }

//        Row sheet5Row2 = sheet5.createRow(6);
//        sheet5Row2.setHeightInPoints(30);
//        Cell sheet5Cell3 = sheet5Row2.createCell(1);
//        sheet5Cell3.setCellValue("会计科目映射表");
//        sheet5Cell3.setCellStyle(normalStyle9);
//        Cell sheet5Cell4 = sheet5Row2.createCell(2);
//        sheet5Cell4.setCellValue("0");
//        sheet5Cell4.setCellStyle(sheet3Style3);
//
//        Row sheet5Row3 = sheet5.createRow(7);
//        sheet5Row3.setHeightInPoints(30);
//        Cell sheet5Cell5 = sheet5Row3.createCell(1);
//        sheet5Cell5.setCellValue("交易账簿业务系统");
//        sheet5Cell5.setCellStyle(normalStyle9);
//        Cell sheet5Cell6 = sheet5Row3.createCell(2);
//        sheet5Cell6.setCellValue("0");
//        sheet5Cell6.setCellStyle(sheet3Style3);
//
//        Row sheet5Row5 = sheet5.createRow(8);
//        sheet5Row5.setHeightInPoints(30);
//        Cell sheet5Cell7 = sheet5Row5.createCell(1);
//        sheet5Cell7.setCellValue("合计");
//        sheet5Cell7.setCellStyle(normalStyle9);
//        Cell sheet5Cell8 = sheet5Row5.createCell(2);
//        sheet5Cell8.setCellValue("0");
//        sheet5Cell8.setCellStyle(sheet3Style3);

        // 设置宽度
        sheet5.setColumnWidth(1, 8000);
        sheet5.setColumnWidth(2, 12000);


        // sheet6
        mergeCells(sheet6, 3, 3, 1, 4, "法人损失数据统计表", tableStyle);
        Row sheet6Row1 = sheet6.createRow(5);
        Cell sheet6Cell1 = sheet6Row1.createCell(1);
        sheet6Cell1.setCellValue("项目");
        sheet6Cell1.setCellStyle(sheet3Style2);
        Cell sheet6Cell2 = sheet6Row1.createCell(2);
        sheet6Cell2.setCellValue("最近第一年");
        sheet6Cell2.setCellStyle(sheet3Style2);
        Cell sheet6Cell3 = sheet6Row1.createCell(3);
        sheet6Cell3.setCellValue("最近第二年");
        sheet6Cell3.setCellStyle(sheet3Style2);
        Cell sheet6Cell4 = sheet6Row1.createCell(4);
        sheet6Cell4.setCellValue("最近第三年");
        sheet6Cell4.setCellStyle(sheet3Style2);
        CellStyle sheet6Style1 = createNormalStyle(workbook,
                "0", "left", null, "1", "1", "1", "1", null);
        // 获取report6
        getLossDataByMeteringId(result, cmMeteringLegal);
        CmBasicLegalLossResult lossResult = (CmBasicLegalLossResult) result.get("lossDataList");
        Row sheet6Row2 = sheet6.createRow(6);
        Cell sheet6Cell5 = sheet6Row2.createCell(1);
        sheet6Cell5.setCellValue("由操风造成的损失");
        sheet6Cell5.setCellStyle(sheet6Style1);
        Cell sheet6Cell6 = sheet6Row2.createCell(2);
        sheet6Cell6.setCellValue(lossResult.getLossValueOne()==null? "0":lossResult.getLossValueOne()+"");
        sheet6Cell6.setCellStyle(sheet3Style3);
        Cell sheet6Cell7 = sheet6Row2.createCell(3);
        sheet6Cell7.setCellValue(lossResult.getLossValueTwo()==null?"0":lossResult.getLossValueTwo()+"");
        sheet6Cell7.setCellStyle(sheet3Style3);
        Cell sheet6Cell8 = sheet6Row2.createCell(4);
        sheet6Cell8.setCellValue(lossResult.getLossValueThree()==null?"0":lossResult.getLossValueThree()+"");
        sheet6Cell8.setCellStyle(sheet3Style3);

        Row sheet6Row3 = sheet6.createRow(7);
        Cell sheet6Cell9 = sheet6Row3.createCell(1);
        sheet6Cell9.setCellValue("由操风造成的损失（上年度）");
        sheet6Cell9.setCellStyle(sheet6Style1);
        Cell sheet6Cell10 = sheet6Row3.createCell(2);
        sheet6Cell10.setCellStyle(normalStyle9);
        Cell sheet6Cell11 = sheet6Row3.createCell(3);
        sheet6Cell11.setCellStyle(sheet3Style3);
        sheet6Cell11.setCellValue(lossResult.getLossValuePreTwo()==null?"0":lossResult.getLossValuePreTwo()+"");
        Cell sheet6Cell12 = sheet6Row3.createCell(4);
        sheet6Cell12.setCellStyle(sheet3Style3);

        Row sheet6Row4 = sheet6.createRow(8);
        Cell sheet6Cell13 = sheet6Row4.createCell(1);
        sheet6Cell13.setCellValue("近10年操风损失的算数平均值");
        sheet6Cell13.setCellStyle(sheet6Style1);
        Cell sheet6Cell14 = sheet6Row4.createCell(2);
        sheet6Cell14.setCellStyle(sheet3Style3);
        sheet6Cell14.setCellValue(lossResult.getLossValueAverage()==null?"0":lossResult.getLossValueAverage()+"");
        Cell sheet6Cell15 = sheet6Row4.createCell(3);
        sheet6Cell15.setCellStyle(normalStyle9);
        Cell sheet6Cell16 = sheet6Row4.createCell(4);
        sheet6Cell16.setCellStyle(normalStyle9);

        Row sheet6Row5 = sheet6.createRow(9);
        Cell sheet6Cell17 = sheet6Row5.createCell(1);
        sheet6Cell17.setCellValue("监管给定的ILM");
        sheet6Cell17.setCellStyle(sheet6Style1);
        Cell sheet6Cell18 = sheet6Row5.createCell(2);
        sheet6Cell18.setCellStyle(sheet3Style3);
        sheet6Cell18.setCellValue(lossResult.getIlm());
        Cell sheet6Cell19 = sheet6Row5.createCell(3);
        sheet6Cell19.setCellStyle(normalStyle9);
        Cell sheet6Cell20 = sheet6Row5.createCell(4);
        sheet6Cell20.setCellStyle(normalStyle9);

        XSSFDrawing drawing = sheet6.createDrawingPatriarch();
        XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 2, 7, 3, 8);
        XSSFSimpleShape line = drawing.createSimpleShape(anchor);
        line.setShapeType(ShapeTypes.LINE);
        line.setLineStyleColor(0, 0, 0);
        line.setLineWidth(1);

        XSSFClientAnchor anchor2 = drawing.createAnchor(0, 0, 0, 0, 3, 8, 4, 9);
        XSSFSimpleShape line2 = drawing.createSimpleShape(anchor2);
        line2.setShapeType(ShapeTypes.LINE);
        line2.setLineStyleColor(0, 0, 0);
        line2.setLineWidth(1);

        XSSFClientAnchor anchor3 = drawing.createAnchor(0, 0, 0, 0, 4, 8, 5, 9);
        XSSFSimpleShape line3 = drawing.createSimpleShape(anchor3);
        line3.setShapeType(ShapeTypes.LINE);
        line3.setLineStyleColor(0, 0, 0);
        line3.setLineWidth(1);

        XSSFClientAnchor anchor4 = drawing.createAnchor(0, 0, 0, 0, 3, 9, 4, 10);
        XSSFSimpleShape line4 = drawing.createSimpleShape(anchor4);
        line4.setShapeType(ShapeTypes.LINE);
        line4.setLineStyleColor(0, 0, 0);
        line4.setLineWidth(1);

        XSSFClientAnchor anchor5 = drawing.createAnchor(0, 0, 0, 0, 4, 9, 5, 10);
        XSSFSimpleShape line5 = drawing.createSimpleShape(anchor5);
        line5.setShapeType(ShapeTypes.LINE);
        line5.setLineStyleColor(0, 0, 0);
        line5.setLineWidth(1);

        // 设置宽度
        sheet6.setColumnWidth(1, 9000);
        sheet6.setColumnWidth(2, 8000);
        sheet6.setColumnWidth(3, 8000);
        sheet6.setColumnWidth(4, 8000);

        response.setHeader("Connection", "close");
        response.setHeader("Content-Type",
                "application/vnd.ms-excel;charset=UTF-8");
        OutputStream out = null;
        String fileName = cmMeteringLegal.getVersion() + ".xlsx";
        fileName = URLEncoder.encode(fileName, "utf-8");
        response.setHeader("Content-Disposition", "attachment;filename="
                + fileName);
        try {
            out = response.getOutputStream();
        } catch (IOException e) {
            log.error(e.getMessage(),e);
        }

        if(out != null){
            try {
                workbook.write(out);
                out.flush();
                out.close();
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> reckonMetering(CmMeteringLegal cmMeteringLegal) {
        // 更新状态为测算中
        int count = cmMeteringLegalMapper.updateReckonMetering(cmMeteringLegal);
        if (count == 0) {
            return Result.error("当前状态不允许测算，请稍后再试");
        }
        cmMeteringLegal = cmMeteringLegalMapper.selectById(cmMeteringLegal.getId());
        List<CmMeteringTableRel> insertRelList = new ArrayList<>();
        // 取出最新的报表入库
        Map<String, Object> result = getCMVersionByMetering(cmMeteringLegal, "1");
        String report1 = (String) result.get("report1");
        String report2 = (String) result.get("report2");
        String report3 = (String) result.get("report3");
        String report4 = (String) result.get("report4");
        String report5 = (String) result.get("report5");
        String report6 = (String) result.get("report6");
        if (report1.equals("X")) {
            return Result.error("会计科目映射表->法人口径不存在，无法进行测算");
        } else {
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report1");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report1);
            insertRelList.add(rel);
        }
        if (report2.equals("X")) {
            return Result.error("损益类科目余额->法人口径不存在，无法进行测算");
        } else {
            // 放入关联关系表中
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report2");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report2);
            insertRelList.add(rel);
        }
        if (report3.equals("X")) {
            return Result.error("生息资产->法人口径不存在，无法进行测算");
        } else {
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report3");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report3);
            insertRelList.add(rel);
        }
        if (report4.equals("X")) {
            return Result.error("子公司股利剔除项不存在，无法进行测算");
        } else {
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report4");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report4);
            insertRelList.add(rel);
        }
        if (report5.equals("X")) {
            return Result.error("交易账簿净损益->法人口径不存在，无法进行测算");
        } else {
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report5");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report5);
            insertRelList.add(rel);
        }
        if (report6.equals("X")) {
            return Result.error("损失数据->法人口径不存在，无法进行测算");
        } else {
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report6");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report6);
            insertRelList.add(rel);
        }

        // 获取BI项
        String names[] = new String[]{"股利收入", "交易账簿净损益", "双账簿"};
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.in("name", names);
        List<SysCategory> categoryList = sysCategoryMapper.selectList(wrapper);

        // 清除数据，重新入库
        cmMeteringLegalMapper.deleteMappingByMeteringId(cmMeteringLegal.getId());
        cmMeteringLegalMapper.deleteAssetByMeteringId(cmMeteringLegal.getId());
        cmMeteringLegalMapper.deleteDividendByMeteringId(cmMeteringLegal.getId());
        cmMeteringLegalMapper.deleteProfitByMeteringId(cmMeteringLegal.getId());
        cmMeteringLegalMapper.deleteLossByMeteringId(cmMeteringLegal.getId());
        cmMeteringLegalMapper.deleteCalculationByMeteringId(cmMeteringLegal.getId());
        cmMeteringLegalMapper.deleteRelByMeteringId(cmMeteringLegal.getId());

        // 开始计算操作风险加权资产测算表
        result.put("meteringId", cmMeteringLegal.getId());
        // 第一：计算会计科目映射表
        List<CmSubjectMappingResult> mappingResultList = iMappingResultService.getMappingResultList(result);
        if (mappingResultList != null && !mappingResultList.isEmpty()) {
            iMappingResultService.saveBatch(mappingResultList);
        }

        // 第二：计算生息资产表
        QueryWrapper wrapper1 = new QueryWrapper();
        wrapper1.eq("version", report3);
        CmBasicInterBearAsset asset = assetMapper.selectOne(wrapper1);
        BigDecimal assetAmount = asset.getAssetAmount();
        if (asset.getAmountUnit().equals("元")) {
            assetAmount = assetAmount.divide(new BigDecimal("10000"));
        }
        // 放入对象中
        CmBasicInterBearAssetResult assetResult = new CmBasicInterBearAssetResult();
        assetResult.setMeteringId(cmMeteringLegal.getId());
        assetResult.setAssetAmount(assetAmount);
        assetResultMapper.insert(assetResult);


        // 第三：计算股利收入统计表
        // 先计算会计科目映射表中M列为股利收入的余额总计
        // TODO:huanjing （单位是元还是万元需要和科技核对）
        List<SysCategory> categoryList1 = categoryList.stream().filter(o -> o.getName().equals("股利收入")).collect(Collectors.toList());
        List<CmSubjectMappingResult> dividendPart1List =
                mappingResultList.stream().filter(o -> StringUtils.isNotBlank(o.getCorrespondBiSubitem()) && o.getCorrespondBiSubitem().equals(categoryList1.get(0).getId())).collect(Collectors.toList());
        BigDecimal dividendPart1 = new BigDecimal("0");
        for (CmSubjectMappingResult result1: dividendPart1List) {
            BigDecimal endBalance = result1.getEndBalance();
            if (endBalance == null) {
                endBalance = new BigDecimal("0");
            }
            dividendPart1 = dividendPart1.add(endBalance);
        }
        dividendPart1 = dividendPart1.divide(new BigDecimal("10000"));

        // 计算股利剔除项
        QueryWrapper wrapper2 = new QueryWrapper();
        wrapper2.eq("version", report4);
        CmBasicCompanyDividendRemove dividendRemove = dividendRemoveMapper.selectOne(wrapper2);
        BigDecimal dividendPart2 = dividendRemove.getDividendAmount();
        if (dividendRemove.getAmountUnit().equals("元")) {
            dividendPart2 = dividendPart2.divide(new BigDecimal("10000"));
        }
        // 计算合计
        BigDecimal dividendTotal = dividendPart1.add(dividendPart2);

        // 放入会计科目余额
        CmBasicCompanyDividendRemoveResult removeResult1 = new CmBasicCompanyDividendRemoveResult();
        removeResult1.setMeteringId(cmMeteringLegal.getId());
        removeResult1.setDataSource("会计科目映射表");
        removeResult1.setAmount(dividendPart2);
        removeResult1.setSortNum(1);
        removeResultMapper.insert(removeResult1);
        // 放入股利剔除项
        CmBasicCompanyDividendRemoveResult removeResult2 = new CmBasicCompanyDividendRemoveResult();
        removeResult2.setMeteringId(cmMeteringLegal.getId());
        removeResult2.setDataSource("子公司股利剔除项");
        removeResult2.setAmount(dividendPart1);
        removeResult2.setSortNum(2);
        removeResultMapper.insert(removeResult2);
        // 放入合计
        CmBasicCompanyDividendRemoveResult removeResult3 = new CmBasicCompanyDividendRemoveResult();
        removeResult3.setMeteringId(cmMeteringLegal.getId());
        removeResult3.setDataSource("合计");
        removeResult3.setAmount(dividendTotal);
        removeResult3.setSortNum(3);
        removeResultMapper.insert(removeResult3);

        // 第四：计算交易账簿净损益 借贷标志(D-借@s@C-贷)
        List<SysCategory> categoryList2 = categoryList.stream().filter(o -> o.getName().equals("交易账簿净损益")).collect(Collectors.toList());
        List<CmSubjectMappingResult> profitPart1List =
                mappingResultList.stream().filter(o -> StringUtils.isNotBlank(o.getCorrespondBiSubitem()) && o.getCorrespondBiSubitem().equals(categoryList2.get(0).getId())).collect(Collectors.toList());
        BigDecimal profitPart1 = new BigDecimal("0");
        for (CmSubjectMappingResult result1 : profitPart1List) {
            BigDecimal endBalance = result1.getEndBalance();
            if (endBalance == null) {
                endBalance = new BigDecimal("0");
            }
            profitPart1 = profitPart1.add(endBalance);
        }
        profitPart1 = profitPart1.divide(new BigDecimal("10000"));

        // 取出第二部分交易账簿业务系统的金额
        BigDecimal profitPart2 = new BigDecimal("0");
        // 筛选出【1.4_交易账簿净损益】B列【科目代码】在【2.1_会计科目映射表】F列对应的M列【对应BI项目】为“双账簿”的科目
        // 定义账簿的科目号
        List<String> profitCodeList = new ArrayList<>();
        List<SysCategory> categoryList3 = categoryList.stream().filter(o -> o.getName().equals("双账簿")).collect(Collectors.toList());
        List<CmSubjectMappingResult> profitPart2List =
                mappingResultList.stream().filter(o -> StringUtils.isNotBlank(o.getCorrespondBiSubitem()) && o.getCorrespondBiSubitem().equals(categoryList3.get(0).getId())).collect(Collectors.toList());
        for (CmSubjectMappingResult result1 : profitPart2List) {
            String lastSubjectCode = result1.getLastSubjectCode();
            profitCodeList.add(lastSubjectCode);
        }
        if (profitCodeList.size()>0) {
            BigDecimal profitC = new BigDecimal("0");
            BigDecimal profitD = new BigDecimal("0");
            BigDecimal subtract = new BigDecimal("0");
            result.put("profitCodeList", profitCodeList);
            List<CmBasicLegalProfitResults> resultsList = profitResultsMapper.selectProfitList(result);
            for (CmBasicLegalProfitResults results : resultsList) {
                String debitAndCredit = results.getDebitAndCredit();
                if (debitAndCredit.equals("C")) {
                    profitC = profitC.add(results.getCnyAmount());
                } else if (debitAndCredit.equals("D")) {
                    profitD = profitD.add(results.getCnyAmount());
                }
            }
            subtract = profitD.subtract(profitC);
            profitPart2 = subtract.divide(new BigDecimal("10000"));
        }
        // 计算合计
        BigDecimal profitTotal = profitPart1.add(profitPart2);

        CmMeteringProfitResult profitResult1 = new CmMeteringProfitResult();
        profitResult1.setMeteringId(cmMeteringLegal.getId());
        profitResult1.setDataSource("会计科目映射表");
        profitResult1.setAmount(profitPart1);
        profitResult1.setSortNum(1);
        profitResultMapper.insert(profitResult1);
        CmMeteringProfitResult profitResult2 = new CmMeteringProfitResult();
        profitResult2.setMeteringId(cmMeteringLegal.getId());
        profitResult2.setDataSource("交易账簿业务系统");
        profitResult2.setAmount(profitPart2);
        profitResult2.setSortNum(2);
        profitResultMapper.insert(profitResult2);
        CmMeteringProfitResult profitResult3 = new CmMeteringProfitResult();
        profitResult3.setMeteringId(cmMeteringLegal.getId());
        profitResult3.setDataSource("合计");
        profitResult3.setAmount(profitTotal);
        profitResult3.setSortNum(3);
        profitResultMapper.insert(profitResult3);

        // 第五：计算损失数据
        QueryWrapper wrapper4 = new QueryWrapper();
        wrapper4.eq("version", report6);
        CmBasicLegalLoss loss = legalLossMapper.selectOne(wrapper4);
        CmBasicLegalLossResult lossResult = new CmBasicLegalLossResult();
        if (loss != null) {
            lossResult.setMeteringId(cmMeteringLegal.getId());
            lossResult.setLossValueAverage(loss.getLossValueAverage());
            lossResult.setLossValueOne(loss.getLossValueOne());
            lossResult.setLossValuePreThree(loss.getLossValuePreThree());
            lossResult.setLossValueTwo(loss.getLossValueTwo());
            lossResult.setLossValueThree(loss.getLossValueThree());
            lossResult.setLossValuePreTwo(loss.getLossValuePreTwo());

            QueryWrapper frWrapper = new QueryWrapper();
            frWrapper.eq("org_type", "2");
            frWrapper.eq("org_state", "2");
            frWrapper.eq("del_flag", 0);
            CmRulesOrganization organization = organizationMapper.selectOne(frWrapper);
            if (organization != null) {
                lossResult.setIlm(organization.getSuperviseIlm());
            }
            legalLossResultMapper.insert(lossResult);
        }

        // 第六：计算操作风险加权资产
        CmMeteringLegal meteringLegal = cmMeteringLegalMapper.selectById(cmMeteringLegal.getId());
        QueryWrapper<CmWeightedAssetsCalculationConfig> wrapper3 = new QueryWrapper<>();
        //wrapper3.ne("compute_number", "0");
        wrapper3.orderByAsc("compute_number");
        List<CmWeightedAssetsCalculationConfig> configLists = configService.list(wrapper3);

        // 获取年份
        String meteringYear = meteringLegal.getMeteringYear();
        // 获取最近第二年
        String lastSecondMeteringYear = (Integer.parseInt(meteringYear) - 1) + "";
        // 获取最近第三年
        String lastThirdMeteringYear = (Integer.parseInt(meteringYear) - 2) + "";
        // 获取审计版本
        String auditVersionNow = meteringLegal.getAuditVersion();
        // 获取最近第二年的审计版本
        String lastTwoAuditVersion = cmMeteringLegalMapper.getLastAuditVersion(auditVersionNow, lastSecondMeteringYear, meteringLegal.getMeteringFlag());
        if ("审计后".equals(auditVersionNow) && StringUtils.isBlank(lastTwoAuditVersion)) {
            lastTwoAuditVersion = cmMeteringLegalMapper.getLastAuditVersion("审计前", lastSecondMeteringYear, meteringLegal.getMeteringFlag());
        }
        if (StringUtils.isBlank(lastTwoAuditVersion)) {
            lastTwoAuditVersion = auditVersionNow;
        }
        // 获取最近第二年的审计版本
        String lastThreeAuditVersion = cmMeteringLegalMapper.getLastAuditVersion(auditVersionNow, lastThirdMeteringYear, meteringLegal.getMeteringFlag());
        if ("审计后".equals(auditVersionNow) && StringUtils.isBlank(lastThreeAuditVersion)) {
            lastThreeAuditVersion = cmMeteringLegalMapper.getLastAuditVersion("审计前", lastThirdMeteringYear, meteringLegal.getMeteringFlag());
        }
        if (StringUtils.isBlank(lastThreeAuditVersion)) {
            lastThreeAuditVersion = auditVersionNow;
        }
        meteringLegal.setLastSecondMeteringYear(lastSecondMeteringYear);
        meteringLegal.setLastThirdMeteringYear(lastThirdMeteringYear);
        meteringLegal.setLastTwoAuditVersion(lastTwoAuditVersion);
        meteringLegal.setLastThreeAuditVersion(lastThreeAuditVersion);

        CmMeteringLegal updateMetering = new CmMeteringLegal();
        // 开始计算
        for (CmWeightedAssetsCalculationConfig config : configLists) {
            // 获取第一年的计算方法
            String disabledFirstValue = config.getDisabledFirstValue();
            BigDecimal firstValue = new BigDecimal("0");
            if ("0".equals(disabledFirstValue)) {
                String firstComputeWay = config.getFirstComputeWay() == null ? "" : config.getFirstComputeWay();
                String firstComputeContent = config.getFirstComputeContent();
                String cellNumFirst = config.getCellNumFirst() == null ? "" : config.getCellNumFirst();
                firstValue = startComputeResult(firstComputeWay, firstComputeContent,
                        cellNumFirst, meteringLegal, configLists, profitTotal, 
                        dividendTotal, lossResult, assetAmount);
            }
            config.setFirstValue(firstValue);
            // 获取最近第二年的数据
            String disabledSecondValue = config.getDisabledSecondValue();
            BigDecimal secondValue = new BigDecimal("0");
            if ("0".equals(disabledSecondValue)) {
                String secondComputeWay = config.getSecondComputeWay() == null ? "" : config.getSecondComputeWay();
                String secondComputeContent = config.getSecondComputeContent();
                String cellNumSecond = config.getCellNumSecond() == null ? "" : config.getCellNumSecond();
                secondValue = startComputeResult(secondComputeWay, secondComputeContent,
                        cellNumSecond, meteringLegal, configLists, profitTotal, 
                        dividendTotal, lossResult, assetAmount);
            }
            config.setSecondValue(secondValue);
            // 获取最近第三年的数据
            String disabledThirdValue = config.getDisabledThirdValue();
            BigDecimal thirdValue = new BigDecimal("0");
            if ("0".equals(disabledThirdValue)) {
                String thirdComputeWay = config.getThirdComputeWay() == null ? "" : config.getThirdComputeWay();
                String thirdComputeContent = config.getThirdComputeContent();
                String cellNumThird = config.getCellNumThird() == null ? "" : config.getCellNumThird();

                thirdValue = startComputeResult(thirdComputeWay, thirdComputeContent,
                        cellNumThird, meteringLegal, configLists, profitTotal, 
                        dividendTotal, lossResult, assetAmount);
            }
            config.setThirdValue(thirdValue);

            CmWeightedAssetsCalculationConfigResult configResult = new CmWeightedAssetsCalculationConfigResult();
            configResult.setDisabledFirstValue(disabledFirstValue);
            configResult.setFirstValue(firstValue);
            configResult.setDisabledSecondValue(disabledSecondValue);
            configResult.setSecondValue(secondValue);
            configResult.setDisabledThirdValue(disabledThirdValue);
            configResult.setThirdValue(thirdValue);
            configResult.setMeteringId(cmMeteringLegal.getId());
            configResult.setConfigId(config.getId());
            configResult.setPid(config.getPid());
            configResult.setHasChild(config.getHasChild());
            configResult.setSortNum(config.getSortNum());
            configResult.setFirstValue(firstValue);
            configResult.setProjectName(config.getProjectName());
            configResult.setSerialNumber(config.getSerialNumber());
            configResult.setTitleLevel(config.getTitleLevel());
            if (config.getCellNumFirst().equals("D30")) {
                updateMetering.setMeteringResult(configResult.getFirstValue());
            }
            resultService.save(configResult);
        }
        // 入库关联关系表
        relService.saveBatch(insertRelList);
        // 更新状态为待提交
        updateMetering.setId(cmMeteringLegal.getId());
        updateMetering.setState("3");
        updateMetering.setResultGenerateTime(new Date());
        cmMeteringLegalMapper.updateById(updateMetering);
        return Result.ok("测算完成");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> reckonMeteringGroup(CmMeteringLegal cmMeteringLegal) throws Exception {
        cmMeteringLegal = cmMeteringLegalMapper.selectById(cmMeteringLegal.getId());
        // 更新状态为测算中
        int count = cmMeteringLegalMapper.updateReckonMetering(cmMeteringLegal);
        if (count == 0) {
            return Result.error("当前状态不允许测算，请稍后再试");
        }
        QueryWrapper jtWrapper = new QueryWrapper();
        jtWrapper.eq("org_type", "1");
        jtWrapper.eq("org_state", "2");
        jtWrapper.eq("del_flag", 0);
        CmRulesOrganization organization1 = organizationMapper.selectOne(jtWrapper);
        if (organization1 == null) {
            throw new Exception("集团未生效，请检查！");
        }
        String isLossDataCheck = organization1.getIsLossDataCheck();
        if (StringUtils.isBlank(isLossDataCheck)) {
            throw new Exception("集团验收状态为空，无法进行测算！");
        }
        String projectName = "";
        String configType = "";
        if ("2".equals(isLossDataCheck)) {
            projectName = "均未验收";
            configType = "2";
        } else if ("3".equals(isLossDataCheck)) {
            projectName = "部分验收";
            configType = "3";
        } else if ("4".equals(isLossDataCheck)) {
            projectName = "全部验收";
            configType = "4";
        } else {
            throw new Exception("集团验收状态未知，请检查！");
        }

        List<CmMeteringTableRel> insertRelList = new ArrayList<>();
        // 取出最新的报表入库
        Map<String, Object> result = getCMVersionByMetering(cmMeteringLegal, "1");
        //TODO:huanjing 判断是否存在X的情况
        // 取出最新的报表入库
        String report1 = (String) result.get("report1");
        String report2 = (String) result.get("report2");
        String report3 = (String) result.get("report3");
        String report4 = (String) result.get("report4");
        String report5 = (String) result.get("report5");
        String report6 = (String) result.get("report6");

        if (report1.equals("X")) {
            return Result.error("会计科目映射表->法人口径不存在，无法进行测算");
        } else {
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report1");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report1);
            insertRelList.add(rel);
        }
        if (report2.equals("X")) {
            return Result.error("损益类科目余额->法人口径不存在，无法进行测算");
        } else {
            // 放入关联关系表中
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report2");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report2);
            insertRelList.add(rel);
        }
        if (report3.equals("X")) {
            return Result.error("生息资产->法人口径不存在，无法进行测算");
        } else {
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report3");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report3);
            insertRelList.add(rel);
        }
        if (report4.equals("X")) {
            return Result.error("子公司股利剔除项不存在，无法进行测算");
        } else {
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report4");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report4);
            insertRelList.add(rel);
        }
        if (report5.equals("X")) {
            return Result.error("交易账簿净损益->法人口径不存在，无法进行测算");
        } else {
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report5");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report5);
            insertRelList.add(rel);
        }
        if (report6.equals("X")) {
            return Result.error("损失数据->法人口径不存在，无法进行测算");
        } else {
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report6");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report6);
            insertRelList.add(rel);
        }

        // 获取会计科目映射表->集团口径 report7
        List<CmVersionCompany> report7List = (List) result.get("report7");
        if (report7List == null || report7List.isEmpty()) {
            throw new Exception("不存在会计科目映射表->集团口径，请检查");
        } else {
            //TODO:需要遍历校验
            List<String> orgNotExistsList = new ArrayList<>();
            for (CmVersionCompany cmVersionCompany : report7List) {
                String version = cmVersionCompany.getVersion();
                if ("X".equals(version)) {
                    orgNotExistsList.add(cmVersionCompany.getOrgName());
                }
            }
            if (orgNotExistsList.size()>0) {
                throw new Exception("【" + String.join(",", orgNotExistsList) + "】不存在会计科目映射表->集团口径，请重新生成对应的集团会计科目映射表");
            }
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report7");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(JSON.toJSONString(report7List));
            insertRelList.add(rel);
        }
        // 获取损益类科目余额->集团口径
        List<CmVersionCompany> report8List = (List) result.get("report8");
        if (report8List == null || report8List.isEmpty()) {
            throw new Exception("不存在损益类科目余额->集团口径，请检查");
        } else {
            List<String> orgNotExistsList = new ArrayList<>();
            for (CmVersionCompany cmVersionCompany : report8List) {
                String version = cmVersionCompany.getVersion();
                if ("X".equals(version)) {
                    orgNotExistsList.add(cmVersionCompany.getOrgName());
                }
            }
            if (orgNotExistsList.size()>0) {
                throw new Exception("【" + String.join(",", orgNotExistsList) + "】不存在损益类科目余额->集团口径，请重新生成对应的集团损益类科目余额");
            }
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report8");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(JSON.toJSONString(report8List));
            insertRelList.add(rel);
        }
        String report9 = (String) result.get("report9");
        if (report9.equals("X")) {
            throw new Exception("生息资产->集团口径不存在，无法进行测算");
        } else {
            // 如果集团验收是部分验收，则校验机构是否存在
            if ("3".equals(isLossDataCheck)) {
                // 计算生效的附属公司是否存在生息资产
                List<String> orgNameList = cmMeteringLegalMapper.getAssetNotInCheck(report9);
                if (orgNameList != null && !orgNameList.isEmpty()) {
                    throw new Exception("【"+ String.join(",", orgNameList) +"】不存在生息资产->集团口径，请重新生成对应的集团生息资产");
                }
            }
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report9");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report9);
            insertRelList.add(rel);
        }

        String report10 = (String) result.get("report10");
        if (report10.equals("X")) {
            throw new Exception("集团内往来抵销数据不存在，无法进行测算");
        } else {
            // 计算生效的集团内往来抵销数据
            List<String> orgNameList = cmMeteringLegalMapper.getOffsetNotInCheck(report10);
            if (orgNameList != null && !orgNameList.isEmpty()) {
                throw new Exception("【"+ String.join(",", orgNameList) + "】不存在集团内往来抵销数据，请重新生成对应的集团内往来抵销数据");
            }
            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report10");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report10);
            insertRelList.add(rel);
        }

        // 获取交易账簿净损益->集团口径
        List<CmVersionCompany> report11List = (List) result.get("report11");
        if (report11List == null || report11List.isEmpty()) {
            throw new Exception("不存在交易账簿净损益->集团口径，请检查");
        } else {
            // 放入关联关系表中
            List<String> orgNotExistsList = new ArrayList<>();
            for (CmVersionCompany cmVersionCompany : report11List) {
                String version = cmVersionCompany.getVersion();
                if ("X".equals(version)) {
                    orgNotExistsList.add(cmVersionCompany.getOrgName());
                }
            }
            if (orgNotExistsList.size()>0) {
                throw new Exception("【" + String.join(",", orgNotExistsList) + "】不存在交易账簿净损益->集团口径，请重新生成对应的集团交易账簿净损益");
            }
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report11");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(JSON.toJSONString(report11List));
            insertRelList.add(rel);
        }
        // 损失数据->集团口径
        String report12 = (String) result.get("report12");
        if (report12.equals("X")) {
            // 放入关联关系表中
            throw new Exception("损失数据->集团口径不存在，无法进行测算");
        } else {
            // 计算生效的集团内往来抵销数据
            List<String> orgNameList = cmMeteringLegalMapper.getLossNotInCheck(report12);
            if (orgNameList != null && !orgNameList.isEmpty()) {
                throw new Exception("【" + String.join(",", orgNameList) + "】不存在损失数据->集团口径，请重新生成对应的集团口径损失数据");
            }
            // 校验验收状态是否一致
            // List<String> orgNameList1 = cmMeteringLegalMapper.getDataCheckIsSame(report12);

            // 放入关联关系表中
            CmMeteringTableRel rel = new CmMeteringTableRel();
            rel.setReport("report12");
            rel.setMeteringId(cmMeteringLegal.getId());
            rel.setVersion(report12);
            insertRelList.add(rel);
        }

        // 获取BI项
        String names[] = new String[]{"股利收入", "交易账簿净损益", "双账簿", "手续费和佣金收入", "手续费和佣金支出", "其他经营性收入", "其他经营性支出", "金融", "利息收入", "利息支出"};
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.in("name", names);
        List<SysCategory> categoryList = sysCategoryMapper.selectList(wrapper);

        // 删除已有的数据
        cmMeteringLegalMapper.deleteOrgRelByMeteringId(cmMeteringLegal.getId());
        cmMeteringLegalMapper.deleteMappingGroupResultByMeteringId(cmMeteringLegal.getId());
        cmMeteringLegalMapper.deleteAssetGroupByMeteringId(cmMeteringLegal.getId());
        cmMeteringLegalMapper.deleteRelByMeteringId(cmMeteringLegal.getId());
        cmMeteringLegalMapper.deleteDividendGroupByMeteringId(cmMeteringLegal.getId());
        cmMeteringLegalMapper.deleteProfitGroupByMeteringId(cmMeteringLegal.getId());
        cmMeteringLegalMapper.deleteLossGroupByMeteringId(cmMeteringLegal.getId());
        cmMeteringLegalMapper.deleteGroupResultByMeteringId(cmMeteringLegal.getId());


        // 获取启用的附属机构和法人
        List<CmRulesOrganization> organizationList = organizationMapper.selectInUseList();

        // 判断是否有法人
        List<CmRulesOrganization> frList = organizationList.stream().filter(o->o.getOrgType().equals("2")).collect(Collectors.toList());
        if (frList == null || frList.isEmpty()) {
            throw new Exception("法人未生效，请检查！");
        }

        // 定义股利收入集合
        List<CmDividendGroupResult> dividendGroupResultList = new LinkedList<>();
        // 定义股利收入顺序
        int dividendGroupIndex = 1;
        // 定义账簿净损益集合
        List<CmMeteringProfitGroupResult> profitGroupResultList = new LinkedList<>();
        // 定义账簿净损益顺序
        int profitGroupIndex = 1;

        // 定义法人损失数据
        CmBasicGroupLossResult lossResultFR = new CmBasicGroupLossResult();
        String isLossDataCheckFR = "";

        // 定义全部的科目余额表数据
        List<CmSubjectMappingGroupResult> mappingResultAllList = new LinkedList<>();
        // 定义验收通过的机构
        List<String> ystgOrgList = new ArrayList<>();
        // 定义未验收的机构
        List<String> wysOrgList = new ArrayList<>();


        // 开始测算，先测算会计科目映射表
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("report1", result.get("report1"));
        queryMap.put("report2", result.get("report2"));
        queryMap.put("report10", result.get("report10"));
        queryMap.put("meteringId", cmMeteringLegal.getId());
        for (CmRulesOrganization organization : organizationList) {
            // 判断
            String orgType = organization.getOrgType();
            queryMap.put("organizationId", organization.getId());
            queryMap.put("orgCode", organization.getOrgCode());

            // 入库测算与机构关系表
            CmMeteringOrgRel orgRel = new CmMeteringOrgRel();
            orgRel.setMeteringId(cmMeteringLegal.getId());
            orgRel.setOrganizationId(organization.getId());
            orgRel.setLedgerAccount(organization.getLedgerAccount());
            orgRelMapper.insert(orgRel);
            queryMap.put("orgRelId", orgRel.getId());

            // 获取科目余额数据
            List<CmSubjectMappingGroupResult> mappingResultList = new ArrayList<>();
            if ("2".equals(orgType)) {
                mappingResultList = mappingGroupResultService.getMappingResultGroupLegalList(queryMap);
            } else {
                // 获取附属公司-会计科目映射表->集团口径的版本
                List<CmVersionCompany> companyList = report7List.stream().filter(o -> o.getOrganizationId().equals(organization.getId())).collect(Collectors.toList());
                if (companyList == null || companyList.isEmpty()) {
                    throw new Exception("附属公司"+organization.getOrgName()+"不存在会计科目映射表->集团口径，请检查！");
                }
                String report7 = companyList.get(0).getVersion();
                if (StringUtils.isBlank(report7) || "X".equals(report7)) {
                    throw new Exception("附属公司"+organization.getOrgName()+"不存在会计科目映射表->集团口径，请检查！");
                }
                // 获取附属公司-损益类科目余额->集团口径的版本
                List<CmVersionCompany> companyList2 = report8List.stream().filter(o -> o.getOrganizationId().equals(organization.getId())).collect(Collectors.toList());
                if (companyList2 == null || companyList2.isEmpty()) {
                    throw new Exception("附属公司"+organization.getOrgName()+"不存在损益类科目余额->集团口径，请检查！");
                }
                String report8 = companyList2.get(0).getVersion();
                if (StringUtils.isBlank(report8) || "X".equals(report8)) {
                    throw new Exception("附属公司"+organization.getOrgName()+"不存在损益类科目余额->集团口径，请检查！");
                }
                queryMap.put("report7", report7);
                queryMap.put("report8", report8);
                mappingResultList = mappingGroupResultService.getMappingResultGroupCompanyList(queryMap);
                
            }
            if (mappingResultList != null && mappingResultList.size() > 0) {
                for (CmSubjectMappingGroupResult groupResult : mappingResultList) {
                    BigDecimal endBalance = groupResult.getEndBalance();
                    BigDecimal offsetAmount = groupResult.getOffsetAmount();
                    if (endBalance != null) {
                        groupResult.setEndBalance(endBalance.divide(new BigDecimal("10000")));
                        if (offsetAmount == null) {
                            groupResult.setOffsetEndAmount(endBalance.divide(new BigDecimal("10000")));
                        } else {
                            groupResult.setOffsetAmount(offsetAmount.divide(new BigDecimal("10000")));
                            groupResult.setOffsetEndAmount(offsetAmount.add(endBalance).divide(new BigDecimal("10000")));
                        }
                    } else {
                        if (offsetAmount != null) {
                            groupResult.setOffsetAmount(offsetAmount.divide(new BigDecimal("10000")));
                            groupResult.setOffsetEndAmount(offsetAmount.divide(new BigDecimal("10000")));
                        } else {
                            groupResult.setOffsetEndAmount(new BigDecimal("0"));
                        }
                    }
                }
                mappingGroupResultService.saveBatch(mappingResultList);
                mappingResultAllList.addAll(mappingResultList);
            } else {
                throw new Exception(organization.getOrgName() + "科目余额不存在，请检查！");
            }

            // 从这边去取法人的会计科目映射表金额,取【2.1_会计科目映射表】M列为“股利收入”的Q列“抵销后余额”
            List<SysCategory> categoryList1 = categoryList.stream().filter(o -> o.getName().equals("股利收入")).collect(Collectors.toList());
            List<CmSubjectMappingGroupResult> dividendPart1List =
                    mappingResultList.stream().filter(o -> StringUtils.isNotBlank(o.getCorrespondBiSubitem()) && o.getCorrespondBiSubitem().equals(categoryList1.get(0).getId())).collect(Collectors.toList());
            BigDecimal dividendPart1 = new BigDecimal("0");
            for (CmSubjectMappingGroupResult result1: dividendPart1List) {
                BigDecimal offsetEndAmount = result1.getOffsetEndAmount();
                if (offsetEndAmount == null) {
                    offsetEndAmount = new BigDecimal("0");
                }
                dividendPart1 = dividendPart1.add(offsetEndAmount);
            }

            if ("2".equals(orgType)) {
                CmDividendGroupResult dividendGroupResult1 = new CmDividendGroupResult();
                dividendGroupResult1.setMeteringId(cmMeteringLegal.getId());
                dividendGroupResult1.setAmount(dividendPart1);
                dividendGroupResult1.setOrgId(organization.getId());
                dividendGroupResult1.setOrgName(organization.getOrgName());
                dividendGroupResult1.setOrgCode(organization.getOrgCode());
                dividendGroupResult1.setDataSource("会计科目映射表");
                dividendGroupResult1.setSortNum(dividendGroupIndex);
                dividendGroupResultList.add(dividendGroupResult1);
                // 计算股利剔除项
                QueryWrapper wrapper2 = new QueryWrapper();
                wrapper2.eq("version", report4);
                CmBasicCompanyDividendRemove dividendRemove = dividendRemoveMapper.selectOne(wrapper2);
                BigDecimal dividendPart2 = dividendRemove.getDividendAmount();
                if (dividendRemove.getAmountUnit().equals("元")) {
                    dividendPart2 = dividendPart2.divide(new BigDecimal("10000"));
                }
                dividendGroupIndex++;
                CmDividendGroupResult dividendGroupResult2 = new CmDividendGroupResult();
                dividendGroupResult2.setMeteringId(cmMeteringLegal.getId());
                dividendGroupResult2.setAmount(dividendPart2);
                dividendGroupResult2.setOrgId(organization.getId());
                dividendGroupResult2.setOrgName(organization.getOrgName());
                dividendGroupResult2.setOrgCode(organization.getOrgCode());
                dividendGroupResult2.setDataSource("子公司股利剔除项");
                dividendGroupResult2.setSortNum(dividendGroupIndex);
                dividendGroupResultList.add(dividendGroupResult2);
                // 法人合计
                dividendGroupIndex++;
                CmDividendGroupResult dividendGroupResult3 = new CmDividendGroupResult();
                dividendGroupResult3.setMeteringId(cmMeteringLegal.getId());
                dividendGroupResult3.setAmount(dividendPart2);
                dividendGroupResult3.setOrgId(organization.getId());
                dividendGroupResult3.setOrgName(organization.getOrgName());
                dividendGroupResult3.setOrgCode(organization.getOrgCode());
                dividendGroupResult3.setDataSource("合计");
                dividendGroupResult3.setSortNum(dividendGroupIndex);
                dividendGroupResultList.add(dividendGroupResult3);
                dividendGroupIndex++;

                // 取【2.1_会计科目映射表】M列【对应BI子项】为“交易账簿净损益”的Q列“抵销后余额”
                List<SysCategory> categoryList2 = categoryList.stream().filter(o -> o.getName().equals("交易账簿净损益")).collect(Collectors.toList());
                List<CmSubjectMappingGroupResult> profitPart1List =
                        mappingResultList.stream().filter(o -> StringUtils.isNotBlank(o.getCorrespondBiSubitem()) && o.getCorrespondBiSubitem().equals(categoryList2.get(0).getId())).collect(Collectors.toList());
                BigDecimal profitPart1 = new BigDecimal("0");
                for (CmSubjectMappingGroupResult result1 : profitPart1List) {
                    BigDecimal offsetEndAmount = result1.getOffsetEndAmount();
                    if (offsetEndAmount == null) {
                        offsetEndAmount = new BigDecimal("0");
                    }
                    profitPart1 = profitPart1.add(offsetEndAmount);
                }
                profitPart1 = profitPart1.divide(new BigDecimal("10000"));

                // 取出第二部分交易账簿业务系统的金额
                BigDecimal profitPart2 = new BigDecimal("0");
                // 筛选出【1.4_交易账簿净损益】B列【科目代码】在【2.1_会计科目映射表】F列对应的M列【对应BI项目】为“双账簿”的科目
                // 定义账簿的科目号
                List<String> profitCodeList = new ArrayList<>();
                List<SysCategory> categoryList3 = categoryList.stream().filter(o -> o.getName().equals("双账簿")).collect(Collectors.toList());
                List<CmSubjectMappingGroupResult> profitPart2List =
                        mappingResultList.stream().filter(o -> StringUtils.isNotBlank(o.getCorrespondBiSubitem()) && o.getCorrespondBiSubitem().equals(categoryList3.get(0).getId())).collect(Collectors.toList());
                for (CmSubjectMappingGroupResult result1 : profitPart2List) {
                    String lastSubjectCode = result1.getLastSubjectCode();
                    profitCodeList.add(lastSubjectCode);
                }
                if (profitCodeList.size()>0) {
                    BigDecimal profitC = new BigDecimal("0");
                    BigDecimal profitD = new BigDecimal("0");
                    result.put("profitCodeList", profitCodeList);
                    List<CmBasicLegalProfitResults> resultsList = profitResultsMapper.selectProfitList(result);
                    for (CmBasicLegalProfitResults results : resultsList) {
                        String debitAndCredit = results.getDebitAndCredit();
                        if (debitAndCredit.equals("C")) {
                            profitC = profitC.add(results.getCnyAmount());
                        } else if (debitAndCredit.equals("D")) {
                            profitD = profitD.add(results.getCnyAmount());
                        }
                    }
                    BigDecimal subtract = profitD.subtract(profitC);
                    profitPart2 = subtract.divide(new BigDecimal("10000"));
                }

                CmMeteringProfitGroupResult groupResult1 = new CmMeteringProfitGroupResult();
                groupResult1.setMeteringId(cmMeteringLegal.getId());
                groupResult1.setAmount(profitPart1);
                groupResult1.setOrgId(organization.getId());
                groupResult1.setOrgName(organization.getOrgName());
                groupResult1.setOrgCode(organization.getOrgCode());
                groupResult1.setDataSource("会计科目映射表");
                groupResult1.setSortNum(profitGroupIndex);
                profitGroupResultList.add(groupResult1);

                profitGroupIndex++;
                CmMeteringProfitGroupResult groupResult2 = new CmMeteringProfitGroupResult();
                groupResult2.setMeteringId(cmMeteringLegal.getId());
                groupResult2.setAmount(profitPart2);
                groupResult2.setOrgId(organization.getId());
                groupResult2.setOrgName(organization.getOrgName());
                groupResult2.setOrgCode(organization.getOrgCode());
                groupResult2.setDataSource("交易账簿业务系统");
                groupResult2.setSortNum(profitGroupIndex);
                profitGroupResultList.add(groupResult2);

                profitGroupIndex++;
                CmMeteringProfitGroupResult groupResult3 = new CmMeteringProfitGroupResult();
                groupResult3.setMeteringId(cmMeteringLegal.getId());
                groupResult3.setAmount(profitPart1.add(profitPart2));
                groupResult3.setOrgId(organization.getId());
                groupResult3.setOrgName(organization.getOrgName());
                groupResult3.setOrgCode(organization.getOrgCode());
                groupResult3.setDataSource("合计");
                groupResult3.setSortNum(profitGroupIndex);
                profitGroupResultList.add(groupResult3);
                profitGroupIndex++;

                // 计算法人损失数据
                QueryWrapper wrapper4 = new QueryWrapper();
                wrapper4.eq("version", report6);
                CmBasicLegalLoss loss = legalLossMapper.selectOne(wrapper4);
                if (loss != null) {
                    lossResultFR.setMeteringId(cmMeteringLegal.getId());
                    lossResultFR.setLossValueAverage(loss.getLossValueAverage());
                    lossResultFR.setLossValueOne(loss.getLossValueOne());
                    lossResultFR.setLossValuePreThree(loss.getLossValuePreThree());
                    lossResultFR.setLossValueTwo(loss.getLossValueTwo());
                    lossResultFR.setLossValueThree(loss.getLossValueThree());
                    lossResultFR.setLossValuePreTwo(loss.getLossValuePreTwo());
                    lossResultFR.setIlm(organization.getSuperviseIlm());
                    lossResultFR.setOrgId(organization.getId());
                    lossResultFR.setOrgName(organization.getOrgName());
                    lossResultFR.setIsLossDataCheck(organization.getIsLossDataCheck());
                    lossResultFR.setIsLossDataCheckName(organization.getIsLossDataCheck().equals("1") ? "验收通过":"未验收");
                    lossResultFR.setSortNum(3);
                    isLossDataCheckFR = organization.getIsLossDataCheck();
                }
            } else {
                // 获取附属公司股利收入的金额
                CmDividendGroupResult dividendGroupResult = new CmDividendGroupResult();
                dividendGroupResult.setMeteringId(cmMeteringLegal.getId());
                dividendGroupResult.setAmount(dividendPart1);
                dividendGroupResult.setOrgId(organization.getId());
                dividendGroupResult.setOrgName(organization.getOrgName());
                dividendGroupResult.setOrgCode(organization.getOrgCode());
                dividendGroupResult.setDataSource("会计科目映射表");
                dividendGroupResult.setSortNum(dividendGroupIndex);
                dividendGroupResultList.add(dividendGroupResult);
                dividendGroupIndex++;

                // 获取附属公司交易账簿净损益的金额
                CmMeteringProfitGroupResult groupResult = new CmMeteringProfitGroupResult();
                BigDecimal profitAmount = new BigDecimal("0");
                groupResult.setMeteringId(cmMeteringLegal.getId());
                groupResult.setOrgId(organization.getId());
                groupResult.setOrgName(organization.getOrgName());
                groupResult.setOrgCode(organization.getOrgCode());
                groupResult.setDataSource("线下报送");
                groupResult.setSortNum(profitGroupIndex);
                List<CmVersionCompany> companyList = report11List.stream().filter(o ->
                        StringUtils.isNotBlank(o.getOrganizationId()) && o.getOrganizationId().equals(organization.getId())).collect(Collectors.toList());

                if (companyList != null && !companyList.isEmpty()) {
                    String version = companyList.get(0).getVersion();
                    if (StringUtils.isNotBlank(version)) {
                        // 查询对应附属公司的交易账簿净损益
                        QueryWrapper wrapper1 = new QueryWrapper();
                        wrapper1.eq("version", version);
                        CmBasicGroupProfit basicGroupProfit = basicGroupProfitMapper.selectOne(wrapper1);
                        if (basicGroupProfit != null) {
                            String unit = basicGroupProfit.getUnit();
                            BigDecimal profitValue = basicGroupProfit.getProfitValue();
                            if (profitValue != null) {
                                if (StringUtils.isNotBlank(unit)) {
                                    if (unit.equals("1")) {
                                        profitAmount = profitValue.divide(new BigDecimal("10000"));
                                    } else {
                                        profitAmount = profitValue;
                                    }
                                } else {
                                    profitAmount = profitValue;
                                }
                            }
                        }
                    }
                }
                groupResult.setAmount(profitAmount);
                profitGroupResultList.add(groupResult);
                profitGroupIndex++;
            }
            // 判断机构是否验收
            if (organization.getIsLossDataCheck().equals("1")) {
                ystgOrgList.add(organization.getId());
            } else {
                wysOrgList.add(organization.getId());
            }
        }

        // 测算集团生息资产统计表
        // 注：当“计量规则管理->计量机构管理”集团“损失数据是否验收”为“均未验收-2”、“全部验收-4”，无法人及附属公司的数据；
        // 为“部分验收-3”时，仅显示法人及“损失数据是否验收”为“是”的附属公司
        if ("2".equals(isLossDataCheck) || "4".equals(isLossDataCheck)) {
            // 取集团的生息资产数据
            List<String> orgTypeList = new ArrayList<>();
            orgTypeList.add("1");
            List<CmBasicGroupAssetData> assetList = cmMeteringLegalMapper.getAssetGroupList(orgTypeList, report9, "0");
            if (assetList == null || assetList.isEmpty()) {
                throw new Exception("生息资产数据->集团口径不存在，请检查");
            } else {
                CmAssetGroupResult assetGroupResult = new CmAssetGroupResult();

                CmBasicGroupAssetData assetData = assetList.get(0);
                BigDecimal value = assetData.getValue();
                if (value == null) {
                    assetGroupResult.setAssetAmount(new BigDecimal("0"));
                } else {
                    String unit = assetData.getUnit();
                    if (StringUtils.isNotBlank(unit)) {
                        if ("1".equals(unit)) {
                            value = value.divide(new BigDecimal("10000"));
                        }
                    }
                    assetGroupResult.setAssetAmount(value);
                }

                assetGroupResult.setSortNum(1);
                assetGroupResult.setOrgName(assetData.getOrgName());
                assetGroupResult.setOrgCode(assetData.getOrgCode());
                assetGroupResult.setOrgId(assetData.getOrgId());
                assetGroupResult.setMeteringId(cmMeteringLegal.getId());
                assetGroupResultMapper.insert(assetGroupResult);
            }
        } else if ("3".equals(isLossDataCheck)) {
            // 取法人的生息资产数据
            QueryWrapper wrapper1 = new QueryWrapper();
            wrapper1.eq("version", report3);
            CmBasicInterBearAsset asset = assetMapper.selectOne(wrapper1);
            BigDecimal assetAmount = asset.getAssetAmount();
            if (asset.getAmountUnit().equals("元")) {
                assetAmount = assetAmount.divide(new BigDecimal("10000"));
            }
            CmRulesOrganization fr = cmRulesOrganizationMapper.selectById(asset.getOrgId());
            CmAssetGroupResult assetGroupResult = new CmAssetGroupResult();
            assetGroupResult.setAssetAmount(assetAmount);
            assetGroupResult.setSortNum(1);
            assetGroupResult.setOrgName(fr.getOrgName());
            assetGroupResult.setOrgCode(fr.getOrgCode());
            assetGroupResult.setOrgId(asset.getOrgId());
            assetGroupResult.setMeteringId(cmMeteringLegal.getId());
            assetGroupResultMapper.insert(assetGroupResult);

            // 获取附属公司
            List<String> orgTypeList = new ArrayList<>();
            orgTypeList.add("3");
            List<CmBasicGroupAssetData> assetList = cmMeteringLegalMapper.getAssetGroupList(orgTypeList, report9, "1");
            int assetIndex = 2;
            for (CmBasicGroupAssetData assetData : assetList) {
                CmAssetGroupResult assetGroupResult1 = new CmAssetGroupResult();
                BigDecimal value = assetData.getValue();
                if (value == null) {
                    assetGroupResult1.setAssetAmount(new BigDecimal("0"));
                } else {
                    String unit = assetData.getUnit();
                    if (StringUtils.isNotBlank(unit)) {
                        if ("1".equals(unit)) {
                            value = value.divide(new BigDecimal("10000"));
                        }
                    }
                    assetGroupResult1.setAssetAmount(value);
                }
                assetGroupResult1.setSortNum(assetIndex);
                assetGroupResult1.setOrgName(assetData.getOrgName());
                assetGroupResult1.setOrgCode(assetData.getOrgCode());
                assetGroupResult1.setOrgId(assetData.getOrgId());
                assetGroupResult1.setMeteringId(cmMeteringLegal.getId());
                assetGroupResultMapper.insert(assetGroupResult1);
                assetIndex++;
            }
        }
        // 测算股利收入统计表
        // 放入集团
        BigDecimal jtAmount = new BigDecimal("0");
        for (CmDividendGroupResult dividendGroupResult : dividendGroupResultList) {
            String dataSource = dividendGroupResult.getDataSource();
            if (!"合计".equals(dataSource)) {
                jtAmount = jtAmount.add(dividendGroupResult.getAmount());
            }
            dividendGroupResultMapper.insert(dividendGroupResult);
        }
        CmDividendGroupResult jtGroup = new CmDividendGroupResult();
        jtGroup.setMeteringId(cmMeteringLegal.getId());
        jtGroup.setAmount(jtAmount);
        jtGroup.setOrgId(organization1.getId());
        jtGroup.setOrgName(organization1.getOrgName());
        jtGroup.setOrgCode(organization1.getOrgCode());
        jtGroup.setDataSource("集团合计");
        jtGroup.setSortNum(dividendGroupIndex);
        dividendGroupResultMapper.insert(jtGroup);

        // 测算交易账簿净损益
        BigDecimal jtProfitAmount = new BigDecimal("0");
        for (CmMeteringProfitGroupResult profitGroupResult : profitGroupResultList) {
            String dataSource = profitGroupResult.getDataSource();
            if (!"合计".equals(dataSource)) {
                jtProfitAmount = jtProfitAmount.add(profitGroupResult.getAmount());
            }
            profitGroupResultMapper.insert(profitGroupResult);
        }

        CmMeteringProfitGroupResult jtProfitGroup = new CmMeteringProfitGroupResult();
        jtProfitGroup.setMeteringId(cmMeteringLegal.getId());
        jtProfitGroup.setAmount(jtProfitAmount);
        jtProfitGroup.setOrgId(organization1.getId());
        jtProfitGroup.setOrgName(organization1.getOrgName());
        jtProfitGroup.setOrgCode(organization1.getOrgCode());
        jtProfitGroup.setDataSource("集团合计");
        jtProfitGroup.setSortNum(profitGroupIndex);
        profitGroupResultMapper.insert(jtProfitGroup);

        //TODO 获取附属公司损失数据的金额：需要判断单位是元还是万元
        List<CmBasicGroupLossResult> groupLossResultList = groupLossResultMapper.selectGroupLossList(report12);
        // groupLossResultList.addAll(groupLossResultList1);
        // 定义集团的数据-验收通过
        String isInsertYSTG = "0";
        CmBasicGroupLossResult groupResultYSTG = new CmBasicGroupLossResult();
        groupResultYSTG.setOrgId(organization1.getId());
        groupResultYSTG.setOrgName(organization1.getOrgName());
        groupResultYSTG.setMeteringId(cmMeteringLegal.getId());
        // groupResultYSTG.setIlm(organization1.getSuperviseIlm());
        BigDecimal lossValueOne1 = new BigDecimal("0");
        BigDecimal lossValuePreTwo1 = new BigDecimal("0");
        BigDecimal lossValueAverage1 = new BigDecimal("0");
        BigDecimal lossValueTwo1 = new BigDecimal("0");
        BigDecimal lossValueThree1 = new BigDecimal("0");
        BigDecimal lossValuePreThree1 = new BigDecimal("0");

        // 定义集团的数据-未验收
        String isInsertWYS = "0";
        CmBasicGroupLossResult groupResultWYS = new CmBasicGroupLossResult();
        groupResultWYS.setOrgId(organization1.getId());
        groupResultWYS.setOrgName(organization1.getOrgName());
        groupResultWYS.setMeteringId(cmMeteringLegal.getId());

        BigDecimal lossValueOne2 = new BigDecimal("0");
        BigDecimal lossValuePreTwo2 = new BigDecimal("0");
        BigDecimal lossValueAverage2 = new BigDecimal("0");
        BigDecimal lossValueTwo2 = new BigDecimal("0");
        BigDecimal lossValueThree2 = new BigDecimal("0");
        BigDecimal lossValuePreThree2 = new BigDecimal("0");

        int groupLossIndex = 4;
        for (CmBasicGroupLossResult groupLossResult : groupLossResultList) {
            groupLossResult.setMeteringId(cmMeteringLegal.getId());
            groupLossResult.setSortNum(groupLossIndex);
            String isLossDataCheck1 = groupLossResult.getIsLossDataCheck();
            if ("1".equals(isLossDataCheck1)) {
                lossValueOne1 = lossValueOne1.add(groupLossResult.getLossValueOne());
                lossValuePreTwo1 = lossValuePreTwo1.add(groupLossResult.getLossValuePreTwo());
                lossValueAverage1 = lossValueAverage1.add(groupLossResult.getLossValueAverage());
                lossValueTwo1 = lossValueTwo1.add(groupLossResult.getLossValueTwo());
                lossValueThree1 = lossValueThree1.add(groupLossResult.getLossValueThree());
                lossValuePreThree1 = lossValuePreThree1.add(groupLossResult.getLossValuePreThree());
                if (StringUtils.isNotBlank(groupLossResult.getIlm())) {
                    groupResultYSTG.setIlm(groupLossResult.getIlm());
                }
                isInsertYSTG = "1";
                groupLossResult.setIsLossDataCheck("1");
                groupLossResult.setIsLossDataCheckName("验收通过");
            } else {
                lossValueOne2 = lossValueOne2.add(groupLossResult.getLossValueOne());
                lossValuePreTwo2 = lossValuePreTwo2.add(groupLossResult.getLossValuePreTwo());
                lossValueAverage2 = lossValueAverage2.add(groupLossResult.getLossValueAverage());
                lossValueTwo2 = lossValueTwo2.add(groupLossResult.getLossValueTwo());
                lossValueThree2 = lossValueThree2.add(groupLossResult.getLossValueThree());
                lossValuePreThree2 = lossValuePreThree2.add(groupLossResult.getLossValuePreThree());
                if (StringUtils.isNotBlank(groupLossResult.getIlm())) {
                    groupResultWYS.setIlm(groupLossResult.getIlm());
                }
                isInsertWYS = "1";
                groupLossResult.setIsLossDataCheck("0");
                groupLossResult.setIsLossDataCheckName("未验收");
            }
            groupLossResultMapper.insert(groupLossResult);
            groupLossIndex++;
        }

        // 判断法人是否验收通过
        if ("1".equals(isLossDataCheckFR)) {
            lossValueOne1 = lossValueOne1.add(lossResultFR.getLossValueOne());
            lossValuePreTwo1 = lossValuePreTwo1.add(lossResultFR.getLossValuePreTwo());
            lossValueAverage1 = lossValueAverage1.add(lossResultFR.getLossValueAverage());
            lossValueTwo1 = lossValueTwo1.add(lossResultFR.getLossValueTwo());
            lossValueThree1 = lossValueThree1.add(lossResultFR.getLossValueThree());
            lossValuePreThree1 = lossValuePreThree1.add(lossResultFR.getLossValuePreThree());
            if (StringUtils.isNotBlank(lossResultFR.getIlm())) {
                groupResultYSTG.setIlm(lossResultFR.getIlm());
            }
            isInsertYSTG = "1";
        } else if ("0".equals(isLossDataCheckFR)) {
            lossValueOne2 = lossValueOne2.add(lossResultFR.getLossValueOne());
            lossValuePreTwo2 = lossValuePreTwo2.add(lossResultFR.getLossValuePreTwo());
            lossValueAverage2 = lossValueAverage2.add(lossResultFR.getLossValueAverage());
            lossValueTwo2 = lossValueTwo2.add(lossResultFR.getLossValueTwo());
            lossValueThree2 = lossValueThree2.add(lossResultFR.getLossValueThree());
            lossValuePreThree2 = lossValuePreThree2.add(lossResultFR.getLossValuePreThree());
            if (StringUtils.isNotBlank(lossResultFR.getIlm())) {
                groupResultWYS.setIlm(lossResultFR.getIlm());
            }
            isInsertWYS = "1";
        }

        if ("1".equals(isInsertYSTG)) {
            groupResultYSTG.setLossValueOne(lossValueOne1);
            groupResultYSTG.setLossValuePreTwo(lossValuePreTwo1);
            groupResultYSTG.setLossValueAverage(lossValueAverage1);
            groupResultYSTG.setLossValueTwo(lossValueTwo1);
            groupResultYSTG.setLossValueThree(lossValueThree1);
            groupResultYSTG.setLossValuePreThree(lossValuePreThree1);
            groupResultYSTG.setIsLossDataCheckName("验收通过");
            groupResultYSTG.setIsLossDataCheck("1");
            groupResultYSTG.setSortNum(1);
            groupLossResultMapper.insert(groupResultYSTG);
        }
        if ("1".equals(isInsertWYS)) {
            groupResultWYS.setLossValueOne(lossValueOne2);
            groupResultWYS.setLossValuePreTwo(lossValuePreTwo2);
            groupResultWYS.setLossValueAverage(lossValueAverage2);
            groupResultWYS.setLossValueTwo(lossValueTwo2);
            groupResultWYS.setLossValueThree(lossValueThree2);
            groupResultWYS.setLossValuePreThree(lossValuePreThree2);
            groupResultWYS.setIsLossDataCheckName("未验收");
            groupResultWYS.setIsLossDataCheck("0");
            groupResultWYS.setSortNum(2);
            groupLossResultMapper.insert(groupResultWYS);
        }
        // 入库法人
        groupLossResultMapper.insert(lossResultFR);

        // 计算加权资产统计表，根据验收状态不同分为3部分统计
        // mappingResultAllList
        // 获取集团加权资产配置表
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("config_type", configType);
        //queryWrapper.ne("project_name", "标准法计算指标");
        queryWrapper.orderByAsc("compute_number");
        List<CmWeightedAssetsCalculationConfigGroup> configList =
                configGroupService.list(queryWrapper);

        // 获取年份
        String meteringYear = cmMeteringLegal.getMeteringYear();
        // 获取最近第二年
        String lastSecondMeteringYear = (Integer.parseInt(meteringYear) - 1) + "";
        // 获取最近第三年
        String lastThirdMeteringYear = (Integer.parseInt(meteringYear) - 2) + "";
        // 获取审计版本
        String auditVersionNow = cmMeteringLegal.getAuditVersion();
        // 获取最近第二年的审计版本
        String lastTwoAuditVersion = cmMeteringLegalMapper.getLastAuditVersion(auditVersionNow, lastSecondMeteringYear, cmMeteringLegal.getMeteringFlag());
        if ("审计后".equals(auditVersionNow) && StringUtils.isBlank(lastTwoAuditVersion)) {
            lastTwoAuditVersion = cmMeteringLegalMapper.getLastAuditVersion("审计前", lastSecondMeteringYear, cmMeteringLegal.getMeteringFlag());
        }
        if (StringUtils.isBlank(lastTwoAuditVersion)) {
            lastTwoAuditVersion = auditVersionNow;
        }
        // 获取最近第二年的审计版本
        String lastThreeAuditVersion = cmMeteringLegalMapper.getLastAuditVersion(auditVersionNow, lastThirdMeteringYear, cmMeteringLegal.getMeteringFlag());
        if ("审计后".equals(auditVersionNow) && StringUtils.isBlank(lastThreeAuditVersion)) {
            lastThreeAuditVersion = cmMeteringLegalMapper.getLastAuditVersion("审计前", lastThirdMeteringYear, cmMeteringLegal.getMeteringFlag());
        }
        if (StringUtils.isBlank(lastThreeAuditVersion)) {
            lastThreeAuditVersion = auditVersionNow;
        }
        cmMeteringLegal.setLastSecondMeteringYear(lastSecondMeteringYear);
        cmMeteringLegal.setLastThirdMeteringYear(lastThirdMeteringYear);
        cmMeteringLegal.setLastTwoAuditVersion(lastTwoAuditVersion);
        cmMeteringLegal.setLastThreeAuditVersion(lastThreeAuditVersion);

        /*if ("2".equals(isLossDataCheck)) {
            // 计算均未验收
            //computeGroupResult2(configList);
        } else if ("3".equals(isLossDataCheck)) {
            // 计算部分验收

        } else {
            // 计算全部验收
            //computeGroupResult2(configList);
        }*/
        CmMeteringLegal updateMetering = new CmMeteringLegal();
        // 计算数据（同一套逻辑即可）
        computeGroupResult2(configList, mappingResultAllList, ystgOrgList, wysOrgList,
                cmMeteringLegal, categoryList, report9);
        List<CmCalculationConfigGroupResult> insertList = new ArrayList<>();
        for (CmWeightedAssetsCalculationConfigGroup configGroup : configList) {
            CmCalculationConfigGroupResult groupResult = new CmCalculationConfigGroupResult();
            groupResult.setMeteringId(cmMeteringLegal.getId());
            groupResult.setConfigId(configGroup.getId());
            groupResult.setSerialNumber(configGroup.getSerialNumber());
            groupResult.setProjectName(configGroup.getProjectName());
            groupResult.setFirstValue(configGroup.getFirstValue());
            groupResult.setFirstComputeContent(configGroup.getFirstComputeContent());
            groupResult.setSecondValue(configGroup.getSecondValue());
            groupResult.setThirdValue(configGroup.getThirdValue());
            groupResult.setConfigType(configGroup.getConfigType());
            groupResult.setIsShowPage(configGroup.getIsShowPage());
            groupResult.setIsBlod(configGroup.getIsBlod());
            groupResult.setSortNum(configGroup.getSortNum());
            groupResult.setTitleLevel(configGroup.getTitleLevel());
            groupResult.setDisabledFirstValue(configGroup.getDisabledFirstValue());
            groupResult.setDisabledSecondValue(configGroup.getDisabledSecondValue());
            groupResult.setDisabledThirdValue(configGroup.getDisabledThirdValue());
            groupResult.setPid(configGroup.getPid());
            groupResult.setHasChild(configGroup.getHasChild());
            insertList.add(groupResult);
            if (configGroup.getCellNumFirst().equals("D52")) {
                updateMetering.setMeteringResult(configGroup.getFirstValue());
            }
        }
        groupResultService.saveBatch(insertList);

        relService.saveBatch(insertRelList);
        // 更新状态为待提交,同步验收状态

        updateMetering.setId(cmMeteringLegal.getId());
        updateMetering.setState("3");
        updateMetering.setResultGenerateTime(new Date());

        updateMetering.setIsLossDataCheck(isLossDataCheck);
        cmMeteringLegalMapper.updateById(updateMetering);
        return Result.OK("测算成功！");

    }

    // 计算部分验收的资产加权测算结果
    private void computeGroupResult2(List<CmWeightedAssetsCalculationConfigGroup> configList,
                                     List<CmSubjectMappingGroupResult> mappingResultAllList,
                                     List<String> ystgOrgList,
                                     List<String> wysOrgList,
                                     CmMeteringLegal cmMeteringLegal,
                                     List<SysCategory> categoryList,
                                     String report9) throws Exception {
        for (CmWeightedAssetsCalculationConfigGroup configGroup : configList) {
            // 计算最近一年的值
            String firstComputeWay = configGroup.getFirstComputeWay();
            String firstComputeContent = configGroup.getFirstComputeContent();
            BigDecimal firstValue = configGroup.getFirstValue();
            if (firstValue == null) {
                BigDecimal value1 = computeGroupResultPart(firstComputeWay, firstComputeContent,
                        ystgOrgList, wysOrgList, mappingResultAllList, categoryList,
                        cmMeteringLegal, configList, report9);
                configGroup.setFirstValue(value1);
            }

            // 计算最近二年的值
            String secondComputeWay = configGroup.getSecondComputeWay();
            String secondComputeContent = configGroup.getSecondComputeContent();
            BigDecimal secondValue = configGroup.getSecondValue();
            if (secondValue == null) {
                BigDecimal value2 = computeGroupResultPart(secondComputeWay, secondComputeContent,
                        ystgOrgList, wysOrgList, mappingResultAllList, categoryList,
                        cmMeteringLegal, configList, report9);
                configGroup.setSecondValue(value2);
            }

            // 计算最近三年的值
            String thirdComputeWay = configGroup.getThirdComputeWay();
            String thirdComputeContent = configGroup.getThirdComputeContent();
            BigDecimal thirdValue = configGroup.getThirdValue();
            if (thirdValue == null) {
                BigDecimal value3 = computeGroupResultPart(thirdComputeWay, thirdComputeContent,
                        ystgOrgList, wysOrgList, mappingResultAllList, categoryList,
                        cmMeteringLegal, configList, report9);
                configGroup.setThirdValue(value3);
            }
        }
    }

    private BigDecimal computeGroupResultPart(String computeWay,
                                              String computeContent,
                                              List<String> ystgOrgList,
                                              List<String> wysOrgList,
                                              List<CmSubjectMappingGroupResult> mappingResultAllList,
                                              List<SysCategory> categoryList,
                                              CmMeteringLegal meteringLegal,
                                              List<CmWeightedAssetsCalculationConfigGroup> configList,
                                              String report9) throws Exception {
        BigDecimal value = new BigDecimal("0");
        // 获取主键
        String meteringId = meteringLegal.getId();
        computeWay = StringUtils.isBlank(computeWay) ? "" : computeWay;
        if (computeWay.equals("SQL")) {
            // 执行SQL语句
            computeContent = computeContent
                    .replaceAll("meteringId", "'" + meteringId + "'")
                    .replaceAll("lastSecondMeteringYear", "'" + meteringLegal.getLastSecondMeteringYear() + "'")
                    .replaceAll("lastThirdMeteringYear", "'" + meteringLegal.getLastThirdMeteringYear() + "'")
                    .replaceAll("lastTwoAuditVersion", "'" + meteringLegal.getLastTwoAuditVersion() + "'")
                    .replaceAll("lastThreeAuditVersion", "'" + meteringLegal.getLastThreeAuditVersion() + "'")
                    .replaceAll("meteringYear", "'" + meteringLegal.getMeteringYear() + "'");
            String valueStr = cmMeteringLegalMapper.selectSql(computeContent);
            if (StringUtils.isBlank(valueStr)) {
                value = new BigDecimal("0");
            } else {
                value = new BigDecimal(valueStr);
            }
        } else if ("JAVA".equals(computeWay)) {
            if ("D9".equals(computeContent)) {
                // =IFERROR(IF(D23<=800000,D23*0.12,IF(D23<=24000000,96000+(D23-800000)*0.15,3576000+(D23-24000000)*0.18)),0)
                BigDecimal D23 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D23")) {
                        D23 = config.getFirstValue();
                        break;
                    }
                }
                if (D23.compareTo(new BigDecimal("800000")) <= 0) {
                    value = BigDecimal.valueOf(D23.doubleValue() * 0.12).setScale(6, RoundingMode.HALF_UP);
                } else if (D23.compareTo(new BigDecimal("24000000")) <= 0) {
                    double valueDou = 96000 + (D23.doubleValue() - 800000) * 0.15;
                    value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);
                } else {
                    double valueDou = 3576000 + (D23.doubleValue() - 24000000) * 0.18;
                    value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);
                }
            } else if ("D11".equals(computeContent)) {
                // =-SUMIF('2.1_会计科目映射表'!M10:M16,"利息收入",'2.1_会计科目映射表'!Q10:Q16)
                // -SUMIF('2.1_会计科目映射表'!M22:M28,"利息收入",'2.1_会计科目映射表'!Q22:Q28)
                // 计算验收通过的
                BigDecimal D11Part1 = new BigDecimal("0");
                if (ystgOrgList.size()>0) {
                    String categoryId = categoryList.stream().filter(o -> o.getName().equals("利息收入")).collect(Collectors.toList()).get(0).getId();
                    List<CmSubjectMappingGroupResult> collect = mappingResultAllList.stream().filter(
                            o -> ystgOrgList.contains(o.getOrganizationId())
                            && StringUtils.isNotBlank(o.getCorrespondBiSubitem())
                            && o.getCorrespondBiSubitem().equals(categoryId))
                            .collect(Collectors.toList());

                    for (CmSubjectMappingGroupResult groupResult : collect) {
                        D11Part1 = D11Part1.add(groupResult.getOffsetEndAmount()==null? new BigDecimal("0"):groupResult.getOffsetEndAmount());
                    }
                }
                value = new BigDecimal(0).subtract(D11Part1);
            } else if ("D12".equals(computeContent)) {
                // =SUMIF('2.1_会计科目映射表'!M10:M16,"利息支出",'2.1_会计科目映射表'!Q10:Q16)+SUMIF('2.1_会计科目映射表'!M22:M28,"利息支出",'2.1_会计科目映射表'!Q22:Q28)
                BigDecimal D12Part1 = new BigDecimal("0");
                String categoryId = categoryList.stream().filter(o -> o.getName().equals("利息支出")).collect(Collectors.toList()).get(0).getId();
                List<CmSubjectMappingGroupResult> collect = mappingResultAllList.stream().filter(
                                        o -> ystgOrgList.contains(o.getOrganizationId())
                                        && StringUtils.isNotBlank(o.getCorrespondBiSubitem())
                                        && o.getCorrespondBiSubitem().equals(categoryId))
                        .collect(Collectors.toList());
                for (CmSubjectMappingGroupResult groupResult : collect) {
                    D12Part1 = D12Part1.add(groupResult.getOffsetEndAmount()==null? new BigDecimal("0"):groupResult.getOffsetEndAmount());
                }
                value = D12Part1;
            } else if ("D16".equals(computeContent)) {
                // =-SUMIF('2.1_会计科目映射表'!M10:M16,"手续费和佣金收入",'2.1_会计科目映射表'!Q10:Q16)
                // -SUMIF('2.1_会计科目映射表'!M22:M28,"手续费和佣金收入",'2.1_会计科目映射表'!Q22:Q28)
                BigDecimal D16Part1 = new BigDecimal("0");
                String categoryId = categoryList.stream().filter(o -> o.getName().equals("手续费和佣金收入")).collect(Collectors.toList()).get(0).getId();
                List<CmSubjectMappingGroupResult> collect = mappingResultAllList.stream().filter(
                                        o -> ystgOrgList.contains(o.getOrganizationId())
                                        && StringUtils.isNotBlank(o.getCorrespondBiSubitem())
                                        && o.getCorrespondBiSubitem().equals(categoryId))
                        .collect(Collectors.toList());
                for (CmSubjectMappingGroupResult groupResult : collect) {
                    D16Part1 = D16Part1.add(groupResult.getOffsetEndAmount()==null? new BigDecimal("0"):groupResult.getOffsetEndAmount());
                }
                value = new BigDecimal(0).subtract(D16Part1);
            } else if ("D17".equals(computeContent)) {
                // =SUMIF('2.1_会计科目映射表'!M10:M16,"手续费和佣金支出",'2.1_会计科目映射表'!Q10:Q16)
                // +SUMIF('2.1_会计科目映射表'!M22:M28,"手续费和佣金支出",'2.1_会计科目映射表'!Q22:Q28)
                BigDecimal D17Part1 = new BigDecimal("0");
                String categoryId = categoryList.stream().filter(o -> o.getName().equals("手续费和佣金支出")).collect(Collectors.toList()).get(0).getId();
                List<CmSubjectMappingGroupResult> collect = mappingResultAllList.stream().filter(
                                o -> ystgOrgList.contains(o.getOrganizationId())
                                        && StringUtils.isNotBlank(o.getCorrespondBiSubitem())
                                        && o.getCorrespondBiSubitem().equals(categoryId))
                        .collect(Collectors.toList());
                for (CmSubjectMappingGroupResult groupResult : collect) {
                    D17Part1 = D17Part1.add(groupResult.getOffsetEndAmount()==null? new BigDecimal("0"):groupResult.getOffsetEndAmount());
                }
                value = D17Part1;
            } else if ("D18".equals(computeContent)) {
                // =-SUMIF('2.1_会计科目映射表'!M10:M16,"其他经营性收入",'2.1_会计科目映射表'!Q10:Q16)
                // -SUMIF('2.1_会计科目映射表'!M22:M28,"其他经营性收入",'2.1_会计科目映射表'!Q22:Q28)
                BigDecimal D18Part1 = new BigDecimal("0");
                String categoryId = categoryList.stream().filter(o -> o.getName().equals("其他经营性收入")).collect(Collectors.toList()).get(0).getId();
                List<CmSubjectMappingGroupResult> collect = mappingResultAllList.stream().filter(
                                o -> ystgOrgList.contains(o.getOrganizationId())
                                        && StringUtils.isNotBlank(o.getCorrespondBiSubitem())
                                        && o.getCorrespondBiSubitem().equals(categoryId))
                        .collect(Collectors.toList());
                for (CmSubjectMappingGroupResult groupResult : collect) {
                    D18Part1 = D18Part1.add(groupResult.getOffsetEndAmount()==null? new BigDecimal("0"):groupResult.getOffsetEndAmount());
                }
                value = new BigDecimal(0).subtract(D18Part1);
            } else if ("D19".equals(computeContent)) {
                // =SUMIF('2.1_会计科目映射表'!M10:M16,"其他经营性支出",'2.1_会计科目映射表'!Q10:Q16)
                // +SUMIF('2.1_会计科目映射表'!M22:M28,"其他经营性支出",'2.1_会计科目映射表'!Q22:Q28)
                // +'2.5_损失数据统计表'!C20+'2.5_损失数据统计表'!C26
                BigDecimal D19Part1 = new BigDecimal("0");
                String categoryId = categoryList.stream().filter(o -> o.getName().equals("其他经营性支出")).collect(Collectors.toList()).get(0).getId();
                List<CmSubjectMappingGroupResult> collect = mappingResultAllList.stream().filter(
                                o -> ystgOrgList.contains(o.getOrganizationId())
                                        && StringUtils.isNotBlank(o.getCorrespondBiSubitem())
                                        && o.getCorrespondBiSubitem().equals(categoryId))
                        .collect(Collectors.toList());
                for (CmSubjectMappingGroupResult groupResult : collect) {
                    D19Part1 = D19Part1.add(groupResult.getOffsetEndAmount()==null? new BigDecimal("0"):groupResult.getOffsetEndAmount());
                }
                //todo：huanjing 计算损失数据，默认万元？
                String D19Part2Str = cmMeteringLegalMapper.getD19Part2Str(meteringId, "1");
                if (StringUtils.isNotBlank(D19Part2Str)) {
                    value = D19Part1.add(new BigDecimal(D19Part2Str));
                } else {
                    value = D19Part1;
                }
            } else if ("E19".equals(computeContent)) {
                // 取上年度已审核计量结果+【'2.5_损失数据统计表'!D8】-【'2.5_损失数据统计表'!D9】
                String part1Str = cmMeteringLegalMapper.getE19GroupPart1(meteringLegal.getLastSecondMeteringYear(),
                        meteringLegal.getLastTwoAuditVersion(), "D19");
                // 取集团验收通过的最近第二年损失数据
                String part2Str = cmMeteringLegalMapper.getE19GroupPart2(meteringId, "1");
                // 取集团验收通过的最近第二年损失数据
                String part3Str = cmMeteringLegalMapper.getE19GroupPart3(meteringId, "1");
                if (StringUtils.isNotBlank(part1Str)) {
                    value = value.add(new BigDecimal(part1Str));
                }
                if (StringUtils.isNotBlank(part2Str)) {
                    value = value.add(new BigDecimal(part2Str));
                }
                if (StringUtils.isNotBlank(part3Str)) {
                    value = value.subtract(new BigDecimal(part3Str));
                }
            } else if ("F19".equals(computeContent)) {
                // 取上年度已审核计量结果+【'2.5_损失数据统计表'!E8】-【'2.5_损失数据统计表'!E9】
                String part1Str = cmMeteringLegalMapper.getE19GroupPart1(meteringLegal.getLastThirdMeteringYear(),
                        meteringLegal.getLastThreeAuditVersion(), "D19");
                // 取集团验收通过的最近第三年损失数据
                String part2Str = cmMeteringLegalMapper.getF19GroupPart2(meteringId, "1");
                // 取集团验收通过的最近第三年损失数据
                String part3Str = cmMeteringLegalMapper.getF19GroupPart3(meteringId, "1");
                if (StringUtils.isNotBlank(part1Str)) {
                    value = value.add(new BigDecimal(part1Str));
                }
                if (StringUtils.isNotBlank(part2Str)) {
                    value = value.add(new BigDecimal(part2Str));
                }
                if (StringUtils.isNotBlank(part3Str)) {
                    value = value.subtract(new BigDecimal(part3Str));
                }
            } else if ("D22".equals(computeContent)) {
                // =-SUMIF('2.1_会计科目映射表'!L10:L16,"金融",'2.1_会计科目映射表'!Q10:Q16)
                // -SUMIF('2.1_会计科目映射表'!L22:L28,"金融",'2.1_会计科目映射表'!Q22:Q28)
                // -D21
                BigDecimal D22Part1 = new BigDecimal("0");
                String categoryId = categoryList.stream().filter(o -> o.getName().equals("金融")).collect(Collectors.toList()).get(0).getId();
                List<CmSubjectMappingGroupResult> collect = mappingResultAllList.stream().filter(
                                o -> ystgOrgList.contains(o.getOrganizationId())
                                        && StringUtils.isNotBlank(o.getCorrespondBiSubitem())
                                        && o.getCorrespondBiSubject().equals(categoryId))
                        .collect(Collectors.toList());
                for (CmSubjectMappingGroupResult groupResult : collect) {
                    D22Part1 = D22Part1.add(groupResult.getOffsetEndAmount()==null? new BigDecimal("0"):groupResult.getOffsetEndAmount());
                }
                // 获取D21
                BigDecimal D21 = new BigDecimal("0");
                List<CmWeightedAssetsCalculationConfigGroup> d21 = configList.stream().filter(o -> StringUtils.isNotBlank(o.getCellNumFirst()) && o.getCellNumFirst().equals("D21")).collect(Collectors.toList());
                if (d21 != null && !d21.isEmpty()) {
                    D21 = d21.get(0).getFirstValue() == null ? new BigDecimal("0") : d21.get(0).getFirstValue();
                }
                value = new BigDecimal("0").subtract(D22Part1).subtract(D21);
            } else if ("D10".equals(computeContent)) {
                // =IFERROR(MIN(AVERAGE(ABS(D11-D12),ABS(E11-E12),ABS(F11-F12)),2.25%*AVERAGE(D13:F13))+AVERAGE(D14:F14),0)

                BigDecimal D11 = new BigDecimal("0");
                BigDecimal D12 = new BigDecimal("0");
                BigDecimal D13 = new BigDecimal("0");
                BigDecimal D14 = new BigDecimal("0");
                BigDecimal E11 = new BigDecimal("0");
                BigDecimal E12 = new BigDecimal("0");
                BigDecimal E13 = new BigDecimal("0");
                BigDecimal E14 = new BigDecimal("0");
                BigDecimal F11 = new BigDecimal("0");
                BigDecimal F12 = new BigDecimal("0");
                BigDecimal F13 = new BigDecimal("0");
                BigDecimal F14 = new BigDecimal("0");
                // 获取D11, D12, E11, E12, F11, F12的值
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D11")) {
                        D11 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D12")) {
                        D12 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D13")) {
                        D13 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D14")) {
                        D14 = config.getFirstValue();
                    }
                    String cellNumSecond = config.getCellNumSecond();
                    if (cellNumSecond.equals("E11")) {
                        E11 = config.getSecondValue();
                    } else if (cellNumFirst.equals("E12")) {
                        E12 = config.getSecondValue();
                    } else if (cellNumFirst.equals("E13")) {
                        E13 = config.getSecondValue();
                    } else if (cellNumFirst.equals("E14")) {
                        E14 = config.getSecondValue();
                    }
                    String cellNumThird = config.getCellNumThird();
                    if (cellNumThird.equals("F11")) {
                        F11 = config.getThirdValue();
                    } else if (cellNumFirst.equals("F12")) {
                        F12 = config.getThirdValue();
                    } else if (cellNumFirst.equals("F13")) {
                        F13 = config.getThirdValue();
                    } else if (cellNumFirst.equals("F14")) {
                        F14 = config.getThirdValue();
                    }
                }
                // 计算三个绝对差的平均值
                BigDecimal absTotal = D11.subtract(D12).abs()
                        .add((E11.subtract(E12).abs())).add(F11.subtract(F12).abs());
                double absAvgDou = absTotal.doubleValue() / 3;
                // 计算D13:F13的平均值的2.25%
                double divideDou = (D13.doubleValue()+E13.doubleValue()+F13.doubleValue())/3*2.25/100;

                // 取较小值
                //BigDecimal min = new BigDecimal("0");
                double min;
                if (absAvgDou<=divideDou) {
                    min = absAvgDou;
                } else {
                    min = divideDou;
                }
                // 加上D14:F14的平均值
                double valueDou = min + (D14.doubleValue() + E14.doubleValue() + F14.doubleValue())/3;
                value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);
            } else if ("D15".equals(computeContent)) {
                // =IFERROR(MAX(AVERAGE(D16:F16),AVERAGE(D17:F17))+MAX(AVERAGE(D18:F18),AVERAGE(D19:F19)),0)
                BigDecimal D16 = new BigDecimal("0");
                BigDecimal D17 = new BigDecimal("0");
                BigDecimal D18 = new BigDecimal("0");
                BigDecimal D19 = new BigDecimal("0");
                BigDecimal E16 = new BigDecimal("0");
                BigDecimal E17 = new BigDecimal("0");
                BigDecimal E18 = new BigDecimal("0");
                BigDecimal E19 = new BigDecimal("0");
                BigDecimal F16 = new BigDecimal("0");
                BigDecimal F17 = new BigDecimal("0");
                BigDecimal F18 = new BigDecimal("0");
                BigDecimal F19 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D16")) {
                        D16 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D17")) {
                        D17 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D18")) {
                        D18 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D19")) {
                        D19 = config.getFirstValue();
                    }
                    String cellNumSecond = config.getCellNumSecond();
                    if (cellNumSecond.equals("E16")) {
                        E16 = config.getSecondValue();
                    } else if (cellNumFirst.equals("E17")) {
                        E17 = config.getSecondValue();
                    } else if (cellNumFirst.equals("E18")) {
                        E18 = config.getSecondValue();
                    } else if (cellNumFirst.equals("E19")) {
                        E19 = config.getSecondValue();
                    }
                    String cellNumThird = config.getCellNumThird();
                    if (cellNumThird.equals("F16")) {
                        F16 = config.getThirdValue();
                    } else if (cellNumFirst.equals("F17")) {
                        F17 = config.getThirdValue();
                    } else if (cellNumFirst.equals("F18")) {
                        F18 = config.getThirdValue();
                    } else if (cellNumFirst.equals("F19")) {
                        F19 = config.getThirdValue();
                    }
                }
                double double16 = (D16.doubleValue() + E16.doubleValue() + F16.doubleValue()) /3;
                double double17 = (D17.doubleValue() + E17.doubleValue() + F17.doubleValue()) /3;
                double maxDou1;
                if (double16 >= double17) {
                    maxDou1 = double16;
                } else {
                    maxDou1 = double17;
                }
                double double18 = (D18.doubleValue() + E18.doubleValue() + F18.doubleValue()) /3;
                double double19 = (D19.doubleValue() + E19.doubleValue() + F19.doubleValue()) /3;
                double maxDou2;
                if (double18 >= double19) {
                    maxDou2 = double18;
                } else {
                    maxDou2 = double19;
                }
                double valueDou = maxDou1 + maxDou2;
                value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);
            } else if ("D20".equals(computeContent)) {
                // =IFERROR(AVERAGE(ABS(D21),ABS(E21),ABS(F21))+AVERAGE(ABS(D22),ABS(E22),ABS(F22)),0)
                BigDecimal D21 = new BigDecimal("0");
                BigDecimal D22 = new BigDecimal("0");
                BigDecimal E21 = new BigDecimal("0");
                BigDecimal E22 = new BigDecimal("0");
                BigDecimal F21 = new BigDecimal("0");
                BigDecimal F22 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D21")) {
                        D21 = config.getFirstValue().abs();
                    } else if (cellNumFirst.equals("D22")) {
                        D22 = config.getFirstValue().abs();
                    }
                    String cellNumSecond = config.getCellNumSecond();
                    if (cellNumSecond.equals("E21")) {
                        E21 = config.getSecondValue().abs();
                    } else if (cellNumFirst.equals("E22")) {
                        E22 = config.getSecondValue().abs();
                    }
                    String cellNumThird = config.getCellNumThird();
                    if (cellNumThird.equals("F21")) {
                        F21 = config.getThirdValue().abs();
                    } else if (cellNumFirst.equals("F22")) {
                        F22 = config.getThirdValue().abs();
                    }
                }
                BigDecimal avg21 = D21.abs().add(E21.abs()).add(F21.abs());
                BigDecimal avg22 = D22.abs().add(E22.abs()).add(F22.abs());
                double valueDou = avg21.doubleValue()/3 + avg22.doubleValue()/3;
                value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);
            } else if ("D23".equals(computeContent)) {
                // =IFERROR(D10+D15+D20,0)
                BigDecimal D10 = new BigDecimal("0");
                BigDecimal D15 = new BigDecimal("0");
                BigDecimal D20 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D10")) {
                        D10 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D15")) {
                        D15 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D20")) {
                        D20 = config.getFirstValue();
                    }
                }
                value = D10.add(D15).add(D20);
            } else if ("D24".equals(computeContent)) {
                // =IFERROR(D25*15,0)
                BigDecimal D25 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D25")) {
                        D25 = config.getFirstValue();
                        break;
                    }
                }
                value = D25.multiply(new BigDecimal("15"));
            } else if ("D26".equals(computeContent)) {
                // =MAX(D27,D28)
                BigDecimal D27 = new BigDecimal("0");
                BigDecimal D28 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D27")) {
                        D27 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D28")) {
                        D28 = config.getFirstValue();
                    }
                }
                if (D27.compareTo(D28) > 0) {
                    value = D27;
                } else {
                    value = D28;
                }
            } else if ("D27".equals(computeContent)) {
                // =IF(D24=0,1,LN(EXP(1)-1+(D24/D9)^0.8))
                BigDecimal D24 = new BigDecimal("0");
                BigDecimal D9 = new BigDecimal("0");

                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D24")) {
                        D24 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D9")) {
                        D9 = config.getFirstValue();
                    }
                }
                if (D24.compareTo(new BigDecimal("0")) == 0) {
                    value = new BigDecimal("1");
                } else {
                    double valueDou = Math.log(Math.exp(1) - 1 + Math.pow(D24.doubleValue() / D9.doubleValue(), 0.8));
                    value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);
                }
            } else if ("D29".equals(computeContent)) {
                // =D9*D26
                BigDecimal D9 = new BigDecimal("0");
                BigDecimal D26 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D9")) {
                        D9 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D26")) {
                        D26 = config.getFirstValue();
                    }
                }
                value = D9.multiply(D26);
            } else if ("D30".equals(computeContent)) {
                // =D29*12.5
                BigDecimal D29 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D29")) {
                        D29 = config.getFirstValue();
                        break;
                    }
                }
                value = D29.multiply(new BigDecimal("12.5"));
            } else if ("D34".equals(computeContent)) {
                // 从这边往下计算验收未通过的数据
                // =-SUMIF('2.1_会计科目映射表'!M34:M41,"利息收入",'2.1_会计科目映射表'!Q34:Q41)
                // 计算验收通过的
                BigDecimal D31Part1 = new BigDecimal("0");
                if (ystgOrgList.size()>0) {
                    String categoryId = categoryList.stream().filter(o -> o.getName().equals("利息收入")).collect(Collectors.toList()).get(0).getId();
                    List<CmSubjectMappingGroupResult> collect = mappingResultAllList.stream().filter(
                                    o -> wysOrgList.contains(o.getOrganizationId())
                                            && StringUtils.isNotBlank(o.getCorrespondBiSubitem())
                                            && o.getCorrespondBiSubitem().equals(categoryId))
                            .collect(Collectors.toList());

                    for (CmSubjectMappingGroupResult groupResult : collect) {
                        D31Part1 = D31Part1.add(groupResult.getOffsetEndAmount()==null? new BigDecimal("0"):groupResult.getOffsetEndAmount());
                    }
                }
                value = new BigDecimal(0).subtract(D31Part1);
            } else if ("D35".equals(computeContent)) {
                // =SUMIF('2.1_会计科目映射表'!M34:M41,"利息支出",'2.1_会计科目映射表'!Q34:Q41)
                BigDecimal D12Part1 = new BigDecimal("0");
                String categoryId = categoryList.stream().filter(o -> o.getName().equals("利息支出")).collect(Collectors.toList()).get(0).getId();
                List<CmSubjectMappingGroupResult> collect = mappingResultAllList.stream().filter(
                                o -> wysOrgList.contains(o.getOrganizationId())
                                        && StringUtils.isNotBlank(o.getCorrespondBiSubitem())
                                        && o.getCorrespondBiSubitem().equals(categoryId))
                        .collect(Collectors.toList());
                for (CmSubjectMappingGroupResult groupResult : collect) {
                    D12Part1 = D12Part1.add(groupResult.getOffsetEndAmount()==null? new BigDecimal("0"):groupResult.getOffsetEndAmount());
                }
                value = D12Part1;
            } else if ("D36".equals(computeContent)) {
                // ='2.2_生息资产统计表'!C8-'3.1_操作风险加权资产测算表（部分验收）'!D13
                // 集团 - 13
                List<String> orgTypeList = new ArrayList<>();
                orgTypeList.add("1");
                List<CmBasicGroupAssetData> assetList = cmMeteringLegalMapper.getAssetGroupList(orgTypeList, report9, "0");
                if (assetList == null || assetList.isEmpty()) {
                    throw new Exception("生息资产数据->集团口径不存在，请检查");
                } else {
                    CmBasicGroupAssetData assetData = assetList.get(0);
                    value = assetData.getValue();
                    if (value != null) {
                        String unit = assetData.getUnit();
                        if (StringUtils.isNotBlank(unit)) {
                            if ("1".equals(unit)) {
                                value = value.divide(new BigDecimal("10000"));
                            }
                        }
                    }
                }
                BigDecimal D13 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D13")) {
                        D13 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                        break;
                    }
                }
                value = value.subtract(D13);
            } else if ("D37".equals(computeContent)) {
                // ='2.3_股利收入统计表'!D13-'3.1_操作风险加权资产测算表（部分验收）'!D14
                String part1Str = cmMeteringLegalMapper.queryD37GroupPart1(meteringId);
                if (StringUtils.isBlank(part1Str)) {
                    part1Str = "0";
                }
                BigDecimal D14 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D14")) {
                        D14 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                        break;
                    }
                }
                value = new BigDecimal(part1Str).subtract(D14);
            }  else if ("D39".equals(computeContent)) {
                // =-SUMIF('2.1_会计科目映射表'!M34:M41,"手续费和佣金收入",'2.1_会计科目映射表'!Q34:Q41)
                BigDecimal D39Part1 = new BigDecimal("0");
                String categoryId = categoryList.stream().filter(o -> o.getName().equals("手续费和佣金收入")).collect(Collectors.toList()).get(0).getId();
                List<CmSubjectMappingGroupResult> collect = mappingResultAllList.stream().filter(
                                o -> wysOrgList.contains(o.getOrganizationId())
                                        && StringUtils.isNotBlank(o.getCorrespondBiSubitem())
                                        && o.getCorrespondBiSubitem().equals(categoryId))
                        .collect(Collectors.toList());
                for (CmSubjectMappingGroupResult groupResult : collect) {
                    D39Part1 = D39Part1.add(groupResult.getOffsetEndAmount()==null? new BigDecimal("0"):groupResult.getOffsetEndAmount());
                }
                value = new BigDecimal(0).subtract(D39Part1);
            } else if ("D40".equals(computeContent)) {
                // =SUMIF('2.1_会计科目映射表'!M34:M41,"手续费和佣金支出",'2.1_会计科目映射表'!Q34:Q41)
                BigDecimal D40Part1 = new BigDecimal("0");
                String categoryId = categoryList.stream().filter(o -> o.getName().equals("手续费和佣金支出")).collect(Collectors.toList()).get(0).getId();
                List<CmSubjectMappingGroupResult> collect = mappingResultAllList.stream().filter(
                                o -> wysOrgList.contains(o.getOrganizationId())
                                        && StringUtils.isNotBlank(o.getCorrespondBiSubitem())
                                        && o.getCorrespondBiSubitem().equals(categoryId))
                        .collect(Collectors.toList());
                for (CmSubjectMappingGroupResult groupResult : collect) {
                    D40Part1 = D40Part1.add(groupResult.getOffsetEndAmount()==null? new BigDecimal("0"):groupResult.getOffsetEndAmount());
                }
                value = D40Part1;
            } else if ("D41".equals(computeContent)) {
                // =-SUMIF('2.1_会计科目映射表'!M34:M41,"其他经营性收入",'2.1_会计科目映射表'!Q34:Q41)
                BigDecimal D41Part1 = new BigDecimal("0");
                String categoryId = categoryList.stream().filter(o -> o.getName().equals("其他经营性收入")).collect(Collectors.toList()).get(0).getId();
                List<CmSubjectMappingGroupResult> collect = mappingResultAllList.stream().filter(
                                o -> wysOrgList.contains(o.getOrganizationId())
                                        && StringUtils.isNotBlank(o.getCorrespondBiSubitem())
                                        && o.getCorrespondBiSubitem().equals(categoryId))
                        .collect(Collectors.toList());
                for (CmSubjectMappingGroupResult groupResult : collect) {
                    D41Part1 = D41Part1.add(groupResult.getOffsetEndAmount()==null? new BigDecimal("0"):groupResult.getOffsetEndAmount());
                }
                value = new BigDecimal(0).subtract(D41Part1);
            } else if ("D42".equals(computeContent)) {
                // =SUMIF('2.1_会计科目映射表'!M34:M41,"其他经营性支出",'2.1_会计科目映射表'!Q34:Q41)
                // +'2.5_损失数据统计表'!C32
                BigDecimal D42Part1 = new BigDecimal("0");
                String categoryId = categoryList.stream().filter(o -> o.getName().equals("其他经营性支出")).collect(Collectors.toList()).get(0).getId();
                List<CmSubjectMappingGroupResult> collect = mappingResultAllList.stream().filter(
                                o -> wysOrgList.contains(o.getOrganizationId())
                                        && StringUtils.isNotBlank(o.getCorrespondBiSubitem())
                                        && o.getCorrespondBiSubitem().equals(categoryId))
                        .collect(Collectors.toList());
                for (CmSubjectMappingGroupResult groupResult : collect) {
                    D42Part1 = D42Part1.add(groupResult.getOffsetEndAmount()==null? new BigDecimal("0"):groupResult.getOffsetEndAmount());
                }
                //todo：huanjing 计算损失数据，默认万元？
                String D19Part2Str = cmMeteringLegalMapper.getD19Part2Str(meteringId, "0");
                if (StringUtils.isNotBlank(D19Part2Str)) {
                    value = D42Part1.add(new BigDecimal(D19Part2Str));
                } else {
                    value = D42Part1;
                }
            } else if ("E42".equals(computeContent)) {
                // 取上年度已审核计量结果+【'2.5_损失数据统计表'!D14】-【'2.5_损失数据统计表'!15】
                String part1Str = cmMeteringLegalMapper.getE19GroupPart1(meteringLegal.getLastSecondMeteringYear(),
                        meteringLegal.getLastTwoAuditVersion(), "D42");
                // 取集团未验收的最近第二年损失数据
                String part2Str = cmMeteringLegalMapper.getE19GroupPart2(meteringId, "0");
                // 取集团未验收的由操作风险事件造成的损失（上年度已审核计量结果）
                String part3Str = cmMeteringLegalMapper.getE19GroupPart3(meteringId, "0");
                if (StringUtils.isNotBlank(part1Str)) {
                    value = value.add(new BigDecimal(part1Str));
                }
                if (StringUtils.isNotBlank(part2Str)) {
                    value = value.add(new BigDecimal(part2Str));
                }
                if (StringUtils.isNotBlank(part3Str)) {
                    value = value.subtract(new BigDecimal(part3Str));
                }
            } else if ("F42".equals(computeContent)) {
                // 取上年度已审核计量结果+【'2.5_损失数据统计表'!E14】-【'2.5_损失数据统计表'!E15】
                String part1Str = cmMeteringLegalMapper.getE19GroupPart1(meteringLegal.getLastThirdMeteringYear(),
                        meteringLegal.getLastThreeAuditVersion(), "D42");
                // 取集团未验收的最近第三年损失数据
                String part2Str = cmMeteringLegalMapper.getF19GroupPart2(meteringId, "0");
                // 取集团未验收的最近第三年损失数据
                String part3Str = cmMeteringLegalMapper.getF19GroupPart3(meteringId, "0");
                if (StringUtils.isNotBlank(part1Str)) {
                    value = value.add(new BigDecimal(part1Str));
                }
                if (StringUtils.isNotBlank(part2Str)) {
                    value = value.add(new BigDecimal(part2Str));
                }
                if (StringUtils.isNotBlank(part3Str)) {
                    value = value.subtract(new BigDecimal(part3Str));
                }
            } else if ("D44".equals(computeContent)) {
                // =-'2.4_交易账簿净损益统计表'!D13-'3.1_操作风险加权资产测算表（部分验收）'!D21
                String part1Str = cmMeteringLegalMapper.queryD44GroupPart1(meteringId);
                if (StringUtils.isNotBlank(part1Str)) {
                    value = value.subtract(new BigDecimal(part1Str));
                }
                BigDecimal D21 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D21")) {
                        D21 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                        break;
                    }
                }
                value = value.subtract(D21);
            } else if ("D45".equals(computeContent)) {
                // =-SUMIF('2.1_会计科目映射表'!L34:L41,"金融",'2.1_会计科目映射表'!Q34:Q41)-D44
                BigDecimal D45Part1 = new BigDecimal("0");
                String categoryId = categoryList.stream().filter(o -> o.getName().equals("金融")).collect(Collectors.toList()).get(0).getId();
                List<CmSubjectMappingGroupResult> collect = mappingResultAllList.stream().filter(
                                o -> wysOrgList.contains(o.getOrganizationId())
                                        && StringUtils.isNotBlank(o.getCorrespondBiSubitem())
                                        && o.getCorrespondBiSubject().equals(categoryId))
                        .collect(Collectors.toList());
                for (CmSubjectMappingGroupResult groupResult : collect) {
                    D45Part1 = D45Part1.add(groupResult.getOffsetEndAmount()==null? new BigDecimal("0"):groupResult.getOffsetEndAmount());
                }
                // 获取D44
                BigDecimal D44 = new BigDecimal("0");
                List<CmWeightedAssetsCalculationConfigGroup> d21 = configList.stream().filter(o ->
                        StringUtils.isNotBlank(o.getCellNumFirst()) && o.getCellNumFirst().equals("D44"))
                        .collect(Collectors.toList());
                if (d21 != null && !d21.isEmpty()) {
                    D44 = d21.get(0).getFirstValue() == null ? new BigDecimal("0") : d21.get(0).getFirstValue();
                }
                value = new BigDecimal("0").subtract(D45Part1).subtract(D44);
            } else if ("D43".equals(computeContent)) {
                // =IFERROR(AVERAGE(ABS(D44),ABS(E44),ABS(F44))+AVERAGE(ABS(D45),ABS(E45),ABS(F45)),0)
                BigDecimal D44 = new BigDecimal("0");
                BigDecimal D45 = new BigDecimal("0");
                BigDecimal E44 = new BigDecimal("0");
                BigDecimal E45 = new BigDecimal("0");
                BigDecimal F44 = new BigDecimal("0");
                BigDecimal F45 = new BigDecimal("0");

                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D44")) {
                        D44 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue() .abs();
                    } else if (cellNumFirst.equals("D45")) {
                        D45 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue().abs();
                    }
                    String cellNumSecond = config.getCellNumSecond();
                    if (cellNumSecond.equals("E44")) {
                        E44 = config.getSecondValue() == null ? new BigDecimal("0") : config.getSecondValue().abs();
                    } else if (cellNumFirst.equals("E45")) {
                        E45 = config.getSecondValue() == null ? new BigDecimal("0") : config.getSecondValue().abs();
                    }
                    String cellNumThird = config.getCellNumThird();
                    if (cellNumThird.equals("F44")) {
                        F44 = config.getThirdValue() == null ? new BigDecimal("0") : config.getThirdValue().abs();
                    } else if (cellNumFirst.equals("F45")) {
                        F45 = config.getThirdValue() == null ? new BigDecimal("0") : config.getThirdValue().abs();
                    }
                }
                BigDecimal avg21 = D44.abs().add(E44.abs()).add(F44.abs());
                BigDecimal avg22 = D45.abs().add(E45.abs()).add(F45.abs());
                double valueDou = avg21.doubleValue()/3 + avg22.doubleValue()/3;
                value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);
            } else if ("D33".equals(computeContent)) {
                // =IFERROR(MIN(AVERAGE(ABS(D34-D35),ABS(E34-E35),ABS(F34-F35)),2.25%*AVERAGE(D36:F36))+AVERAGE(D37:F37),0)
                BigDecimal D34 = new BigDecimal("0");
                BigDecimal D35 = new BigDecimal("0");
                BigDecimal D36 = new BigDecimal("0");
                BigDecimal D37 = new BigDecimal("0");
                BigDecimal E34 = new BigDecimal("0");
                BigDecimal E35 = new BigDecimal("0");
                BigDecimal E36 = new BigDecimal("0");
                BigDecimal E37 = new BigDecimal("0");
                BigDecimal F34 = new BigDecimal("0");
                BigDecimal F35 = new BigDecimal("0");
                BigDecimal F36 = new BigDecimal("0");
                BigDecimal F37 = new BigDecimal("0");
                // 获取D11, D12, E11, E12, F11, F12的值
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D34")) {
                        D34 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    } else if (cellNumFirst.equals("D35")) {
                        D35 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    } else if (cellNumFirst.equals("D36")) {
                        D36 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    } else if (cellNumFirst.equals("D37")) {
                        D37 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    }
                    String cellNumSecond = config.getCellNumSecond();
                    if (cellNumSecond.equals("E34")) {
                        E34 = config.getSecondValue() == null ? new BigDecimal("0") : config.getSecondValue();
                    } else if (cellNumFirst.equals("E35")) {
                        E35 = config.getSecondValue() == null ? new BigDecimal("0") : config.getSecondValue();
                    } else if (cellNumFirst.equals("E36")) {
                        E36 = config.getSecondValue() == null ? new BigDecimal("0") : config.getSecondValue();
                    } else if (cellNumFirst.equals("E37")) {
                        E37 = config.getSecondValue() == null ? new BigDecimal("0") : config.getSecondValue();
                    }
                    String cellNumThird = config.getCellNumThird();
                    if (cellNumThird.equals("F34")) {
                        F34 = config.getThirdValue() == null ? new BigDecimal("0") : config.getThirdValue();
                    } else if (cellNumFirst.equals("F35")) {
                        F35 = config.getThirdValue() == null ? new BigDecimal("0") : config.getThirdValue();
                    } else if (cellNumFirst.equals("F36")) {
                        F36 = config.getThirdValue() == null ? new BigDecimal("0") : config.getThirdValue();
                    } else if (cellNumFirst.equals("F37")) {
                        F37 = config.getThirdValue() == null ? new BigDecimal("0") : config.getThirdValue();
                    }
                }
                // 计算三个绝对差的平均值
                BigDecimal absTotal = D34.subtract(D35).abs()
                        .add((E34.subtract(E35).abs())).add(F34.subtract(F35).abs());
                double absAvgDou = absTotal.doubleValue() / 3;
                // 计算D13:F13的平均值的2.25%
                double divideDou = (D36.doubleValue()+E36.doubleValue()+F36.doubleValue())/3*2.25/100;

                // 取较小值
                //BigDecimal min = new BigDecimal("0");
                double min;
                if (absAvgDou<=divideDou) {
                    min = absAvgDou;
                } else {
                    min = divideDou;
                }
                // 加上D14:F14的平均值
                double valueDou = min + (D37.doubleValue() + E37.doubleValue() + F37.doubleValue())/3;
                value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);
            } else if ("D38".equals(computeContent)) {
                // =IFERROR(MAX(AVERAGE(D39:F39),AVERAGE(D40:F40))+MAX(AVERAGE(D41:F41),AVERAGE(D42:F42)),0)
                BigDecimal D39 = new BigDecimal("0");
                BigDecimal D40 = new BigDecimal("0");
                BigDecimal D41 = new BigDecimal("0");
                BigDecimal D42 = new BigDecimal("0");
                BigDecimal E39 = new BigDecimal("0");
                BigDecimal E40 = new BigDecimal("0");
                BigDecimal E41 = new BigDecimal("0");
                BigDecimal E42 = new BigDecimal("0");
                BigDecimal F39 = new BigDecimal("0");
                BigDecimal F40 = new BigDecimal("0");
                BigDecimal F41 = new BigDecimal("0");
                BigDecimal F42 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D39")) {
                        D39 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    } else if (cellNumFirst.equals("D40")) {
                        D40 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    } else if (cellNumFirst.equals("D41")) {
                        D41 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    } else if (cellNumFirst.equals("D42")) {
                        D42 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    }
                    String cellNumSecond = config.getCellNumSecond();
                    if (cellNumSecond.equals("E39")) {
                        E39 = config.getSecondValue() == null ? new BigDecimal("0") : config.getSecondValue();
                    } else if (cellNumFirst.equals("E40")) {
                        E40 = config.getSecondValue() == null ? new BigDecimal("0") : config.getSecondValue();
                    } else if (cellNumFirst.equals("E41")) {
                        E41 = config.getSecondValue() == null ? new BigDecimal("0") : config.getSecondValue();
                    } else if (cellNumFirst.equals("E42")) {
                        E42 = config.getSecondValue() == null ? new BigDecimal("0") : config.getSecondValue();
                    }
                    String cellNumThird = config.getCellNumThird();
                    if (cellNumThird.equals("F39")) {
                        F39 = config.getThirdValue() == null ? new BigDecimal("0") : config.getThirdValue();
                    } else if (cellNumFirst.equals("F40")) {
                        F40 = config.getThirdValue() == null ? new BigDecimal("0") : config.getThirdValue();
                    } else if (cellNumFirst.equals("F41")) {
                        F41 = config.getThirdValue() == null ? new BigDecimal("0") : config.getThirdValue();
                    } else if (cellNumFirst.equals("F42")) {
                        F42 = config.getThirdValue() == null ? new BigDecimal("0") : config.getThirdValue();
                    }
                }
                double doublE39 = (D39.doubleValue() + E39.doubleValue() + F39.doubleValue()) /3;
                double doublE40 = (D40.doubleValue() + E40.doubleValue() + F40.doubleValue()) /3;
                double maxDou1;
                if (doublE39 >= doublE40) {
                    maxDou1 = doublE39;
                } else {
                    maxDou1 = doublE40;
                }
                double doublE41 = (D41.doubleValue() + E41.doubleValue() + F41.doubleValue()) /3;
                double doublE42 = (D42.doubleValue() + E42.doubleValue() + F42.doubleValue()) /3;
                double maxDou2;
                if (doublE41 >= doublE42) {
                    maxDou2 = doublE41;
                } else {
                    maxDou2 = doublE42;
                }
                double valueDou = maxDou1 + maxDou2;
                value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);
            } else if ("D46".equals(computeContent)) {
                // =IFERROR(D33+D38+D43,0)
                BigDecimal D33 = new BigDecimal("0");
                BigDecimal D38 = new BigDecimal("0");
                BigDecimal D43 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D33")) {
                        D33 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    } else if (cellNumFirst.equals("D38")) {
                        D38 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    } else if (cellNumFirst.equals("D43")) {
                        D43 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    }
                }
                value = D33.add(D38).add(D43);
            } else if ("D32".equals(computeContent)) {
                // =IFERROR(IF(AND(D23+D46>24000000,D23>24000000),D46*0.18,IF(AND(D23+D46>24000000,D23<=24000000,D23>800000),(24000000-D23)*0.15+(D23+D46-24000000)*0.18,IF(AND(D23+D46>24000000,D23<=800000),(800000-D23)*0.12+(24000000-800000)*0.15+(D23+D46-24000000)*0.18,IF(AND(D23+D46>800000,D23>800000),D46*0.15,IF(AND(D23+D46>800000,D23<=800000),(800000-D23)*0.12+(D23+D46-800000)*0.15,D46*0.12))))),0)
                BigDecimal D23 = new BigDecimal("0");
                BigDecimal D46 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D23")) {
                        D23 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    } else if (cellNumFirst.equals("D46")) {
                        D46 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    }
                }
                double d23 = D23.doubleValue();
                double d46 = D46.doubleValue();
                if (d23 + d46 > 24000000 && d23 > 24000000) {
                    value = new BigDecimal(d46 * 0.18);
                } else if (d23 + d46 > 24000000 && d23 <= 24000000 && d23 > 800000) {
                    value = new BigDecimal((24000000 - d23) * 0.15 + (d23 + d46 - 24000000) * 0.18);
                } else if (d23 + d46 > 24000000 && d23 <= 800000) {
                    value = new BigDecimal((800000 - d23) * 0.12 + (24000000 - 800000) * 0.15 + (d23 + d46 - 24000000) * 0.18);
                } else if (d23 + d46 > 800000 && d23 > 800000) {
                    value = new BigDecimal(d46 * 0.15);
                } else if (d23 + d46 > 800000 && d23 <= 800000) {
                    value = new BigDecimal((800000 - d23) * 0.12 + (d23 + d46 - 800000) * 0.15);
                } else {
                    value = new BigDecimal(d46 * 0.12);
                }
            } else if ("D48".equals(computeContent)) {
                // =D32*D47
                BigDecimal D32 = new BigDecimal("0");
                BigDecimal D47 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D32")) {
                        D32 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    } else if (cellNumFirst.equals("D47")) {
                        D47 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    }
                }
                value = D32.multiply(D47);
            } else if ("D49".equals(computeContent)) {
                // =D48*12.5
                BigDecimal D48 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D48")) {
                        D48 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                        break;
                    }
                }
                value = D48.multiply(new BigDecimal("12.5"));
            } else if ("D51".equals(computeContent)) {
                BigDecimal D29 = new BigDecimal("0");
                BigDecimal D48 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D29")) {
                        D29 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    } else if (cellNumFirst.equals("D48")) {
                        D48 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                    }
                }
                value = D29.add(D48);
            } else if ("D52".equals(computeContent)) {
                // =D51*12.5
                BigDecimal D51 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfigGroup config : configList) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D51")) {
                        D51 = config.getFirstValue() == null ? new BigDecimal("0") : config.getFirstValue();
                        break;
                    }
                }
                value = D51.multiply(new BigDecimal("12.5"));
            }
        } else {
            return null;
        }
        return value;
    }

    @Override
    public Result<List<Map<String, Object>>> getGroupSubjectList(CmMeteringLegal cmMeteringLegal, Integer pageNo, Integer pageSize) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        // 获取机构信息
        List<CmMeteringOrgRel> orgRelList = orgRelMapper.selectListByMeteringId(cmMeteringLegal);
        int tableIndex = 1;
        for (CmMeteringOrgRel orgRel : orgRelList) {
            String orgType = orgRel.getOrgType();
            String orgName = orgRel.getOrgName();
            String isLossDataCheck = orgRel.getIsLossDataCheck();
            String isLossDataCheckName = "";
            if ("1".equals(isLossDataCheck)) {
                isLossDataCheckName = "是";
            } else if ("0".equals(isLossDataCheck)) {
                isLossDataCheckName = "否";
            }

            // 根据metering_id, group_org_rel_id获取列表数据（需要分页）
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("metering_id", orgRel.getMeteringId());
            queryWrapper.eq("organization_id", orgRel.getOrganizationId());
            queryWrapper.orderByAsc("last_subject_code");
            Page<CmSubjectMappingGroupResult> page = new Page<CmSubjectMappingGroupResult>(pageNo, pageSize);
            IPage<CmSubjectMappingGroupResult> pageList = mappingGroupResultService.page(page, queryWrapper);
            // 放入一条数据
            String name = orgName + "-损失数据是否验收(" + isLossDataCheckName + ")";
            // 获取科目层级
            int ledgerAccount = Integer.parseInt(orgRel.getLedgerAccount());
            // 获取表头
            //List<Map<String, Object>> columns = getSubjectColumnsByOrgType(orgType, orgRel.getLedgerAccount(), name);

            // 定义字段总长度
            int totalColumn = ledgerAccount*2 + 7;

            // 放入数据
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("tableData", pageList);

            // 定义放入是否存在G04
            String isG04Column = "0";
            if (orgType.equals("2")) {
                isG04Column = "1";
                totalColumn = totalColumn + 3;
            }
            resultMap.put("ledgerAccount", ledgerAccount*2);
            resultMap.put("isG04Column", isG04Column);
            resultMap.put("tableIndex", tableIndex);
            resultMap.put("totalColumn", totalColumn);
            resultMap.put("title", name);

            // 放入rel主键
            resultMap.put("organizationId", orgRel.getOrganizationId());
            resultList.add(resultMap);
            tableIndex++;
        }
        return Result.OK(resultList);

    }

    @Override
    public void exportGroupXls(HttpServletRequest request,
                               HttpServletResponse response,
                               CmMeteringLegal cmMeteringLegal) throws Exception {
        Map<String, Object> result = new HashMap<>();
        cmMeteringLegal = cmMeteringLegalMapper.selectById(cmMeteringLegal.getId());
        String isLossDateName = "";
        if (cmMeteringLegal.getIsLossDataCheck().equals("2")) {
            isLossDateName = "均未验收";
        } else if (cmMeteringLegal.getIsLossDataCheck().equals("3")) {
            isLossDateName = "部分验收";
        } else {
            isLossDateName = "全部验收";
        }
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 创建第一个sheet页
        Sheet sheet1 = workbook.createSheet("操作风险加权资产测算表（" + isLossDateName + "）");
        Sheet sheet2 = workbook.createSheet("会计科目映射表");
        Sheet sheet3 = workbook.createSheet("生息资产统计表");
        Sheet sheet4 = workbook.createSheet("股利收入统计表");
        Sheet sheet5 = workbook.createSheet("交易账簿净损益统计表");
        XSSFSheet sheet6 = workbook.createSheet("损失数据统计表");

        // 设置主标题样式（sheet页通用）
        CellStyle tableStyle = createTableStyle(workbook);

        // 生成sheet1

        //        Workbook workbook,
        //        String isBold,
        //        String position,
        //        String backgroundColor,
        //        String borderTop,
        //        String borderLeft,
        //        String borderBottom,
        //        String borderRight,
        //        String indention
        // 定义样式1：上边框加粗，左边框加粗，字体居中，无背景颜色，无缩进
        CellStyle normalStyle1 = createNormalStyle(workbook, "0",
                "center", null, "2", "2",
                "1", "1", null);
        // 定义样式1：上边框加粗，字体居中，无背景颜色，无缩进
        CellStyle normalStyle2 = createNormalStyle(workbook, "0",
                "center", null, "2", "1",
                "1", "1", null);
        // 定义样式3：上边框加粗，右边框加粗, 字体居中，无背景颜色，无缩进
        CellStyle normalStyle3 = createNormalStyle(workbook, "0",
                "center", null, "2", "1",
                "1", "2", null);
        // 定义样式4：字体居中，无背景颜色，无缩进，边框不加粗
        CellStyle normalStyle4 = createNormalStyle(workbook,
                "0", "center", null, "1",
                "1", "1", "1", null);
        // 定义样式5：右边框加粗，字体居中，无背景颜色，无缩进
        CellStyle normalStyle5 = createNormalStyle(workbook,
                "0", "center", null, "1",
                "1", "1", "2", null);
        // 定义样式6：左边框加粗，字体居中，上边框加粗，无背景颜色，无缩进
        CellStyle normalStyle6 = createNormalStyle(workbook,
                "0", "center", null, "2",
                "2", "1", "1", null);
        // 定义样式7：左边框加粗，字体居左，上边框加粗，无背景颜色，无缩进
        CellStyle normalStyle7 = createNormalStyle(workbook,
                "1", "left", null, "2",
                "1", "1", "1", null);
        // 定义样式8：字体居中，上边框加粗，右边框加粗，无背景颜色，无缩进
        CellStyle normalStyle8 = createNormalStyle(workbook,
                "1", "center", null, "2",
                "1", "1", "2", null);
        // 定义样式9：字体居中，左边框加粗，下边框加粗，无背景颜色，无缩进
        CellStyle normalStyle9 = createNormalStyle(workbook,
                "0", "center", null, "1",
                "2", "2", "1", null);
        // 定义样式10：字体居左，下边框加粗，字体加粗，无背景颜色，无缩进
        CellStyle normalStyle10 = createNormalStyle(workbook,
                "1", "left", null, "1",
                "1", "2", "1", null);
        // 定义样式11：字体居右，下边框加粗，背景颜色为紫色，无缩进
        CellStyle normalStyle11 = createNormalStyle(workbook,
                "0", "right", "46", "1",
                "1", "2", "1", null);

        // 定义样式12：字体居右，下边框加粗，背景颜色为灰色，无缩进
        CellStyle normalStyle12 = createNormalStyle(workbook,
                "0", "right", "22", "1",
                "1", "2", "1", null);

        // 定义样式13：字体居右，下边框加粗，右边框加粗背景颜色为灰色，无缩进
        CellStyle normalStyle13 = createNormalStyle(workbook,
                "0", "right", "22", "1",
                "1", "2", "2", null);

        // 定义样式14：字体居中，左边框加粗，无背景颜色，无缩进
        CellStyle normalStyle14 = createNormalStyle(workbook,
                "0", "center", null, "1",
                "2", "1", "1", null);

        // 定义样式15：字体居左，无背景颜色，无缩进，字体加粗
        CellStyle normalStyle15 = createNormalStyle(workbook,
                "1", "left", null, "1",
                "1", "1", "1", null);
        // 定义样式15：字体居左，无背景颜色，缩进1，字体加粗
        CellStyle normalStyle16 = createNormalStyle(workbook,
                "1", "left", null, "1",
                "1", "1", "1", "1");
        // 定义样式16：字体居右，紫色，无缩进
        CellStyle normalStyle17 = createNormalStyle(workbook,
                "0", "right", "46", "1",
                "1", "1", "1", null);
        // 定义样式18：字体居右，灰色，无缩进
        CellStyle normalStyle18 = createNormalStyle(workbook,
                "0", "right", "22", "1",
                "1", "1", "1", null);
        // 定义样式19：字体居右，灰色，右边框加粗，无缩进
        CellStyle normalStyle19 = createNormalStyle(workbook,
                "0", "right", "22", "1",
                "1", "1", "2", null);
        // 定义样式20：字体居左，无背景颜色，缩进2
        CellStyle normalStyle20 = createNormalStyle(workbook,
                "1", "left", null, "1",
                "1", "1", "1", "2");
        // 定义样式21：字体居左，无背景颜色，缩进3
        CellStyle normalStyle21 = createNormalStyle(workbook,
                "0", "left", null, "1",
                "1", "1", "1", "3");
        // 定义样式22：字体居右，黄色，无缩进
        CellStyle normalStyle22 = createNormalStyle(workbook,
                "0", "right", "13", "1",
                "1", "1", "1", null);
        // 定义样式23：字体居右，黄色，无缩进，右边框加粗
        CellStyle normalStyle23 = createNormalStyle(workbook,
                "0", "right", "13", "1",
                "1", "1", "2", null);

        // 定义样式24：字体居中，字体加粗，灰色，无缩进
        CellStyle normalStyle24 = createNormalStyle(workbook,
                "1", "center", "22", "1", "1",
                "1", "1", null);

        // 定义样式25：字体居中，黄色，无缩进
        CellStyle normalStyle25 = createNormalStyle(workbook,
                "0", "center", "13", "1",
                "1", "1", "1", null);

        // 定义样式26：字体居中，字体加粗，无缩进
        CellStyle normalStyle26 = createNormalStyle(workbook,
                "1", "center", null, "1", "1",
                "1", "1", null);

        // 定义样式27：字体居左，无背景颜色，无缩进
        CellStyle normalStyle27 = createNormalStyle(workbook,
                "0", "left", null, "1",
                "1", "1", "1", null);

        // 合并列
        mergeCells(sheet1, 3, 3, 1, 5, "集团操作风险加权资产测算表", tableStyle);

        mergeCells(sheet1, 5, 6, 1, 1, "序号", normalStyle1);
        mergeCells(sheet1, 5, 6, 2, 2, "项目", normalStyle2);
        // 放入列表名称

        Row detailRow5 = sheet1.getRow(5);
        detailRow5.setHeightInPoints(18);
        Cell cell1 = detailRow5.createCell(3);
        cell1.setCellValue("A");
        cell1.setCellStyle(normalStyle2);
        Cell cell2 = detailRow5.createCell(4);
        cell2.setCellValue("B");
        cell2.setCellStyle(normalStyle2);
        Cell cell3 = detailRow5.createCell(5);
        cell3.setCellValue("C");
        cell3.setCellStyle(normalStyle3);

        Row detailRow6 = sheet1.getRow(6);
        detailRow6.setHeightInPoints(18);
        Cell cell4 = detailRow6.createCell(3);
        cell4.setCellValue("最近第一年");
        cell4.setCellStyle(normalStyle4);
        Cell cell5 = detailRow6.createCell(4);
        cell5.setCellValue("最近第二年");
        cell5.setCellStyle(normalStyle4);
        Cell cell6 = detailRow6.createCell(5);
        cell6.setCellValue("最近第三年");
        cell6.setCellStyle(normalStyle5);

        // 放入数据
        QueryWrapper<CmCalculationConfigGroupResult> wrapper = new QueryWrapper();
        wrapper.ne("sort_num", "0");
        wrapper.eq("metering_id", cmMeteringLegal.getId());
        wrapper.orderByAsc("sort_num");
        List<CmCalculationConfigGroupResult> groupResultList = groupResultService.list(wrapper);
        int meteringIndex = 1;
        int sheet1DataIndex = 7;
        // 定义D列紫色，不在里面则为黄色
        List<Integer> cellD_color46 = new ArrayList<>();
        Collections.addAll(cellD_color46, 8, 9, 14, 19, 22, 23, 25, 26, 28, 29, 31, 33, 37, 42, 45, 47, 49, 50);
        // 定义E列灰色，不在里面则为黄色
        List<Integer> cellE_color22 = new ArrayList<>();
        Collections.addAll(cellE_color22, 8, 9, 14, 19, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 37, 42, 45, 46, 47, 48, 50);
        // 定义F列灰色，不在里面则为黄色
        List<Integer> cellF_color22 = new ArrayList<>();
        Collections.addAll(cellF_color22, 8, 9, 14, 19, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 37, 42, 45, 46, 47, 48, 50);

        for (CmCalculationConfigGroupResult groupResult : groupResultList) {
            String serialNumber = groupResult.getSerialNumber();
            // 创建行
            Row dataRow = sheet1.createRow(sheet1DataIndex);
            dataRow.setHeightInPoints(30);
            // 序号列
            Cell cellB = dataRow.createCell(1);
            // 项目列
            Cell cellC = dataRow.createCell(2);
            // 最近第一年列
            Cell cellD = dataRow.createCell(3);
            // 最近第二年列
            Cell cellE = dataRow.createCell(4);
            // 最近第三年列
            Cell cellF = dataRow.createCell(5);

            // B列 左边框加粗，字体居中
            if ("1".equals(serialNumber)) {
                // 如果是 标准法计算指标
                // B列左边框加粗，字体居中，上边框加粗
                cellB.setCellValue(serialNumber);
                cellB.setCellStyle(normalStyle6);
                // C列字体居左，上边框加粗，字体加粗，无缩进
                cellC.setCellValue(groupResult.getProjectName());
                cellC.setCellStyle(normalStyle7);
                // D、E、F列进行合并，字体加粗居中，上边框加粗，有边框加粗
                mergeCells(sheet1, sheet1DataIndex, sheet1DataIndex, 3, 5, groupResult.getFirstComputeContent(), normalStyle8);
                dataRow.setHeightInPoints(30);
            } else {
                // 如果是最后一行
                if (meteringIndex == groupResultList.size()) {
                    cellB.setCellValue(serialNumber);
                    cellB.setCellStyle(normalStyle9);
                    cellC.setCellValue(groupResult.getProjectName());
                    cellC.setCellStyle(normalStyle10);
                    cellD.setCellValue(groupResult.getFirstValue().toString());
                    cellD.setCellStyle(normalStyle11);
                    cellE.setCellStyle(normalStyle12);
                    cellF.setCellStyle(normalStyle13);
                } else {
                    cellB.setCellValue(serialNumber);
                    cellB.setCellStyle(normalStyle14);

                    cellC.setCellValue(groupResult.getProjectName());
                    String titleLevel = groupResult.getTitleLevel() == null ? "" : groupResult.getTitleLevel();
                    if (titleLevel.equals("1")) {
                        cellC.setCellStyle(normalStyle16);
                    } else if (titleLevel.equals("2")) {
                        cellC.setCellStyle(normalStyle20);
                    } else if (titleLevel.equals("3")) {
                        cellC.setCellStyle(normalStyle21);
                    } else {
                        cellC.setCellStyle(normalStyle15);
                    }
                    cellD.setCellValue(groupResult.getFirstValue() == null ? "" : groupResult.getFirstValue().toString());
                    if (cellD_color46.contains(sheet1DataIndex)) {
                        cellD.setCellStyle(normalStyle17);
                    } else {
                        cellD.setCellStyle(normalStyle22);
                    }
                    cellE.setCellValue(groupResult.getSecondValue() == null ? "" : groupResult.getSecondValue().toString());
                    if (cellE_color22.contains(sheet1DataIndex)) {
                        cellE.setCellStyle(normalStyle18);
                    } else {
                        cellE.setCellStyle(normalStyle22);
                    }
                    cellF.setCellValue(groupResult.getThirdValue() == null ? "" : groupResult.getThirdValue().toString());
                    if (cellF_color22.contains(sheet1DataIndex)) {
                        cellF.setCellStyle(normalStyle19);
                    } else {
                        cellF.setCellStyle(normalStyle23);
                    }
                }
            }
            meteringIndex++;
            sheet1DataIndex++;
        }
        // 设置宽度
        sheet1.setColumnWidth(1, 2000);
        sheet1.setColumnWidth(2, 9000);
        sheet1.setColumnWidth(3, 9000);
        sheet1.setColumnWidth(4, 9000);
        sheet1.setColumnWidth(5, 9000);


        // sheet2
        // 定义表头颜色:会计科目基本信息
        CellStyle sheet2Style1 = createNormalStyle2(workbook,
                "1", "center", "23", "1", "1", "1", "1", null);
        // 定义表头颜色:G04映射关系
        CellStyle sheet2Style2 = createNormalStyle2(workbook,
                "1", "center", "48", "1", "1", "1", "1", null);
        // 定义表头颜色:BI映射关系
        CellStyle sheet2Style3 = createNormalStyle2(workbook,
                "1", "center", "60", "1", "1", "1", "1", null);

        // 定义表头颜色:万元
        CellStyle sheet2Style4 = createNormalStyle2(workbook,
                "1", "center", "8", "1", "1", "1", "1", null);


        // 定义表头列
        int mappingIndex = 6;
        // 定义BI列数
        int G04Count = 0;

        // 定义会计科目起始列
        int cellStart1 = 1;
        // 定义会计科目结束列
        int cellEnd1 = 0;
        // 定义G04起始列
        int cellStart2 = 0;
        // 定义G04结束列
        int cellEnd2 = 0;


        // 先查询机构科目余额关联关系表
        List<CmMeteringOrgRel> orgRelList = orgRelMapper.selectListByMeteringId(cmMeteringLegal);


        // 取出最大值（）
        int maxMegerCount = 0;
        // 定义最大列是不是法人
        String isMaxFr = "0";
        // 定义是否存在法人
        String isExistFR = "0";
        for (CmMeteringOrgRel orgRel : orgRelList) {
            // 会计科目层级
            int ledgerAccount = Integer.parseInt(orgRel.getLedgerAccount());
            if ("2".equals(orgRel.getOrgType())) {
                if (maxMegerCount < ledgerAccount*2+3) {
                    maxMegerCount = ledgerAccount*2+3;
                    isMaxFr = "1";
                }
                isExistFR = "1";
            } else {
                if (maxMegerCount < ledgerAccount*2) {
                    maxMegerCount = ledgerAccount*2;
                    isMaxFr = "0";
                }
            }
        }
        // 开始放入Excel
        for (CmMeteringOrgRel orgRel : orgRelList) {
            // 会计科目层级
            int ledgerAccount = Integer.parseInt(orgRel.getLedgerAccount());
            // 会计科目基本信息结束列
            cellEnd1 = ledgerAccount*2;

            // 定义BI映射关系起始列
            int cellStart3 = 0;
            // 定义BI映射关系结束列
            int cellEnd3 = 0;

            // 判断最大合并列是否等于层级数，若相等，肯定不存在法人，因为法人是奇数，附属公司是偶数
/*            if (maxMegerCount == cellEnd1) {
                // 起始列为第一列
                cellStart1 = 1;
                cellStart3 = cellEnd1 + 1;
                cellEnd3 = cellEnd1+4;
                mergeCells(sheet6, titleMergeRow, titleMergeRow, cellStart1, cellEnd1, "会计科目基本信息", sheet2Style1);
//                // 判断是否存在法人
//                if ("1".equals(isExistFR)) {
//                    cellStart2 = ledgerAccount*2+1;
//                    cellEnd2 = ledgerAccount*2+3;
//                    cellStart3 = ledgerAccount*2+4;
//                    cellEnd3 = ledgerAccount*2+7;
//                    mergeCells(sheet6, titleMergeRow, titleMergeRow,cellStart2,cellEnd2, "G04映射关系", sheet2Style2);
//                } else {
//                    cellStart2 = 0;
//                    cellEnd2 = 0;
//                    cellStart3 = ledgerAccount*2+1;
//                    cellEnd3 = ledgerAccount*2+4;
//                }
            } else {// maxMegerCount > cellEnd1

                // 如果存在法人
                if (orgRel.getOrgType().equals("2")) {
                    // 如果最大值是法人
//                    if ("1".equals(isMaxFr)) {
//
//
//                    } else {
//
//                    }
                    cellEnd2 = maxMegerCount;
                    cellStart2 = maxMegerCount-2;
                    mergeCells(sheet6, titleMergeRow, titleMergeRow,
                            cellStart2, cellEnd2, "G04映射关系", sheet2Style2);

                    cellEnd1 = maxMegerCount-3;
                    cellStart1 = maxMegerCount - cellEnd1 + 1;
                    mergeCells(sheet6, titleMergeRow, titleMergeRow,
                            cellStart1, cellEnd1, "会计科目基本信息", sheet2Style1);
                } else {
                    cellEnd1 = maxMegerCount;
                    cellStart1 = maxMegerCount - cellEnd1 + 1;
                    cellEnd2 = 0;
                    cellStart2 = 0;
                    mergeCells(sheet6, titleMergeRow, titleMergeRow,
                            cellStart1, cellEnd1, "会计科目基本信息", sheet2Style1);
                }


            }*/

            Row sheet2Row6 = sheet2.getRow(mappingIndex) == null ? sheet2.createRow(mappingIndex) : sheet2.getRow(mappingIndex);
            sheet2Row6.setHeightInPoints(15);
            if (orgRel.getOrgType().equals("2")) {
                cellEnd2 = maxMegerCount;
                cellStart2 = maxMegerCount-2;
                mergeCells(sheet2, mappingIndex-1, mappingIndex-1,
                        cellStart2, cellEnd2, "G04映射关系", sheet2Style2);

                cellEnd1 = maxMegerCount-3;
                cellStart1 = cellEnd1 - ledgerAccount*2 + 1;
                mergeCells(sheet2, mappingIndex-1, mappingIndex-1,
                        cellStart1, cellEnd1, "会计科目基本信息", sheet2Style1);

                // 获取第二列表头
                Cell sheetCell1 = sheet2Row6.createCell(maxMegerCount);
                sheetCell1.setCellValue("G04三级项目");
                sheetCell1.setCellStyle(sheet2Style2);
                Cell sheetCell2 = sheet2Row6.createCell(maxMegerCount-1);
                sheetCell2.setCellValue("G04二级项目");
                sheetCell2.setCellStyle(sheet2Style2);
                Cell sheetCell3 = sheet2Row6.createCell(maxMegerCount-2);
                sheetCell3.setCellValue("G04三级项目");
                sheetCell3.setCellStyle(sheet2Style2);
            } else {
                cellEnd1 = maxMegerCount;
                cellStart1 = maxMegerCount - cellEnd1 + 1;
                cellEnd2 = 0;
                cellStart2 = 0;
                mergeCells(sheet2, mappingIndex-1, mappingIndex-1,
                        cellStart1, cellEnd1, "会计科目基本信息", sheet2Style1);
            }

            if (ledgerAccount == 5) {
                createSheet2Row(sheet2Row6, cellEnd1, "五级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-1, "五级科目代码", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-2, "四级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-3, "四级科目代码", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-4, "三级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-5, "三级科目代码", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-6, "二级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-7, "二级科目代码", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-8, "一级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-9, "一级科目代码", sheet2Style1);
            } else if (ledgerAccount == 4) {
                createSheet2Row(sheet2Row6, cellEnd1, "四级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-1, "四级科目代码", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-2, "三级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-3, "三级科目代码", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-4, "二级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-5, "二级科目代码", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-6, "一级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-7, "一级科目代码", sheet2Style1);
            } else if (ledgerAccount == 3) {
                createSheet2Row(sheet2Row6, cellEnd1, "三级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-1, "三级科目代码", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-2, "二级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-3, "二级科目代码", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-4, "一级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-5, "一级科目代码", sheet2Style1);
            } else if (ledgerAccount == 2) {
                createSheet2Row(sheet2Row6, cellEnd1, "二级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-1, "二级科目代码", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-2, "一级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-3, "一级科目代码", sheet2Style1);
            } else if (ledgerAccount == 1) {
                createSheet2Row(sheet2Row6, cellEnd1, "一级科目名称", sheet2Style1);
                createSheet2Row(sheet2Row6, cellEnd1-1, "一级科目代码", sheet2Style1);
            }

            // 放入 BI映射关系
            Cell sheetCell10 = sheet2Row6.createCell(maxMegerCount + 1);
            sheetCell10.setCellValue("是否符合BI定义");
            sheetCell10.setCellStyle(sheet2Style3);
            Cell sheetCell11 = sheet2Row6.createCell(maxMegerCount + 2);
            sheetCell11.setCellValue("对应BI项目");
            sheetCell11.setCellStyle(sheet2Style3);
            Cell sheetCell12 = sheet2Row6.createCell(maxMegerCount + 3);
            sheetCell12.setCellValue("对应BI子项");
            sheetCell12.setCellStyle(sheet2Style3);
            Cell sheetCell13 = sheet2Row6.createCell(maxMegerCount + 4);
            sheetCell13.setCellValue("对应BI典型子项");
            sheetCell13.setCellStyle(sheet2Style3);

            // 合并BI映射关系
            mergeCells(sheet2, mappingIndex-1, mappingIndex-1, maxMegerCount + 1, maxMegerCount + 4, "BI映射关系", sheet2Style3);
            // 合并科目余额（借正贷负/万元）
            mergeCells(sheet2, mappingIndex-1, mappingIndex, maxMegerCount + 5, maxMegerCount + 5, "科目余额（借正贷负/万元）", sheet2Style4);

            // 合并科目余额（借正贷负/万元）
            mergeCells(sheet2, mappingIndex-1, mappingIndex, maxMegerCount + 6, maxMegerCount + 6, "抵销金额（借正贷负/万元）", sheet2Style4);
            // 抵销后余额（借正贷负/万元）
            mergeCells(sheet2, mappingIndex-1, mappingIndex, maxMegerCount + 7, maxMegerCount + 7, "抵销后余额（借正贷负/万元）", sheet2Style4);

            Row sheet2Row5 = sheet2.getRow(mappingIndex-1) == null ? sheet2.createRow(mappingIndex-1) : sheet2.getRow(mappingIndex-1);
            sheet2Row5.setHeightInPoints(15);

            // 法人-损失数据是否验收（是）
            mappingIndex++;
            mergeCells(sheet2, mappingIndex, mappingIndex, cellStart1, maxMegerCount + 7, orgRel.getOrgName()+"-损失数据是否验收（" + (orgRel.getIsLossDataCheck().equals("1") ? "是" : "否") + "）", normalStyle26);
            Row sheet2Row7 = sheet2.getRow(mappingIndex-1) == null ? sheet2.createRow(mappingIndex-1) : sheet2.getRow(mappingIndex-1);
            sheet2Row7.setHeightInPoints(15);
            Row sheet2Row8 = sheet2.getRow(mappingIndex) == null ? sheet2.createRow(mappingIndex) : sheet2.getRow(mappingIndex);
            sheet2Row8.setHeightInPoints(40);
            // 放入数据
            mappingIndex++;

            List<CmSubjectMappingGroupResult> records = getMappingGroupByMeteringId(orgRel);

            for (CmSubjectMappingGroupResult groupResult : records) {
                // 创建行
                Row dataRow = sheet2.createRow(mappingIndex);
                if ("2".equals(orgRel.getOrgType())) {
                    // 获取第二列表头
                    Cell sheetCell1 = dataRow.createCell(maxMegerCount);
                    sheetCell1.setCellValue(groupResult.getG04ProjectThree());
                    sheetCell1.setCellStyle(normalStyle4);
                    Cell sheetCell2 = dataRow.createCell(maxMegerCount-1);
                    sheetCell2.setCellValue(groupResult.getG04ProjectTwo());
                    sheetCell2.setCellStyle(normalStyle4);
                    Cell sheetCell3 = dataRow.createCell(maxMegerCount-2);
                    sheetCell3.setCellValue(groupResult.getG04ProjectOne());
                    sheetCell3.setCellStyle(normalStyle4);
                }


                if (ledgerAccount == 5) {
                    createSheet2Row(dataRow, cellEnd1, groupResult.getSubjectNameFive(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-1, groupResult.getSubjectCodeFive(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-2, groupResult.getSubjectNameFour(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-3, groupResult.getSubjectCodeFour(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-4, groupResult.getSubjectNameThree(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-5, groupResult.getSubjectCodeThree(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-6, groupResult.getSubjectNameTwo(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-7, groupResult.getSubjectCodeTwo(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-8, groupResult.getSubjectNameOne(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-9, groupResult.getSubjectCodeOne(), normalStyle4);
                } else if (ledgerAccount == 4) {
                    createSheet2Row(dataRow, cellEnd1, groupResult.getSubjectNameFour(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-1, groupResult.getSubjectCodeFour(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-2, groupResult.getSubjectNameThree(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-3, groupResult.getSubjectCodeThree(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-4, groupResult.getSubjectNameTwo(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-5, groupResult.getSubjectCodeTwo(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-6, groupResult.getSubjectNameOne(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-7, groupResult.getSubjectCodeOne(), normalStyle4);
                } else if (ledgerAccount == 3) {
                    createSheet2Row(dataRow, cellEnd1, groupResult.getSubjectNameThree(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-1, groupResult.getSubjectCodeThree(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-2, groupResult.getSubjectNameTwo(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-3, groupResult.getSubjectCodeTwo(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-4, groupResult.getSubjectNameOne(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-5, groupResult.getSubjectCodeOne(), normalStyle4);
                } else if (ledgerAccount == 2) {
                    createSheet2Row(dataRow, cellEnd1, groupResult.getSubjectNameTwo(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-1, groupResult.getSubjectCodeTwo(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-2, groupResult.getSubjectNameOne(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-3, groupResult.getSubjectCodeOne(), normalStyle4);
                } else if (ledgerAccount == 1) {
                    createSheet2Row(dataRow, cellEnd1, groupResult.getSubjectNameOne(), normalStyle4);
                    createSheet2Row(dataRow, cellEnd1-1, groupResult.getSubjectCodeOne(), normalStyle4);
                }
                // 放入Bi
                // 放入 BI映射关系
                Cell sheetCell14 = dataRow.createCell(maxMegerCount + 1);
                sheetCell14.setCellValue(groupResult.getIsFitBiName());
                sheetCell14.setCellStyle(normalStyle4);
                Cell sheetCell15 = dataRow.createCell(maxMegerCount + 2);
                sheetCell15.setCellValue(groupResult.getCorrespondBiSubjectName());
                sheetCell15.setCellStyle(normalStyle4);
                Cell sheetCell16 = dataRow.createCell(maxMegerCount + 3);
                sheetCell16.setCellValue(groupResult.getCorrespondBiSubitemName());
                sheetCell16.setCellStyle(normalStyle4);
                Cell sheetCell17 = dataRow.createCell(maxMegerCount + 4);
                sheetCell17.setCellValue(groupResult.getCorrespondBiTypicalSubitemName());
                sheetCell17.setCellStyle(normalStyle4);

                // 科目余额（借正贷负/万元）
                Cell sheetCell18 = dataRow.createCell(maxMegerCount + 5);
                sheetCell18.setCellValue(groupResult.getEndBalance() == null ? "0" : groupResult.getEndBalance().toString());
                sheetCell18.setCellStyle(normalStyle22);

                // 抵销金额（借正贷负/万元）
                Cell sheetCell19 = dataRow.createCell(maxMegerCount + 6);
                sheetCell19.setCellValue(groupResult.getOffsetAmount() == null ? "0" : groupResult.getOffsetAmount().toString());
                sheetCell19.setCellStyle(normalStyle22);

                // 抵销后余额（借正贷负/万元）
                Cell sheetCell20 = dataRow.createCell(maxMegerCount + 7);
                sheetCell20.setCellValue(groupResult.getOffsetEndAmount() == null ? "0" : groupResult.getOffsetEndAmount().toString());
                sheetCell20.setCellStyle(normalStyle22);
                mappingIndex++;
                dataRow.setHeightInPoints(15);
            }
            mappingIndex = mappingIndex + 2;
        }
        mergeCells(sheet2, 3, 3, 1, maxMegerCount, "集团会计科目映射表", tableStyle);

        // 设置宽度
        for (int i = 1; i<= maxMegerCount; i++) {
            sheet2.setColumnWidth(i, 3000);
        }
        sheet2.setColumnWidth(maxMegerCount+1, 4000);
        sheet2.setColumnWidth(maxMegerCount+2, 4000);
        sheet2.setColumnWidth(maxMegerCount+3, 4000);
        sheet2.setColumnWidth(maxMegerCount+4, 4000);
        sheet2.setColumnWidth(maxMegerCount+5, 6000);
        sheet2.setColumnWidth(maxMegerCount+6, 6000);
        sheet2.setColumnWidth(maxMegerCount+7, 8000);


        // sheet3
        mergeCells(sheet3, 3, 3, 1, 2, "集团生息资产统计表", tableStyle);
        // 设置sheet4表头
        Row sheet3Row1 = sheet3.createRow(5);
        sheet3Row1.setHeightInPoints(20);
        Cell sheet3Cell1 = sheet3Row1.createCell(1);
        sheet3Cell1.setCellValue("机构");
        sheet3Cell1.setCellStyle(normalStyle24);
        Cell sheet3Cell2 = sheet3Row1.createCell(2);
        sheet3Cell2.setCellValue("余额（万元）");
        sheet3Cell2.setCellStyle(normalStyle24);
        // 获取数据
        getAssetByMeteringId(result, cmMeteringLegal);
        List<CmAssetGroupResult> assetResultList = (List) result.get("assetResultList");
        // 遍历数据
        int assetIndex = 6;
        for (CmAssetGroupResult groupResult : assetResultList) {
            Row dataRow = sheet3.createRow(assetIndex);
            dataRow.setHeightInPoints(28);
            Cell cellB = dataRow.createCell(1);
            cellB.setCellValue(groupResult.getOrgName());
            cellB.setCellStyle(normalStyle4);
            Cell cellC = dataRow.createCell(2);
            cellC.setCellValue(groupResult.getAssetAmount() == null ? "" : groupResult.getAssetAmount().toString());
            cellC.setCellStyle(normalStyle25);
            assetIndex++;
        }
        // 设置宽度
        sheet3.setColumnWidth(1, 3000);
        sheet3.setColumnWidth(2, 9000);


        // sheet4
        mergeCells(sheet4, 3, 3, 1, 3, "集团股利收入统计表", tableStyle);
        // 设置sheet4表头
        Row sheet4Row1 = sheet4.createRow(5);
        sheet4Row1.setHeightInPoints(20);
        Cell sheet4Cell1 = sheet4Row1.createCell(1);
        sheet4Cell1.setCellValue("机构");
        sheet4Cell1.setCellStyle(normalStyle24);
        Cell sheet4Cell2 = sheet4Row1.createCell(2);
        sheet4Cell2.setCellValue("数据来源");
        sheet4Cell2.setCellStyle(normalStyle24);
        Cell sheet4Cell3 = sheet4Row1.createCell(3);
        sheet4Cell3.setCellValue("金额（借正贷负/万元）");
        sheet4Cell3.setCellStyle(normalStyle24);
        // 获取数据
        getDividendByMeteringId(result, cmMeteringLegal);
        List<CmDividendGroupResult> dividendList = (List) result.get("dividendList");
        // 判断是否包含合计
        List<CmDividendGroupResult> frSheet4List = dividendList.stream().filter(o -> o.getDataSource().equals("合计")).collect(Collectors.toList());
        // 遍历数据
        int dividendIndex = 6;
        for (CmDividendGroupResult groupResult : dividendList) {
            if (frSheet4List != null && !frSheet4List.isEmpty()) {
                // 需要合并法人，7，8，9行
                if (dividendIndex == 6) {
                    mergeCells(sheet4, 6, 8, 1, 1, "法人", normalStyle4);
                }
            }
            Row dataRow = sheet4.getRow(dividendIndex) == null ? sheet4.createRow(dividendIndex) : sheet4.getRow(dividendIndex) ;
            dataRow.setHeightInPoints(28);

            if (dataRow.getCell(1) == null && !groupResult.getDataSource().equals("集团合计")) {
                Cell cellB = dataRow.createCell(1);
                cellB.setCellValue(groupResult.getOrgName());
                cellB.setCellStyle(normalStyle4);
            }
            Cell cellC = dataRow.createCell(2);
            if (groupResult.getDataSource().equals("合计")) {
                cellC.setCellStyle(normalStyle26);
                cellC.setCellValue(groupResult.getDataSource());
            } else if (groupResult.getDataSource().equals("集团合计")) {
                mergeCells(sheet4, dividendIndex, dividendIndex, 1, 2, "集团合计", normalStyle26);
            } else {
                cellC.setCellValue(groupResult.getDataSource());
                cellC.setCellStyle(normalStyle4);
            }
            Cell cellD = dataRow.createCell(3);
            cellD.setCellValue(groupResult.getAmount() == null ? "" : groupResult.getAmount().toString());
            cellD.setCellStyle(normalStyle25);
            dividendIndex++;
        }
        // 设置宽度
        sheet4.setColumnWidth(1, 3000);
        sheet4.setColumnWidth(2, 6000);
        sheet4.setColumnWidth(3, 9000);

        // sheet5
        mergeCells(sheet5, 3, 3, 1, 3, "集团交易账簿净损益统计表", tableStyle);
        // 设置sheet5表头
        Row sheet5Row1 = sheet5.createRow(5);
        sheet5Row1.setHeightInPoints(20);
        Cell sheet5Cell1 = sheet5Row1.createCell(1);
        sheet5Cell1.setCellValue("机构");
        sheet5Cell1.setCellStyle(normalStyle24);
        Cell sheet5Cell2 = sheet5Row1.createCell(2);
        sheet5Cell2.setCellValue("数据来源");
        sheet5Cell2.setCellStyle(normalStyle24);
        Cell sheet5Cell3 = sheet5Row1.createCell(3);
        sheet5Cell3.setCellValue("金额（借正贷负/万元）");
        sheet5Cell3.setCellStyle(normalStyle24);
        // 获取数据
        getProfitByMeteringId(result, cmMeteringLegal);
        List<CmMeteringProfitGroupResult> accountBookList = (List) result.get("accountBookList");
        // 判断是否包含合计
        List<CmMeteringProfitGroupResult> frSheet5List = accountBookList.stream().filter(o -> o.getDataSource().equals("合计")).collect(Collectors.toList());
        // 遍历数据
        int accountIndex = 6;
        for (CmMeteringProfitGroupResult groupResult : accountBookList) {
            if (frSheet5List != null && !frSheet5List.isEmpty()) {
                // 需要合并法人，7，8，9行
                if (accountIndex == 6) {
                    mergeCells(sheet5, 6, 8, 1, 1, "法人", normalStyle4);
                }
            }
            Row dataRow = sheet5.getRow(accountIndex) == null ? sheet5.createRow(accountIndex) : sheet5.getRow(accountIndex) ;
            dataRow.setHeightInPoints(28);

            if (dataRow.getCell(1) == null && !groupResult.getDataSource().equals("集团合计")) {
                Cell cellB = dataRow.createCell(1);
                cellB.setCellValue(groupResult.getOrgName());
                cellB.setCellStyle(normalStyle4);
            }

            Cell cellC = dataRow.createCell(2);
            if (groupResult.getDataSource().equals("合计")) {
                cellC.setCellStyle(normalStyle26);
                cellC.setCellValue(groupResult.getDataSource());
            } else if (groupResult.getDataSource().equals("集团合计")) {
                mergeCells(sheet5, accountIndex, accountIndex, 1, 2, "集团合计", normalStyle26);
            } else {
                cellC.setCellValue(groupResult.getDataSource());
                cellC.setCellStyle(normalStyle4);
            }
            Cell cellD = dataRow.createCell(3);
            cellD.setCellValue(groupResult.getAmount() == null ? "" : groupResult.getAmount().toString());
            cellD.setCellStyle(normalStyle25);
            accountIndex++;
        }
        // 设置宽度
        sheet5.setColumnWidth(1, 3000);
        sheet5.setColumnWidth(2, 6000);
        sheet5.setColumnWidth(3, 9000);


        // sheet6
        mergeCells(sheet6, 3, 3, 1, 4, "集团损失数据统计表", tableStyle);
        // 获取report6
        getLossDataByMeteringId(result, cmMeteringLegal);
        List<CmBasicGroupLossResult> lossResult = (List) result.get("lossDataList");

        // 定义行数
        int lossIndex = 5;
        // 定义线的开始
        int indexLineRowStart = 8;
        int indexLineRowEnd = 9;
        int indexLineCellStart = 2;
        int indexLineCellEnd = 3;

        for (CmBasicGroupLossResult groupLossResult : lossResult) {
            // 首先是表头行
            Row sheet6Row1 = sheet6.createRow(lossIndex);
            sheet6Row1.setHeightInPoints(15);
            Cell sheet6Cell1 = sheet6Row1.createCell(1);
            sheet6Cell1.setCellValue("项目");
            sheet6Cell1.setCellStyle(normalStyle24);
            Cell sheet6Cell2 = sheet6Row1.createCell(2);
            sheet6Cell2.setCellValue("最近第一年");
            sheet6Cell2.setCellStyle(normalStyle24);
            Cell sheet6Cell3 = sheet6Row1.createCell(3);
            sheet6Cell3.setCellValue("最近第二年");
            sheet6Cell3.setCellStyle(normalStyle24);
            Cell sheet6Cell4 = sheet6Row1.createCell(4);
            sheet6Cell4.setCellValue("最近第三年");
            sheet6Cell4.setCellStyle(normalStyle24);

            // 数据行
            lossIndex++;
            // 合并列
            mergeCells(sheet6, lossIndex, lossIndex, 1, 4, groupLossResult.getOrgName()+"-"+groupLossResult.getIsLossDataCheckName(), normalStyle15);
            // 由操作风险事件造成的损失
            lossIndex++;
            Row sheet6Row2 = sheet6.createRow(lossIndex);
            sheet6Row2.setHeightInPoints(15);
            Cell cellB_1 = sheet6Row2.createCell(1);
            cellB_1.setCellValue("由操风造成的损失");
            cellB_1.setCellStyle(normalStyle27);
            Cell cellC_1 = sheet6Row2.createCell(2);
            cellC_1.setCellValue(groupLossResult.getLossValueOne()==null? "0":groupLossResult.getLossValueOne().toString());
            cellC_1.setCellStyle(normalStyle25);
            Cell cellD_1 = sheet6Row2.createCell(3);
            cellD_1.setCellValue(groupLossResult.getLossValueTwo()==null?"0":groupLossResult.getLossValueTwo().toString());
            cellD_1.setCellStyle(normalStyle25);
            Cell cellE_1 = sheet6Row2.createCell(4);
            cellE_1.setCellValue(groupLossResult.getLossValueThree()==null?"0":groupLossResult.getLossValueThree().toString());
            cellE_1.setCellStyle(normalStyle25);

            // 由操作风险事件造成的损失（上年度已审核计量结果）
            lossIndex++;
            Row sheet6Row3 = sheet6.createRow(lossIndex);
            sheet6Row3.setHeightInPoints(15);
            Cell cellB_2 = sheet6Row3.createCell(1);
            cellB_2.setCellValue("由操风造成的损失（上年度已审核计量结果）");
            cellB_2.setCellStyle(normalStyle27);
            Cell cellC_2 = sheet6Row3.createCell(2);
            cellC_2.setCellStyle(normalStyle4);
            Cell cellD_2 = sheet6Row3.createCell(3);
            cellD_2.setCellStyle(normalStyle25);
            cellD_2.setCellValue(groupLossResult.getLossValuePreTwo()==null?"0":groupLossResult.getLossValuePreTwo().toString());
            Cell cellE_2 = sheet6Row3.createCell(4);
            cellE_2.setCellStyle(normalStyle25);
            cellE_2.setCellValue(groupLossResult.getLossValuePreThree()==null?"0":groupLossResult.getLossValuePreThree().toString());

            // 过去10年操作风险损失的算数平均值
            lossIndex++;
            Row sheet6Row4 = sheet6.createRow(lossIndex);
            sheet6Row4.setHeightInPoints(15);
            Cell cellB_3 = sheet6Row4.createCell(1);
            cellB_3.setCellValue("近10年操风损失的算数平均值");
            cellB_3.setCellStyle(normalStyle27);
            Cell cellC_3 = sheet6Row4.createCell(2);
            cellC_3.setCellStyle(normalStyle25);
            cellC_3.setCellValue(groupLossResult.getLossValueAverage()==null?"0":groupLossResult.getLossValueAverage().toString());
            Cell cellD_3 = sheet6Row4.createCell(3);
            cellD_3.setCellStyle(normalStyle4);
            Cell cellE_3 = sheet6Row4.createCell(4);
            cellE_3.setCellStyle(normalStyle4);

            // 监管给定的ILM
            lossIndex++;
            Row sheet6Row5 = sheet6.createRow(lossIndex);
            sheet6Row5.setHeightInPoints(15);
            Cell cellB_4 = sheet6Row5.createCell(1);
            cellB_4.setCellValue("监管给定的ILM");
            cellB_4.setCellStyle(normalStyle27);
            Cell cellC_4 = sheet6Row5.createCell(2);
            cellC_4.setCellStyle(normalStyle25);
            cellC_4.setCellValue(groupLossResult.getIlm());
            Cell cellD_4 = sheet6Row5.createCell(3);
            cellD_4.setCellStyle(normalStyle4);
            Cell cellE_4 = sheet6Row5.createCell(4);
            cellE_4.setCellStyle(normalStyle4);

            // 画线
            XSSFDrawing drawing = sheet6.createDrawingPatriarch();
            XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, indexLineCellStart, indexLineRowStart, indexLineCellEnd, indexLineRowEnd);
            XSSFSimpleShape line = drawing.createSimpleShape(anchor);
            line.setShapeType(ShapeTypes.LINE);
            line.setLineStyleColor(0, 0, 0);
            line.setLineWidth(1);

            XSSFClientAnchor anchor2 = drawing.createAnchor(0, 0, 0, 0, indexLineCellStart+1, indexLineRowStart+1, indexLineCellEnd+1, indexLineRowEnd+1);
            XSSFSimpleShape line2 = drawing.createSimpleShape(anchor2);
            line2.setShapeType(ShapeTypes.LINE);
            line2.setLineStyleColor(0, 0, 0);
            line2.setLineWidth(1);

            XSSFClientAnchor anchor3 = drawing.createAnchor(0, 0, 0, 0, indexLineCellStart+2, indexLineRowStart+1, indexLineCellEnd+2, indexLineRowEnd+1);
            XSSFSimpleShape line3 = drawing.createSimpleShape(anchor3);
            line3.setShapeType(ShapeTypes.LINE);
            line3.setLineStyleColor(0, 0, 0);
            line3.setLineWidth(1);

            XSSFClientAnchor anchor4 = drawing.createAnchor(0, 0, 0, 0, indexLineCellStart+1, indexLineRowStart+2, indexLineCellEnd+1, indexLineRowEnd+2);
            XSSFSimpleShape line4 = drawing.createSimpleShape(anchor4);
            line4.setShapeType(ShapeTypes.LINE);
            line4.setLineStyleColor(0, 0, 0);
            line4.setLineWidth(1);

            XSSFClientAnchor anchor5 = drawing.createAnchor(0, 0, 0, 0, indexLineCellStart+2, indexLineRowStart+2, indexLineCellEnd+2, indexLineRowEnd+2);
            XSSFSimpleShape line5 = drawing.createSimpleShape(anchor5);
            line5.setShapeType(ShapeTypes.LINE);
            line5.setLineStyleColor(0, 0, 0);
            line5.setLineWidth(1);

            lossIndex++;
            indexLineRowStart = indexLineRowStart + 6;
            indexLineRowEnd = indexLineRowEnd + 6;
        }

        // 设置宽度
        sheet6.setColumnWidth(1, 9000);
        sheet6.setColumnWidth(2, 9000);
        sheet6.setColumnWidth(3, 9000);
        sheet6.setColumnWidth(4, 9000);

        response.setHeader("Connection", "close");
        response.setHeader("Content-Type",
                "application/vnd.ms-excel;charset=UTF-8");
        OutputStream out = null;
        String fileName = cmMeteringLegal.getVersion() + ".xlsx";
        fileName = URLEncoder.encode(fileName, "utf-8");
        response.setHeader("Content-Disposition", "attachment;filename="
                + fileName);
        try {
            out = response.getOutputStream();
        } catch (IOException e) {
            log.error(e.getMessage(),e);
        }

        if(out != null){
            try {
                workbook.write(out);
                out.flush();
                out.close();
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }

    }

    @Override
    public Result<String> batchSubmitByWorkflow(List<String> ids) {
        List<CmMeteringLegal> resultList = new LinkedList<>();
        for (String id : ids) {
            CmMeteringLegal cmMeteringLegal = cmMeteringLegalMapper.selectById(id);
            // 判断有没有“待复核”、“待审核”或“审核通过”的数据,排除本身
            int inProcessCount = cmMeteringLegalMapper.getInProcessCount(cmMeteringLegal);
            if (inProcessCount > 0) {
                return Result.error("当前计量年度【"+cmMeteringLegal.getMeteringYear()+"】审计版本【"+cmMeteringLegal.getAuditVersion()+"】下已有处于审批流或审核通过的版本，请勿重复操作");
            }
            resultList.add(cmMeteringLegal);
        }
        for (CmMeteringLegal legal : resultList) {
            Map<String, Object> variables = new HashMap<>();
            variables.put("meteringProcess", legal);
            workflowInstanceService.createWorkflowInstance("meteringProcess", legal.getId(), variables);
        }
        return Result.OK("提交审核成功!");
    }

    @Override
    public Result<String> batchRevokeByWorkflow(List<String> ids) {
        List<CmMeteringLegal> resultList = new LinkedList<>();
        for (String id : ids) {
            CmMeteringLegal cmMeteringLegal = cmMeteringLegalMapper.selectById(id);
            String state = cmMeteringLegal.getState();
            if (!"4".equals(state) && !"6".equals(state)) {
                return Result.error(cmMeteringLegal.getVersion() + "当前状态不允许撤销");
            }
            resultList.add(cmMeteringLegal);
        }
        for (CmMeteringLegal legal : resultList) {
            Map<String, Object> variables = new HashMap<>();
            variables.put("meteringRejectProcess", legal);
            workflowInstanceService.createWorkflowInstance("meteringRejectProcess", legal.getId(), variables);
        }
        return Result.ok("撤销成功！");
    }

    private List<CmSubjectMappingGroupResult> getMappingGroupByMeteringId(CmMeteringOrgRel orgRel) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("metering_id", orgRel.getMeteringId());
        queryWrapper.eq("organization_id", orgRel.getOrganizationId());
        queryWrapper.orderByAsc("last_subject_code");
        List<SysCategory> categoryList = sysCategoryMapper.selectList(new QueryWrapper<>());

        QueryWrapper wrapper = new QueryWrapper();
        List<CmSubjectMappingGroupResult> mappingGroupResultList = mappingGroupResultService.list(wrapper);
        for (CmSubjectMappingGroupResult mappingResult : mappingGroupResultList) {
            if (StringUtils.isNotBlank(mappingResult.getIsFitBi())) {
                if ("1".equals(mappingResult.getIsFitBi())) {
                    mappingResult.setIsFitBiName("是");
                } else {
                    mappingResult.setIsFitBiName("否");
                }
            }
            if (StringUtils.isNotBlank(mappingResult.getCorrespondBiSubject())) {
                String name = categoryList.stream().filter(o -> o.getId().equals(mappingResult.getCorrespondBiSubject())).collect(Collectors.toList()).get(0).getName();
                mappingResult.setCorrespondBiSubjectName(name);
            }
            if (StringUtils.isNotBlank(mappingResult.getCorrespondBiSubitem())) {
                String name = categoryList.stream().filter(o -> o.getId().equals(mappingResult.getCorrespondBiSubitem())).collect(Collectors.toList()).get(0).getName();
                mappingResult.setCorrespondBiSubitemName(name);
            }
            if (StringUtils.isNotBlank(mappingResult.getCorrespondBiTypicalSubitem())) {
                String name = categoryList.stream().filter(o -> o.getId().equals(mappingResult.getCorrespondBiTypicalSubitem())).collect(Collectors.toList()).get(0).getName();
                mappingResult.setCorrespondBiTypicalSubitemName(name);
            }
        }
        return mappingGroupResultList;
    }

    private void createSheet2Row(Row row, int cellEnd1, String value, CellStyle sheet2Style1) {
        Cell sheetCell1 = row.createCell(cellEnd1);
        sheetCell1.setCellValue(value);
        sheetCell1.setCellStyle(sheet2Style1);
    }

    private List<Map<String, Object>> getSubjectColumnsByOrgType(String orgType, String ledgerAccount, String name) {
        List<Map<String, Object>> columns = new LinkedList<>();

        // 定义 会计科目基本信息 表头
        Map<String, Object> subjectBaseInfo = new LinkedHashMap<>();

        subjectBaseInfo.put("title", "会计科目基本信息");
        List<Map<String, Object>> subjectBaseInfoList = new LinkedList<>();
        if (ledgerAccount.equals("1")) {
            List<Map<String, Object>> column1 = getSubjectColumn1(name);
            subjectBaseInfoList.addAll(column1);
        } else if ("2".equals(ledgerAccount)) {
            List<Map<String, Object>> column1 = getSubjectColumn1(name);
            List<Map<String, Object>> column2 = getSubjectColumn2();
            subjectBaseInfoList.addAll(column1);
            subjectBaseInfoList.addAll(column2);
        } else if ("3".equals(ledgerAccount)) {
            List<Map<String, Object>> column1 = getSubjectColumn1(name);
            List<Map<String, Object>> column2 = getSubjectColumn2();
            List<Map<String, Object>> column3 = getSubjectColumn3();
            subjectBaseInfoList.addAll(column1);
            subjectBaseInfoList.addAll(column2);
            subjectBaseInfoList.addAll(column3);
        } else if ("4".equals(ledgerAccount)) {
            List<Map<String, Object>> column1 = getSubjectColumn1(name);
            List<Map<String, Object>> column2 = getSubjectColumn2();
            List<Map<String, Object>> column3 = getSubjectColumn3();
            List<Map<String, Object>> column4 = getSubjectColumn4();
            subjectBaseInfoList.addAll(column1);
            subjectBaseInfoList.addAll(column2);
            subjectBaseInfoList.addAll(column3);
            subjectBaseInfoList.addAll(column4);
        } else if ("5".equals(ledgerAccount)) {
            List<Map<String, Object>> column1 = getSubjectColumn1(name);
            List<Map<String, Object>> column2 = getSubjectColumn2();
            List<Map<String, Object>> column3 = getSubjectColumn3();
            List<Map<String, Object>> column4 = getSubjectColumn4();
            List<Map<String, Object>> column5 = getSubjectColumn5();
            subjectBaseInfoList.addAll(column1);
            subjectBaseInfoList.addAll(column2);
            subjectBaseInfoList.addAll(column3);
            subjectBaseInfoList.addAll(column4);
            subjectBaseInfoList.addAll(column5);
        }
        subjectBaseInfo.put("children", subjectBaseInfoList);
        columns.add(subjectBaseInfo);
        // 判断是法人还是附属公司
        if ("2".equals(orgType)) {
            // 法人放入G04映射关系
            Map<String, Object> G04Map = new LinkedHashMap<>();
            G04Map.put("title", "G04映射关系");
            List<Map<String, Object>> column1 = getG04Column();
            G04Map.put("children", column1);
            columns.add(G04Map);
        }
        // 放入BI映射关系
        Map<String, Object> BIMap = new LinkedHashMap<>();
        BIMap.put("title", "BI映射关系");
        List<Map<String, Object>> columnBI = getBIColumn();
        BIMap.put("children", columnBI);
        columns.add(BIMap);
        // 放入科目余额
        Map<String, Object> amount1 = new HashMap<>();
        amount1.put("title", "科目余额（借正贷负/万元）");
        amount1.put("align", "center");
        amount1.put("dataIndex", "endBalance");
        amount1.put("width", 120);
        columns.add(amount1);
        // 放入抵销金额（借正贷负/万元）
        Map<String, Object> amount2 = new HashMap<>();
        amount2.put("title", "抵销金额（借正贷负/万元）");
        amount2.put("align", "center");
        amount2.put("dataIndex", "offsetAmount");
        amount2.put("width", 120);
        columns.add(amount2);
        // 放入抵销后余额（借正贷负/万元）
        Map<String, Object> amount3 = new HashMap<>();
        amount3.put("title", "抵销后余额（借正贷负/万元）");
        amount3.put("align", "center");
        amount3.put("dataIndex", "offsetEndAmount");
        amount3.put("width", 120);
        columns.add(amount3);

        return columns;
    }

    private List<Map<String, Object>> getBIColumn() {
        List<Map<String, Object>> columnList = new LinkedList<>();
        Map<String, Object> resultColumn1 = new HashMap<>();
        resultColumn1.put("title", "是否符合BI定义");
        resultColumn1.put("align", "center");
        resultColumn1.put("dataIndex", "isFitBiName");
        resultColumn1.put("ellipsis", true);
        resultColumn1.put("width", 120);
        Map<String, Object> resultColumn2 = new HashMap<>();
        resultColumn2.put("title", "对应BI项目");
        resultColumn2.put("align", "center");
        resultColumn2.put("dataIndex", "correspondBiSubjectName");
        resultColumn2.put("ellipsis", true);
        resultColumn2.put("width", 120);
        Map<String, Object> resultColumn3 = new HashMap<>();
        resultColumn3.put("title", "对应BI子项");
        resultColumn3.put("align", "center");
        resultColumn3.put("dataIndex", "correspondBiSubitemName");
        resultColumn3.put("ellipsis", true);
        resultColumn3.put("width", 120);
        Map<String, Object> resultColumn4 = new HashMap<>();
        resultColumn4.put("title", "对应BI典型子项");
        resultColumn4.put("align", "center");
        resultColumn4.put("dataIndex", "correspondBiTypicalSubitemName");
        resultColumn4.put("ellipsis", true);
        resultColumn4.put("width", 120);
        columnList.add(resultColumn1);
        columnList.add(resultColumn2);
        columnList.add(resultColumn3);
        columnList.add(resultColumn4);
        return columnList;
    }

    private List<Map<String, Object>> getG04Column() {
        List<Map<String, Object>> columnList = new LinkedList<>();
        Map<String, Object> resultColumn1 = new HashMap<>();
        resultColumn1.put("title", "G04一级项目");
        resultColumn1.put("align", "center");
        resultColumn1.put("dataIndex", "g04ProjectOne");
        resultColumn1.put("ellipsis", true);
        resultColumn1.put("width", 120);
        Map<String, Object> resultColumn2 = new HashMap<>();
        resultColumn2.put("title", "G04二级项目");
        resultColumn2.put("align", "center");
        resultColumn2.put("dataIndex", "g04ProjectTwo");
        resultColumn2.put("ellipsis", true);
        resultColumn2.put("width", 120);
        Map<String, Object> resultColumn3 = new HashMap<>();
        resultColumn3.put("title", "G04三级项目");
        resultColumn3.put("align", "center");
        resultColumn3.put("dataIndex", "g04ProjectThree");
        resultColumn3.put("ellipsis", true);
        resultColumn3.put("width", 120);
        columnList.add(resultColumn1);
        columnList.add(resultColumn2);
        columnList.add(resultColumn3);
        return columnList;
    }

    private List<Map<String, Object>> getSubjectColumn5() {
        List<Map<String, Object>> columnList = new LinkedList<>();
        Map<String, Object> resultColumn1 = new HashMap<>();
        resultColumn1.put("title", "五级科目代码");
        resultColumn1.put("align", "center");
        resultColumn1.put("dataIndex", "subjectCodeFive");
        resultColumn1.put("ellipsis", true);
        resultColumn1.put("width", 120);
        Map<String, Object> resultColumn2 = new HashMap<>();
        resultColumn2.put("title", "五级科目名称");
        resultColumn2.put("align", "center");
        resultColumn2.put("dataIndex", "subjectNameFive");
        resultColumn2.put("ellipsis", true);
        resultColumn2.put("width", 120);
        columnList.add(resultColumn1);
        columnList.add(resultColumn2);
        return columnList;
    }

    private List<Map<String, Object>> getSubjectColumn4() {
        List<Map<String, Object>> columnList = new LinkedList<>();
        Map<String, Object> resultColumn1 = new HashMap<>();
        resultColumn1.put("title", "四级科目代码");
        resultColumn1.put("align", "center");
        resultColumn1.put("dataIndex", "subjectCodeFour");
        resultColumn1.put("ellipsis", true);
        resultColumn1.put("width", 120);
        Map<String, Object> resultColumn2 = new HashMap<>();
        resultColumn2.put("title", "四级科目名称");
        resultColumn2.put("align", "center");
        resultColumn2.put("dataIndex", "subjectNameFour");
        resultColumn2.put("ellipsis", true);
        resultColumn2.put("width", 120);
        columnList.add(resultColumn1);
        columnList.add(resultColumn2);
        return columnList;
    }

    private List<Map<String, Object>> getSubjectColumn3() {
        List<Map<String, Object>> columnList = new LinkedList<>();
        Map<String, Object> resultColumn1 = new HashMap<>();
        resultColumn1.put("title", "三级科目代码");
        resultColumn1.put("align", "center");
        resultColumn1.put("dataIndex", "subjectCodeThree");
        resultColumn1.put("ellipsis", true);
        resultColumn1.put("width", 120);
        Map<String, Object> resultColumn2 = new HashMap<>();
        resultColumn2.put("title", "三级科目名称");
        resultColumn2.put("align", "center");
        resultColumn2.put("dataIndex", "subjectNameThree");
        resultColumn2.put("ellipsis", true);
        resultColumn2.put("width", 120);
        columnList.add(resultColumn1);
        columnList.add(resultColumn2);
        return columnList;
    }

    private List<Map<String, Object>> getSubjectColumn2() {
        List<Map<String, Object>> columnList = new LinkedList<>();
        Map<String, Object> resultColumn1 = new HashMap<>();
        resultColumn1.put("title", "二级科目代码");
        resultColumn1.put("align", "center");
        resultColumn1.put("dataIndex", "subjectCodeTwo");
        resultColumn1.put("ellipsis", true);
        resultColumn1.put("width", 120);
        Map<String, Object> resultColumn2 = new HashMap<>();
        resultColumn2.put("title", "二级科目名称");
        resultColumn2.put("align", "center");
        resultColumn2.put("dataIndex", "subjectNameTwo");
        resultColumn2.put("ellipsis", true);
        resultColumn2.put("width", 120);
        columnList.add(resultColumn1);
        columnList.add(resultColumn2);
        return columnList;
    }

    private List<Map<String, Object>> getSubjectColumn1(String name) {
        List<Map<String, Object>> columnList = new LinkedList<>();
        Map<String, Object> resultColumn1 = new HashMap<>();
        resultColumn1.put("title", "一级科目代码");
        resultColumn1.put("align", "center");
        resultColumn1.put("dataIndex", "subjectCodeOne");
        resultColumn1.put("ellipsis", true);
        resultColumn1.put("width", 120);

//        List<Map<String, Object>> children = getBIColumn();
//        Map<String, Object> childrenMap = new LinkedHashMap<>();
//        childrenMap.put("title", name);
//        childrenMap.put("align", "center");
//        childrenMap.put("dataIndex", "XXX");
//        children.add(childrenMap);
//
//        resultColumn1.put("children", childrenMap);



        Map<String, Object> resultColumn2 = new HashMap<>();
        resultColumn2.put("title", "一级科目名称");
        resultColumn2.put("align", "center");
        resultColumn2.put("dataIndex", "subjectNameOne");
        resultColumn2.put("ellipsis", true);
        resultColumn2.put("width", 120);

        columnList.add(resultColumn1);
        columnList.add(resultColumn2);
        return columnList;
    }

    private BigDecimal startComputeResult(String computeWay,
                                          String computeContent,
                                          String cellNum,
                                          CmMeteringLegal meteringLegal,
                                          List<CmWeightedAssetsCalculationConfig> configLists,
                                          BigDecimal profitTotal,
                                          BigDecimal dividendTotal,
                                          CmBasicLegalLossResult lossResult,
                                          BigDecimal assetAmount) {
        BigDecimal value = new BigDecimal("0");

        // 获取主键
        String meteringId = meteringLegal.getId();

        if ("SQL".equals(computeWay)) {
            computeContent = computeContent
                    .replaceAll("meteringId", "'" + meteringId + "'")
                    .replaceAll("lastSecondMeteringYear", "'" + meteringLegal.getLastSecondMeteringYear() + "'")
                    .replaceAll("lastThirdMeteringYear", "'" + meteringLegal.getLastThirdMeteringYear() + "'")
                    .replaceAll("lastTwoAuditVersion", "'" + meteringLegal.getLastTwoAuditVersion() + "'")
                    .replaceAll("lastThreeAuditVersion", "'" + meteringLegal.getLastThreeAuditVersion() + "'")
                    .replaceAll("meteringYear", "'" + meteringLegal.getMeteringYear() + "'");
            String valueStr = cmMeteringLegalMapper.selectSql(computeContent);
            if (StringUtils.isBlank(valueStr)) {
                value = new BigDecimal("0");
            } else {
                value = new BigDecimal(valueStr);
            }
        } else if ("JAVA".equals(computeWay)) {
            if ("D9".equals(cellNum)) {
                // =IFERROR(IF(D23<=800000,D23*0.12,IF(D23<=24000000,96000+(D23-800000)*0.15,3576000+(D23-24000000)*0.18)),0)
                BigDecimal D23 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfig config : configLists) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D23")) {
                        D23 = config.getFirstValue();
                        break;
                    }
                }
                if (D23.compareTo(new BigDecimal("800000")) <= 0) {
                    value = BigDecimal.valueOf(D23.doubleValue() * 0.12).setScale(6, RoundingMode.HALF_UP);
                    //value = D23.multiply(new BigDecimal("0.12"));
                } else if (D23.compareTo(new BigDecimal("24000000")) <= 0) {
                    double valueDou = 96000 + (D23.doubleValue() - 800000) * 0.15;
                    value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);
                    // value = new BigDecimal("96000").add(D23.subtract(new BigDecimal("800000")).multiply(new BigDecimal("0.15")));
                } else {
                    double valueDou = 3576000 + (D23.doubleValue() - 24000000) * 0.18;
                    value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);
                    //value = new BigDecimal("3576000").add(D23.subtract(new BigDecimal("24000000")).multiply(new BigDecimal("0.18")));
                }
            } else if ("D10".equals(cellNum)) {
                BigDecimal D11 = new BigDecimal("0");
                BigDecimal D12 = new BigDecimal("0");
                BigDecimal D13 = new BigDecimal("0");
                BigDecimal D14 = new BigDecimal("0");
                BigDecimal E11 = new BigDecimal("0");
                BigDecimal E12 = new BigDecimal("0");
                BigDecimal E13 = new BigDecimal("0");
                BigDecimal E14 = new BigDecimal("0");
                BigDecimal F11 = new BigDecimal("0");
                BigDecimal F12 = new BigDecimal("0");
                BigDecimal F13 = new BigDecimal("0");
                BigDecimal F14 = new BigDecimal("0");
                // 获取D11, D12, E11, E12, F11, F12的值
                for (CmWeightedAssetsCalculationConfig config : configLists) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D11")) {
                        D11 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D12")) {
                        D12 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D13")) {
                        D13 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D14")) {
                        D14 = config.getFirstValue();
                    }
                    String cellNumSecond = config.getCellNumSecond();
                    if (cellNumSecond.equals("E11")) {
                        E11 = config.getSecondValue();
                    } else if (cellNumFirst.equals("E12")) {
                        E12 = config.getSecondValue();
                    } else if (cellNumFirst.equals("E13")) {
                        E13 = config.getSecondValue();
                    } else if (cellNumFirst.equals("E14")) {
                        E14 = config.getSecondValue();
                    }
                    String cellNumThird = config.getCellNumThird();
                    if (cellNumThird.equals("F11")) {
                        F11 = config.getThirdValue();
                    } else if (cellNumFirst.equals("F12")) {
                        F12 = config.getThirdValue();
                    } else if (cellNumFirst.equals("F13")) {
                        F13 = config.getThirdValue();
                    } else if (cellNumFirst.equals("F14")) {
                        F14 = config.getThirdValue();
                    }
                }
                // =IFERROR(MIN(AVERAGE(ABS(D11-D12),ABS(E11-E12),ABS(F11-F12)),2.25%*AVERAGE(D13:F13))+AVERAGE(D14:F14),0)
                // 计算三个绝对差的平均值
                BigDecimal absTotal = D11.subtract(D12).abs()
                        .add((E11.subtract(E12).abs())).add(F11.subtract(F12).abs());
                double absAvgDou = absTotal.doubleValue() / 3;
                // 计算D13:F13的平均值的2.25%
                double divideDou = (D13.doubleValue()+E13.doubleValue()+F13.doubleValue())/3*2.25/100;
//                BigDecimal divide = (D13.add(E13).add(F13))
//                        .multiply(new BigDecimal("2.25"))
//                        .divide(new BigDecimal("100"))
//                        .divide(new BigDecimal("3"), 6, RoundingMode.HALF_UP);

                // 取较小值
                //BigDecimal min = new BigDecimal("0");
                double min;
                if (absAvgDou<=divideDou) {
                    min = absAvgDou;
                } else {
                    min = divideDou;
                }
                // 加上D14:F14的平均值
                double valueDou = min + (D14.doubleValue() + E14.doubleValue() + F14.doubleValue())/3;
                value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);
                //value = min.add((D14.add(E14).add(F14)).divide(new BigDecimal("3"), 6, RoundingMode.HALF_UP));
            } else if ("D13".equals(cellNum)) {
                value = assetAmount;
            } else if ("D14".equals(cellNum)) {
                value = new BigDecimal("0").subtract(dividendTotal);
            } else if ("D15".equals(cellNum)) {
                // =IFERROR(MAX(AVERAGE(D16:F16),AVERAGE(D17:F17))+MAX(AVERAGE(D18:F18),AVERAGE(D19:F19)),0)
                BigDecimal D16 = new BigDecimal("0");
                BigDecimal D17 = new BigDecimal("0");
                BigDecimal D18 = new BigDecimal("0");
                BigDecimal D19 = new BigDecimal("0");
                BigDecimal E16 = new BigDecimal("0");
                BigDecimal E17 = new BigDecimal("0");
                BigDecimal E18 = new BigDecimal("0");
                BigDecimal E19 = new BigDecimal("0");
                BigDecimal F16 = new BigDecimal("0");
                BigDecimal F17 = new BigDecimal("0");
                BigDecimal F18 = new BigDecimal("0");
                BigDecimal F19 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfig config : configLists) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D16")) {
                        D16 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D17")) {
                        D17 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D18")) {
                        D18 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D19")) {
                        D19 = config.getFirstValue();
                    }
                    String cellNumSecond = config.getCellNumSecond();
                    if (cellNumSecond.equals("E16")) {
                        E16 = config.getSecondValue();
                    } else if (cellNumFirst.equals("E17")) {
                        E17 = config.getSecondValue();
                    } else if (cellNumFirst.equals("E18")) {
                        E18 = config.getSecondValue();
                    } else if (cellNumFirst.equals("E19")) {
                        E19 = config.getSecondValue();
                    }
                    String cellNumThird = config.getCellNumThird();
                    if (cellNumThird.equals("F16")) {
                        F16 = config.getThirdValue();
                    } else if (cellNumFirst.equals("F17")) {
                        F17 = config.getThirdValue();
                    } else if (cellNumFirst.equals("F18")) {
                        F18 = config.getThirdValue();
                    } else if (cellNumFirst.equals("F19")) {
                        F19 = config.getThirdValue();
                    }
                }

                double double16 = (D16.doubleValue() + E16.doubleValue() + F16.doubleValue()) /3;
                double double17 = (D17.doubleValue() + E17.doubleValue() + F17.doubleValue()) /3;
                double maxDou1;
                if (double16 >= double17) {
                    maxDou1 = double16;
                } else {
                    maxDou1 = double17;
                }

                double double18 = (D18.doubleValue() + E18.doubleValue() + F18.doubleValue()) /3;
                double double19 = (D19.doubleValue() + E19.doubleValue() + F19.doubleValue()) /3;
                double maxDou2;
                if (double18 >= double19) {
                    maxDou2 = double18;
                } else {
                    maxDou2 = double19;
                }

                double valueDou = maxDou1 + maxDou2;
                value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);

//                BigDecimal avg16 = (D16.add(E16).add(F16)).divide(new BigDecimal("3"), 6, RoundingMode.HALF_UP);
//                BigDecimal avg17 = (D17.add(E17).add(F17)).divide(new BigDecimal("3"), 6, RoundingMode.HALF_UP);
//                BigDecimal max1 = new BigDecimal("0");
//                if (avg16.compareTo(avg17) > 0) {
//                    max1 = avg16;
//                } else {
//                    max1 = avg17;
//                }
//
//
//                BigDecimal avg18 = (D18.add(E18).add(F18)).divide(new BigDecimal("3"), 6, RoundingMode.HALF_UP);
//                BigDecimal avg19 = (D19.add(E19).add(F19)).divide(new BigDecimal("3"), 6, RoundingMode.HALF_UP);
//                BigDecimal max2 = new BigDecimal("0");
//                if (avg18.compareTo(avg19) > 0) {
//                    max2 = avg18;
//                } else {
//                    max2 = avg19;
//                }
//                value = max1.add(max2);
            } else if ("D19".equals(cellNum)) {
                // =SUMIF('2.1_会计科目映射表'!M:M,"其他经营性支出",'2.1_会计科目映射表'!O:O)+'2.5_损失数据统计表'!C7
                String part1Str = cmMeteringLegalMapper.getD19Part1(meteringLegal);
                BigDecimal part1 = new BigDecimal("0");
                if (StringUtils.isNotBlank(part1Str)) {
                    part1 = new BigDecimal(part1Str);
                }
                BigDecimal lossValueOne = lossResult.getLossValueOne() == null ? new BigDecimal("0") : lossResult.getLossValueOne();
                value = part1.add(lossValueOne);
            } else if ("E19".equals(cellNum)) {
                // 取上年度已审核计量结果+【'2.5_损失数据统计表'!D7】-【'2.5_损失数据统计表'!D8】
                String part1Str = cmMeteringLegalMapper.getE19Part1(meteringLegal.getLastSecondMeteringYear(), meteringLegal.getLastTwoAuditVersion());
                BigDecimal part1 = new BigDecimal("0");
                if (StringUtils.isNotBlank(part1Str)) {
                    part1 = new BigDecimal(part1Str);
                }
                BigDecimal lossValueTwo = lossResult.getLossValueTwo() == null ? new BigDecimal("0") : lossResult.getLossValueTwo();
                BigDecimal lossValuePreTwo = lossResult.getLossValuePreTwo() == null ? new BigDecimal("0") : lossResult.getLossValuePreTwo();
                value = part1.add(lossValueTwo).subtract(lossValuePreTwo);
            } else if ("F19".equals(cellNum)) {
                // 取上年度已审核计量结果+【'2.5_损失数据统计表'!E7】-【'2.5_损失数据统计表'!E8】
                String part1Str = cmMeteringLegalMapper.getE19Part1(meteringLegal.getLastThirdMeteringYear(), meteringLegal.getLastThreeAuditVersion());
                BigDecimal part1 = new BigDecimal("0");
                if (StringUtils.isNotBlank(part1Str)) {
                    part1 = new BigDecimal(part1Str);
                }
                BigDecimal lossValueThree = lossResult.getLossValueThree() == null ? new BigDecimal("0") : lossResult.getLossValueThree();
                BigDecimal lossValuePreThree = lossResult.getLossValuePreThree() == null ? new BigDecimal("0") : lossResult.getLossValuePreThree();
                value = part1.add(lossValueThree).subtract(lossValuePreThree);
            } else if ("D20".equals(cellNum)) {
                // =IFERROR(AVERAGE(ABS(D21),ABS(E21),ABS(F21))+AVERAGE(ABS(D22),ABS(E22),ABS(F22)),0)
                BigDecimal D21 = new BigDecimal("0");
                BigDecimal D22 = new BigDecimal("0");
                BigDecimal E21 = new BigDecimal("0");
                BigDecimal E22 = new BigDecimal("0");
                BigDecimal F21 = new BigDecimal("0");
                BigDecimal F22 = new BigDecimal("0");

                for (CmWeightedAssetsCalculationConfig config : configLists) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D21")) {
                        D21 = config.getFirstValue().abs();
                    } else if (cellNumFirst.equals("D22")) {
                        D22 = config.getFirstValue().abs();
                    }
                    String cellNumSecond = config.getCellNumSecond();
                    if (cellNumSecond.equals("E21")) {
                        E21 = config.getSecondValue().abs();
                    } else if (cellNumFirst.equals("E22")) {
                        E22 = config.getSecondValue().abs();
                    }
                    String cellNumThird = config.getCellNumThird();
                    if (cellNumThird.equals("F21")) {
                        F21 = config.getThirdValue().abs();
                    } else if (cellNumFirst.equals("F22")) {
                        F22 = config.getThirdValue().abs();
                    }
//                    BigDecimal avg21 = (D21.add(E21).add(F21)).divide(new BigDecimal("3"), 6, RoundingMode.HALF_UP);
//                    BigDecimal avg22 = (D22.add(E22).add(F22)).divide(new BigDecimal("3"), 6, RoundingMode.HALF_UP);
//                    value = avg21.add(avg22);
                }
                BigDecimal avg21 = D21.abs().add(E21.abs()).add(F21.abs());
                BigDecimal avg22 = D22.abs().add(E22.abs()).add(F22.abs());
                double valueDou = avg21.doubleValue()/3 + avg22.doubleValue()/3;
                value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);
            } else if ("D21".equals(cellNum)) {
                value = new BigDecimal("0").subtract(profitTotal);
            } else if ("D22".equals(cellNum)) {
                // =-SUMIF('2.1_会计科目映射表'!L:L,"金融",'2.1_会计科目映射表'!O:O)-D21
                BigDecimal D21 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfig config : configLists) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D21")) {
                        D21 = config.getFirstValue();
                        break;
                    }
                }
                // 获取金融部份
                String part1 = cmMeteringLegalMapper.getD22Part1(meteringLegal.getId());
                if (StringUtils.isBlank(part1)) {
                    part1 = "0";
                }
                value = new BigDecimal("0").subtract(new BigDecimal(part1)).subtract(D21);
            } else if ("D23".equals(cellNum)) {
                // =IFERROR(D10+D15+D20,0)
                BigDecimal D10 = new BigDecimal("0");
                BigDecimal D15 = new BigDecimal("0");
                BigDecimal D20 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfig config : configLists) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D10")) {
                        D10 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D15")) {
                        D15 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D20")) {
                        D20 = config.getFirstValue();
                    }
                }
                value = D10.add(D15).add(D20);
            } else if ("D24".equals(cellNum)) {
                // =IFERROR(D25*15,0)
                BigDecimal D25 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfig config : configLists) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D25")) {
                        D25 = config.getFirstValue();
                        break;
                    }
                }
                value = D25.multiply(new BigDecimal("15"));
            } else if ("D25".equals(cellNum)) {
                // ='2.5_损失数据统计表'!C9
                BigDecimal lossValueAverage = lossResult.getLossValueAverage() == null ? new BigDecimal("0") : lossResult.getLossValueAverage();
                value = lossValueAverage;
            } else if ("D26".equals(cellNum)) {
                // =MAX(D27,D28)
                BigDecimal D27 = new BigDecimal("0");
                BigDecimal D28 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfig config : configLists) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D27")) {
                        D27 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D28")) {
                        D28 = config.getFirstValue();
                    }
                }
                if (D27.compareTo(D28) > 0) {
                    value = D27;
                } else {
                    value = D28;
                }
            } else if ("D27".equals(cellNum)) {
                // =IF(D24=0,1,LN(EXP(1)-1+(D24/D9)^0.8))
                BigDecimal D24 = new BigDecimal("0");
                BigDecimal D9 = new BigDecimal("0");

                for (CmWeightedAssetsCalculationConfig config : configLists) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D24")) {
                        D24 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D9")) {
                        D9 = config.getFirstValue();
                    }
                }
                if (D24.compareTo(new BigDecimal("0")) == 0) {
                    value = new BigDecimal("1");
                } else {

                    double valueDou = Math.log(Math.exp(1) - 1 + Math.pow(D24.doubleValue() / D9.doubleValue(), 0.8));
                    value = BigDecimal.valueOf(valueDou).setScale(6, RoundingMode.HALF_UP);
                    /*BigDecimal ratio = D24.divide(D9);
                    BigDecimal powValue = BigDecimal.valueOf(Math.pow(ratio.doubleValue(), new BigDecimal("0.8").doubleValue()));

                    // EXP(1)-1 + powValue
                    BigDecimal E = new BigDecimal("2.718281828459045");

                    BigDecimal expPart = E.subtract(new BigDecimal("1"));
                    BigDecimal sum = expPart.add(powValue);
                    //value = BigDecimalMath.ln(sum);
                    value = new BigDecimal(Math.log(sum.doubleValue()));*/
                }
            } else if ("D29".equals(cellNum)) {
                // =D9*D26
                BigDecimal D9 = new BigDecimal("0");
                BigDecimal D26 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfig config : configLists) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D9")) {
                        D9 = config.getFirstValue();
                    } else if (cellNumFirst.equals("D26")) {
                        D26 = config.getFirstValue();
                    }
                }
                value = D9.multiply(D26);
            } else if ("D30".equals(cellNum)) {
                // =D29*12.5
                BigDecimal D29 = new BigDecimal("0");
                for (CmWeightedAssetsCalculationConfig config : configLists) {
                    String cellNumFirst = config.getCellNumFirst();
                    if (cellNumFirst.equals("D29")) {
                        D29 = config.getFirstValue();
                        break;
                    }
                }
                value = D29.multiply(new BigDecimal("12.5"));
            }
        } else {

        }
        return value;
    }

    public static void main(String[] args) {

        BigDecimal D24 = new BigDecimal("100");
        BigDecimal D9 = new BigDecimal("3");

        double valueDou = Math.log(Math.exp(1) - 1 + Math.pow(D24.doubleValue() / D9.doubleValue(), 0.8));
        System.out.println(valueDou);
        System.out.println(Math.exp(1) - 1);
        System.out.println(Math.pow(D24.doubleValue() / D9.doubleValue(), 0.8));

    }


    // 辅助数学计算类
    // private static final MathContext MC = new MathContext(16, RoundingMode.HALF_UP);
    /*public static class BigDecimalMath {
        public static BigDecimal pow(BigDecimal x, BigDecimal y) {
            return new BigDecimal(Math.pow(x.doubleValue(), y.doubleValue()));
        }

//        public static BigDecimal ln(BigDecimal x, MathContext mc) {
//            return new BigDecimal(Math.log(x.doubleValue()), mc);
//        }
        public static BigDecimal ln(BigDecimal x) {
            return new BigDecimal(Math.log(x.doubleValue()));
        }
    }*/

    private CellStyle createNormalStyle2(Workbook workbook,
                                         String isBold,
                                         String position,
                                         String backgroundColor,
                                         String borderTop,
                                         String borderLeft,
                                         String borderBottom,
                                         String borderRight,
                                         String indention) {
        CellStyle style = workbook.createCellStyle();
        if (position.equals("center")) {
            style.setAlignment(HorizontalAlignment.CENTER);
        } else if (position.equals("left")) {
            style.setAlignment(HorizontalAlignment.LEFT);
        } else {
            style.setAlignment(HorizontalAlignment.RIGHT);
        }
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        style.setWrapText(true);
        // 设置边框
        if (borderTop.equals("1")) {
            style.setBorderTop(BorderStyle.THIN);
        } else {
            style.setBorderTop(BorderStyle.MEDIUM);
        }
        if (borderLeft.equals("1")) {
            style.setBorderLeft(BorderStyle.THIN);
        } else {
            style.setBorderLeft(BorderStyle.MEDIUM);
        }
        if (borderBottom.equals("1")) {
            style.setBorderBottom(BorderStyle.THIN);
        } else {
            style.setBorderBottom(BorderStyle.MEDIUM);
        }
        if (borderRight.equals("1")) {
            style.setBorderRight(BorderStyle.THIN);
        } else {
            style.setBorderRight(BorderStyle.MEDIUM);
        }
        // 设置字体
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short)10);
        font.setColor(IndexedColors.WHITE.getIndex());
        if (isBold.equals("1")) {
            font.setBold(true);
        }
        style.setFont(font);

        if (StringUtils.isNotBlank(backgroundColor)) {
            // IndexedColors.BLACK.getIndex()
            style.setFillForegroundColor(Short.parseShort(backgroundColor));
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        }

        if (StringUtils.isNotBlank(indention)) {
            style.setIndention(Short.parseShort(indention));
        }

        return style;
    }

    private CellStyle createNormalStyle(Workbook workbook,
                                        String isBold,
                                        String position,
                                        String backgroundColor,
                                        String borderTop,
                                        String borderLeft,
                                        String borderBottom,
                                        String borderRight,
                                        String indention) {
        CellStyle style = workbook.createCellStyle();
        if (position.equals("center")) {
            style.setAlignment(HorizontalAlignment.CENTER);
        } else if (position.equals("left")) {
            style.setAlignment(HorizontalAlignment.LEFT);
        } else {
            style.setAlignment(HorizontalAlignment.RIGHT);
        }
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        style.setWrapText(true);
        // 设置边框
        if (borderTop.equals("1")) {
            style.setBorderTop(BorderStyle.THIN);
        } else {
            style.setBorderTop(BorderStyle.MEDIUM);
        }
        if (borderLeft.equals("1")) {
            style.setBorderLeft(BorderStyle.THIN);
        } else {
            style.setBorderLeft(BorderStyle.MEDIUM);
        }
        if (borderBottom.equals("1")) {
            style.setBorderBottom(BorderStyle.THIN);
        } else {
            style.setBorderBottom(BorderStyle.MEDIUM);
        }
        if (borderRight.equals("1")) {
            style.setBorderRight(BorderStyle.THIN);
        } else {
            style.setBorderRight(BorderStyle.MEDIUM);
        }
        // 设置字体
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short)10);
        if (isBold.equals("1")) {
            font.setBold(true);
        }
        style.setFont(font);

        if (StringUtils.isNotBlank(backgroundColor)) {
             style.setFillForegroundColor(Short.parseShort(backgroundColor));
             style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        }

        if (StringUtils.isNotBlank(indention)) {
            style.setIndention(Short.parseShort(indention));// 首行缩进
        }

        return style;
    }

    private CellStyle createTitleStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
        // 设置边框
        style.setBorderTop(BorderStyle.THICK);
        style.setBorderBottom(BorderStyle.THICK);
        style.setBorderLeft(BorderStyle.THICK);
        style.setBorderRight(BorderStyle.THICK);

        // 设置字体
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short)10);
        //font.setColor(IndexedColors.WHITE.getIndex());

        // 设置背景色
        // style.setFillForegroundColor(IndexedColors.BLUE1.getIndex());
        // style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setFont(font);

        return style;
    }

    private CellStyle createTableStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);

        // 设置边框
//        style.setBorderTop(BorderStyle.THIN);
//        style.setBorderBottom(BorderStyle.THIN);
//        style.setBorderLeft(BorderStyle.THIN);
//        style.setBorderRight(BorderStyle.THIN);

        // 设置字体
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short)16);
        font.setBold(true);
        //font.setColor(IndexedColors.WHITE.getIndex());

        // 设置背景色
        style.setFillForegroundColor(IndexedColors.BLUE1.getIndex());
        // style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setFont(font);

        return style;
    }

    private static void mergeCells(Sheet sheet, int firstRow, int lastRow,
                                   int firstCol, int lastCol, String value, CellStyle style) {
        sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));

        // 为合并区域的所有单元格设置边框
        for (int rowNum = firstRow; rowNum <= lastRow; rowNum++) {
            Row row = sheet.getRow(rowNum) == null ? sheet.createRow(rowNum) : sheet.getRow(rowNum);
            row.setHeightInPoints(20);
            for (int colNum = firstCol; colNum <= lastCol; colNum++) {
                Cell cell = row.createCell(colNum);
                if (style != null) {
                    cell.setCellStyle(style);
                }
                if (rowNum == firstRow && colNum == firstCol) {
                    cell.setCellValue(value);
                }
            }
        }
    }

    private void getReportList(Map<String, Object> result,
                               String meteringFlag,
                               String isLossDataCheck) {
        // 获取操纵风险加权资产测算表空数据
        if (meteringFlag.equals("2")) {
            List<CmWeightedAssetsCalculationConfig> configList = configService.queryListByCode(false, "0");
            result.put("weightedAssets", configList);
        } else {
            List<CmWeightedAssetsCalculationConfigGroup> configList = queryGroupConfigList(isLossDataCheck);
            result.put("weightedAssets", configList);
        }


        // 获取生息资产表表头（就是空的，不需要查询）

        // 获取法人损失数据统计表
        List<Map<String, Object>> lossDataList = new LinkedList<>();
//        Map<String, Object> lossDataMap1 = new HashMap<>();
//        lossDataMap1.put("subjectName", "由操风造成的损失");
//        Map<String, Object> lossDataMap2 = new HashMap<>();
//        lossDataMap2.put("subjectName", "由操风造成的损失（上年度）");
//        Map<String, Object> lossDataMap3 = new HashMap<>();
//        lossDataMap3.put("subjectName", "近10年操风损失的算数平均值");
//        Map<String, Object> lossDataMap4 = new HashMap<>();
//        lossDataMap4.put("subjectName", "监管给定的ILM");
//        lossDataMap4.put("lastFirstYearValue", "");
//        lossDataList.add(lossDataMap1);
//        lossDataList.add(lossDataMap2);
//        lossDataList.add(lossDataMap3);
//        lossDataList.add(lossDataMap4);
        result.put("lossDataList", lossDataList);

        // 交易账簿净损益统计表
        List<Map<String, Object>> accountBookList = new LinkedList<>();
        Map<String, Object> accountBookMap1 = new HashMap<>();
        accountBookMap1.put("dataSource", "会计科目映射表");
        Map<String, Object> accountBookMap2 = new HashMap<>();
        accountBookMap2.put("dataSource", "交易账簿业务系统");
        Map<String, Object> accountBookMap3 = new HashMap<>();
        accountBookMap3.put("dataSource", "合计");
        accountBookList.add(accountBookMap1);
        accountBookList.add(accountBookMap2);
        accountBookList.add(accountBookMap3);
        result.put("accountBookList", accountBookList);

        // 股利收入统计表
        List<Map<String, Object>> dividendList = new LinkedList<>();
        Map<String, Object> dividendMap1 = new HashMap<>();
        dividendMap1.put("dataSource", "会计科目映射表");
        Map<String, Object> dividendMap2 = new HashMap<>();
        dividendMap2.put("dataSource", "子公司股利剔除项");
        Map<String, Object> dividendMap3 = new HashMap<>();
        dividendMap3.put("dataSource", "合计");
        dividendList.add(dividendMap1);
        dividendList.add(dividendMap2);
        dividendList.add(dividendMap3);
        result.put("dividendList", dividendList);
    }

    private List<CmWeightedAssetsCalculationConfigGroup> queryGroupConfigList(String isLossDataCheck) {
        QueryWrapper wrapper = new QueryWrapper();
        CmWeightedAssetsCalculationConfigGroup configGroup = new CmWeightedAssetsCalculationConfigGroup();
        if ("2".equals(isLossDataCheck)) {
            wrapper.eq("project_name", "均未验收");
            configGroup = configGroupService.getOne(wrapper);
        } else if ("3".equals(isLossDataCheck)) {
            wrapper.eq("project_name", "部分验收");
            configGroup = configGroupService.getOne(wrapper);
        } else if ("4".equals(isLossDataCheck)) {
            wrapper.eq("project_name", "全部验收");
            configGroup = configGroupService.getOne(wrapper);
        }
        List<CmWeightedAssetsCalculationConfigGroup> resultList = new ArrayList<>();
        if (configGroup != null && StringUtils.isNotBlank(configGroup.getId())) {
            resultList = configGroupService.queryListByParentCode(false, configGroup.getId());
        }
        return resultList;
    }

    private Map<String, Object> getCMVersionByMetering(CmMeteringLegal cmMeteringLegal, String isMetering) {
        cmMeteringLegal = cmMeteringLegalMapper.selectById(cmMeteringLegal.getId());
        Map<String, Object> resultMap = new HashMap<>();
        // 获取审计版本
        String auditVersion = cmMeteringLegal.getAuditVersion();
        String auditVersionCode = "1";
        if("审计后".equals(auditVersion)) {
            auditVersionCode = "2";
        }
        // 获取年份
        String meteringYear = cmMeteringLegal.getMeteringYear();
        // 获取状态
        String state = cmMeteringLegal.getState();
        // 获取口径
        String meteringFlag = cmMeteringLegal.getMeteringFlag();

        if (state.equals("1") || state.equals("2") || "1".equals(isMetering)) {
            // 获取启用状态的会计科目映射表->法人口径
            String report1 = cmMeteringLegalMapper.getReport1(auditVersion, meteringYear);
            if (StringUtils.isBlank(report1)) {
                resultMap.put("report1", "X");
            } else {
                resultMap.put("report1", report1);
            }

            // 获取启用状态的损益类科目余额->法人口径
            String report2 = cmMeteringLegalMapper.getReport2(auditVersion, meteringYear);
            if (auditVersion.equals("审计后") && StringUtils.isBlank(report2)) {
                report2 = cmMeteringLegalMapper.getReport2("审计前", meteringYear);
            }
            if (StringUtils.isBlank(report2)) {
                resultMap.put("report2", "X");
            } else {
                resultMap.put("report2", report2);
            }

            // 获取启用状态的生息资产->法人口径
            String report3 = cmMeteringLegalMapper.getReport3(auditVersion, meteringYear);
            if (auditVersion.equals("审计后") && StringUtils.isBlank(report3)) {
                report3 = cmMeteringLegalMapper.getReport3("审计前", meteringYear);
            }
            if (StringUtils.isBlank(report3)) {
                resultMap.put("report3", "X");
            } else {
                resultMap.put("report3", report3);
            }

            // 获取启用状态的子公司股利剔除项->法人口径
            String report4 = cmMeteringLegalMapper.getReport4(auditVersion, meteringYear);
            if (auditVersion.equals("审计后") && StringUtils.isBlank(report4)) {
                report4 = cmMeteringLegalMapper.getReport4("审计前", meteringYear);
            }
            if (StringUtils.isBlank(report4)) {
                resultMap.put("report4", "X");
            } else {
                resultMap.put("report4", report4);
            }

            // 获取启用状态的交易账簿净损益->法人口径
            String report5 = cmMeteringLegalMapper.getReport5(auditVersion, meteringYear, auditVersionCode);
            if (auditVersion.equals("审计后") && StringUtils.isBlank(report5)) {
                resultMap.put("report5", "X");
            } else {
                resultMap.put("report5", report5);
            }

            // 获取启用状态的损失数据->法人口径
            String report6 = cmMeteringLegalMapper.getReport6(auditVersionCode, meteringYear);
            if (auditVersion.equals("审计后") && StringUtils.isBlank(report6)) {
                report6 = cmMeteringLegalMapper.getReport6("1", meteringYear);
            }
            if (StringUtils.isBlank(report6)) {
                resultMap.put("report6", "X");
            } else {
                resultMap.put("report6", report6);
            }

            // 如果是集团口径
            if ("1".equals(meteringFlag)) {
                // 获取启用状态的会计科目映射表->集团口径（伪造数据）

                List<CmVersionCompany> report7 = cmMeteringLegalMapper.getReport7(meteringYear);
                resultMap.put("report7", report7 == null ? new ArrayList<>() : report7);

                // 获取损益类科目余额->集团口径
                List<CmVersionCompany> report8 = cmMeteringLegalMapper.getReport8(auditVersionCode, meteringYear);
                // 需要区分审计前和审计后
                if (auditVersion.equals("审计后")) {
                    for (CmVersionCompany company : report8) {
                        String version = company.getVersion();
                        if ("X".equals(version)) {
                            // 查询审计前的
                            CmVersionCompany auditBefore = cmMeteringLegalMapper.getReport8Before("1", meteringYear, company.getOrganizationId());
                            if (!"X".equals(auditBefore)) {
                                company.setVersion(auditBefore.getVersion());
                                company.setId(auditBefore.getId());
                            }
                        }
                    }
                }
                resultMap.put("report8", report8);

                // 获取生息资产->集团口径
                String report9 = cmMeteringLegalMapper.getReport9(meteringYear, auditVersionCode);
                if (auditVersion.equals("审计后") && StringUtils.isBlank(report9)) {
                    report9 = cmMeteringLegalMapper.getReport9(meteringYear, "1");
                }
                if (StringUtils.isBlank(report9)) {
                    resultMap.put("report9", "X");
                } else {
                    resultMap.put("report9", report9);
                }

                // 获取集团内往来抵销数据
                String report10 = cmMeteringLegalMapper.getReport10(auditVersionCode, meteringYear);
                if (auditVersion.equals("审计后") && StringUtils.isBlank(report10)) {
                    report10 = cmMeteringLegalMapper.getReport10("1", meteringYear);
                }
                if (StringUtils.isBlank(report10)) {
                    resultMap.put("report10", "X");
                } else {
                    resultMap.put("report10", report10);
                }

                // 获取交易账簿净损益->集团口径
                List<CmVersionCompany> report11 = cmMeteringLegalMapper.getReport11(auditVersionCode, meteringYear);
                // 需要区分审计前和审计后
                if (auditVersion.equals("审计后")) {
                    for (CmVersionCompany company : report11) {
                        String version = company.getVersion();
                        if ("X".equals(version)) {
                            // 查询审计前的
                            CmVersionCompany auditBefore = cmMeteringLegalMapper.getReport11Before("1", meteringYear, company.getOrganizationId());
                            if (!"X".equals(auditBefore)) {
                                company.setVersion(auditBefore.getVersion());
                                company.setId(auditBefore.getId());
                            }
                        }
                    }
                }
                resultMap.put("report11", report11);

                // 获取损失数据->集团口径
                String report12 = cmMeteringLegalMapper.getReport12(auditVersionCode, meteringYear);
                if (auditVersion.equals("审计后") && StringUtils.isBlank(report12)) {
                    report12 = cmMeteringLegalMapper.getReport12("1", meteringYear);
                }
                if (StringUtils.isBlank(report12)) {
                    resultMap.put("report12", "X");
                } else {
                    resultMap.put("report12", report12);
                }
            }
        } else {
            // 使用已经固定的版本
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("metering_id", cmMeteringLegal.getId());
            List<CmMeteringTableRel> relList = relService.list(wrapper);
            for (CmMeteringTableRel rel : relList) {
                String report = rel.getReport();
                if (report.equals("report7") || report.equals("report8") || report.equals("report11")) {
                    String version = rel.getVersion();
                    if (StringUtils.isNotBlank(version)) {
                        List<CmVersionCompany> list = JSONArray.parseArray(version, CmVersionCompany.class);
                        resultMap.put(rel.getReport(), list);
                    }
                } else {
                    resultMap.put(rel.getReport(), rel.getVersion());
                }
            }
        }

        // 查询
        return resultMap;
    }
}
