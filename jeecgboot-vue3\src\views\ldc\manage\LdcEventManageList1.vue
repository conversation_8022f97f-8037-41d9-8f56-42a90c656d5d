<template>
  <div class="p-2" v-if="isShowList">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
        <a-col :lg="6">
        <a-form-item name="eventCode">
        <template #label><span title="事件编号">事件编号</span></template>
            <a-input v-model:value="queryParam.eventCode" allow-clear/>
        </a-form-item>
        </a-col>
          <a-col :lg="6">
            <a-form-item name="eventClassifyFirst">
              <template #label><span title="事件分类(一级)">事件分类</span></template>
              <a-input placeholder="请输入事件分类(一级)" v-model:value="queryParam.eventClassifyFirst" allow-clear ></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :lg="6">
              <a-form-item name="eventClassifySecond">
                <template #label><span title="事件分类(二级)">事件分类</span></template>
                <a-input placeholder="请输入事件分类(二级)" v-model:value="queryParam.eventClassifySecond" allow-clear ></a-input>
              </a-form-item>
            </a-col>
          <a-col :lg="6">
          <a-form-item name="eventName">
          <template #label><span title="事件名称">事件名称</span></template>
              <a-input v-model:value="queryParam.eventName" allow-clear/>
          </a-form-item>
          </a-col>
            <a-col :lg="6">
              <a-form-item name="isEnd">
                <template #label><span title="是否结束">是否结束</span></template>
                <j-select-multiple placeholder="请选择是否结束" v-model:value="queryParam.isEnd" dictCode="whether" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="status">
                <template #label><span title="状态">状态</span></template>
                <a-input placeholder="请输入状态" v-model:value="queryParam.status" allow-clear ></a-input>
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="isLossEvent">
                <template #label><span title="是否为损失事件">是否为损</span></template>
                <j-select-multiple placeholder="请选择是否为损失事件" v-model:value="queryParam.isLossEvent" dictCode="whether" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="isRemove">
                <template #label><span title="是否经监管认可剔除">是否经监</span></template>
                <j-select-multiple placeholder="请选择是否经监管认可剔除" v-model:value="queryParam.isRemove" dictCode="whether" allow-clear />
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'manage:ldc_event_manage:add'"  @click="handleAdd" preIcon="ant-design:plus-outlined"> 新建</a-button>
        <a-button type="default" @click="handleCopyAdd" preIcon="ant-design:copy-outlined"> 复制新建</a-button>
        <a-button type="default" @click="handleSubmit" preIcon="ant-design:check-circle-outlined"> 提交</a-button>
        <a-button type="default" @click="handleRepeal" preIcon="ant-design:delete-outlined"> 废止</a-button>
        <a-button v-if="isShowMergeButton" type="default" @click="handleMerge" preIcon="ant-design:plus-outlined"> 合并事件</a-button>
        <a-button type="default" @click="handleWithdraw" preIcon="ant-design:rollback-outlined"> 撤回</a-button>
      

        <!-- <div style="text-align: right; margin-right: auto;">
      
          <a-button  type="default" @click="downloadDocument" preIcon="ant-design:paper-clip-outlined"> 附件下载</a-button>

        <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="exportAllColumn">
                全字段导出
              </a-menu-item>
              <a-menu-item key="2" @click="exportStreamlineColumn">
                精简版导出
              </a-menu-item>
            </a-menu>
          </template>
          <a-button style="margin-left: 5px; margin-right: 5px;" type="default" v-auth="'manage:ldc_event_manage:exportXls'" preIcon="ant-design:export-outlined"> 事件下载</a-button>
        </a-dropdown>
        <j-upload-button type="primary" v-auth="'manage:ldc_event_manage:importExcel'"  preIcon="ant-design:import-outlined" @click="onImportXls">事件上传</j-upload-button>
        </div> -->
        </template>
        
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"/>
      </template>
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
      <template #toolbar>
        <a-button  type="default" @click="downloadDocument" preIcon="ant-design:paper-clip-outlined"> 附件下载</a-button>

        <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="exportAllColumn">
                全字段导出
              </a-menu-item>
              <a-menu-item key="2" @click="exportStreamlineColumn">
                精简版导出
              </a-menu-item>
            </a-menu>
          </template>
          <a-button style="margin-left: 5px; margin-right: 5px;" type="default" v-auth="'manage:ldc_event_manage:exportXls'" preIcon="ant-design:export-outlined"> 事件下载</a-button>
        </a-dropdown>
                <j-upload-button type="primary" v-auth="'manage:ldc_event_manage:importExcel'"  preIcon="ant-design:import-outlined" @click="onImportXls">事件上传</j-upload-button>

      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <LdcEventManageModal ref="registerModal" @success="handleSuccess"></LdcEventManageModal>
  </div>
  <LdcEventManageForm @manageBack="manageBack" v-if="isShow1" :operateFlag="operateFlag" :manageInfo="manageInfo"></LdcEventManageForm>
  <LdcEventManageMergerForm @manageBack="manageBack" v-if="isShow2" :manageMergerInfo="manageMergerInfo"></LdcEventManageMergerForm>
  <LdcEventHistoryListModal ref="registerModal2"></LdcEventHistoryListModal>
  <LdcEventManageViewForm  @manageBack="manageBack" v-if="isShow3" :manageInfo="manageInfo"></LdcEventManageViewForm>
</template>

<script lang="ts" name="manage-ldcEventManage" setup>
  import { ref, reactive, onBeforeMount } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, superQuerySchema } from './LdcEventManage.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, 
    submitFormList, handleWithdrawToFirst, judgeCanMerge, getEventCode, 
    updateStatusToUpdate, batchRepeal, downloadZip, lockEvent } from './LdcEventManage.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import LdcEventManageModal from './components/LdcEventManageModal.vue'
  import { useUserStore } from '/@/store/modules/user';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
  import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
  import JSelectUser from '/@/components/Form/src/jeecg/components/JSelectUser.vue';
  import JInput from "/@/components/Form/src/jeecg/components/JInput.vue";
  import LdcEventManageForm from './components/LdcEventManageForm.vue';
  import LdcEventManageMergerForm from './components/LdcEventManageMergerForm.vue';
  import { message } from 'ant-design-vue';
  import LdcEventHistoryListModal from '../operateHistory/LdcEventHistoryListModal.vue';
  import LdcEventManageViewForm from './components/LdcEventManageViewForm.vue';
  import { Modal } from 'ant-design-vue';
  import { useMethods } from '/@/hooks/system/useMethods';

  const { handleExportXls } = useMethods();
  const isShowList = ref<boolean>(true);
  const isShow1 = ref<boolean>(false);
  const isShow2 = ref<boolean>(false);
  const isShow3 = ref<boolean>(false);
  const operateFlag = ref("");
  const manageInfo = ref({});
  const manageMergerInfo = ref({});
  const isShowMergeButton = ref<boolean>(false);
  const roleList = ref([]);

  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const registerModal2 = ref();
  const userStore = useUserStore();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '损失事件管理表',
      api: list,
      columns,
      canResize:false,
      useSearchForm: false,
      showIndexColumn: true,
      showTableSetting: false,
      actionColumn: {
        width: 250,
        fixed: 'right',
      },
      beforeFetch: async (params) => {
        //queryParam.isHidden = '0';
        queryParam.isShowPage1 = '1';
        queryParam.isParticipateProcess = '1';
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: "损失事件管理表",
      url: getExportUrl,
      params: queryParam,
    },
	  importConfig: {
	    url: getImportUrl,
	    success: handleSuccess
	  },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] = tableContext;
  const labelCol = reactive({
    xs:24,
    sm:4,
    xl:6,
    xxl:4
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    operateFlag.value = '1';
    isShowList.value = false;
    isShow1.value = true;


    // registerModal.value.disableSubmit = false;
    // registerModal.value.add();
  }
  
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    const backResult = ['7', '8', '10', '20', '9', '21', '22', '23', '24', '25'];
    if (backResult.indexOf(record.status) > -1) {
      Modal.confirm({
        title: '确认编辑',
        content: '确认编辑事件将锁定无法再被撤回，是否确认？',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {  // 确认回调
         
          const res = await lockEvent(record);
          if (res.success) {
            record.status_dictText = '审核退回修改中';
            operateFlag.value = '2';
            manageInfo.value = record;
            isShowList.value = false;
            isShow1.value = true;
          } else {
            message.warning("锁定异常")
          }
        },
        onCancel() {  // 取消回调
          console.log('取消重置操作');
        }
      });
    } else {
      operateFlag.value = '2';
      manageInfo.value = record;
      isShowList.value = false;
      isShow1.value = true;
    }

    // registerModal.value.disableSubmit = false;
    // registerModal.value.edit(record);
  }

  /**
   * 查看
   * @param record
   */
  function handleView(record: Recordable) {
    manageInfo.value = record;
    isShowList.value = false;
    isShow3.value = true;
  }
   
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }
   
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
   
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
   
  /**
   * 成功回调
   */
  function handleSuccess() {
    isShow1.value = false;
    isShow2.value = false;
    isShow3.value = false;
    isShowList.value = true;

    (selectedRowKeys.value = []) && reload();
  }

  function judgeCanEdit(record) {
    let isConBackStatus = true;
    if (record.processFlag == '1' && roleList.value.includes("fh_cfzg")) {
      isConBackStatus = false;
    } else if (record.processFlag == '1' && roleList.value.includes("fhbm_cfglg")) {
      isConBackStatus = false;
    } else if (record.processFlag == '2' && roleList.value.includes("zhgbm_cfglh")) {
      isConBackStatus = false;
    } else if (record.processFlag == '3' && roleList.value.includes("zh_ldc_glg")) {
      isConBackStatus = false;
    } else if (record.processFlag == '4' && roleList.value.includes("zgs_cfzg")) {
      isConBackStatus = false;
    } else if (record.processFlag == '5' && roleList.value.includes("czyh_cfzg")) {
      isConBackStatus = false;
    } else if (record.processFlag == '6' && roleList.value.includes("zh_ldc_shg")) {
      isConBackStatus = false;
    } else if (record.processFlag == '7' && roleList.value.includes("fh_cfshg")) {
      isConBackStatus = false;
    }
    return isConBackStatus;
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    const backResult = ['7', '8', '10', '20', '9', '21', '22', '23', '24', '25'];
    let isConBackStatus = true;
    if (backResult.indexOf(record.status) > -1) {
      isConBackStatus = false;
      if (record.opinionType == '1') {
        isConBackStatus = judgeCanEdit(record);
      } else {
        isConBackStatus = true;
      }
    } else {
      if (record.status == '1' || record.status == '11' || record.status == '27') {
        isConBackStatus = judgeCanEdit(record);
      } else {
        isConBackStatus = true;
      }
    }

    let isDisableUpdateButton = true;
    // 是否可以更新
    if(record.status == '6') {
      let nodeRole = '';
      if (record.processFlag == '1' && roleList.value.includes("fh_cfzg")) {
        isDisableUpdateButton = false;
        nodeRole = 'fh_cfzg';
      } else if (record.processFlag == '1' && roleList.value.includes("fhbm_cfglg")) {
        isDisableUpdateButton = false;
        nodeRole = 'fhbm_cfglg';
      } else if (record.processFlag == '2' && roleList.value.includes("zhgbm_cfglh")) {
        isDisableUpdateButton = false;
        nodeRole = 'zhgbm_cfglh';
      } else if (record.processFlag == '3' && roleList.value.includes("zh_ldc_glg")) {
        isDisableUpdateButton = false;
        nodeRole = 'zh_ldc_glg';
      } else if (record.processFlag == '4' && roleList.value.includes("zgs_cfzg")) {
        isDisableUpdateButton = false;
        nodeRole = 'zgs_cfzg';
      } else if (record.processFlag == '5' && roleList.value.includes("czyh_cfzg")) {
        isDisableUpdateButton = false;
        nodeRole = 'czyh_cfzg';
      } else if (record.processFlag == '6' && roleList.value.includes("zh_ldc_shg")) {
        isDisableUpdateButton = false;
        nodeRole = 'zh_ldc_shg';
      } else if (record.processFlag == '7' && roleList.value.includes("fh_cfshg")) {
        isDisableUpdateButton = false;
        nodeRole = 'fh_cfshg';
      }
      record.nodeRole = nodeRole;
    }
    // isDisableUpdateButton = false;
    return [
      {
        label: '查看',
        onClick: handleView.bind(null, record),
      },
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'manage:ldc_event_manage:edit',
        disabled: isConBackStatus
      },
      {
        label: '更新',
        disabled: isDisableUpdateButton,
        popConfirm: {
          title: '是否确认更新',
          confirm: handleUpdate.bind(null, record),
          placement: 'topLeft',
        }
      },
      {
        label: '处理过程',
        onClick: handleProcess.bind(null, record),
      },
    ];
  }

  async function handleUpdate(record) {
      // 将状态变为更新中
      let param = {
        'id': record.id,
        'nodeRole': record.nodeRole
      }
      const res = await updateStatusToUpdate(param);
      if (res.success) {
        handleSuccess();
      } else {
        message.error("置为更新中异常");
      }
  }

  function handleProcess(record) {
    registerModal2.value.viewHistory(record);
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }
  
  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }
  

  /**
   * form点击事件(以逗号分割)
   * @param key
   * @param value
   */
  function handleFormJoinChange(key, value) {
    if (typeof value != 'string') {
      queryParam[key] = value.join(',');
    }
  }

  function manageBack() {
    // isShow1.value = false;
    // isShowList.value = true;
    // selectedRowKeys.value = [];
    // rowSelection.selectedRows = [];
    alert(123);
    isShow3.value = false;
    isShow2.value = false;
    isShow1.value = false;
    handleSuccess();
  };


  async function handleCopyAdd() {
    let selectedLength = rowSelection.selectedRows.length;
    if (selectedLength != 1 ) {
      message.warn('请选择一条损失事件进行复制新建!');
      return;
    }
    let record = rowSelection.selectedRows[0];
    // 有些字段必须设置成初始值
    record.fillCount = 1;
    record.status = '1';
    record.mergeState = '1';
    record.mergeId = '';
    record.isRoot = '1';
    record.isDelete = '0';
    record.createBy = '';
    record.createTime = null;
    record.updateBy = '';
    record.updateTime = null;
    record.sysOrgId = '';
    record.isHidden = '0';
    record.showPage = '1';
    record.isShowPage1 = '1';
    record.isShowPage2 = '0';
    record.isShowPage3 = '0';
    record.nodeLevel = '1';
    record.mergerRootId = '';
    record.isParticipateProcess = '1';

    // 获取编号
		const res = await getEventCode();
		if (res.success) {
			record.serialIndex = res.result.serialIndex;
			record.eventCode = res.result.eventCode;
		} else {
			message.error(res.message);
			return;
		}

    operateFlag.value = '3';
    manageInfo.value = record;
    isShowList.value = false;
    isShow1.value = true;
  }

  /**
   * 损失事件提交
   */
  async function handleSubmit() {

    let selectedLength = rowSelection.selectedRows.length;
    if (selectedLength == 0 ) {
      message.warn('请选择要提交的损失事件!');
      return;
    }
    let param = {
      'ids': rowSelection.selectedRowKeys
    }  
    const res = await submitFormList(param);
    if (res.success) {
      message.success("提交成功");
      handleSuccess();
    } else {
      message.warn(res.message);
    }
  }

  /**
   * 提交撤回
   */
  async function handleWithdraw() {
    let selectedLength = rowSelection.selectedRows.length;
    if (selectedLength == 0 ) {
      message.warn('请选择要撤回的损失事件!');
      return;
    }
    let param = {
      'ids': rowSelection.selectedRowKeys
    }
    const res = await handleWithdrawToFirst(param);
    if (res.success) {
      message.success("撤回成功");
      handleSuccess();
    } else {
      message.warn(res.message);
    }
  }

  /**
   * 合并事件
   */
  async function handleMerge() {
    let selectedLength = rowSelection.selectedRows.length;
    if (selectedLength == 0 ) {
      message.warn('请选择要合并的损失事件!');
      return;
    }
    if (selectedLength < 2) {
      message.warn('至少选择两条需要合并的损失事件!');
      return;
    }
    let param = {
      'ids': rowSelection.selectedRowKeys
    }
    const res = await judgeCanMerge(param);
    if (res.success) {
      let record = res.result;
      manageMergerInfo.value = record;
      isShowList.value = false;
      isShow2.value = true;
    } else {
      message.error(res.message);
    }
  }

  function manageMergerBack() {
    handleSuccess();
  };


  onBeforeMount(async () => {
    roleList.value = userStore.getUserInfo.roleList;
    // 判断是否包含分行ldc审核岗或者总行ldc审核岗
    if (roleList.value.includes('zh_ldc_shg') || roleList.value.includes('fh_cfshg')) {
      isShowMergeButton.value = true;
    } else {
      isShowMergeButton.value = false;
    }
  });

  /**
   * 废止
   */
  async function handleRepeal() {
    let selectedLength = rowSelection.selectedRows.length;
    if (selectedLength == 0 ) {
      message.warn('请选择要废止的损失事件!');
      return;
    }
    // let param = {
    //   'ids': rowSelection.selectedRowKeys
    // }
    await batchRepeal({ ids: selectedRowKeys.value }, handleSuccess);
  }

  async function downloadDocument() {
    let selectedLength = rowSelection.selectedRows.length;
    if (selectedLength == 0 ) {
      message.warn('请选择要下载的损失事件!');
      return;
    }
    if (selectedLength != 1 ) {
      message.warn('只能选择一条要下载的损失事件!');
      return;
    }

    await downloadZip(rowSelection.selectedRows[0].id);
    // window.location.href = '/gientech-boot/manage/ldcEventManage/downloadZip?id='+ rowSelection.selectedRows[0].id;
  }

  function exportAllColumn() {
    let paramsForm = queryParam;
    let exportName = '损失事件管理表-全字段';
    paramsForm.columnType = 'all';
    handleExportXls(exportName, getExportUrl, paramsForm);
  }

function exportStreamlineColumn() {
  let paramsForm = queryParam;
  let exportName = '损失事件管理表-精简版';
  paramsForm.columnType = 'streamline';
  handleExportXls(exportName, getExportUrl, paramsForm);
}
  


</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust{
      min-width: 100px !important;
    }
    .query-group-split-cust{
      width: 30px;
      display: inline-block;
      text-align: center
    }
    .ant-form-item:not(.ant-form-item-with-help){
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),:deep(.ant-input-number){
      width: 100%;
    }
  }
</style>
