package com.gientech.rule.manage.reformedAndAbolished.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.common.process.enitity.CommonProcess;
import com.gientech.common.process.service.ICommonProcessService;
import com.gientech.rule.manage.reformedAndAbolished.entity.RuleSystemReformedAndAbolished;
import com.gientech.rule.manage.reformedAndAbolished.service.IRuleSystemReformedAndAbolishedService;
import com.gientech.rule.manage.utils.GenerateTaskNum;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 内外规-制度立改废任务管理
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
@Tag(name = "内外规-制度立改废任务管理")
@RestController
@RequestMapping("/rule/manage/reformedAndAbolished/ruleSystemReformedAndAbolished")
@Slf4j
public class RuleSystemReformedAndAbolishedController extends JeecgController<RuleSystemReformedAndAbolished, IRuleSystemReformedAndAbolishedService> {
    @Autowired
    private IRuleSystemReformedAndAbolishedService ruleSystemReformedAndAbolishedService;
    @Autowired
    private GenerateTaskNum generate;
    // 工作流
    @Autowired
    private IWorkflowInstanceService workflowInstanceService;
    @Autowired
    private IWorkflowTaskService workflowTaskService;
    private final String businessKey = "ruleSystemRAAdd";
    // 处理过程
    @Autowired
    private ICommonProcessService processService;

    /**
     * 分页列表查询
     *
     * @param ruleSystemReformedAndAbolished
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "内外规-制度立改废任务管理-分页列表查询")
    @Operation(summary = "内外规-制度立改废任务管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<RuleSystemReformedAndAbolished>> queryPageList(RuleSystemReformedAndAbolished ruleSystemReformedAndAbolished,
                                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                                       HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("taskType", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("abolitionSystem", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("consumerRights", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("whtherDraw", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("systemLevel", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("scopeApplication", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("systemSecrecy", QueryRuleEnum.LIKE_WITH_OR);
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        QueryWrapper<RuleSystemReformedAndAbolished> queryWrapper = QueryGenerator.initQueryWrapper(ruleSystemReformedAndAbolished, req.getParameterMap(), customeRuleMap);
        queryWrapper.and(wrapper -> wrapper.eq("issuing_dept_one", user.getOrgCode()).or().eq("after_issuing_dept_one", user.getOrgCode()));
        Page<RuleSystemReformedAndAbolished> page = new Page<RuleSystemReformedAndAbolished>(pageNo, pageSize);
        IPage<RuleSystemReformedAndAbolished> pageList = ruleSystemReformedAndAbolishedService.page(page, queryWrapper);
        IPage<RuleSystemReformedAndAbolished> list = ruleSystemReformedAndAbolishedService.queryChildDetail(pageList);
        return Result.OK(list);
    }

    /**
     * 添加
     *
     * @param ruleSystemReformedAndAbolished
     * @return
     */
    @AutoLog(value = "内外规-制度立改废任务管理-保存")
    @Operation(summary = "内外规-制度立改废任务管理-保存")
    @RequiresPermissions("rule.manage.reformedAndAbolished:rule_system_reformed_and_abolished:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody RuleSystemReformedAndAbolished ruleSystemReformedAndAbolished) {
        String str = ruleSystemReformedAndAbolishedService.newsave(ruleSystemReformedAndAbolished);
        if ("000000".equals(str)) {
            return Result.OK("保存成功!");
        } else {
            return Result.error(str);
        }
    }

    /**
     * 编辑
     *
     * @param ruleSystemReformedAndAbolished
     * @return
     */
    @AutoLog(value = "内外规-制度立改废任务管理-编辑")
    @Operation(summary = "内外规-制度立改废任务管理-编辑")
    @RequiresPermissions("rule.manage.reformedAndAbolished:rule_system_reformed_and_abolished:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody RuleSystemReformedAndAbolished ruleSystemReformedAndAbolished) {
        ruleSystemReformedAndAbolishedService.updateById(ruleSystemReformedAndAbolished);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "内外规-制度立改废任务管理-通过id删除")
    @Operation(summary = "内外规-制度立改废任务管理-通过id删除")
    @RequiresPermissions("rule.manage.reformedAndAbolished:rule_system_reformed_and_abolished:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        ruleSystemReformedAndAbolishedService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "内外规-制度立改废任务管理-批量删除")
    @Operation(summary = "内外规-制度立改废任务管理-批量删除")
    @RequiresPermissions("rule.manage.reformedAndAbolished:rule_system_reformed_and_abolished:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.ruleSystemReformedAndAbolishedService.newdelete(ids, "delete");
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "内外规-制度立改废任务管理-通过id查询")
    @Operation(summary = "内外规-制度立改废任务管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<RuleSystemReformedAndAbolished> queryById(@RequestParam(name = "id", required = true) String id) {
        RuleSystemReformedAndAbolished ruleSystemReformedAndAbolished = ruleSystemReformedAndAbolishedService.getById(id);
        if (ruleSystemReformedAndAbolished == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(ruleSystemReformedAndAbolished);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param ruleSystemReformedAndAbolished
     */
    @RequiresPermissions("rule.manage.reformedAndAbolished:rule_system_reformed_and_abolished:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RuleSystemReformedAndAbolished ruleSystemReformedAndAbolished) {
        return super.exportXls(request, ruleSystemReformedAndAbolished, RuleSystemReformedAndAbolished.class, "内外规-制度立改废任务管理");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("rule.manage.reformedAndAbolished:rule_system_reformed_and_abolished:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RuleSystemReformedAndAbolished.class);
    }

	 /**
	  * 查找父级部门
	  * @param
	  * @return
	  */
	 @GetMapping("/getParentDepartId")
	 public Result<DictModel> getParentDepartId(){
		 return generate.getParentDepartId();
	 }


    /**
     * 提交审核
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "内外规-部门制度规划管理-提交审核")
    @Operation(summary = "内外规-部门制度规划管理-提交审核")
    @PostMapping(value = "/submitRequest")
    public Result<String> submitRequest(@RequestParam(name = "ids", required = true) String ids) {
        // 加入查询权限
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleSystemReformedAndAbolished> rrs = service.listByIds(idList);
        for (RuleSystemReformedAndAbolished rr : rrs) {
            //提交审核时已提交人的权限为主
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, rr);
            workflowInstanceService.createWorkflowInstance(businessKey, rr.getId(), variables);
        }
        processService.saveProcessBatch(businessKey, idList, "提交审核");
        return Result.OK("提交审核成功!");
    }

	 /**
	  * 分页列表查询
	  *
	  * @param rr
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  * @return
	  */
	 @Operation(summary="内外规-部门制度规划管理-分页列表查询")
	 @GetMapping(value = "/examineList")
	 public Result<IPage<RuleSystemReformedAndAbolished>> examineList(RuleSystemReformedAndAbolished rr,
																@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
																@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
																HttpServletRequest req) {
		 // 加入查询权限
		 LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		 Set<String> sysRole = Arrays.stream(sysUser.getRoleCode().split(",")).collect(Collectors.toSet());
		 // 自定义查询规则
		 Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
		 // 自定义多选的查询规则为：LIKE_WITH_OR
		 QueryWrapper<RuleSystemReformedAndAbolished> queryWrapper = QueryGenerator.initQueryWrapper(rr, req.getParameterMap(),customeRuleMap);
		 List<String> idList = new ArrayList<>();
		 boolean isComplete = Boolean.parseBoolean(req.getParameter("isComplete"));
		 if (sysRole.contains("rule_examine")) {
			 // 总行部门操风审核岗
			 idList.addAll(
					 workflowTaskService.getWorkflowTaskBusinessIdList(businessKey, "rule_examine", sysUser.getOrgCode(), isComplete)
			 );
		 }
		 if (sysRole.contains("rule_approval")) {
			 // 总行部门操风审核岗
			 idList.addAll(
					 workflowTaskService.getWorkflowTaskBusinessIdList(businessKey, "rule_approval", sysUser.getOrgCode(), isComplete)
			 );
		 }
		 if (sysRole.contains("rule_manage")) {
			 // 总行部门操风审核岗
			 idList.addAll(
					 workflowTaskService.getWorkflowTaskBusinessIdList(businessKey, "rule_approval", sysUser.getOrgCode(), isComplete)
			 );
		 }
		 if (sysRole.contains("rule_manage")) {
			 // 总行部门操风审核岗
			 idList.addAll(
					 workflowTaskService.getWorkflowTaskBusinessIdList(businessKey, "rule_manage", sysUser.getOrgCode(), isComplete)
			 );
		 }
		 if (sysRole.contains("zh_nkhgshg")) {
			 // 总行部门操风审核岗
			 idList.addAll(
					 workflowTaskService.getWorkflowTaskBusinessIdList(businessKey, "zh_nkhgshg", sysUser.getOrgCode(), isComplete)
			 );
		 }
		 if (sysRole.contains("zh_nkhgspg")) {
			 // 总行部门操风审核岗
			 idList.addAll(
					 workflowTaskService.getWorkflowTaskBusinessIdList(businessKey, "zh_nkhgspg", sysUser.getOrgCode(), isComplete)
			 );
		 }
		 Page<RuleSystemReformedAndAbolished> page = new Page<RuleSystemReformedAndAbolished>(pageNo, pageSize);
		 if (idList.isEmpty()) {
			 return Result.ok(page);
		 }
		 queryWrapper.in("id", idList.toArray());
		 IPage<RuleSystemReformedAndAbolished> pageList = ruleSystemReformedAndAbolishedService.page(page, queryWrapper);

        return Result.OK(pageList);
    }

    /**
     * 审核通过
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "审核通过")
    @Operation(summary = "审核通过")
    @PostMapping(value = "/passRequest")
//	@RequiresPermissions("kri:alert:examine")
    public Result<String> passRequest(@RequestParam(name = "ids", required = true) String ids,@RequestParam(name = "complianceOpinion", required = true) String complianceOpinion,@RequestParam(name = "complianceFlag", required = true) String complianceFlag) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleSystemReformedAndAbolished> rrs = service.listByIds(idList);
        for (RuleSystemReformedAndAbolished rr : rrs) {
            //合规意见不为空更新合规意见
            if(!"".equals(complianceOpinion)&&complianceOpinion!=null){
                if(!"".equals(complianceFlag)&&complianceFlag!=null&&"1".equals(complianceFlag)){
                    rr.setComplianceOpinion(complianceOpinion);
                    service.updateById(rr);
                }
                if(!"".equals(complianceFlag)&&complianceFlag!=null&&"2".equals(complianceFlag)){
                    rr.setComplianceAuditOpinion(complianceOpinion);
                    service.updateById(rr);
                }
            }
            //执行工作流
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, rr.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approval", true);
                executeVariables.put(businessKey, rr);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }
        processService.saveProcessBatch(businessKey, idList, "审核通过");
        return Result.ok("审核通过成功!");
    }

    /**
     * 审核退回
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "审核退回")
    @Operation(summary = "审核退回")
    @PostMapping(value = "/givebackRequest")
    public Result<String> givebackRequest(@RequestParam(name = "ids", required = true) String ids,@RequestParam(name = "complianceOpinion", required = true) String complianceOpinion,@RequestParam(name = "complianceFlag", required = true) String complianceFlag) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleSystemReformedAndAbolished> rrs = service.listByIds(idList);
        for (RuleSystemReformedAndAbolished rr : rrs) {
            //合规意见不为空更新合规意见
            if(!"".equals(complianceOpinion)&&complianceOpinion!=null){
                if(!"".equals(complianceFlag)&&complianceFlag!=null&&"1".equals(complianceFlag)){
                    rr.setComplianceOpinion(complianceOpinion);
                    service.updateById(rr);
                }
                if(!"".equals(complianceFlag)&&complianceFlag!=null&&"2".equals(complianceFlag)){
                    rr.setComplianceAuditOpinion(complianceOpinion);
                    service.updateById(rr);
                }
            }
            //执行工作流
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, rr.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approval", false);
                executeVariables.put(businessKey, rr);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }
        processService.saveProcessBatch(businessKey, idList, "审核退回");
        return Result.ok("审核退回成功!");
    }

    /**
     * 处理过程
     *
     * @param id 预警调查id
     * @return 处理过程列表
     */
    @AutoLog(value = "处理过程")
    @Operation(summary = "处理过程")
    @GetMapping(value = "/process")
    public Result<IPage<CommonProcess>> process(@RequestParam(name = "id", required = true) String id) {
        LambdaQueryWrapper<CommonProcess> processQueryWrapper = new LambdaQueryWrapper<>();
        processQueryWrapper.eq(CommonProcess::getBusinessKey, businessKey);
        processQueryWrapper.eq(CommonProcess::getBusinessId, id);
        processQueryWrapper.orderByAsc(CommonProcess::getCreateTime);
        List<CommonProcess> list = processService.list(processQueryWrapper);
        // 借用分页的字典翻译
        IPage<CommonProcess> page = new Page<>();
        page.setRecords(list);
        return Result.ok(page);
    }


}
