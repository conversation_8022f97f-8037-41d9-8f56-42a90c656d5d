<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam"
              :label-col="labelCol"
              :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="8">
            <a-form-item name="reportYear">
              <template #label><span title="报告年度">报告年度</span></template>
              <a-date-picker picker="year" valueFormat="YYYY" placeholder="请选择报告年度"
                             v-model:value="queryParam.reportYear"
                             style="width: 100%"
                             allow-clear/>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item name="reportQuarterly">
              <template #label><span title="报告季度">报告季度</span></template>
              <j-select-multiple placeholder="请选择报告季度"
                                 v-model:value="queryParam.reportQuarterly"
                                 dictCode="orr_report_quarterly" allow-clear/>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :lg="8">
              <a-form-item name="reportName">
                <template #label><span title="报告名称">报告名称</span></template>
                <JInput placeholder="请输入报告名称" v-model:value="queryParam.reportName"/>
              </a-form-item>
            </a-col>
            <a-col :lg="8">
              <a-form-item name="reportStatus">
                <template #label><span title="报告状态">报告状态</span></template>
                <j-select-multiple v-if="props.processKey == 1" placeholder="请选择报告状态"
                                   v-model:value="queryParam.reportStatus"
                                   dictCode="orr_report_status_processing" allow-clear/>
                <j-select-multiple v-if="props.processKey == 2" placeholder="请选择报告状态"
                                   v-model:value="queryParam.reportStatus"
                                   dictCode="orr_report_status_processed" allow-clear/>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset"
                          style="margin-left: 8px">重置</a-button>
                <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <Icon
                    :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'"/>
                </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button v-if="props.processKey === 1" type="primary" v-auth="''" @click="handlePass"
                  preIcon="ant-design:check-outlined"> 通过
        </a-button>
        <a-button v-if="props.processKey === 1" type="primary" v-auth="''" @click="handleReturn"
                  preIcon="ant-design:rollback-outlined"> 退回
        </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"/>
      </template>
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
    <ProcessModal ref="processRef" :getProcess="getProcess"/>
  </div>
</template>

<script lang="ts" name="orr.report.regular-orrRegularReport" setup>
import {ref, reactive, defineProps, defineExpose} from 'vue';
import {BasicTable, TableAction} from '/src/components/Table';
import {useListPage} from '/@/hooks/system/useListPage';
import {columns} from '../../regular/OrrRegularReport.data';
import {
  processList,
  batchPass,
  batchReturn,
  getProcess,
} from '../../regular/OrrRegularReport.api';
import {useUserStore} from '/@/store/modules/user';
import JSelectMultiple from '/src/components/Form/src/jeecg/components/JSelectMultiple.vue';
import JInput from "/src/components/Form/src/jeecg/components/JInput.vue";
import {useMessage} from "@/hooks/web/useMessage";
import {useGo} from "@/hooks/web/usePage";
import ProcessModal from "@/views/kri/input/components/ProcessModal.vue";

const props = defineProps({
  // 1-全行常规报告 2-总行部门常规报告/分行常规报告
  reportType: {type: Number, default: ''},
  processKey: {type: Number, default: true},
});

const formRef = ref();
const processRef = ref();
const queryParam = reactive<any>({});
const toggleSearchStatus = ref<boolean>(false);
const userStore = useUserStore();
const {createMessage} = useMessage();
const go = useGo();
//注册table数据
const {prefixCls, tableContext} = useListPage({
  tableProps: {
    title: '操作风险报告-常规报告管理',
    api: processList,
    columns,
    canResize: false,
    useSearchForm: false,
    actionColumn: {
      width: 130,
      fixed: 'right',
    },
    defSort: {
      column: 'reportDeadlineDate',
      order: 'desc'
    },
    beforeFetch: async (params) => {

      queryParam.processKey = props.processKey;

      // 设置类型
      queryParam.type = props.reportType;
      Object.assign(params, queryParam)

      return params;
    },
  },
});
const [registerTable, {
  reload,
  collapseAll,
  updateTableDataRecord,
  findTableDataRecord,
  getDataSource
}, {rowSelection, selectedRowKeys, selectedRows}] = tableContext;
const labelCol = reactive({
  xs: 8,
  sm: 8,
  xl: 8,
  xxl: 6
});
const wrapperCol = reactive({
  xs: 16,
  sm: 16,
});

/**
 * 提交审核
 */
async function handlePass() {

  let rowKeys = selectedRowKeys.value;

  if (rowKeys.length <= 0) {
    createMessage.warn("请选择报告！");
    return;
  }

  await batchPass(rowKeys)
    .then((res) => {
      if (res.success) {
        createMessage.success(res.message);
        handleSuccess();
      } else {
        createMessage.warn(res.message);
      }
    })
    .finally(() => {

    })

}

/**
 * 撤销
 */
async function handleReturn() {

  let rowKeys = selectedRowKeys.value;

  if (rowKeys.length <= 0) {
    createMessage.warn("请选择报告！");
    return;
  }

  await batchReturn(rowKeys)
    .then((res) => {
      if (res.success) {
        createMessage.success(res.message);
        handleSuccess();
      } else {
        createMessage.warn(res.message);
      }
    })
    .finally(() => {

    })

}

/**
 * 查看
 */
function handleDetail(record: Recordable) {
  go({
    path: '/report/detail',
    query: {id: record.id}
  });
}

/**
 * 处理过程
 */
function handleProcess(record: Recordable) {
  processRef.value.handleOpen(record.id);
}

/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '查看',
      onClick: handleDetail.bind(null, record),
    },
    {
      label: '处理过程',
      onClick: handleProcess.bind(null, record),
    },
  ];
}

/**
 * 查询
 */
function searchQuery() {
  reload();
}

/**
 * 重置
 */
function searchReset() {
  queryParam.reportQuarterlyStr = null;
  formRef.value.resetFields();
  selectedRowKeys.value = [];
  //刷新数据
  reload();
}

defineExpose({
  searchQuery
});

</script>

<style lang="less" scoped>
.jeecg-basic-table-form-container {
  padding: 0;

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }

  .query-group-cust {
    min-width: 100px !important;
  }

  .query-group-split-cust {
    width: 30px;
    display: inline-block;
    text-align: center
  }

  .ant-form-item:not(.ant-form-item-with-help) {
    margin-bottom: 16px;
    height: 32px;
  }

  :deep(.ant-picker), :deep(.ant-input-number) {
    width: 100%;
  }
}
</style>
