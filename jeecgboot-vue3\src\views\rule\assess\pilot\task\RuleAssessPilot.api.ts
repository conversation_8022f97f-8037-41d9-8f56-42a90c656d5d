import { defHttp } from "/@/utils/http/axios";
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = "/rule/assess/pilot/task/list",
  generateTaskCode = "/rule/assess/pilot/task/generateTaskCode",
  save = "/rule/assess/pilot/task/add",
  edit = "/rule/assess/pilot/task/edit",
  updateAssessResult = "/rule/assess/pilot/task/updateAssessResult",
  deleteOne = "/rule/assess/pilot/task/delete",
  deleteBatch = "/rule/assess/pilot/task/deleteBatch",
  initiate = "/rule/assess/pilot/task/initiate",
  querySubByTaskId = "/rule/assess/pilot/task/querySubByTaskId",
  addSubTask = "/rule/assess/pilot/task/addSubTask",

  submitRequest = '/rule/assess/pilot/task/submitRequest',
}

/**
 * 查询子表数据
 * @param params
 */
export const querySubByTaskId = (params) => defHttp.get({
  url: Api.querySubByTaskId,
  params
});

/**
 * 列表接口
 * @param params
 */
export const list = (params: any) =>
  defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params: any, handleSuccess: Function) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params: any, handleSuccess: Function) => {
  createConfirm({
    iconType: "warning",
    title: "确认删除",
    content: "是否删除选中数据",
    okText: "确认",
    cancelText: "取消",
    onOk: async () => {
      return defHttp.delete({
        url: Api.deleteBatch,
        data: params
      }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
};

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params: any, isUpdate: boolean) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

/**
 * 发起评估任务
 * @param params
 * @param handleSuccess
 */
export const initiate = (params: any, handleSuccess: Function) => {
  createConfirm({
    iconType: "warning",
    title: "确认发起",
    content: "是否发起选中评估任务？",
    okText: "确认",
    cancelText: "取消",
    onOk: async () => {
      return defHttp.post({
        url: Api.initiate,
        data: params
      }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
};

/**
 * 新增子任务
 * @param params
 */
export const addSubTask = (params: any) => defHttp.post({
  url: Api.addSubTask,
  params
});

/**
 * 生成任务编号
 */
export const generateTaskCode = () => defHttp.post({
  url: Api.generateTaskCode,
  params: {}
});

/**
 * 评估结论录入
 * @param params
 */
export const updateAssessResult = (params: any) => defHttp.post({
  url: Api.updateAssessResult,
  params
});

/**
 * 提交审核
 * @param params
 * @param handleSuccess
 */
export const submitRequest = (params: any, handleSuccess: Function) => {
  createConfirm({
    iconType: 'warning',
    title: '确认提交审核',
    content: '是否对选中数据提交审核?',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      return defHttp
        .post(
          {
            url: Api.submitRequest,
            data: params,
          },
          { joinParamsToUrl: true }
        )
        .then(() => {
          handleSuccess();
        });
    },
  });
};
