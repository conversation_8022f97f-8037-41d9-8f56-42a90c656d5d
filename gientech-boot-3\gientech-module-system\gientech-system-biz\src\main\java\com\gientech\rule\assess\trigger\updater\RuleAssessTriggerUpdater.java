package com.gientech.rule.assess.trigger.updater;

import com.gientech.rule.assess.trigger.entity.RuleAssessTrigger;
import com.gientech.rule.assess.trigger.mapper.RuleAssessTriggerMapper;
import com.gientech.workflow.updater.BusinessDataUpdater;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2025年08月08日 09:55
 */
@Slf4j
@Service
public class RuleAssessTriggerUpdater implements BusinessDataUpdater {

    @Autowired
    private RuleAssessTriggerMapper ruleAssessTriggerMapper;

    private final String businessKey = "ruleAssessTrigger";

    @Override
    public String getBusinessKey() {
        return this.businessKey;
    }

    @Override
    public Class<?> getBusinessDataType() {
        return RuleAssessTrigger.class;
    }

    @Override
    public void beforeProcessTask(Map<String, Object> businessData) {
        Object data = businessData.get(businessKey);
        if (data instanceof RuleAssessTrigger ruleAssessTrigger) {
            String id = ruleAssessTrigger.getId();
            String processStatus = ruleAssessTrigger.getProcessStatus();

            ruleAssessTrigger = ruleAssessTriggerMapper.selectById(id);
            ruleAssessTrigger.setProcessStatus(processStatus);
            ruleAssessTriggerMapper.updateById(ruleAssessTrigger);
        } else {
            log.error("{} is not a RuleAssessTrigger", data.getClass().getName());
        }
    }
}
