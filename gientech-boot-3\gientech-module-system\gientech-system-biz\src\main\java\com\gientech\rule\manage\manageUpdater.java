package com.gientech.rule.manage;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gientech.rule.manage.reformedAndAbolished.entity.RuleSystemReformedAndAbolished;
import com.gientech.rule.manage.reformedAndAbolished.mapper.RuleSystemReformedAndAbolishedMapper;
import com.gientech.rule.regulations.deptSystemPlan.entity.RuleDeptSystemManagement;
import com.gientech.rule.regulations.deptSystemPlan.mapper.RuleDeptSystemManagementMapper;
import com.gientech.rule.regulations.deptSystemPlan.service.IRuleDeptSystemManagementService;
import com.gientech.rule.regulations.institutionSystemPlan.entity.RuleInstitutionSystemManagement;
import com.gientech.rule.regulations.institutionSystemPlan.mapper.RuleInstitutionSystemManagementMapper;
import com.gientech.rule.regulations.institutionSystemPlan.service.IRuleInstitutionSystemManagementService;
import com.gientech.workflow.updater.BusinessDataUpdater;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025年06月03日 14:27
 */
@Slf4j
@Service
public class manageUpdater implements BusinessDataUpdater {
    @Autowired
    private RuleSystemReformedAndAbolishedMapper infoMapper;


    private final String businessKey = "ruleSystemRAAdd";

    @Autowired
    public void setRuleSystemReformedAndAbolishedMapper(RuleSystemReformedAndAbolishedMapper infoMapper) {
        this.infoMapper = infoMapper;
    }

    @Override
    public String getBusinessKey() {
        return this.businessKey;
    }

    @Override
    public Class<?> getBusinessDataType() {
        return RuleSystemReformedAndAbolished.class;
    }

    @Override
    public void beforeProcessTask(Map<String, Object> businessData) {
        Object data = businessData.get(businessKey);
        if (data instanceof RuleSystemReformedAndAbolished rule) {
            String id = rule.getId();
            String Status = rule.getProcessStatus();
            String type = rule.getTaskType();
            rule = infoMapper.selectById(id);
            rule.setProcessStatus(Status);
            if("1".equals(Status)){
                rule.setTaskStatus("1");//维护任务状态 草稿
            }
            if("2".equals(Status)||"3".equals(Status)){
                rule.setTaskStatus("2");//维护任务状态 待部门审核/审批
            }
            if("4".equals(Status)){
                rule.setTaskStatus("3");//维护任务状态 待会签
            }
            if("5".equals(Status)){
                rule.setTaskStatus("5");//维护任务状态 待提交合规审核
            }
            if("7".equals(Status)){
                rule.setTaskStatus("6");//维护任务状态 待上会
            }
            //交接审核通过时，自动将制度发文机构和部门替换成交接后机构和部门,将交接后部门替换成空
            if("4".equals(type)&&"7".equals(Status)){
                List<String> lists=Arrays.asList(rule.getHandoverIds().split(","));
                for(String ids:lists){
                    RuleSystemReformedAndAbolished rr=infoMapper.selectById(id);
                    rr.setSystemIssuingBody(rule.getAfterIssuingBody());
                    rr.setIssuingDeptOne(rule.getAfterIssuingDeptOne());
                    rr.setIssuingDeptTwo(rule.getAfterIssuingDeptTwo());
                    rr.setAfterIssuingBody("");
                    rr.setAfterIssuingDeptOne("");
                    rr.setAfterIssuingDeptTwo("");
                    infoMapper.updateById(rr);
                }
            }
            infoMapper.updateById(rule);
        } else {
            log.error("{} is not a KriIndicatorInfo", data.getClass().getName());
        }
    }
}
