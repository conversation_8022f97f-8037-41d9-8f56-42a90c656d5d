<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gientech.ldc.manage.mapper.LdcEventManageMapper">

    <select id="getMaxSerialIndex" resultType="String">
        select
            max(serial_index)
        from ldc_event_manage
        where fill_depart = #{fillDepart}
    </select>

    <select id="queryAuditedList" resultType="com.gientech.ldc.manage.entity.LdcEventManage">
        select
            t.*
        from ldc_event_manage t
        where 1=1
        and exists (select 1 from ldc_event_history a
            where t.id = a.event_id and a.is_audit = '1' and a.operate_user = #{manage.operateUser})
        and t.is_participate_process = '1'
    </select>

    <select id="selectEventPageList" resultType="com.gientech.ldc.manage.entity.LdcEventManage">
        select * from ldc_event_manage ${ew.customSqlSegment}
        union
        select * from (select * from ldc_event_manage ${ew.customSqlSegment} and status = '6') t
        where exists (select 1 from ldc_event_depart a where t.id = a.event_id
            and a.depart_no = #{orgId})
    </select>

    <select id="selectEventPageListByDepart" resultType="com.gientech.ldc.manage.entity.LdcEventManage">
        select
            *
        from ldc_event_manage t
        where status = '6' and is_participate_process = '1'
        and exists (select 1 from ldc_event_depart a where t.id = a.event_id and a.depart_no = #{orgId})
        <if test="manage.isShowPage1 != null and manage.isShowPage1 != ''">
            and is_show_page1 = #{manage.isShowPage1}
        </if>
        <if test="manage.isShowPage2 != null and manage.isShowPage2 != ''">
            and is_show_page2 = #{manage.isShowPage2}
        </if>
        <if test="manage.isShowPage3 != null and manage.isShowPage3 != ''">
            and is_show_page3 = #{manage.isShowPage3}
        </if>
        <if test="manage.eventCode != null and manage.eventCode != ''">
            and event_code like '%' || #{manage.eventCode} || '%'
        </if>
        <if test="manage.eventName != null and manage.eventName != ''">
            and event_name like '%' || #{manage.eventName} || '%'
        </if>
        <if test="manage.isEnd != null and manage.isEnd != ''">
            and is_end = #{manage.isEnd}
        </if>
        <if test="manage.eventClassifyFirst != null and manage.eventClassifyFirst != ''">
            and event_classify_first = #{manage.eventClassifyFirst}
        </if>
        <if test="manage.eventClassifySecond != null and manage.eventClassifySecond != ''">
            and event_classify_second = #{manage.eventClassifySecond}
        </if>
        <if test="manage.isLossEvent != null and manage.isLossEvent != ''">
            and is_loss_event = #{manage.isLossEvent}
        </if>
        <if test="manage.isLossEvent != null and manage.isLossEvent != ''">
            and is_loss_event = #{manage.isLossEvent}
        </if>
    </select>


    <select id="selectEventPageListByParam" resultType="com.gientech.ldc.manage.entity.LdcEventManage">
        select * from ldc_event_manage
        where 1=1 and is_participate_process = '1'
        <if test="manage.status != null and manage.status!= ''">
            and status = #{manage.status}
        </if>
        and process_flag in
        <foreach collection="processList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and fill_depart in
        <foreach collection="departList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="manage.isShowPage1 != null and manage.isShowPage1 != ''">
            and is_show_page1 = #{manage.isShowPage1}
        </if>
        <if test="manage.isShowPage2 != null and manage.isShowPage2 != ''">
            and is_show_page2 = #{manage.isShowPage2}
        </if>
        <if test="manage.isShowPage3 != null and manage.isShowPage3 != ''">
            and is_show_page3 = #{manage.isShowPage3}
        </if>
        <if test="manage.eventCode != null and manage.eventCode != ''">
<!--            and event_code like concat('%',#{manage.eventCode, jdbcType=VARCHAR},'%')-->
            and event_code like '%' || #{manage.eventCode} || '%'
        </if>

        <if test="manage.eventName != null and manage.eventName != ''">
<!--            and event_name like concat(concat('%',#{manage.eventName}),'%')-->
            and event_name like '%' || #{manage.eventName} || '%'

        </if>
        <if test="manage.isEnd != null and manage.isEnd != ''">
            and is_end = #{manage.isEnd}
        </if>
        <if test="manage.eventClassifyFirst != null and manage.eventClassifyFirst != ''">
            and event_classify_first = #{manage.eventClassifyFirst}
        </if>
        <if test="manage.eventClassifySecond != null and manage.eventClassifySecond != ''">
            and event_classify_second = #{manage.eventClassifySecond}
        </if>
        <if test="manage.isLossEvent != null and manage.isLossEvent != ''">
            and is_loss_event = #{manage.isLossEvent}
        </if>
        <if test="manage.isLossEvent != null and manage.isLossEvent != ''">
            and is_loss_event = #{manage.isLossEvent}
        </if>
        union
        select
        *
        from ldc_event_manage t
        where status = '6' and is_participate_process = '1'
        and exists (select 1 from ldc_event_depart a where t.id = a.event_id and a.depart_no = #{orgId})
        <if test="manage.isShowPage1 != null and manage.isShowPage1 != ''">
            and is_show_page1 = #{manage.isShowPage1}
        </if>
        <if test="manage.isShowPage2 != null and manage.isShowPage2 != ''">
            and is_show_page2 = #{manage.isShowPage2}
        </if>
        <if test="manage.isShowPage3 != null and manage.isShowPage3 != ''">
            and is_show_page3 = #{manage.isShowPage3}
        </if>
        <if test="manage.eventCode != null and manage.eventCode != ''">
<!--            and event_code like concat('%',#{manage.eventCode, jdbcType=VARCHAR},'%')-->
            and event_code like '%' || #{manage.eventCode} || '%'
        </if>
        <if test="manage.eventName != null and manage.eventName != ''">
            and event_name like '%' || #{manage.eventName} || '%'
        </if>
        <if test="manage.isEnd != null and manage.isEnd != ''">
            and is_end = #{manage.isEnd}
        </if>
        <if test="manage.eventClassifyFirst != null and manage.eventClassifyFirst != ''">
            and event_classify_first = #{manage.eventClassifyFirst}
        </if>
        <if test="manage.eventClassifySecond != null and manage.eventClassifySecond != ''">
            and event_classify_second = #{manage.eventClassifySecond}
        </if>
        <if test="manage.isLossEvent != null and manage.isLossEvent != ''">
            and is_loss_event = #{manage.isLossEvent}
        </if>
        <if test="manage.isLossEvent != null and manage.isLossEvent != ''">
            and is_loss_event = #{manage.isLossEvent}
        </if>
    </select>


    <select id="judgeCanOperate" resultType="String">
        select
            id
        from ldc_event_manage
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_participate_process = '1'
        and process_flag in
        <foreach collection="processList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and fill_depart in
        <foreach collection="departList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateEditStates">
        update ldc_event_manage set status = '27' where id = #{id} and status not in('27')
    </update>

    <select id="selectEventPageListForExport" resultType="com.gientech.ldc.manage.entity.LdcEventManage">
        select * from ldc_event_manage
        where 1=1 and is_participate_process = '1'
        <if test="manage.status != null and manage.status!= ''">
            and status = #{manage.status}
        </if>
        and process_flag in
        <foreach collection="processList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and fill_depart in
        <foreach collection="departList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="manage.isShowPage1 != null and manage.isShowPage1 != ''">
            and is_show_page1 = #{manage.isShowPage1}
        </if>
        <if test="manage.isShowPage2 != null and manage.isShowPage2 != ''">
            and is_show_page2 = #{manage.isShowPage2}
        </if>
        <if test="manage.isShowPage3 != null and manage.isShowPage3 != ''">
            and is_show_page3 = #{manage.isShowPage3}
        </if>
        <if test="manage.eventCode != null and manage.eventCode != ''">
            <!--            and event_code like concat('%',#{manage.eventCode, jdbcType=VARCHAR},'%')-->
            and event_code like '%' || #{manage.eventCode} || '%'
        </if>

        <if test="manage.eventName != null and manage.eventName != ''">
            <!--            and event_name like concat(concat('%',#{manage.eventName}),'%')-->
            and event_name like '%' || #{manage.eventName} || '%'

        </if>
        <if test="manage.isEnd != null and manage.isEnd != ''">
            and is_end = #{manage.isEnd}
        </if>
        <if test="manage.eventClassifyFirst != null and manage.eventClassifyFirst != ''">
            and event_classify_first = #{manage.eventClassifyFirst}
        </if>
        <if test="manage.eventClassifySecond != null and manage.eventClassifySecond != ''">
            and event_classify_second = #{manage.eventClassifySecond}
        </if>
        <if test="manage.isLossEvent != null and manage.isLossEvent != ''">
            and is_loss_event = #{manage.isLossEvent}
        </if>
        <if test="manage.isLossEvent != null and manage.isLossEvent != ''">
            and is_loss_event = #{manage.isLossEvent}
        </if>
        union
        select
        *
        from ldc_event_manage t
        where status = '6' and is_participate_process = '1'
        and exists (select 1 from ldc_event_depart a where t.id = a.event_id and a.depart_no = #{orgId})
        <if test="manage.isShowPage1 != null and manage.isShowPage1 != ''">
            and is_show_page1 = #{manage.isShowPage1}
        </if>
        <if test="manage.isShowPage2 != null and manage.isShowPage2 != ''">
            and is_show_page2 = #{manage.isShowPage2}
        </if>
        <if test="manage.isShowPage3 != null and manage.isShowPage3 != ''">
            and is_show_page3 = #{manage.isShowPage3}
        </if>
        <if test="manage.eventCode != null and manage.eventCode != ''">
            <!--            and event_code like concat('%',#{manage.eventCode, jdbcType=VARCHAR},'%')-->
            and event_code like '%' || #{manage.eventCode} || '%'
        </if>
        <if test="manage.eventName != null and manage.eventName != ''">
            and event_name like '%' || #{manage.eventName} || '%'
        </if>
        <if test="manage.isEnd != null and manage.isEnd != ''">
            and is_end = #{manage.isEnd}
        </if>
        <if test="manage.eventClassifyFirst != null and manage.eventClassifyFirst != ''">
            and event_classify_first = #{manage.eventClassifyFirst}
        </if>
        <if test="manage.eventClassifySecond != null and manage.eventClassifySecond != ''">
            and event_classify_second = #{manage.eventClassifySecond}
        </if>
        <if test="manage.isLossEvent != null and manage.isLossEvent != ''">
            and is_loss_event = #{manage.isLossEvent}
        </if>
        <if test="manage.isLossEvent != null and manage.isLossEvent != ''">
            and is_loss_event = #{manage.isLossEvent}
        </if>
    </select>

    <select id="getCauseClassification" resultType="java.lang.String">
        select first_level_name from ldc_param_or_risk_cause_version
    </select>

    <select id="querySubjectCodeList" resultType="map">
        select
            account_code ||'_'|| account_name as "text",
            account_code as "value"
        from external_data.zzxt_cux_fnd_all_account_v order by account_code asc
    </select>


    <select id="querySubjectNameList" resultType="map">
        select
            account_code ||'_'|| account_name as "text",
            account_name as "value"
        from external_data.zzxt_cux_fnd_all_account_v order by account_code asc
    </select>

    <select id="queryLdcRelRcsaList" resultType="com.gientech.ldc.manage.entity.LdcEventManage">
        select
            t.*
        from ldc_event_manage t
        left join ldc_process_rel a on t.id = a.ldc_id
        where a.matrix_name = #{info.matrixName}
        and exists (select 1 from ldc_event_recycle_details b where t.id = b.ldc_event_id
        <![CDATA[ and b.posting_date <= #{info.evaluateEndDate} and b.posting_date >= #{info.evaluateStartDate}) ]]>
        <if test="info.fillDepart != null and info.fillDepart != ''">
            and t.fill_depart = #{info.fillDepart}
        </if>
        order by t.this_fill_date desc, t.event_code desc
    </select>
    <select id="selectList111" resultType="com.gientech.ldc.manage.entity.LdcEventManage">
        select * from ldc_event_manage t where manual_rel_ids is not null
        and not exists (select 1 from ldc_entry_clue a where t.manual_rel_ids = a.id and a.clue_status = '12')
        and t.id not in ('1953078067723251714','1953078318806872065', '1953078022303133697', '1953079438375661569', '1953076872224976897')
    </select>

</mapper>