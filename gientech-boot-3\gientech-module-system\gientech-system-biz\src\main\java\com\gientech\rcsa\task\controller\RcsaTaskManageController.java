package com.gientech.rcsa.task.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import com.gientech.rcsa.scheme.entity.RcsaSchemeManage;
import com.gientech.rcsa.task.vo.RcsaTaskExportAllVo;
import com.gientech.rcsa.task.vo.RcsaTaskExportColumnVo;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import com.gientech.rcsa.task.entity.RcsaTaskManage;
import com.gientech.rcsa.task.service.IRcsaTaskManageService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: RCSA评估任务表
 * @Author: jeecg-boot
 * @Date:   2025-07-17
 * @Version: V1.0
 */
@Tag(name="RCSA评估任务表")
@RestController
@RequestMapping("/rcsa/task/rcsaTaskManage")
@Slf4j
public class RcsaTaskManageController extends JeecgController<RcsaTaskManage, IRcsaTaskManageService> {
	@Autowired
	private IRcsaTaskManageService rcsaTaskManageService;
	@Autowired
	private ISysDepartService departService;
	 @Autowired
	 private IWorkflowInstanceService workflowInstanceService;
	 @Autowired
	 private IWorkflowTaskService workflowTaskService;
	 private final String businessKey = "rcsaTaskProcess";
	
	/**
	 * 分页列表查询
	 *
	 * @param rcsaTaskManage
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "RCSA评估任务表-分页列表查询")
	@Operation(summary="RCSA评估任务表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<RcsaTaskManage>> queryPageList(RcsaTaskManage rcsaTaskManage,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		LoginUser sysUser =(LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("planType", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("taskState", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<RcsaTaskManage> queryWrapper = QueryGenerator.initQueryWrapper(rcsaTaskManage, req.getParameterMap(),customeRuleMap);

		// 判断是人管管理页面还是查询页面
		String pageFlag = rcsaTaskManage.getPageFlag() == null ? "" : rcsaTaskManage.getPageFlag();
		if ("1".equals(pageFlag)) {
			queryWrapper.eq("evaluate_depart", sysUser.getOrgId());
		} else if ("2".equals(pageFlag)){
			SysDepart sysDepart = departService.getDepartById(sysUser.getOrgId());
			if (sysDepart.getDepartName().equals("内控合规部")) {
				queryWrapper.eq("lead_depart", sysUser.getOrgId());
			} else if (sysDepart.getDepartName().contains("分行")) {
				queryWrapper.eq("evaluate_depart", sysUser.getOrgId());
			} else {
				queryWrapper.eq("distribute_depart", sysUser.getOrgId());
			}
		}
		Page<RcsaTaskManage> page = new Page<RcsaTaskManage>(pageNo, pageSize);
		IPage<RcsaTaskManage> pageList = rcsaTaskManageService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param rcsaTaskManage
	 * @return
	 */
	@AutoLog(value = "RCSA评估任务表-添加")
	@Operation(summary="RCSA评估任务表-添加")
	@RequiresPermissions("rcsa.task:rcsa_task_manage:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody RcsaTaskManage rcsaTaskManage) {
		rcsaTaskManageService.save(rcsaTaskManage);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param rcsaTaskManage
	 * @return
	 */
	@AutoLog(value = "RCSA评估任务表-编辑")
	@Operation(summary="RCSA评估任务表-编辑")
	@RequiresPermissions("rcsa.task:rcsa_task_manage:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody RcsaTaskManage rcsaTaskManage) {
		rcsaTaskManageService.updateById(rcsaTaskManage);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "RCSA评估任务表-通过id删除")
	@Operation(summary="RCSA评估任务表-通过id删除")
	@RequiresPermissions("rcsa.task:rcsa_task_manage:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		rcsaTaskManageService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "RCSA评估任务表-批量删除")
	@Operation(summary="RCSA评估任务表-批量删除")
	@RequiresPermissions("rcsa.task:rcsa_task_manage:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.rcsaTaskManageService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "RCSA评估任务表-通过id查询")
	@Operation(summary="RCSA评估任务表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<RcsaTaskManage> queryById(@RequestParam(name="id",required=true) String id) {
		RcsaTaskManage rcsaTaskManage = rcsaTaskManageService.getById(id);
		if(rcsaTaskManage==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(rcsaTaskManage);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param rcsaTaskManage
    */
    @RequiresPermissions("rcsa.task:rcsa_task_manage:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RcsaTaskManage rcsaTaskManage) {
        return super.exportXls(request, rcsaTaskManage, RcsaTaskManage.class, "RCSA评估任务表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("rcsa.task:rcsa_task_manage:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RcsaTaskManage.class);
    }

	/**
	 * 提交
	 * @param taskManage
	 * @return
	 */
	@AutoLog(value = "RCSA评估任务表-提交")
	@PostMapping(value = "/submit")
	@RequiresPermissions("rcsa.task:rcsa_task_manage:submit")
	public Result<String> submit(@RequestBody RcsaTaskManage taskManage) {
//		try {
//			return rcsaTaskManageService.submit(taskManage);
//		} catch (Exception e) {
//			log.error("RCSA评估任务提交异常！", e);
//			return Result.error("提交异常！");
//		}

		try {
			return rcsaTaskManageService.submitByWorkflow(taskManage);
		} catch (Exception e) {
			log.error("RCSA评估任务提交异常！", e);
			return Result.error("提交异常！");
		}
	}


	/**
	 * 审核通过
	 * @param taskManage
	 * @return
	 * @throws Exception
	 */
	@AutoLog(value = "RCSA评估任务表-审核通过")
	@PostMapping(value = "/auditPass")
	@RequiresPermissions("rcsa.task:rcsa_task_manage:pass")
	public Result<String> auditPass(@RequestBody RcsaTaskManage taskManage) {
//		try {
//			rcsaTaskManageService.auditPass(taskManage);
//			return Result.ok("审核通过成功！");
//		} catch (Exception e) {
//			log.error("RCSA评估任务审核通过异常！", e);
//			return Result.error("审核通过异常！");
//		}
		RcsaTaskManage manage = rcsaTaskManageService.getById(taskManage.getId());
		WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, manage.getId());
		if (workflowTask != null) {
			Map<String, Object> executeVariables = new HashMap<>();
			executeVariables.put("approval", true);
			executeVariables.put(businessKey, manage);
			workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
		}
		return Result.ok("审核通过成功！");
	}

	/**
	 * 审核退回
	 * @param taskManage
	 * @return
	 * @throws Exception
	 */
	@AutoLog(value = "RCSA评估任务表-审核退回")
	@PostMapping(value = "/auditReject")
	@RequiresPermissions("rcsa.task:rcsa_task_manage:reject")
	public Result<String> auditReject(@RequestBody RcsaTaskManage taskManage) {
//		try {
//			rcsaTaskManageService.auditReject(taskManage);
//			return Result.ok("审核退回成功！");
//		} catch (Exception e) {
//			log.error("RCSA评估任务审核退回异常！", e);
//			return Result.error("审核退回异常！");
//		}

		RcsaTaskManage manage = rcsaTaskManageService.getById(taskManage.getId());
		WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, manage.getId());
		if (workflowTask != null) {
			Map<String, Object> executeVariables = new HashMap<>();
			executeVariables.put("approval", false);
			executeVariables.put(businessKey, manage);
			workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
		}
		return Result.ok("审核退回成功!");
	}

	/**
	 * 导出单条任务所有矩阵数据
	 * @param request
	 * @param response
	 * @param columnVo
	 * @throws Exception
	 */
	@Operation(summary="RCSA评估任务表-单行-导出")
	@RequiresPermissions("metering:cm_metering_legal:exportXls")
	@RequestMapping(value = "/exportOneXls")
	public void exportOneXls(HttpServletRequest request,
							 HttpServletResponse response,
							 RcsaTaskExportColumnVo columnVo) throws Exception {
		rcsaTaskManageService.exportOneXls(request, response, columnVo);
	}

	 /**
	  * 导出任务数据
	  * @param request
	  * @param response
	  * @param taskInfo
	  * @throws Exception
	  */
	 @Operation(summary="RCSA评估任务表-单行-导出")
	 @RequiresPermissions("metering:cm_metering_legal:exportXls")
	 @RequestMapping(value = "/exportAllUrl")
	 public void exportAllUrl(HttpServletRequest request,
							  		 HttpServletResponse response,
									 RcsaTaskExportAllVo taskInfo) throws Exception {
		 LoginUser sysUser =(LoginUser) SecurityUtils.getSubject().getPrincipal();
		 String pageFlag = taskInfo.getPageFlag() == null ? "" : taskInfo.getPageFlag();
		 if ("2".equals(pageFlag)) {
			 SysDepart sysDepart = departService.getDepartById(sysUser.getOrgId());
			 if (sysDepart.getDepartName().equals("内控合规部")) {
				taskInfo.setLeadDepart(sysUser.getOrgId());
			 } else if (sysDepart.getDepartName().contains("分行")) {
				 taskInfo.setEvaluateDepartList(Arrays.asList(sysUser.getOrgId()));
			 } else {
				 taskInfo.setDistributeDepart(sysUser.getOrgId());
			 }
		 }
		 rcsaTaskManageService.exportAllUrl(request, response, taskInfo);
	 }

}
