package com.gientech.rule.assess.year.updater;

import com.gientech.rule.assess.year.entity.RuleAssessYear;
import com.gientech.rule.assess.year.entity.RuleAssessYearSub;
import com.gientech.rule.assess.year.mapper.RuleAssessYearMapper;
import com.gientech.workflow.updater.BusinessDataUpdater;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2025年08月07日 23:25
 */
@Slf4j
@Service
public class RuleAssessYearUpdater implements BusinessDataUpdater {

    @Autowired
    private RuleAssessYearMapper ruleAssessYearMapper;

    private final String businessKey = "ruleAssessYear";

    @Override
    public String getBusinessKey() {
        return this.businessKey;
    }

    @Override
    public Class<?> getBusinessDataType() {
        return RuleAssessYear.class;
    }

    @Override
    public void beforeProcessTask(Map<String, Object> businessData) {
            Object data = businessData.get(businessKey);
            if (data instanceof RuleAssessYear ruleAssessYear) {
                String id = ruleAssessYear.getId();
                String processStatus = ruleAssessYear.getProcessStatus();

                ruleAssessYear = ruleAssessYearMapper.selectById(id);
                ruleAssessYear.setProcessStatus(processStatus);
                ruleAssessYearMapper.updateById(ruleAssessYear);
            } else {
                log.error("{} is not a RuleAssessYear", data.getClass().getName());
            }
    }
}
