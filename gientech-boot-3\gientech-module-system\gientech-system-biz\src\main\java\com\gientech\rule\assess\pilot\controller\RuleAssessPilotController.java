package com.gientech.rule.assess.pilot.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.common.process.enitity.CommonProcess;
import com.gientech.common.process.service.ICommonProcessService;
import com.gientech.rule.assess.pilot.entity.RuleAssessPilot;
import com.gientech.rule.assess.pilot.entity.RuleAssessPilotSub;
import com.gientech.rule.assess.pilot.service.IRuleAssessPilotService;
import com.gientech.rule.assess.pilot.service.IRuleAssessPilotSubService;
import com.gientech.rule.assess.pilot.vo.RuleAssessDetailVO;
import com.gientech.rule.assess.pilot.vo.RuleAssessPilotPage;
import com.gientech.rule.assess.pilot.vo.RuleAssessResultInputVO;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.util.*;

/**
 * 内外规库管理-制度试运行评估任务
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-24
 */
@Slf4j
@RestController
@Tag(name = "内外规库管理-制度试运行评估任务")
@RequestMapping("/rule/assess/pilot/task")
public class RuleAssessPilotController {

    private IRuleAssessPilotService ruleAssessPilotService;

    @Autowired
    public void setRuleAssessPilotService(IRuleAssessPilotService ruleAssessPilotService) {
        this.ruleAssessPilotService = ruleAssessPilotService;
    }

    private IRuleAssessPilotSubService ruleAssessPilotSubService;

    @Autowired
    public void setRuleAssessPilotSubService(IRuleAssessPilotSubService ruleAssessPilotSubService) {
        this.ruleAssessPilotSubService = ruleAssessPilotSubService;
    }

    @Autowired
    private IWorkflowInstanceService instanceService;

    @Autowired
    private IWorkflowTaskService workflowTaskService;

    @Autowired
    private ICommonProcessService processService;

    private final String businessKey = "ruleAssessPilot";

    /**
     * 分页列表查询
     *
     * @param ruleAssessPilot 实体参数
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @param req 请求参数
     * @return 查询结果
     */
    @GetMapping(value = "/list")
    @Operation(summary = "分页列表查询")
    public Result<IPage<RuleAssessDetailVO>> queryPageList(RuleAssessPilot ruleAssessPilot,
                                                           @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                           @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                           HttpServletRequest req) {
        QueryWrapper<RuleAssessPilot> queryWrapper = QueryGenerator.initQueryWrapper(ruleAssessPilot, req.getParameterMap());
        Page<RuleAssessPilot> page = new Page<>(pageNo, pageSize);
        IPage<RuleAssessDetailVO> pageList = ruleAssessPilotService.pageAssessDetail(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 生成制度试运行评估任务编号
     *
     * @return 任务编号
     */
    @PostMapping("/generateTaskCode")
    @Operation(summary = "生成制度试运行评估任务编号")
    public Result<Map<String, String>> generateTaskCode() {
        try {
            String taskCode = ruleAssessPilotService.generateTaskCode();
            Map<String, String> result = new HashMap<>();
            result.put("taskCode", taskCode);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("生成任务编号失败", e);
            return Result.error("生成任务编号失败：" + e.getMessage());
        }
    }

    /**
     * 添加
     *
     * @param ruleAssessDetailVO 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/add")
    @AutoLog(value = "内外规库管理-制度试运行评估任务-添加")
    @RequiresPermissions("rule.assess:rule_assess_pilot:add")
    public Result<String> add(@RequestBody RuleAssessDetailVO ruleAssessDetailVO) {
        ruleAssessPilotService.addNewAssessPilot(ruleAssessDetailVO);
        return Result.OK("添加成功！");
    }

    /**
     * 发起评估任务
     *
     * @param ids 评估任务id列表
     * @return 是否成功
     */
    @Operation(summary = "发起试运行评估任务")
    @PostMapping(value = "initiate")
    public Result<String> initiateTask(@RequestParam(value = "ids") List<String> ids) {
        if (ruleAssessPilotService.initiateTask(ids)) {
            return Result.ok("发起试运行评估任务成功！");
        } else
            return Result.error("发起试运行评估任务失败!");
    }

    /**
     * 查询子任务列表（分页）
     *
     * @param taskId 主任务id
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @return 分页结果
     */
    @GetMapping("/querySubByTaskId")
    @Operation(summary = "根据主任务ID分页查询子任务列表")
    public Result<IPage<RuleAssessPilotSub>> querySubByTaskId(@RequestParam(name = "taskId") String taskId,
                                                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<RuleAssessPilotSub> page = new Page<>(pageNo, pageSize);
        IPage<RuleAssessPilotSub> result = ruleAssessPilotService.querySubByTaskIdWithPage(taskId, page);
        return Result.OK(result);
    }


    /**
     * 编辑
     *
     * @param ruleAssessPilot 参数
     * @return 是否成功
     */
    @Operation(summary = "编辑")
    @AutoLog(value = "内外规库管理-制度试运行评估任务-编辑")
    @RequiresPermissions("rule.assess:rule_assess_pilot:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody RuleAssessPilot ruleAssessPilot) {
        ruleAssessPilotService.updateById(ruleAssessPilot);
        return Result.OK("编辑成功!");
    }

    /**
     * 评估结论录入
     *
     * @param assessResultInputVO 评估结论录入参数
     * @return 是否成功
     */
    @Operation(summary = "评估结论录入")
    @AutoLog(value = "内外规库管理-制度试运行评估任务-评估结论录入")
    @RequiresPermissions("rule.assess:rule_assess_pilot:conclusion")
    @RequestMapping(value = "/updateAssessResult", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> updateAssessResult(@Valid @RequestBody RuleAssessResultInputVO assessResultInputVO) {
        boolean success = ruleAssessPilotService.updateAssessResult(
                assessResultInputVO.getId(),
                assessResultInputVO.getAssessResult(),
                assessResultInputVO.getAssessResultDescription()
        );
        if (success) {
            return Result.OK("评估结论录入成功!");
        } else {
            return Result.error("评估结论录入失败!");
        }
    }

    /**
     * 追加评估任务
     *
     * @param subTask 追加的子任务参数
     * @return 是否成功
     */
    @PostMapping("/addSubTask")
    @Operation(summary = "为评估任务追加子任务")
    @RequiresPermissions("rule.assess:rule_assess_pilot:append")
    public Result<String> addSubTask(@RequestBody RuleAssessPilotSub subTask) {
        // 检查是否已存在相同的子任务
        boolean exists = ruleAssessPilotService.checkSubTaskExists(
                subTask.getTaskId(),
                subTask.getAssessOrgCode(),
                subTask.getAssessDeptCode()
        );
        if (exists) {
            return Result.error("该评估机构和部门已存在子任务，不能重复添加");
        }

        ruleAssessPilotService.addSubTask(subTask);
        return Result.OK("评估任务追加成功");
    }

    /**
     * 通过id删除
     *
     * @param id 实体对象主键
     * @return 是否成功
     */
    @Operation(summary = "通过id删除")
    @DeleteMapping(value = "/delete")
    @AutoLog(value = "内外规库管理-制度试运行评估任务-通过id删除")
    @RequiresPermissions("rule.assess:rule_assess_pilot:delete")
    public Result<String> delete(@RequestParam(name = "id") String id) {
        ruleAssessPilotService.delMain(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 多个实体对象主键
     * @return 是否成功
     */
    @Operation(summary = "批量删除")
    @DeleteMapping(value = "/deleteBatch")
    @AutoLog(value = "内外规库管理-制度试运行评估任务-批量删除")
    @RequiresPermissions("rule.assess:rule_assess_pilot:deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.ruleAssessPilotService.delBatchMain(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id 实体对象主键
     * @return 查询结果
     */
    @GetMapping(value = "/queryById")
    @Operation(summary = "通过id查询")
    public Result<RuleAssessPilot> queryById(@RequestParam(name = "id") String id) {
        RuleAssessPilot ruleAssessPilot = ruleAssessPilotService.getById(id);
        if (ruleAssessPilot == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(ruleAssessPilot);
    }

    /**
     * 导出excel
     *
     * @param request 请求参数
     * @param ruleAssessPilot 实体表单参数
     */
    @RequestMapping(value = "/exportXls")
    @RequiresPermissions("rule.assess:rule_assess_pilot:exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RuleAssessPilot ruleAssessPilot) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<RuleAssessPilot> queryWrapper = QueryGenerator.initQueryWrapper(ruleAssessPilot, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        //配置选中数据查询条件
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            queryWrapper.in("id", selectionList);
        }
        //Step.2 获取导出数据
        List<RuleAssessPilot> ruleAssessPilotList = ruleAssessPilotService.list(queryWrapper);

        // Step.3 组装pageList
        List<RuleAssessPilotPage> pageList = new ArrayList<>();
        for (RuleAssessPilot main : ruleAssessPilotList) {
            RuleAssessPilotPage vo = new RuleAssessPilotPage();
            BeanUtils.copyProperties(main, vo);
            List<RuleAssessPilotSub> ruleAssessPilotSubList = ruleAssessPilotSubService.selectByTaskId(main.getId());
            vo.setRuleAssessPilotSubList(ruleAssessPilotSubList);
            pageList.add(vo);
        }

        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "内外规库管理-制度试运行评估任务列表");
        mv.addObject(NormalExcelConstants.CLASS, RuleAssessPilotPage.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("内外规库管理-制度试运行评估任务数据", "导出人:" + sysUser.getRealname(), "内外规库管理-制度试运行评估任务"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request 请求参数
     * @return 是否成功
     */
    @RequiresPermissions("rule.assess:rule_assess_pilot:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<RuleAssessPilotPage> list = ExcelImportUtil.importExcel(file.getInputStream(), RuleAssessPilotPage.class, params);
                for (RuleAssessPilotPage page : list) {
                    RuleAssessPilot po = new RuleAssessPilot();
                    BeanUtils.copyProperties(page, po);
                    ruleAssessPilotService.saveMain(po, page.getRuleAssessPilotSubList());
                }
                return Result.OK("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return Result.OK("文件导入失败！");
    }

    /**
     * 提交审核
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "提交审核")
    @Operation(summary = "提交审核")
    @PostMapping(value = "/submitRequest")
    @RequiresPermissions("rule.assess:rule_assess_pilot:submit")
    public Result<String> submitRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessPilot> ruleAssessPilotList = ruleAssessPilotService.listByIds(idList);
        for (RuleAssessPilot ruleAssessPilot : ruleAssessPilotList) {
            // 创建审核工作流
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, ruleAssessPilot);

            instanceService.createWorkflowInstance(businessKey, ruleAssessPilot.getId(), variables);
        }
        processService.saveProcessBatch(businessKey, idList, "提交审核");
        return Result.ok("提交审核成功!");
    }

    /**
     * 审核通过
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "制度试运行评分审核-审核通过")
    @Operation(summary = "审核通过")
    @PostMapping(value = "/passRequest")
    @RequiresPermissions("rule.assess:rule_assess_pilot:examine")
    public Result<String> passRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessPilot> ruleAssessPilotList = ruleAssessPilotService.listByIds(idList);
        for (RuleAssessPilot ruleAssessPilot : ruleAssessPilotList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, ruleAssessPilot.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approve", true);
                executeVariables.put(businessKey, ruleAssessPilot);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }
        processService.saveProcessBatch(businessKey, idList, "审核通过");
        return Result.ok("审核通过成功!");
    }

    /**
     * 审核退回
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "制度试运行评分审核-审核退回")
    @Operation(summary = "审核退回")
    @PostMapping(value = "/givebackRequest")
    @RequiresPermissions("rule.assess:rule_assess_pilot:examine")
    public Result<String> givebackRequest(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<RuleAssessPilot> ruleAssessPilotList = ruleAssessPilotService.listByIds(idList);
        for (RuleAssessPilot ruleAssessPilot : ruleAssessPilotList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, ruleAssessPilot.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approve", false);
                executeVariables.put(businessKey, ruleAssessPilot);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }
        processService.saveProcessBatch(businessKey, idList, "审核退回");
        return Result.ok("审核退回成功!");
    }

    /**
     * 处理过程
     *
     * @param id 子任务id
     * @return 处理过程列表
     */
    @AutoLog(value = "制度试运行评分审核-处理过程")
    @Operation(summary = "处理过程")
    @GetMapping(value = "/process")
//    @RequiresPermissions("rule.assess:rule_assess_pilot:process")
    public Result<IPage<CommonProcess>> process(@RequestParam(name = "id", required = true) String id) {
        // 借用分页的字典翻译
        IPage<CommonProcess> page = new Page<>();
        page.setRecords(processService.getProcess(businessKey, id));
        return Result.ok(page);
    }
}
