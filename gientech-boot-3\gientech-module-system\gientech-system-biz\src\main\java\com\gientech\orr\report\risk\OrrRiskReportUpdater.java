package com.gientech.orr.report.risk;

import com.gientech.orr.report.regular.entity.OrrRegularReport;
import com.gientech.orr.report.regular.mapper.OrrRegularReportMapper;
import com.gientech.orr.report.risk.entity.OrrRiskReport;
import com.gientech.orr.report.risk.mapper.OrrRiskReportMapper;
import com.gientech.workflow.updater.BusinessDataUpdater;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025年07月03日 13:45
 */
@Slf4j
@Service
public class OrrRiskReportUpdater implements BusinessDataUpdater {

    private OrrRiskReportMapper orrRiskReportMapper;

    private final String businessKey = "orrRiskReport";

    @Autowired
    public void setOrrRiskReportMapper(OrrRiskReportMapper orrRiskReportMapper) {
        this.orrRiskReportMapper = orrRiskReportMapper;
    }

    @Override
    public String getBusinessKey() {
        return this.businessKey;
    }

    @Override
    public Class<?> getBusinessDataType() {
        return OrrRiskReport.class;
    }

    @Override
    public void beforeProcessTask(Map<String, Object> businessData) {
        Object data = businessData.get(businessKey);
        if (data instanceof OrrRiskReport orrRiskReport) {
            String id = orrRiskReport.getId();
            String state = orrRiskReport.getReportStatus();
            Date reportSubmitDate = orrRiskReport.getReportSubmitDate();
            orrRiskReport = orrRiskReportMapper.selectById(id);
            orrRiskReport.setReportStatus(state);
            orrRiskReport.setReportSubmitDate(reportSubmitDate);
            orrRiskReportMapper.updateById(orrRiskReport);
        } else {
            log.error("{} is not a OrrRiskReport", data.getClass().getName());
        }
    }
}
