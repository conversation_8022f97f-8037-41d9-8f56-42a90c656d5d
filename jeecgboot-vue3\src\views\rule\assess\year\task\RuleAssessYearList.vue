<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="128">
          <a-col :span="8">
            <a-form-item name="taskCode">
              <template #label>
                <span title="任务编号">任务编号</span>
              </template>
              <JInput placeholder="请输入" v-model:value="queryParam.taskCode" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="taskName">
              <template #label>
                <span title="任务名称">任务名称</span>
              </template>
              <JInput placeholder="请输入" v-model:value="queryParam.taskName" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="taskLevel">
              <template #label>
                <span title="任务层级">任务层级</span>
              </template>
              <j-select-multiple placeholder="请选择" v-model:value="queryParam.taskLevel" dictCode="rule_assess_year_level" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="assessType">
              <template #label>
                <span title="评估类型">评估类型</span>
              </template>
              <j-select-multiple placeholder="请选择" v-model:value="queryParam.assessType" dictCode="rule_assess_pilot_assess_type" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="endTime">
              <template #label>
                <span title="结束时间">结束时间</span>
              </template>
              <a-date-picker valueFormat="YYYY-MM-DD" placeholder="请选择" v-model:value="queryParam.endTime" allow-clear />
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :span="8">
              <a-form-item name="assessOrgCode">
                <template #label>
                  <span title="参与评估机构/部门">参与评估机构/部门</span>
                </template>
                <j-select-dept placeholder="请选择" v-model:value="queryParam.assessOrgCode" checkStrictly allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="taskStatus">
                <template #label>
                  <span title="任务状态">任务状态</span>
                </template>
                <j-select-multiple placeholder="请选择" v-model:value="queryParam.taskStatus" dictCode="rule_assess_year_task_status" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="processStatus">
                <template #label>
                  <span title="流程状态">流程状态</span>
                </template>
                <j-select-multiple
                  placeholder="请选择"
                  v-model:value="queryParam.processStatus"
                  dictCode="rule_assess_pilot_process_status"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </template>
          <a-col :span="8">
            <span style="float: right; overflow: hidden" class="table-page-search-submitButtons">
              <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
              </a>
              <a-button preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
              <a-button type="primary" preIcon="ant-design:search-outlined" @click="reload" style="margin-left: 8px">查询</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'rule.assess.year:task:add'" @click="handleAdd" preIcon="ant-design:plus-outlined">新建 </a-button>
        <a-button
          type="default"
          v-auth="'rule.assess.year:task:submit'"
          @click="handleSubmit"
          preIcon="ant-design:upload-outlined"
          :disabled="selectedRowKeys.length === 0"
          >提交
        </a-button>
        <a-button
          type="default"
          v-auth="'rule.assess.year:task:urge'"
          @click="handleUrge"
          preIcon="ant-design:bell-outlined"
          :disabled="selectedRowKeys.length === 0"
          >督办
        </a-button>
        <a-button
          type="default"
          v-auth="'rule.assess.year:task:deleteBatch'"
          @click="batchHandleDelete"
          preIcon="ant-design:delete-outlined"
          :disabled="selectedRowKeys.length === 0"
          >删除
        </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <RuleAssessYearModal ref="registerModal" @success="handleSuccess" />
    <!-- 详情弹窗 -->
    <RuleAssessYearDetailModal ref="detailModalRef" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="rule.assess.year-ruleAssessYear" setup>
  import { ref, reactive } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns } from './RuleAssessYear.data';
  import { list, batchDelete, submitRequest, urgeTasks } from './RuleAssessYear.api';
  import RuleAssessYearModal from './components/RuleAssessYearModal.vue';
  import RuleAssessYearDetailModal from './components/RuleAssessYearDetailModal.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
  import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
  import { JInput } from '@/components/Form';

  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const detailModalRef = ref();
  const { createMessage } = useMessage();
  //注册table数据
  const { tableContext } = useListPage({
    tableProps: {
      title: '存量制度全面评估任务',
      api: list,
      columns,
      canResize: false,
      useSearchForm: false,
      actionColumn: {
        width: 120,
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
      },
      tableSetting: {
        redo: false,
        size: false,
        setting: false,
      },
      beforeFetch: async (params) => {
        return Object.assign(params, queryParam);
      },
    },
  });
  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;
  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  /**
   * 新增事件
   */
  function handleAdd() {
    registerModal.value.disableSubmit = false;
    registerModal.value.add();
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    registerModal.value.disableSubmit = false;
    registerModal.value.edit(record);
  }

  /**
   * 详情
   */
  function handleDetail(record: any) {
    detailModalRef.value.open(record);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 提交审核
   */
  async function handleSubmit() {
    let ids: any[] = [];
    let rows = selectedRows.value;
    let findRow = rows.find((row) => {
      return row.taskStatus !== '1' || row.processStatus !== '1';
    });
    if (findRow) {
      createMessage.warning('请选择待发起的草稿数据!');
      return;
    }
    ids = selectedRowKeys.value;
    submitRequest({ ids: ids }, handleSuccess);
  }

  /**
   * 督办任务
   */
  async function handleUrge() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请选择要督办的任务');
      return;
    }
    try {
      await urgeTasks(selectedRowKeys.value);
      createMessage.success('督办成功');
      handleSuccess();
    } catch (error) {
      createMessage.error('督办失败');
    }
  }

  /**
   * 操作栏
   */
  function getTableAction(record: any) {
    return [
      {
        label: '查看',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '修改',
        onClick: handleEdit.bind(null, record),
        auth: 'rule.assess.year:task:edit',
        ifShow: () => record.processStatus === '1', // 只有草稿状态才能修改
      },
    ];
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
    .ant-form-item:not(.ant-form-item-with-help) {
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),
    :deep(.ant-input-number) {
      width: 100%;
    }
  }
</style>
