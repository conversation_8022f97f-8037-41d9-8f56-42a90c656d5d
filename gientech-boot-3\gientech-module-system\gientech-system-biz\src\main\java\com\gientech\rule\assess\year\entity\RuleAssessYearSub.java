package com.gientech.rule.assess.year.entity;

import java.io.Serial;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 存量制度全面评估任务-子任务
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-29
 */
@Data
@TableName("rule_assess_year_sub")
@Accessors(chain = true)
@Schema(description="存量制度全面评估任务-子任务")
public class RuleAssessYearSub implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;

    /**评估任务主键*/
    @Excel(name = "评估任务主键", width = 15)
    @Schema(description = "评估任务主键")
    private java.lang.String yearId;

    /**子任务编号*/
    @Excel(name = "子任务编号", width = 15)
    @Schema(description = "子任务编号")
    private java.lang.String taskCode;

    /**评估机构*/
    @Excel(name = "评估机构", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "评估机构")
    private java.lang.String assessOrgCode;

    /**评估部门*/
    @Excel(name = "评估部门", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "评估部门")
    private java.lang.String assessDeptCode;

    /**下年度需修订制度数量*/
    @Excel(name = "下年度需修订制度数量", width = 15)
    @Schema(description = "下年度需修订制度数量")
    private java.lang.Integer needEditNumber;

    /**下年度需新增制度数量*/
    @Excel(name = "下年度需新增制度数量", width = 15)
    @Schema(description = "下年度需新增制度数量")
    private java.lang.Integer needAddNumber;

    /**下年度需废止制度数量*/
    @Excel(name = "下年度需废止制度数量", width = 15)
    @Schema(description = "下年度需废止制度数量")
    private java.lang.Integer needDeleteNumber;

    /**反馈人*/
    @Excel(name = "反馈人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "反馈人")
    private java.lang.String feedbackPerson;

    /**反馈时间*/
    @Excel(name = "反馈时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "反馈时间")
    private java.util.Date feedbackTime;

    /**任务状态*/
    @Excel(name = "任务状态", width = 15, dicCode = "rule_assess_pilot_sub_task_status")
    @Dict(dicCode = "rule_assess_pilot_sub_task_status")
    @Schema(description = "任务状态")
    private java.lang.String taskStatus;

    /**流程状态*/
    @Excel(name = "流程状态", width = 15, dicCode = "rule_assess_year_sub_process_status")
    @Dict(dicCode = "rule_assess_year_sub_process_status")
    @Schema(description = "流程状态")
    private java.lang.String processStatus;

    /**删除标记*/
    @Schema(description = "删除标记")
    @TableLogic
    private java.lang.Integer delFlag;

    /**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;

    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;

    /**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;

    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;

    /**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;

}
