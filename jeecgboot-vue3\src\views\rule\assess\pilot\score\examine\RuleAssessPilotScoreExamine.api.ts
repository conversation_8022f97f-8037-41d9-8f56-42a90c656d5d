import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm, createMessage } = useMessage();

enum Api {
  // 审核查询
  examineList = '/rule/assess/pilot/score/examineList',
  // 审核通过
  passRequest = '/rule/assess/pilot/score/passRequest',
  // 审核退回
  givebackRequest = '/rule/assess/pilot/score/givebackRequest',
  // 处理过程
  process = '/rule/assess/pilot/score/process',
}

/**
 * 审核列表接口
 * @param params
 */
export const examineList = (params) => defHttp.get({ url: Api.examineList, params });

/**
 * 审核通过
 * @param params
 * @param handleSuccess
 */
export const passRequest = (params: any, handleSuccess: Function) => {
  createConfirm({
    iconType: 'warning',
    title: '确认审核通过',
    content: '是否确定审核通过该数据？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      return defHttp
        .post(
          {
            url: Api.passRequest,
            data: params,
          },
          { joinParamsToUrl: true, isTransformResponse: false }
        )
        .then((res) => {
          if (res.success) {
            createMessage.success(res.message);
            handleSuccess();
          } else {
            createMessage.error(res.message);
          }
        });
    },
  });
};

/**
 * 审核退回
 * @param params
 * @param handleSuccess
 */
export const givebackRequest = (params: any, handleSuccess: Function) => {
  createConfirm({
    iconType: 'warning',
    title: '确认审核退回',
    content: '是否确定审核退回该数据？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      return defHttp
        .post(
          {
            url: Api.givebackRequest,
            data: params,
          },
          { joinParamsToUrl: true, isTransformResponse: false }
        )
        .then((res) => {
          if (res.success) {
            createMessage.success(res.message);
            handleSuccess();
          } else {
            createMessage.error(res.message);
          }
        });
    },
  });
};

/**
 * 处理过程
 * @param params
 */
export const getProcess = (params: any) => {
  return defHttp.get({ url: Api.process, params }, { isTransformResponse: false, joinParamsToUrl: true });
};
