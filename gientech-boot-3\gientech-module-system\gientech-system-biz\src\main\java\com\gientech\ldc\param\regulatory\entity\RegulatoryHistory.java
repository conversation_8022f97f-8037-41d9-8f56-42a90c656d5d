package com.gientech.ldc.param.regulatory.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 操作风险损失事件类型（监管分类）（历史表）
 * @Author: jeecg-boot
 * @Date:   2025-04-18
 * @Version: V1.0
 */
@Data
@TableName("ldc_param_or_regulatory_history")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="操作风险损失事件类型（监管分类）（历史表）")
public class RegulatoryHistory implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**一级分类*/
	@Excel(name = "一级分类", width = 15)
    @Schema(description = "一级分类")
    private String firstLevelName;
	/**简要解释*/
	@Excel(name = "简要解释", width = 15)
    @Schema(description = "简要解释")
    private String brieflyExplain;
	/**对应本地化事件分类（一级）*/
	@Excel(name = "对应本地化事件分类（一级）", width = 15)
    @Schema(description = "对应本地化事件分类（一级）")
    private String counterFirstLevelName;
	/**二级分类*/
	@Excel(name = "二级分类", width = 15)
    @Schema(description = "二级分类")
    private String secondLevelName;
	/**三级分类*/
	@Excel(name = "三级分类", width = 15)
    @Schema(description = "三级分类")
    private String threeLevelName;
	/**进一步说明或举例*/
	@Excel(name = "进一步说明或举例", width = 15)
    @Schema(description = "进一步说明或举例")
    private String furtherExplanation;
	/**表单类型*/
    @Schema(description = "表单类型")
    private Integer type;
	/**版本号*/
    @Schema(description = "版本号")
    private String version;
	/**操作人员*/
    @Schema(description = "操作人员")
    private String createId;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
}
