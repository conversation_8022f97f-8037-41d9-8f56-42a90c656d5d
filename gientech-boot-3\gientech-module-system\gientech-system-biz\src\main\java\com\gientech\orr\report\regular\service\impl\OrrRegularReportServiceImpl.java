package com.gientech.orr.report.regular.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gientech.common.process.service.ICommonProcessService;
import com.gientech.orr.constant.ReportConstant;
import com.gientech.orr.report.config.entity.OrrReportConfig;
import com.gientech.orr.report.config.mapper.OrrReportConfigMapper;
import com.gientech.orr.report.regular.entity.OrrRegularReport;
import com.gientech.orr.report.regular.mapper.OrrRegularReportMapper;
import com.gientech.orr.report.regular.service.IOrrRegularReportService;
import com.gientech.orr.report.template.entity.OrrReportTemplate;
import com.gientech.orr.report.template.mapper.OrrReportTemplateMapper;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: 操作风险报告-常规报告管理
 * @Author: jeecg-boot
 * @Date: 2025-07-08
 * @Version: V1.0
 */
@Service
public class OrrRegularReportServiceImpl extends ServiceImpl<OrrRegularReportMapper, OrrRegularReport> implements IOrrRegularReportService {

    public static final String businessKey = "orrReport";

    @Autowired
    private OrrRegularReportMapper orrRegularReportMapper;

    @Autowired
    private OrrReportTemplateMapper orrReportTemplateMapper;

    @Autowired
    private OrrReportConfigMapper orrReportConfigMapper;

    @Autowired
    private IWorkflowInstanceService instanceService;

    @Autowired
    private ICommonProcessService processService;

    @Autowired
    private IWorkflowTaskService workflowTaskService;

    /**
     * 批量提交
     *
     * @param ids
     * @return
     */
    public Result<String> batchSubmit(List<String> ids) {

        List<OrrRegularReport> orrRegularReportList = orrRegularReportMapper.selectBatchIds(ids);

        for (OrrRegularReport orrRegularReport : orrRegularReportList) {
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, orrRegularReport);

            // 开始工作流
            instanceService.createWorkflowInstance(businessKey, orrRegularReport.getId(), variables);
        }

        // 添加处理过程
        processService.saveProcessBatch(businessKey, ids, "提交审核");

        return Result.ok("提交成功，请等待审核！");
    }

    /**
     * 批量撤销
     *
     * @param ids
     * @return
     */
    public Result<String> batchRevoke(List<String> ids) {

        List<OrrRegularReport> orrRegularReportList = orrRegularReportMapper.selectBatchIds(ids);

        for (OrrRegularReport orrRegularReport : orrRegularReportList) {
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, orrRegularReport);
            if ("2".equals(orrRegularReport.getReportStatus())) {
                // 审核中
                WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, orrRegularReport.getId());
                variables.put("revoke", true);
                workflowTaskService.completeTask(workflowTask.getId(), variables);
            }else{
                // 审核通过 开始撤销申请
                instanceService.createWorkflowInstance(businessKey, orrRegularReport.getId(), variables);
            }
        }

        // 添加处理过程
        processService.saveProcessBatch(businessKey, ids, "撤销");

        return Result.ok("撤销成功，请等待审核！");
    }

    /**
     * 批量通过
     *
     * @param ids
     * @return
     */
    public Result<String> passBatch(List<String> ids) {
        List<OrrRegularReport> orrRegularReportList = orrRegularReportMapper.selectBatchIds(ids);
        for (OrrRegularReport orrRegularReport : orrRegularReportList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, orrRegularReport.getId());
            if (workflowTask != null) {
                Map<String, Object> variables = new HashMap<>();
                variables.put("approve", true);
                variables.put(businessKey, orrRegularReport);
                workflowTaskService.completeTask(workflowTask.getId(), variables);
            }
        }

        processService.saveProcessBatch(businessKey, ids, "审核通过");
        return Result.ok("通过成功！");
    }

    /**
     * 批量退回
     *
     * @param ids
     * @return
     */
    public Result<String> returnBatch(List<String> ids) {

        List<OrrRegularReport> orrRegularReportList = orrRegularReportMapper.selectBatchIds(ids);
        for (OrrRegularReport orrRegularReport : orrRegularReportList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, orrRegularReport.getId());
            if (workflowTask != null) {
                Map<String, Object> variables = new HashMap<>();
                variables.put("approve", false);
                variables.put(businessKey, orrRegularReport);
                workflowTaskService.completeTask(workflowTask.getId(), variables);
            }
        }

        processService.saveProcessBatch(businessKey, ids, "审核退回");

        return Result.ok("退回成功！");
    }

    /**
     * 重置模板
     *
     * @param id
     * @return
     */
    public Result<OrrRegularReport> resetReport(String id) {

        // 通过id查询数据
        OrrRegularReport orrRegularReport = orrRegularReportMapper.selectById(id);
        String type = orrRegularReport.getType();

        // 去查询模板
        QueryWrapper<OrrReportTemplate> orrReportTemplateQueryWrapper = new QueryWrapper<>();
        orrReportTemplateQueryWrapper.eq("type", type);

        OrrReportTemplate orrReportTemplate = orrReportTemplateMapper.selectOne(orrReportTemplateQueryWrapper);
        String reportHtml = orrReportTemplate.getReportHtml();

        // 将报告的内容进行重置
        orrRegularReport.setReportHtml(reportHtml);
        orrRegularReportMapper.updateById(orrRegularReport);

        return Result.OK(orrRegularReport);
    }

    /**
     * 通过ids批量查询查询
     *
     * @param ids
     * @return
     */
    public Result<List<OrrRegularReport>> batchQueryById(List<String> ids) {

        List<OrrRegularReport> orrRegularReportList = orrRegularReportMapper.selectBatchIds(ids);

        return Result.ok(orrRegularReportList);
    }

    /**
     * 数据导入
     *
     * @param orrRegularReport
     * @return
     */
    public Result<List<HashMap<String, Object>>> dataImport(OrrRegularReport orrRegularReport) {

        // 参数Map
        Map<String, Object> params = new HashMap<>();

        List<HashMap<String, Object>> returnObject = new ArrayList<>();

        String id = orrRegularReport.getId();
        String reportHtml = orrRegularReport.getReportHtml();

        // 查询报表信息并添加参数
        orrRegularReport = orrRegularReportMapper.selectById(id);
        String thisYear = DateUtils.formatDate(orrRegularReport.getReportYear(), "yyyy");
        params.put("thisYear", thisYear);
        params.put("beforeYear", (Integer.valueOf(thisYear) - 1));
        params.put("twoYear", (Integer.valueOf(thisYear) - 2));
        params.put("threeYear", (Integer.valueOf(thisYear) - 3));

        String reportQuarterly = orrRegularReport.getReportQuarterly();
        params.put("quarterDes", getQuarterDes(reportQuarterly));

        // 获取所有的key
        List<String> keyList = parseSymbols(reportHtml);

        // 查询报告数据，对年进行替换
        if (keyList.contains(ReportConstant.YEAR)) {

            returnObject.add(new HashMap<String, Object>() {{
                put("key", ReportConstant.YEAR);
                put("value", thisYear);
                put("type", ReportConstant.TYPE_DIGIT);
            }});
        }

        // 查询报告数据，对季度进行替换
        if (keyList.contains(ReportConstant.QUARTER)) {

            returnObject.add(new HashMap<String, Object>() {{
                put("key", ReportConstant.QUARTER);
                put("value", reportQuarterly);
                put("type", ReportConstant.TYPE_DIGIT);
            }});
        }

        // 查询配置
        QueryWrapper<OrrReportConfig> configQueryWrapper = new QueryWrapper<>();
        configQueryWrapper.in("config_key", keyList);

        List<OrrReportConfig> orrReportConfigList = orrReportConfigMapper.selectList(configQueryWrapper);

        // 文字类型：去循环，直接返回
        List<OrrReportConfig> list2 = orrReportConfigList.stream()
                .filter(config -> "2".equals(config.getConfigType()))
                .toList();
        for (OrrReportConfig orrReportConfig : list2) {
            returnObject.add(new HashMap<String, Object>() {{
                put("key", orrReportConfig.getConfigKey());
                put("value", orrReportConfig.getConfigValue());
                put("type", ReportConstant.TYPE_DIGIT);
            }});
        }

        // sql类型：执行sql语句来查询数据后再替换
        List<OrrReportConfig> list1 = orrReportConfigList.stream()
                .filter(config -> "1".equals(config.getConfigType()))
                .toList();
        for (OrrReportConfig orrReportConfig : list1) {

            String configValue = orrReportConfig.getConfigValue();

            String count = orrReportConfigMapper.queryCount(configValue, params);
            returnObject.add(new HashMap<String, Object>() {{
                put("key", orrReportConfig.getConfigKey());
                put("value", count);
                put("type", ReportConstant.TYPE_DIGIT);
            }});
        }

        // 图表类型：需要查询并返回list，来进行替换
        List<OrrReportConfig> list3 = orrReportConfigList.stream()
                .filter(config -> "3".equals(config.getConfigType()))
                .toList();
        for (OrrReportConfig orrReportConfig : list3) {

            String configValue = orrReportConfig.getConfigValue();

            List<Map<String, Object>> list = orrReportConfigMapper.queryList(configValue, params);
            returnObject.add(new HashMap<String, Object>() {{
                put("key", orrReportConfig.getConfigKey());
                put("value", list);
                put("type", ReportConstant.TYPE_CHART);
            }});
        }

        return Result.ok(returnObject);
    }

    /**
     * 通过的回调
     */
    public void batchSubmitCallback(OrrRegularReport orrRegularReport) {

        orrRegularReport.setReportStatus("3");
        orrRegularReport.setReportSubmitDate(new Date());

    }

    /**
     * 查询所有’{}‘符号中的内容
     *
     * @param html
     * @return
     */
    protected static List<String> parseSymbols(String html) {
        // 1. 移除所有HTML标签和CSS样式
        String textOnly = html.replaceAll("<[^>]+>", ""); // 移除HTML标签
        textOnly = textOnly.replaceAll("&[a-z]+;", " ");  // 替换HTML实体（如&nbsp;）
        textOnly = textOnly.replaceAll("\\s+", " ");      // 合并连续空白字符

        // 2. 解析花括号中的占位符
        List<String> symbolList = new ArrayList<>();
        Pattern pattern = Pattern.compile("\\{.*?\\}");
        Matcher matcher = pattern.matcher(textOnly);

        while (matcher.find()) {
            symbolList.add(matcher.group());
        }
        return symbolList;
    }

    /**
     * 获取季度的描述
     *
     * @param reportQuarterly
     * @return
     */
    protected static String getQuarterDes(String reportQuarterly) {
        String reportQuarterlyStr = "";

        if ("第一季度".equals(reportQuarterly)) {
            reportQuarterlyStr = "1月至3月";
        } else if ("第二季度".equals(reportQuarterly)) {
            reportQuarterlyStr = "1月至6月";
        } else if ("第三季度".equals(reportQuarterly)) {
            reportQuarterlyStr = "1月至9月";
        } else if ("第四季度".equals(reportQuarterly)) {
            reportQuarterlyStr = "1月至12月";
        }

        return reportQuarterlyStr;
    }
}
