<template>
  
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24"> 
          <a-col :lg="8">
            <a-form-item name="version">
              <template #label><span title="版本号">版本号</span></template>
              <a-select v-model:value="queryParam.historyVersion" placeholder="请选择版本号" allow-clear>
                  <a-select-option v-for="item in historyVersionList" :key="item.key" :value="item.value" @click="getVersionInfo(item)">
                    {{item.value}}
                  </a-select-option>
                </a-select>
              <!-- <a-input placeholder="" v-model:value="queryParam.history" allow-clear ></a-input> -->
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item name="updateDate">
              <template #label><span title="版本更新日期">版本更新日期</span></template>
              <!-- <a-input disabled v-model:value="updateDate"></a-input> -->
              <a-date-picker disabled v-model:value="updateDate" value-format="YYYY-MM-DD"  style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item name="createId">
              <template #label><span title="维护人">维护人</span></template>
              <a-input disabled v-model:value="createName"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <div style="color: #262626; font-family: '黑体', sans-serif; font-weight: bold; font-size: 18px;
            display: flex; justify-content: center; align-items: center; margin-left: 45%;">
           {{title}}
        </div>
        <!-- <a-button
          type="primary"
          v-auth="'ldc.param.localeventcrs:ldc_param_or_localeventcrs_history:add'"
          @click="handleAdd"
          preIcon="ant-design:plus-outlined"
        >
          新增</a-button
        >
        <a-button
          type="primary"
          v-auth="'ldc.param.localeventcrs:ldc_param_or_localeventcrs_history:exportXls'"
          preIcon="ant-design:export-outlined"
          @click="onExportXls"
        >
          导出</a-button
        >
        <j-upload-button
          type="primary"
          v-auth="'ldc.param.localeventcrs:ldc_param_or_localeventcrs_history:importExcel'"
          preIcon="ant-design:import-outlined"
          @click="onImportXls"
          >导入</j-upload-button
        >
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'ldc.param.localeventcrs:ldc_param_or_localeventcrs_history:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown> -->
        <!-- 高级查询 -->
        <!-- <super-query :config="superQueryConfig" @search="handleSuperQuery" /> -->
      </template>
      <!--操作栏-->
      <!-- <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template> -->
      <template v-slot:bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <div style="margin: auto;  margin-left: 45%; margin-top: 20px;">
        <a-button type="default" @click="bankList"> < 返回   </a-button>
    </div>
    <!-- 表单区域 -->
    <LdcParamOrLocaleventcrsHistoryModal ref="registerModal" @success="handleSuccess"></LdcParamOrLocaleventcrsHistoryModal>
  </div>
</template>

<script lang="ts" name="ldc.param.localeventcrs-ldcParamOrLocaleventcrsHistory" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, superQuerySchema } from './LdcParamOrLocaleventcrsHistory.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, querylecrsversionlist, getUpdateAndUser } from './LdcParamOrLocaleventcrsHistory.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import LdcParamOrLocaleventcrsHistoryModal from './components/LdcParamOrLocaleventcrsHistoryModal.vue';
  import { useUserStore } from '/@/store/modules/user';

  let propsData = defineProps(['version']);
  const historyVersionList = ref([]);
  const updateDate = ref();
  const createName = ref();
  const title = ref("本地化事件分类及填报标准");
  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const userStore = useUserStore();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '本地化事件分类及填报历史',
      api: list,
      columns,
      canResize: false,
      useSearchForm: false,
      showActionColumn: false,
      showHeader: true,
      showTableSetting: false,
      showIndexColumn: true,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: async (params) => {
        return Object.assign(params, queryParam);
      },
      defSort: {
        column: ["eventLevelOne", "id"],
        order: "asc"
      },
    },
    exportConfig: {
      name: '本地化事件分类及填报历史',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] = tableContext;
  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    registerModal.value.disableSubmit = false;
    registerModal.value.add();
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    registerModal.value.disableSubmit = false;
    registerModal.value.edit(record);
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'ldc.param.localeventcrs:ldc_param_or_localeventcrs_history:edit',
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'ldc.param.localeventcrs:ldc_param_or_localeventcrs_history:delete',
      },
    ];
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }

  // 返回
  const emit = defineEmits(['lechBack']);
  function bankList() {
    emit('lechBack');
  }

  onMounted(() => {
    queryParam.version = propsData.version.replace('V', '');
    // 查询历史版本下拉列表
    getHistoryAllVersionList();
  });

  async function getHistoryAllVersionList(){
    const res = await querylecrsversionlist();
    historyVersionList.value = res;
  }

  async function getVersionInfo(item) {
    title.value = "本地化事件分类及填报标准" + item.value;
    queryParam.version = item.key;
    // 根据版本号，查找对应的维护人和更新日期
    const res = await getUpdateAndUser({'version': item.key});
    updateDate.value = res.updateDate;
    createName.value = res.createName;
    reload();
  }


</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
    .ant-form-item:not(.ant-form-item-with-help) {
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),
    :deep(.ant-input-number) {
      width: 100%;
    }
  }
</style>
