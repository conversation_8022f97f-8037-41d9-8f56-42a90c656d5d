<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam"
              :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="8">
            <a-form-item name="clueNumber">
              <template #label><span title="线索编号">线索编号</span></template>
              <JInput v-model:value="queryParam.clueNumber" placeholder="请输入线索编号"/>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item name="clueName">
              <template #label><span title="线索名称">线索名称</span></template>
              <JInput v-model:value="queryParam.clueName" placeholder="请输入线索名称"/>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :lg="8">
              <a-form-item name="reportYear">
                <template #label><span title="所属报告年度">所属报告年度</span></template>
                <a-date-picker picker="year" valueFormat="YYYY-MM-DD"
                               placeholder="请选择所属报告年度"
                               v-model:value="queryParam.reportYear" allow-clear/>
              </a-form-item>
            </a-col>
            <a-col :lg="8">
              <a-form-item name="reportQuarterly">
                <template #label><span title="所属报告季度">所属报告季度</span></template>
                <j-select-multiple placeholder="请选择所属报告年度"
                                   v-model:value="queryParam.reportQuarterly"
                                   dictCode="orr_report_quarterly" allow-clear/>
              </a-form-item>
            </a-col>
            <a-col :lg="8">
              <a-form-item name="clueStatus">
                <template #label><span title="线索状态">线索状态</span></template>
                <j-select-multiple v-if="props.processKey == 1" placeholder="请选择线索状态"
                                   v-model:value="queryParam.clueStatus" dictCode="orr_clue_status_processing"
                                   allow-clear/>
                <j-select-multiple v-if="props.processKey == 2" placeholder="请选择线索状态"
                                   v-model:value="queryParam.clueStatus" dictCode="orr_clue_status_processed"
                                   allow-clear/>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset"
                          style="margin-left: 8px">重置</a-button>
                <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <Icon
                      :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'"/>
                </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button v-if="props.processKey == 1" type="primary" v-auth="''" @click="handleExamine"
                  preIcon="ant-design:check-outlined"> 通过
        </a-button>
        <a-button v-if="props.processKey == 1" type="primary" v-auth="''" @click="handleRevoke"
                  preIcon="ant-design:rollback-outlined"> 退回
        </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"/>
      </template>
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
    <ProcessModal ref="processRef" :getProcess="getProcess"/>
  </div>
</template>

<script lang="ts" name="orr.clue-orrReportClue" setup>
import {ref, reactive, defineProps} from 'vue';
import {BasicTable, TableAction} from '/@/components/Table';
import {useListPage} from '/@/hooks/system/useListPage';
import {columns} from '../../clue/OrrReportClue.data';
import {
  processList,
  passBatch,
  returnBatch,
  getProcess,
} from '../../clue/OrrReportClue.api';
import {useUserStore} from '/@/store/modules/user';
import JSelectMultiple from '/src/components/Form/src/jeecg/components/JSelectMultiple.vue';
import JInput from "/src/components/Form/src/jeecg/components/JInput.vue";
import {useMessage} from "@/hooks/web/useMessage";
import ProcessModal from "@/views/kri/input/components/ProcessModal.vue";

const props = defineProps({
  processKey: {type: Number, default: true},
});
const formRef = ref();
const processRef = ref();
const queryParam = reactive<any>({});
const toggleSearchStatus = ref<boolean>(false);
const registerModal = ref();
const userStore = useUserStore();
const {createMessage} = useMessage();
//注册table数据
const {prefixCls, tableContext, onExportXls, onImportXls} = useListPage({
  tableProps: {
    title: '操作风险报告-线索表',
    api: processList,
    columns,
    canResize: false,
    useSearchForm: false,
    actionColumn: {
      width: 140,
      fixed: 'right',
    },
    defSort: {
      column: 'clueNumber',
      order: 'desc'
    },
    beforeFetch: async (params) => {
      queryParam.processKey = props.processKey;
      return Object.assign(params, queryParam);
    },
  },
});
const [registerTable, {
  reload,
  collapseAll,
  updateTableDataRecord,
  findTableDataRecord,
  getDataSource
}, {rowSelection, selectedRowKeys, selectedRows}] = tableContext;
const labelCol = reactive({
  xs: 8,
  sm: 8,
  xl: 8,
  xxl: 6
});
const wrapperCol = reactive({
  xs: 16,
  sm: 16,
});

/**
 * 批量通过
 */
async function handleExamine() {

  let rowKeys = selectedRowKeys.value;

  if (rowKeys.length <= 0) {
    createMessage.warn("请选择报告线索！");
    return;
  }

  await passBatch({ids: rowKeys})
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          handleSuccess();
        } else {
          createMessage.warn(res.message);
        }
      })
      .finally(() => {

      })

}

/**
 * 批量退回
 */
async function handleRevoke() {

  let rowKeys = selectedRowKeys.value;

  if (rowKeys.length <= 0) {
    createMessage.warn("请选择报告线索！");
    return;
  }

  await returnBatch({ids: rowKeys})
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          handleSuccess();
        } else {
          createMessage.warn(res.message);
        }
      })
      .finally(() => {

      })

}

/**
 * 查看
 */
function handleDetail(record: Recordable) {
  registerModal.value.disableSubmit = true;
  registerModal.value.edit(record);
}

/**
 * 处理过程
 */
function handleProcess(record: Recordable) {
  processRef.value.handleOpen(record.id);
}

/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '查看',
      onClick: handleDetail.bind(null, record),
      auth: 'orr.clue:orr_report_clue:edit'
    },
    {
      label: '处理过程',
      onClick: handleProcess.bind(null, record),
      auth: 'orr.clue:orr_report_clue:edit'
    },
  ];
}

/**
 * 查询
 */
function searchQuery() {
  reload();
}

/**
 * 重置
 */
function searchReset() {
  formRef.value.resetFields();
  selectedRowKeys.value = [];
  //刷新数据
  reload();
}

defineExpose({
  searchQuery
});

</script>

<style lang="less" scoped>
.jeecg-basic-table-form-container {
  padding: 0;

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }

  .query-group-cust {
    min-width: 100px !important;
  }

  .query-group-split-cust {
    width: 30px;
    display: inline-block;
    text-align: center
  }

  .ant-form-item:not(.ant-form-item-with-help) {
    margin-bottom: 16px;
    height: 32px;
  }

  :deep(.ant-picker), :deep(.ant-input-number) {
    width: 100%;
  }
}
</style>
