package com.gientech.rule.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.gientech.rule.manage.reformedAndAbolished.mapper.RuleSystemReformedAndAbolishedMapper;
import com.gientech.rule.system.constant.RuleSystemConsultConstant;
import com.gientech.rule.system.entity.RuleSystemConsult;
import com.gientech.rule.system.entity.RuleSystemConsultRelate;
import com.gientech.rule.system.mapper.RuleSystemConsultMapper;
import com.gientech.rule.system.mapper.RuleSystemConsultRelateMapper;
import com.gientech.rule.system.service.IRuleSystemConsultService;
import com.gientech.rule.system.vo.RuleSystemConsultFeedbackVO;
import com.gientech.rule.system.vo.RuleSystemConsultVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 内外规模块-制度咨询表
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-21
 */
@Service
public class RuleSystemConsultServiceImpl extends ServiceImpl<RuleSystemConsultMapper, RuleSystemConsult> implements IRuleSystemConsultService {

    // 制度立改废(制度信息表)
    private RuleSystemReformedAndAbolishedMapper ruleSystemReformedAndAbolishedMapper;

    @Autowired
    public void setRuleSystemReformedAndAbolishedMapper(RuleSystemReformedAndAbolishedMapper ruleSystemReformedAndAbolishedMapper) {
        this.ruleSystemReformedAndAbolishedMapper = ruleSystemReformedAndAbolishedMapper;
    }

    @Autowired
    private RuleSystemConsultRelateMapper ruleSystemConsultRelateMapper;

    @Override
    public Page<RuleSystemConsultVO> selectVOPage(Page<RuleSystemConsultVO> page, Wrapper<RuleSystemConsultVO> queryWrapper) {
        Page<RuleSystemConsultVO> ruleSystemConsultVOPage = ruleSystemReformedAndAbolishedMapper.selectConsultVOPage(page, queryWrapper);
        ruleSystemConsultVOPage.getRecords().forEach(ruleSystemConsultVO -> {
            // 查询子表咨询信息
            List<RuleSystemConsult> ruleSystemConsults = baseMapper.selectByRelateId(ruleSystemConsultVO.getId());
            ruleSystemConsultVO.setConsultList(ruleSystemConsults);
        });
        return ruleSystemConsultVOPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveVO(RuleSystemConsultVO ruleSystemConsultVO) {
        RuleSystemConsultRelate ruleSystemConsultRelate = new RuleSystemConsultRelate();
        ruleSystemConsultRelate.setSystemId(ruleSystemConsultVO.getSystemId());
        ruleSystemConsultRelateMapper.insert(ruleSystemConsultRelate);
        // 插入子表数据
        List<RuleSystemConsult> consultList = ruleSystemConsultVO.getConsultList();
        if (consultList != null && !consultList.isEmpty()) {
            consultList.forEach(consult -> {
                consult.setRelateId(ruleSystemConsultRelate.getId());
                consult.setSystemId(ruleSystemConsultRelate.getSystemId());
            });
            String sqlStatement = SqlHelper.getSqlStatement(mapperClass, SqlMethod.INSERT_ONE);
            return this.executeBatch(consultList, 100, (sqlSession, entity) -> sqlSession.insert(sqlStatement, entity));
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateVO(RuleSystemConsultVO ruleSystemConsultVO) {
        // 删除子表数据
        baseMapper.deleteByRelateId(ruleSystemConsultVO.getId());
        List<RuleSystemConsult> consultList = ruleSystemConsultVO.getConsultList();
        if (consultList != null && !consultList.isEmpty()) {
            consultList.forEach(consult -> {
                consult.setRelateId(ruleSystemConsultVO.getId());
                consult.setSystemId(ruleSystemConsultVO.getSystemId());
            });
            String sqlStatement = SqlHelper.getSqlStatement(mapperClass, SqlMethod.INSERT_ONE);
            return this.executeBatch(consultList, 100, (sqlSession, entity) -> sqlSession.insert(sqlStatement, entity));
        }
        return false;
    }

    @Override
    public Page<RuleSystemConsultFeedbackVO> selectFeedbackVOPage(Page<RuleSystemConsultFeedbackVO> page, Wrapper<RuleSystemConsultFeedbackVO> queryWrapper) {
        return baseMapper.selectFeedbackVOPage(page, queryWrapper);
    }

    @Override
    public boolean feedback(RuleSystemConsultFeedbackVO ruleSystemConsultFeedbackVO) {
        RuleSystemConsult ruleSystemConsult = new RuleSystemConsult();
        BeanUtils.copyProperties(ruleSystemConsultFeedbackVO, ruleSystemConsult);
        ruleSystemConsult.setConsultStatus(RuleSystemConsultConstant.CONSULT_STATUS_HAS);
        return SqlHelper.retBool(baseMapper.updateById(ruleSystemConsult));
    }
}
