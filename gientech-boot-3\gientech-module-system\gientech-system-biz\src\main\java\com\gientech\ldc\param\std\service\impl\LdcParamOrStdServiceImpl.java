package com.gientech.ldc.param.std.service.impl;

import com.gientech.ldc.param.grade.entity.LdcParamOrGradeCriterionEdit;
import com.gientech.ldc.param.grade.entity.LdcParamOrGradeCriterionHistory;
import com.gientech.ldc.param.grade.entity.LdcParamOrGradeCriterionVersion;
import com.gientech.ldc.param.grade.mapper.LdcParamOrGradeCriterionEditMapper;
import com.gientech.ldc.param.grade.mapper.LdcParamOrGradeCriterionHistoryMapper;
import com.gientech.ldc.param.grade.mapper.LdcParamOrGradeCriterionVersionMapper;
import com.gientech.ldc.param.importorec.entity.LdcParamOrImportOrecEdit;
import com.gientech.ldc.param.importorec.entity.LdcParamOrImportOrecHistory;
import com.gientech.ldc.param.importorec.entity.LdcParamOrImportOrecVersion;
import com.gientech.ldc.param.importorec.mapper.LdcParamOrImportOrecEditMapper;
import com.gientech.ldc.param.importorec.mapper.LdcParamOrImportOrecHistoryMapper;
import com.gientech.ldc.param.importorec.mapper.LdcParamOrImportOrecVersionMapper;
import com.gientech.ldc.param.localeventcrs.entity.LdcParamOrLocaleventcrsEdit;
import com.gientech.ldc.param.localeventcrs.entity.LdcParamOrLocaleventcrsHistory;
import com.gientech.ldc.param.localeventcrs.entity.LdcParamOrLocaleventcrsVersion;
import com.gientech.ldc.param.localeventcrs.mapper.LdcParamOrLocaleventcrsEditMapper;
import com.gientech.ldc.param.localeventcrs.mapper.LdcParamOrLocaleventcrsHistoryMapper;
import com.gientech.ldc.param.localeventcrs.mapper.LdcParamOrLocaleventcrsVersionMapper;
import com.gientech.ldc.param.lossform.entity.LdcParamOrLossFormEdit;
import com.gientech.ldc.param.lossform.entity.LdcParamOrLossFormHistory;
import com.gientech.ldc.param.lossform.entity.LdcParamOrLossFormVersion;
import com.gientech.ldc.param.lossform.mapper.LdcParamOrLossFormEditMapper;
import com.gientech.ldc.param.lossform.mapper.LdcParamOrLossFormHistoryMapper;
import com.gientech.ldc.param.lossform.mapper.LdcParamOrLossFormVersionMapper;
import com.gientech.ldc.param.regulatory.entity.RegulatoryEdit;
import com.gientech.ldc.param.regulatory.entity.RegulatoryHistory;
import com.gientech.ldc.param.regulatory.entity.RegulatoryVersion;
import com.gientech.ldc.param.regulatory.mapper.RegulatoryEditMapper;
import com.gientech.ldc.param.regulatory.mapper.RegulatoryHistoryMapper;
import com.gientech.ldc.param.regulatory.mapper.RegulatoryVersionMapper;
import com.gientech.ldc.param.riskCause.entity.RiskCauseEdit;
import com.gientech.ldc.param.riskCause.entity.RiskCauseHistory;
import com.gientech.ldc.param.riskCause.entity.RiskCauseVersion;
import com.gientech.ldc.param.riskCause.mapper.RiskCauseEditMapper;
import com.gientech.ldc.param.riskCause.mapper.RiskCauseHistoryMapper;
import com.gientech.ldc.param.riskCause.mapper.RiskCauseVersionMapper;
import com.gientech.ldc.param.std.entity.LdcParamOrStd;
import com.gientech.ldc.param.std.mapper.LdcParamOrStdMapper;
import com.gientech.ldc.param.std.service.ILdcParamOrStdService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.mapper.SysUserMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Description: 操作风险事件相关标准参数管理
 * @Author: jeecg-boot
 * @Date:   2025-04-17
 * @Version: V1.0
 */
@Service
public class LdcParamOrStdServiceImpl extends ServiceImpl<LdcParamOrStdMapper, LdcParamOrStd> implements ILdcParamOrStdService {

    @Autowired
    private LdcParamOrStdMapper ldcParamOrStdMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private LdcParamOrLocaleventcrsEditMapper ldcParamOrLocaleventcrsEditMapper;
    @Autowired
    private LdcParamOrLocaleventcrsVersionMapper ldcParamOrLocaleventcrsVersionMapper;
    @Autowired
    private LdcParamOrLocaleventcrsHistoryMapper ldcParamOrLocaleventcrsHistoryMapper;
    @Autowired
    private LdcParamOrImportOrecEditMapper ldcParamOrImportOrecEditMapper;
    @Autowired
    private LdcParamOrImportOrecVersionMapper ldcParamOrImportOrecVersionMapper;
    @Autowired
    private LdcParamOrImportOrecHistoryMapper ldcParamOrImportOrecHistoryMapper;

    @Autowired
    private LdcParamOrLossFormEditMapper ldcParamOrLossFormEditMapper;
    @Autowired
    private LdcParamOrLossFormVersionMapper ldcParamOrLossFormVersionMapper;
    @Autowired
    private LdcParamOrLossFormHistoryMapper ldcParamOrLossFormHistoryMapper;

    @Autowired
    private LdcParamOrGradeCriterionEditMapper ldcParamOrGradeCriterionEditMapper;
    @Autowired
    private LdcParamOrGradeCriterionVersionMapper ldcParamOrGradeCriterionVersionMapper;
    @Autowired
    private LdcParamOrGradeCriterionHistoryMapper ldcParamOrGradeCriterionHistoryMapper;

    @Autowired
    private RegulatoryEditMapper regulatoryEditMapper;
    @Autowired
    private RegulatoryVersionMapper regulatoryVersionMapper;
    @Autowired
    private RegulatoryHistoryMapper regulatoryHistoryMapper;

    @Autowired
    private RiskCauseEditMapper riskCauseEditMapper;
    @Autowired
    private RiskCauseHistoryMapper riskCauseHistoryMapper;
    @Autowired
    private RiskCauseVersionMapper riskCauseVersionMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> setValidation(LdcParamOrStd ldcParamOrStd) {
        String type = ldcParamOrStd.getType();
        // 第一步：查询参数类型对应的编辑表中是否存在本人修改的数据
        // 第二步：若存在
        // 2.1 将版本数据insert到历史版本表中，
        // 2.2 删除版本库中 在编辑表中存在的数据 和 版本库中状态为已删除的数据
        // 2.3 将编辑表中的数据insert到版本库中，（编辑表中的状态为删除的数据同样需要insert）
        // 2.4 将版本库中的版本号 + 0.1
        // 2.5 将主页面的版本号+ 0.1，同时更新操作人，生效时间
        // 第三步：若不存在，则提示无数据变更，不能进行操作

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        String userId = sysUser.getId();
        // 查询编辑表是否存在本人的数据
        Map<String, Object> map = new HashMap<>();
        //map.put("create_id", userId);
        map.put("create_by", userName);
        if ("1".equals(type)) {
            List<LdcParamOrLocaleventcrsEdit> editList = ldcParamOrLocaleventcrsEditMapper.selectByMap(map);
            if (editList == null || editList.isEmpty()) {
                return Result.error("没有本人修改的数据");
            } else {
                // 将正式表数据移入历史表
                List<LdcParamOrLocaleventcrsVersion> versionList = ldcParamOrLocaleventcrsVersionMapper.selectByMap(new HashMap<>());
                for (LdcParamOrLocaleventcrsVersion version : versionList) {
                    LdcParamOrLocaleventcrsHistory history = new LdcParamOrLocaleventcrsHistory();
                    BeanUtils.copyProperties(version, history);
                    history.setId(null);
                    ldcParamOrLocaleventcrsHistoryMapper.insert(history);
                }
                // 删除正式表数据
                ldcParamOrLocaleventcrsVersionMapper.deleteByMap(new HashMap<>());
                // 将编辑表本人数据入库正式表
                for (LdcParamOrLocaleventcrsEdit edit : editList) {
                    LdcParamOrLocaleventcrsVersion version = new LdcParamOrLocaleventcrsVersion();
                    BeanUtils.copyProperties(edit, version);
                    version.setId(null);
                    version.setUpdateTime(new Date());
                    version.setUpdateBy(userName);
                    version.setCreateId(userId);
                    ldcParamOrLocaleventcrsVersionMapper.insert(version);
                }
                // 删除编辑表本人
                Map<String, Object> deleteMap = new HashMap<>();
                //deleteMap.put("create_id", userId);
                map.put("create_by", userName);
                ldcParamOrLocaleventcrsEditMapper.deleteByMap(deleteMap);
            }
        } else if ("2".equals(type)) {
            // 操作风险损失事件类型（监管分类）
            //map.put("create_id", userId);
            map.put("create_by", userName);
            List<RegulatoryEdit> editList = regulatoryEditMapper.selectByMap(map);
            if (editList == null || editList.isEmpty()) {
                return Result.error("没有本人修改的数据");
            } else {
                // 将正式表数据移入历史表
                List<RegulatoryVersion> versionList = regulatoryVersionMapper.selectByMap(new HashMap<>());
                for (RegulatoryVersion version : versionList) {
                    RegulatoryHistory history = new RegulatoryHistory();
                    BeanUtils.copyProperties(version, history);
                    history.setId(null);
                    regulatoryHistoryMapper.insert(history);
                }
                // 删除正式表数据
                regulatoryVersionMapper.deleteByMap(new HashMap<>());
                // 将编辑表本人数据入库正式表
                for (RegulatoryEdit edit : editList) {
                    RegulatoryVersion version = new RegulatoryVersion();
                    BeanUtils.copyProperties(edit, version);
                    version.setId(null);
                    version.setUpdateTime(new Date());
                    version.setUpdateBy(userName);
                    version.setCreateId(userId);
                    regulatoryVersionMapper.insert(version);
                }
                // 删除编辑表本人
                Map<String, Object> deleteMap = new HashMap<>();
                //deleteMap.put("create_id", userId);
                map.put("create_by", userName);
                regulatoryEditMapper.deleteByMap(deleteMap);
            }
        } else if ("3".equals(type)) {
            // 操作风险事件损失形态分类
            //map.put("create_id", userId);
            map.put("create_by", userName);
            List<LdcParamOrLossFormEdit> editList = ldcParamOrLossFormEditMapper.selectByMap(map);
            if (editList == null || editList.isEmpty()) {
                return Result.error("没有本人修改的数据");
            } else {
                // 将正式表数据移入历史表
                List<LdcParamOrLossFormVersion> versionList = ldcParamOrLossFormVersionMapper.selectByMap(new HashMap<>());
                for (LdcParamOrLossFormVersion version : versionList) {
                    LdcParamOrLossFormHistory history = new LdcParamOrLossFormHistory();
                    BeanUtils.copyProperties(version, history);
                    history.setId(null);
                    ldcParamOrLossFormHistoryMapper.insert(history);
                }
                // 删除正式表数据
                ldcParamOrLossFormVersionMapper.deleteByMap(new HashMap<>());
                // 将编辑表本人数据入库正式表
                for (LdcParamOrLossFormEdit edit : editList) {
                    LdcParamOrLossFormVersion version = new LdcParamOrLossFormVersion();
                    BeanUtils.copyProperties(edit, version);
                    version.setId(null);
                    version.setUpdateTime(new Date());
                    version.setUpdateBy(userName);
                    version.setCreateId(userId);
                    ldcParamOrLossFormVersionMapper.insert(version);
                }
                // 删除编辑表本人
                Map<String, Object> deleteMap = new HashMap<>();
                //deleteMap.put("create_id", userId);
                map.put("create_by", userName);
                ldcParamOrLossFormEditMapper.deleteByMap(deleteMap);
            }
        } else if ("4".equals(type)) {
            // 操作风险事件影响评估及严重度分级标准
            //map.put("create_id", userId);
            map.put("create_by", userName);
            List<LdcParamOrGradeCriterionEdit> editList = ldcParamOrGradeCriterionEditMapper.selectByMap(map);
            if (editList == null || editList.isEmpty()) {
                return Result.error("没有本人修改的数据");
            } else {
                // 将正式表数据移入历史表
                List<LdcParamOrGradeCriterionVersion> versionList = ldcParamOrGradeCriterionVersionMapper.selectByMap(new HashMap<>());
                for (LdcParamOrGradeCriterionVersion version : versionList) {
                    LdcParamOrGradeCriterionHistory history = new LdcParamOrGradeCriterionHistory();
                    BeanUtils.copyProperties(version, history);
                    history.setId(null);
                    ldcParamOrGradeCriterionHistoryMapper.insert(history);
                }
                // 删除正式表数据
                ldcParamOrGradeCriterionVersionMapper.deleteByMap(new HashMap<>());
                // 将编辑表本人数据入库正式表
                for (LdcParamOrGradeCriterionEdit edit : editList) {
                    LdcParamOrGradeCriterionVersion version = new LdcParamOrGradeCriterionVersion();
                    BeanUtils.copyProperties(edit, version);
                    version.setId(null);
                    version.setUpdateTime(new Date());
                    version.setUpdateBy(userName);
                    version.setCreateId(userId);
                    ldcParamOrGradeCriterionVersionMapper.insert(version);
                }
                // 删除编辑表本人
                Map<String, Object> deleteMap = new HashMap<>();
                //deleteMap.put("create_id", userId);
                map.put("create_by", userName);
                ldcParamOrGradeCriterionEditMapper.deleteByMap(deleteMap);
            }
        } else if ("5".equals(type)) {
            // 操作风险原因分类
            //map.put("create_id", userId);
            map.put("create_by", userName);
            List<RiskCauseEdit> editList = riskCauseEditMapper.selectByMap(map);
            if (editList == null || editList.isEmpty()) {
                return Result.error("没有本人修改的数据");
            } else {
                // 将正式表数据移入历史表
                List<RiskCauseVersion> versionList = riskCauseVersionMapper.selectByMap(new HashMap<>());
                for (RiskCauseVersion version : versionList) {
                    RiskCauseHistory history = new RiskCauseHistory();
                    BeanUtils.copyProperties(version, history);
                    history.setId(null);
                    riskCauseHistoryMapper.insert(history);
                }
                // 删除正式表数据
                riskCauseVersionMapper.deleteByMap(new HashMap<>());
                // 将编辑表本人数据入库正式表
                for (RiskCauseEdit edit : editList) {
                    RiskCauseVersion version = new RiskCauseVersion();
                    BeanUtils.copyProperties(edit, version);
                    version.setId(null);
                    version.setUpdateTime(new Date());
                    version.setUpdateBy(userName);
                    version.setCreateId(userId);
                    riskCauseVersionMapper.insert(version);
                }
                // 删除编辑表本人
                Map<String, Object> deleteMap = new HashMap<>();
                //deleteMap.put("create_id", userId);
                map.put("create_by", userName);
                riskCauseEditMapper.deleteByMap(deleteMap);
            }
        } else if ("6".equals(type)) {
            //map.put("create_id", userId);
            map.put("create_by", userName);
            List<LdcParamOrImportOrecEdit> editList = ldcParamOrImportOrecEditMapper.selectByMap(map);
            if (editList == null || editList.isEmpty()) {
                return Result.error("没有本人修改的数据");
            } else {
                // 将正式表数据移入历史表
                List<LdcParamOrImportOrecVersion> versionList = ldcParamOrImportOrecVersionMapper.selectByMap(new HashMap<>());
                for (LdcParamOrImportOrecVersion version : versionList) {
                    LdcParamOrImportOrecHistory history = new LdcParamOrImportOrecHistory();
                    BeanUtils.copyProperties(version, history);
                    history.setId(null);
                    version.setUpdateTime(new Date());
                    version.setUpdateBy(userName);
                    ldcParamOrImportOrecHistoryMapper.insert(history);
                }
                // 删除正式表数据
                ldcParamOrImportOrecVersionMapper.deleteByMap(new HashMap<>());
                // 将编辑表本人数据入库正式表
                for (LdcParamOrImportOrecEdit edit : editList) {
                    LdcParamOrImportOrecVersion version = new LdcParamOrImportOrecVersion();
                    BeanUtils.copyProperties(edit, version);
                    version.setId(null);
                    version.setUpdateTime(new Date());
                    version.setUpdateBy(userName);
                    version.setCreateId(userId);
                    ldcParamOrImportOrecVersionMapper.insert(version);
                }
                // 删除编辑表本人
                Map<String, Object> deleteMap = new HashMap<>();
                // deleteMap.put("create_id", userId);
                map.put("create_by", userName);
                ldcParamOrImportOrecEditMapper.deleteByMap(deleteMap);
            }
        }
        // 更新参数表
        ldcParamOrStd.setOperateUser(userId);
        ldcParamOrStd.setValidationTime(new Date());
        ldcParamOrStd.setValidationState("Y");
        ldcParamOrStdMapper.updateById(ldcParamOrStd);
        return Result.OK("设为已生效成功");
    }

    @Override
    public Result<String> setUnValidation(LdcParamOrStd ldcParamOrStd) {
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String userId = sysUser.getUsername();
            LdcParamOrStd std = new LdcParamOrStd();
            std.setId(ldcParamOrStd.getId());
            std.setValidationState("N");
            std.setOperateUser(userId);
            ldcParamOrStdMapper.updateById(std);
            return Result.OK("设为未生效成功");
        } catch (Exception e) {
            log.error("设为未生效异常", e);
            return Result.error("设为未生效异常");
        }
    }

    @Override
    public List<LdcParamOrStd> judgeVersionByUser(List<LdcParamOrStd> resultOld) {
        List<LdcParamOrStd> result = new LinkedList<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        String userId = sysUser.getId();
        // 遍历
        for (LdcParamOrStd ldcParamOrStd : resultOld) {
            String type = ldcParamOrStd.getType();
            String version = "";
            // 查询编辑表是否存在本人的数据
            Map<String, Object> map = new HashMap<>();
            map.put("create_id", userId);
            if ("1".equals(type)) {
                List<LdcParamOrLocaleventcrsEdit> editList = ldcParamOrLocaleventcrsEditMapper.selectByMap(map);
                if (editList != null && !editList.isEmpty()) {
                    version = "V" + editList.get(0).getVersion();
                    ldcParamOrStd.setValidationTime(null);
                    ldcParamOrStd.setOperateUser(userId);
                    ldcParamOrStd.setValidationState("N");
                    ldcParamOrStd.setOperateUserName(sysUser.getRealname());
                } else {
                    // 拿正式版本
                    String maxVersion = ldcParamOrLocaleventcrsVersionMapper.selectMaxVersion();
                    if (StringUtils.isNotBlank(maxVersion)) {
                        version = "V" + maxVersion;
                    }
                    if (StringUtils.isNotBlank(ldcParamOrStd.getOperateUser())) {
                        SysUser sysUser1 = sysUserMapper.selectById(ldcParamOrStd.getOperateUser());
                        ldcParamOrStd.setOperateUserName(sysUser1.getRealname());
                    }
                }
                ldcParamOrStd.setVersion(version);
            } else if ("2".equals(type)) {
                List<RegulatoryEdit> editList = regulatoryEditMapper.selectByMap(map);
                if (editList != null && !editList.isEmpty()) {
                    version = "V" + editList.get(0).getVersion();
                    ldcParamOrStd.setValidationTime(null);
                    ldcParamOrStd.setOperateUser(userId);
                    ldcParamOrStd.setValidationState("N");
                    ldcParamOrStd.setOperateUserName(sysUser.getRealname());
                } else {
                    // 拿正式版本
                    String maxVersion = regulatoryVersionMapper.selectMaxVersion();
                    if (StringUtils.isNotBlank(maxVersion)) {
                        version = "V" + maxVersion;
                    }
                    if (StringUtils.isNotBlank(ldcParamOrStd.getOperateUser())) {
                        SysUser sysUser1 = sysUserMapper.selectById(ldcParamOrStd.getOperateUser());
                        ldcParamOrStd.setOperateUserName(sysUser1.getRealname());
                    }
                }
                ldcParamOrStd.setVersion(version);
            } else if ("3".equals(type)) {
                List<LdcParamOrLossFormEdit> editList = ldcParamOrLossFormEditMapper.selectByMap(map);
                if (editList != null && !editList.isEmpty()) {
                    version = "V" + editList.get(0).getVersion();
                    ldcParamOrStd.setValidationTime(null);
                    ldcParamOrStd.setOperateUser(userId);
                    ldcParamOrStd.setValidationState("N");
                    ldcParamOrStd.setOperateUserName(sysUser.getRealname());
                } else {
                    // 拿正式版本
                    String maxVersion = ldcParamOrLossFormVersionMapper.selectMaxVersion();
                    if (StringUtils.isNotBlank(maxVersion)) {
                        version = "V" + maxVersion;
                    }
                    if (StringUtils.isNotBlank(ldcParamOrStd.getOperateUser())) {
                        SysUser sysUser1 = sysUserMapper.selectById(ldcParamOrStd.getOperateUser());
                        ldcParamOrStd.setOperateUserName(sysUser1.getRealname());
                    }
                }
                ldcParamOrStd.setVersion(version);
            } else if ("4".equals(type)) {
                List<LdcParamOrGradeCriterionEdit> editList = ldcParamOrGradeCriterionEditMapper.selectByMap(map);
                if (editList != null && !editList.isEmpty()) {
                    version = "V" + editList.get(0).getVersion();
                    ldcParamOrStd.setValidationTime(null);
                    ldcParamOrStd.setOperateUser(userId);
                    ldcParamOrStd.setValidationState("N");
                    ldcParamOrStd.setOperateUserName(sysUser.getRealname());
                } else {
                    // 拿正式版本
                    String maxVersion = ldcParamOrGradeCriterionVersionMapper.selectMaxVersion();
                    if (StringUtils.isNotBlank(maxVersion)) {
                        version = "V" + maxVersion;
                    }
                    if (StringUtils.isNotBlank(ldcParamOrStd.getOperateUser())) {
                        SysUser sysUser1 = sysUserMapper.selectById(ldcParamOrStd.getOperateUser());
                        ldcParamOrStd.setOperateUserName(sysUser1.getRealname());
                    }
                }
                ldcParamOrStd.setVersion(version);
            } else if ("5".equals(type)) {
                List<RiskCauseEdit> editList = riskCauseEditMapper.selectByMap(map);
                if (editList != null && !editList.isEmpty()) {
                    version = "V" + editList.get(0).getVersion();
                    ldcParamOrStd.setValidationTime(null);
                    ldcParamOrStd.setOperateUser(userId);
                    ldcParamOrStd.setValidationState("N");
                    ldcParamOrStd.setOperateUserName(sysUser.getRealname());
                } else {
                    // 拿正式版本
                    String maxVersion = riskCauseVersionMapper.selectMaxVersion();
                    if (StringUtils.isNotBlank(maxVersion)) {
                        version = "V" + maxVersion;
                    }
                    if (StringUtils.isNotBlank(ldcParamOrStd.getOperateUser())) {
                        SysUser sysUser1 = sysUserMapper.selectById(ldcParamOrStd.getOperateUser());
                        ldcParamOrStd.setOperateUserName(sysUser1.getRealname());
                    }
                }
                ldcParamOrStd.setVersion(version);
            } else if ("6".equals(type)) {
                List<LdcParamOrImportOrecEdit> editList = ldcParamOrImportOrecEditMapper.selectByMap(map);
                if (editList != null && !editList.isEmpty()) {
                    version = "V" + editList.get(0).getVersion();
                    ldcParamOrStd.setValidationTime(null);
                    ldcParamOrStd.setOperateUser(userId);
                    ldcParamOrStd.setValidationState("N");
                    ldcParamOrStd.setOperateUserName(sysUser.getRealname());
                } else {
                    // 拿正式版本
                    String maxVersion = ldcParamOrImportOrecVersionMapper.selectMaxVersion();
                    if (StringUtils.isNotBlank(maxVersion)) {
                        version = "V" + maxVersion;
                    }
                    if (StringUtils.isNotBlank(ldcParamOrStd.getOperateUser())) {
                        SysUser sysUser1 = sysUserMapper.selectById(ldcParamOrStd.getOperateUser());
                        ldcParamOrStd.setOperateUserName(sysUser1.getRealname());
                    }
                }
                ldcParamOrStd.setVersion(version);
            }
            result.add(ldcParamOrStd);
        }
        return result;
    }

    @Override
    public Result<String> getEditVersion(LdcParamOrStd ldcParamOrStd) {
        String version = "";
        String type = ldcParamOrStd.getType();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        String userId = sysUser.getId();
        // 查询编辑表是否存在本人的数据
        Map<String, Object> map = new HashMap<>();
        map.put("create_id", userId);
        if ("1".equals(type)) {
            List<LdcParamOrLocaleventcrsEdit> editList = ldcParamOrLocaleventcrsEditMapper.selectByMap(map);
            if (editList != null && !editList.isEmpty()) {
                version = "V" + editList.get(0).getVersion();
            } else {
                // 拿正式版本
                String maxVersion = ldcParamOrLocaleventcrsVersionMapper.selectMaxVersion();
                if (StringUtils.isNotBlank(maxVersion)) {
                    version = "V" + String.format("%.1f",(Double.parseDouble(maxVersion) + 0.1));
                }
            }
        } else if ("2".equals(type)) {
            List<RegulatoryEdit> editList = regulatoryEditMapper.selectByMap(map);
            if (editList != null && !editList.isEmpty()) {
                version = "V" + editList.get(0).getVersion();
            } else {
                // 拿正式版本
                String maxVersion = regulatoryVersionMapper.selectMaxVersion();
                if (StringUtils.isNotBlank(maxVersion)) {
                    version = "V" + String.format("%.1f",(Double.parseDouble(maxVersion) + 0.1));
                }
            }
        } else if ("3".equals(type)) {
            List<LdcParamOrLossFormEdit> editList = ldcParamOrLossFormEditMapper.selectByMap(map);
            if (editList != null && !editList.isEmpty()) {
                version = "V" + editList.get(0).getVersion();
            } else {
                // 拿正式版本
                String maxVersion = ldcParamOrLossFormVersionMapper.selectMaxVersion();
                if (StringUtils.isNotBlank(maxVersion)) {
                    version = "V" + String.format("%.1f",(Double.parseDouble(maxVersion) + 0.1));
                }
            }
        } else if ("4".equals(type)) {
            List<LdcParamOrGradeCriterionEdit> editList = ldcParamOrGradeCriterionEditMapper.selectByMap(map);
            if (editList != null && !editList.isEmpty()) {
                version = "V" + editList.get(0).getVersion();
            } else {
                // 拿正式版本
                String maxVersion = ldcParamOrGradeCriterionVersionMapper.selectMaxVersion();
                if (StringUtils.isNotBlank(maxVersion)) {
                    version = "V" + String.format("%.1f",(Double.parseDouble(maxVersion) + 0.1));
                }
            }
        } else if ("5".equals(type)) {
            List<RiskCauseEdit> editList = riskCauseEditMapper.selectByMap(map);
            if (editList != null && !editList.isEmpty()) {
                version = "V" + editList.get(0).getVersion();
            } else {
                // 拿正式版本
                String maxVersion = riskCauseVersionMapper.selectMaxVersion();
                if (StringUtils.isNotBlank(maxVersion)) {
                    version = "V" + String.format("%.1f",(Double.parseDouble(maxVersion) + 0.1));
                }
            }
        } else {
            List<LdcParamOrImportOrecEdit> editList = ldcParamOrImportOrecEditMapper.selectByMap(map);
            if (editList != null && !editList.isEmpty()) {
                version = "V" + editList.get(0).getVersion();
            } else {
                // 拿正式版本
                String maxVersion = ldcParamOrImportOrecVersionMapper.selectMaxVersion();
                if (StringUtils.isNotBlank(maxVersion)) {
                    version = "V" + String.format("%.1f",(Double.parseDouble(maxVersion) + 0.1));
                }
            }
        }
        if (StringUtils.isBlank(version)) {
            version = "V1.0";
        }
        return Result.OK(version);
    }


}
