package com.gientech.orr.report.risk.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 操作风险报告-重大操作风险事件管理
 * @Author: jeecg-boot
 * @Date:   2025-07-16
 * @Version: V1.0
 */
@Data
@TableName("orr_risk_report")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="操作风险报告-重大操作风险事件管理")
public class OrrRiskReport implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**报告名称*/
	@Excel(name = "报告名称", width = 15)
    @Schema(description = "报告名称")
    private java.lang.String reportName;
	/**报告机构*/
	@Excel(name = "报告机构", width = 15)
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "报告机构")
    private java.lang.String reportAgencies;
	/**报告部门*/
	@Excel(name = "报告部门", width = 15)
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "报告部门")
    private java.lang.String reportDepartment;
	/**事件发现日期*/
	@Excel(name = "事件发现日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "事件发现日期")
    private java.util.Date eventDiscoveryDate;
	/**报告截止日期*/
	@Excel(name = "报告截止日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "报告截止日期")
    private java.util.Date reportDeadlineDate;
	/**报告内容（html格式））*/
	@Excel(name = "报告内容（html格式））", width = 15)
    @Schema(description = "报告内容（html格式））")
    private java.lang.String reportHtml;
	/**报告状态*/
	@Excel(name = "报告状态", width = 15, dicCode = "orr_report_status")
	@Dict(dicCode = "orr_report_status")
    @Schema(description = "报告状态")
    private java.lang.String reportStatus;
	/**报告提交日期*/
	@Excel(name = "报告提交日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "报告提交日期")
    private java.util.Date reportSubmitDate;
	/**删除标记*/
	@Excel(name = "删除标记", width = 15)
    @Schema(description = "删除标记")
    @TableLogic
    private java.lang.Integer delFlag;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;

    public void handlePass(String reportStatus) {
        this.reportStatus = reportStatus;
        this.reportSubmitDate = new Date();
    }

    public void handleReturn(String reportStatus) {
        this.reportStatus = reportStatus;
        this.reportSubmitDate = null;
    }
}
