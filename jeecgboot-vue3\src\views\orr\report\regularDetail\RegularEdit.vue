<template>
  <a-spin :spinning="confirmLoading">
    <div class="p-2" style="margin-top: 10px">
      <!-- 按钮操作区 -->
      <div class="mb-4" style="display: flex; justify-content: space-between; align-items: center;">
  <!-- 左侧按钮组 -->
  <div>
    <a-button type="primary" @click="handleDataImport"
              preIcon="ant-design:reconciliation-outlined" class="mr-2" v-if="isShow">
      数据导入
    </a-button>
    <a-button @click="handleClueDistribution"
              preIcon="ant-design:form-outlined" class="mr-2" v-if="isShow">
      线索派发
    </a-button>
  </div>
  
  <!-- 右侧按钮组 -->
  <div>
    <a-button @click="handleReset"
              preIcon="ant-design:undo-outlined" class="mr-2" v-if="isShow">
      重置
    </a-button>
    <a-button type="primary" @click="handleSave"
              preIcon="ant-design:save-outlined">
      保存
    </a-button>
  </div>
</div>
      <RegularTinyMce ref="regularTinyMce" :clueList="clueList"/>
    </div>
    <!-- 表单区域 -->
    <OrrReportClueModal ref="registerModal"></OrrReportClueModal>
  </a-spin>
</template>

<script lang="ts" name="orr.report.regular-orrRegularReport-editAndDetail" setup>
import {computed, ref, watch} from 'vue';
import {useRoute} from 'vue-router';
import {useUserStore} from '/@/store/modules/user';
import RegularTinyMce from './components/RegularTinyMce.vue';
import OrrReportClueModal from '/src/views/orr/clue/components/OrrReportClueDistributeModal.vue'
import {
  queryById,
  saveOrUpdate,
  resetReport,
  dataImport,
} from '/@/views/orr/report/regular/OrrRegularReport.api';
import {queryByReportId} from '/@/views/orr/clue/OrrReportClue.api';
import {useMessage} from "@/hooks/web/useMessage";
import * as echarts from 'echarts/core';
import { BarChart } from 'echarts/charts';
import { GridComponent, LegendComponent, TooltipComponent, TitleComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
const userStore = useUserStore();
const {createMessage} = useMessage();
const route = useRoute();
const id = ref(route.query.id || '')
const state = ref(route.query.state || '')
const confirmLoading = ref(false);
const regularTinyMce = ref();
const registerModal = ref();
const type = ref();
let isShow = computed(() => type.value == '1');
const emit = defineEmits([]);
const clueList = ref([]);
echarts.use([
  BarChart,
  GridComponent,
  LegendComponent,
  TooltipComponent,
  TitleComponent,
  CanvasRenderer
]);
watch(
  [
    () => route.query.id
  ],
  ([newId]) => {
    id.value = newId || '';
    toInit();
  },
  {immediate: true}
);

/**
 * 初始化
 */
async function toInit() {

  // 查询报告信息
  await queryById({id: id.value})
    .then((res) => {
      if (res.success) {
        type.value = res.result.type;
        regularTinyMce.value.handleSetContent(res.result.reportHtml);
      }
    })
    .finally(() => {
    })

  // 查询线索信息
  await queryByReportId({reportId: id.value})
    .then((res) => {
      if (res.success) {
        let result = res.result.records;
        clueList.value = result;
        regularTinyMce.value.highlightClueAnchors(result);
      }
    })
    .finally(() => {
    })
}

/**
 * 数据导入
 */
async function handleDataImport() {
  let handleGetContent = regularTinyMce.value.handleGetContent();
  
  confirmLoading.value = true;
  await dataImport({id: id.value, reportHtml: handleGetContent})
    .then(async (res) => {
      if (res.success) {
        createMessage.success("数据导入成功！");
        let result = res.result;
        let chartResult = [];
        
        // 文字替换
        result.forEach((item) => {
          if (item.type == 'digit') {
            handleGetContent = handleGetContent.replaceAll(item.key, item.value);
          } else if (item.type == 'chart') {
            if (item.key == '{kri.errorResult.details}') {
              const value = item.value.map((data, index) => {
                return `${index + 1}.${data.monitoringDepartment}的${data.indicatorName}指标：监测期间指标异常${data.redCount + data.yellowCount}次，其中落入黄色区域${data.yellowCount}次、红色区域${data.redCount}次。出现预警的原因为：${data.causeAnalyse}，将采取${data.improvementSuggestion}等措施使指标值重新回到绿色区域。<br>`;
              }).join('');
              handleGetContent = handleGetContent.replaceAll(item.key, value);
            } else if (item.key == '{rcsa.risk.reject.des}') {
              // 是 评估出需关注的主要风险 ，需要特殊处理
              const value = item.value.map((data, index) => {
                return `${index + 1}.${data.evaluatedepart}的${data.matrixname}流程：“${data.tachename}”环节的“${data.riskdescription}”风险，调整后剩余风险等级为“${data.adjustedremainrisklevel}”，并针对该风险结果提出“${data.improveadvice}”的改进建议。<br>`;
              }).join('');
              handleGetContent = handleGetContent.replaceAll(item.key, value);
            } else {
              chartResult.push(item);
            }
          }
        });

        console.log("chartResult=", chartResult);
        
        // 处理其他非ECharts图表（表格等）
        chartResult.forEach((chartItem) => {
          if (chartItem.key === '{kri.errorResult.table}' && chartItem.value && Array.isArray(chartItem.value)) {
            const tableHtml = generateTableHtml(chartItem.value, chartItem.key);
            handleGetContent = handleGetContent.replaceAll(chartItem.key, tableHtml);
          } else if (chartItem.key === '{rcsa.risk.grade.table}' && chartItem.value && Array.isArray(chartItem.value)) {
            const riskGradeTableHtml = generateRiskGradeTableHtml(chartItem.value[0]);
            handleGetContent = handleGetContent.replaceAll(chartItem.key, riskGradeTableHtml);
          } else if (chartItem.key === '{rcsa.risk.reject.table}' && chartItem.value && Array.isArray(chartItem.value)) {
            const riskRejectTableHtml = generateRiskRejectTableHtml(chartItem.value);
            handleGetContent = handleGetContent.replaceAll(chartItem.key, riskRejectTableHtml);
          } else if (chartItem.key === '{kri.risk.errorResult.table}' && chartItem.value && Array.isArray(chartItem.value)) {
            const tableHtml = generateSeparateTables(chartItem.value, chartItem.key);
            handleGetContent = handleGetContent.replaceAll(chartItem.key, tableHtml);
          } else if (chartItem.key === '{pressure.result.table}' && chartItem.value && Array.isArray(chartItem.value)) {
            const pressureTableHtml = generatePressureResultTable(chartItem.value);
            handleGetContent = handleGetContent.replaceAll(chartItem.key, pressureTableHtml);
         }
        });
        
        // 处理ECharts图表 - 异步处理
        const chartPromises = chartResult.map(async (chartItem) => {
          if (chartItem.key === '{rcsa.risk.depart.chart}' && chartItem.value && Array.isArray(chartItem.value)) {
            const chartHtml = await generateDepartChartEChart(chartItem.value);
            handleGetContent = handleGetContent.replaceAll(chartItem.key, chartHtml);
          }  else if (chartItem.key === '{rcsa.risk.matrix.chart}' && chartItem.value && Array.isArray(chartItem.value)) {
            const matrixChartHtml = await generateMatrixChartEChart(chartItem.value);
            handleGetContent = handleGetContent.replaceAll(chartItem.key, matrixChartHtml);
          }  else if (chartItem.key === '{ldc.lossMoney.yoy.et}' && chartItem.value && Array.isArray(chartItem.value)) {
          const lossMoneyChartHtml = await generateLossMoneyYoyChart(chartItem.value);
          handleGetContent = handleGetContent.replaceAll(chartItem.key, lossMoneyChartHtml);
          }  else if (chartItem.key === '{ldc.depart.isLoss.et}' && chartItem.value && Array.isArray(chartItem.value)) {
          const departLossChartHtml = await generateDepartLossChart(chartItem.value);
          handleGetContent = handleGetContent.replaceAll(chartItem.key, departLossChartHtml);
          }  else if (chartItem.key === '{ldc.eventType.isLoss.et}' && chartItem.value && Array.isArray(chartItem.value)) {
          const eventTypeChartHtml = await generateEventTypeChart(chartItem.value);
          handleGetContent = handleGetContent.replaceAll(chartItem.key, eventTypeChartHtml);
          } else if (chartItem.key === '{ldc.reason.isLoss.et}' && chartItem.value && Array.isArray(chartItem.value)) {
          const reasonChartHtml = await generateReasonChart(chartItem.value);
          handleGetContent = handleGetContent.replaceAll(chartItem.key, reasonChartHtml);
          }
        });
        
        await Promise.all(chartPromises);
        
        // 更新内容
        regularTinyMce.value.handleSetContent(handleGetContent);
      }
    })
    .finally(() => {
      confirmLoading.value = false;
    });
}
function generateTableHtml(data, title) {
  if (!data || data.length === 0) return '';

  // 表格标题
  const tableTitle = title.includes('table') ? '异常关键风险指标检测结果' : '数据表格';

  // 表格头部（调整列名顺序，确保与数据对应）
  const headers = [
    '序号', '指标名称', '监控部门/机构', '监控频率',
    '阈值区间', '最近一期结果', '监测期间指标异常次数', '监测期间指标落入黄色区域次数', '监测期间指标落入红色区域次数'
  ];

  let tableHtml = `
    <table border="1" cellpadding="5" cellspacing="0" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
      <caption style="font-weight: bold; margin-bottom: 10px; font-size: 16px;">${tableTitle}</caption>
      <thead>
        <tr style="background-color: #f5f5f5;">
          ${headers.map(header => `<th style="border: 1px solid #ddd; padding: 8px; text-align: center;">${header}</th>`).join('')}
        </tr>
      </thead>
      <tbody>
  `;

  // 表格数据行
  data.forEach((item, index) => {
    // 计算异常次数总和（黄色预警+红色预警）
    const totalAbnormal = (item.yellowCount || 0) + (item.redCount || 0);
    const thresholdIntervals = parseThresholds(item.thresholds);

    tableHtml += `
      <tr>
        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${index + 1}</td>
        <td style="border: 1px solid #ddd; padding: 8px;">${item.indicatorName || ''}</td>
        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.monitoringDepartment || ''}</td>
        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.monitoringFrequency || ''}</td>
        <td style="border: 1px solid #ddd; padding: 0;">
          <table style="width: 100%; border-collapse: collapse;">
            <tbody>
              ${thresholdIntervals}
            </tbody>
          </table>
        </td>
        <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${item.metricValues || ''}</td>
        <!-- 异常次数 = 黄色 + 红色 -->
        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${totalAbnormal}</td>
        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.yellowCount || 0}</td>
        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.redCount || 0}</td>
      </tr>
    `;
  });

  tableHtml += `
      </tbody>
    </table>
  `;

  return tableHtml;
}

// 优化阈值区间样式（添加底部横线）
function parseThresholds(thresholds) {
  if (!thresholds) return '';

  const intervals = thresholds.split(';');

  return intervals.map(interval => {
    const parts = interval.split('|');
    if (parts.length !== 2) return `<tr><td colspan="2">${interval}</td></tr>`;

    const range = parts[0].trim();
    const colorCode = parts[1].trim();
    const color = getThresholdColor(colorCode);

    return `
      <tr>
        <!-- 颜色块底部添加横线，与区间文本对齐 -->
        <td style="background-color: ${color}; width: 20px; height: 25px;  border-bottom: 1px solid #ddd;"></td>
        <!-- 区间文本底部添加横线，与第三张图一致 -->
        <td style="padding: 4px 8px;  text-align: left; border-bottom: 1px solid #ddd;">${range}</td>
      </tr>
    `;
  }).join('');
}

function getThresholdColor(colorCode) {
  switch(colorCode) {
    case '1': return '#ff4d4f'; // 红色
    case '2': return '#faad14'; // 黄色
    case '3': return '#52c41a'; // 绿色
    default: return '#d9d9d9'; // 灰色
  }
}

function generateRiskGradeTableHtml(data) {
  if (!data) return '';
  
  const tableHtml = `
    <table border="1" cellpadding="5" cellspacing="0" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
      <thead>
        <tr style="background-color: #f5f5f5;">
          <!-- 矩阵总数跨两行合并 -->
          <th rowspan="2" style="border: 1px solid #ddd; padding: 8px; background-color: rgb(191,191,191); text-align: center;">矩阵总数</th>
          <!-- 风险总数跨两行合并 -->
          <th rowspan="2" style="border: 1px solid #ddd; padding: 8px; background-color: rgb(191,191,191); text-align: center;">风险总数</th>
          <!-- 调整后剩余风险等级跨一列（覆盖下方5个等级） -->
          <th colspan="5" style="border: 1px solid #ddd; background-color: rgb(191,191,191);  padding: 8px; text-align: center;">调整后剩余风险等级</th>
          <!-- 不接受的剩余风险数量跨两行合并 -->
          <th rowspan="2" style="border: 1px solid #ddd; background-color: rgb(191,191,191); padding: 8px; text-align: center;">不接受的剩余风险数量</th>
        </tr>
        <tr style="background-color: #f5f5f5;">
          <th style="border: 1px solid #ddd; padding: 8px; text-align: center; background-color: rgb(191,191,191); color: black;">很高</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: center; background-color: rgb(191,191,191); color: black;">高</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: center; background-color: rgb(191,191,191); color: black;">中</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: center; background-color: rgb(191,191,191); color: black;">低</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: center; background-color: rgb(191,191,191); color: black;">轻微</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">${data.matrixcount || 0}</td>
          <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">${data.totalcount || 0}</td>
          <td style="border: 1px solid #ddd; padding: 8px; text-align: center; background-color: rgb(255,0,0); color: black; font-weight: bold;">${data.total5count || 0} (${data.ratio5 || '0%'})</td>
          <td style="border: 1px solid #ddd; padding: 8px; text-align: center;  background-color: rgb(255,192,0); color: black; font-weight: bold;">${data.total4count || 0} (${data.ratio4 || '0%'})</td>
          <td style="border: 1px solid #ddd; padding: 8px; text-align: center;  background-color: rgb(255,255,153); color: black; font-weight: bold;">${data.total3count || 0} (${data.ratio3 || '0%'})</td>
          <td style="border: 1px solid #ddd; padding: 8px; text-align: center;  background-color: rgb(254,219,97); color: black; font-weight: bold;">${data.total2count || 0} (${data.ratio2 || '0%'})</td>
          <td style="border: 1px solid #ddd; padding: 8px; text-align: center;background-color: rgb(146,208,80); color: black; font-weight: bold;">${data.total1count || 0} (${data.ratio1 || '0%'})</td>
          <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">${data.total0count || 0} (${data.ratio0 || '0%'})</td>
        </tr>
      </tbody>
    </table>
  `;
  
  return tableHtml;
}


function generateRiskRejectTableHtml(data) {
  if (!data || data.length === 0) return '';
  
  const tableHtml = `
    <table border="1" cellpadding="5" cellspacing="0" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
      <thead>
        <tr style="background-color: #f5f5f5;">
          <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">评估机构/部门</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">矩阵名称</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">环节名称</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">风险描述</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">固有风险等级</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">剩余风险等级</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">调整后剩余风险等级</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">改进建议</th>
        </tr>
      </thead>
      <tbody>
        ${data.map((item, index) => `
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.evaluatedepart || ''}</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.matrixname || ''}</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.tachename || ''}</td>
            <td style="border: 1px solid #ddd; padding: 8px;">${item.riskdescription || ''}</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.inherentrisklevel || ''}</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.remainrisklevel || ''}</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.adjustedremainrisklevel || ''}</td>
            <td style="border: 1px solid #ddd; padding: 8px;">${item.improveadvice || ''}</td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  `;
  
  return tableHtml;
}




function generatePressureResultTable(data) {
  if (!data || data.length === 0) return '';

  // 取第一条数据作为主要数据源
  const mainData = data[0];
  
  const tableHtml = `
    <table border="1" cellpadding="5" cellspacing="0" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
      <thead>
        <tr style="background-color: #808080;">
          <th style="border: 1px solid #000; padding: 8px; text-align: center; color: white; background-color: rgb(128,128,128); font-weight: bold;">损失情景</th>
          <th style="border: 1px solid #000; padding: 8px; text-align: center; background-color: rgb(255,255,0); color: black; font-weight: bold;">轻度压力</th>
          <th style="border: 1px solid #000; padding: 8px; text-align: center; background-color: rgb(255,192,0); color: black; font-weight: bold;">中度压力</th>
          <th style="border: 1px solid #000; padding: 8px; text-align: center; background-color: rgb(192,0,0); color: white; font-weight: bold;">重度压力</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td style="border: 1px solid #000; padding: 8px; text-align: center; background-color: #d3d3d3; font-weight: bold;">基准年度损失估测方法</td>
          <td colspan="3" style="border: 1px solid #000; padding: 8px; text-align: center; font-weight: bold;">
            ${mainData.selectionmethodname || 'XX'}
          </td>
        </tr>
        <tr>
          <td style="border: 1px solid #000; padding: 8px;  background-color: #d3d3d3; font-weight: bold;">年度损失增幅情景</td>
          <td style="border: 1px solid #000; padding: 8px; text-align: center;">
            ${mainData.futureamt1 || '0.00'}
          </td>
          <td style="border: 1px solid #000; padding: 8px; text-align: center;">
            ${mainData.futureamt2 || '0.00'}
          </td>
          <td style="border: 1px solid #000; padding: 8px; text-align: center;">
            ${mainData.futureamt3 || '0.00'}
          </td>
        </tr>
        <tr>
          <td style="border: 1px solid #000; padding: 8px;  background-color: #d3d3d3; font-weight: bold;">产品业务假定损失情景</td>
          <td style="border: 1px solid #000; padding: 8px; text-align: center;">
            ${mainData.lightinfluence || '0'}
          </td>
          <td style="border: 1px solid #000; padding: 8px; text-align: center;">
            ${mainData.mediuminfluence || '0'}
          </td>
          <td style="border: 1px solid #000; padding: 8px; text-align: center;">
            ${mainData.heavyinfluence || '0'}
          </td>
        </tr>
        <tr>
          <td style="border: 1px solid #000; padding: 8px; color: white; background-color: rgb(112,173,71); font-weight: bold;">净损失金额总计</td>
          <td style="border: 1px solid #000; padding: 8px; text-align: center;">
            ${calculateTotal(mainData.futureamt1, mainData.lightinfluence)}
          </td>
          <td style="border: 1px solid #000; padding: 8px; text-align: center;">
            ${calculateTotal(mainData.futureamt2, mainData.mediuminfluence)}
          </td>
          <td style="border: 1px solid #000; padding: 8px; text-align: center;">
            ${calculateTotal(mainData.futureamt3, mainData.heavyinfluence)}
          </td>
        </tr>
      </tbody>
    </table>
  `;

  return tableHtml;
}


function calculateTotal(futureAmt, influence) {
  const amt = parseFloat(futureAmt) || 0;
  const inf = parseFloat(influence) || 0;
  return (amt + inf).toFixed(2);
}




function generateDepartChartEChart(data) {
  if (!data || data.length === 0) return Promise.resolve('');
  
  return new Promise((resolve) => {
    const tempContainer = document.createElement('div');
    tempContainer.style.width = '1200px';
    tempContainer.style.height = '800px';
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    document.body.appendChild(tempContainer);
    
    try {
      const chart = echarts.init(tempContainer);
      const title = data[0]?.tablename || '风险评估结果的矩阵分布图（按数量排序）';
      const sortedData = [...data].sort((a, b) => (b.total || 0) - (a.total || 0));
      
      const option = {
        title: {
          text: title,
          left: 'center',
          top: 20,
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>';
            let total = 0;
            params.forEach(param => {
              if (param.value > 0) {
                result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
                total += param.value;
              }
            });
            result += '<strong>总计: ' + total + '</strong>';
            return result;
          }
        },
        legend: {
          data: ['很高', '高', '中', '低', '轻微'],
          orient: 'vertical', // 垂直布局
          right: 10, // 放在右侧
          top: 'center', // 垂直居中
          itemGap: 20,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '15%',
          right: '10%',
          bottom: '15%',
          top: '25%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 12,
            color: '#666'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#e0e0e0',
              type: 'solid'
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#ccc'
            }
          }
        },
        yAxis: {
          type: 'category',
          data: sortedData.map(item => item.evaluatedepart || ''),
          axisLabel: {
            fontSize: 12,
            color: '#333',
            margin: 10
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          inverse: true // 让数据从上到下按total排序
        },
        series: [
          {
            name: '很高',
            type: 'bar',
            stack: 'total',
            data: sortedData.map(item => item.total5 || 0),
            itemStyle: { 
              color: '#ff4d4f',
              borderWidth: 0
            },
            barWidth: '70%',
            label: {
              show: true,
              position: 'inside',
              fontSize: 11,
              color: '#fff',
              fontWeight: 'bold',
              formatter: function(params) {
                return params.value > 0 ? params.value : '';
              }
            }
          },
          {
            name: '高',
            type: 'bar',
            stack: 'total',
            data: sortedData.map(item => item.total4 || 0),
            itemStyle: { 
              color: '#faad14',
              borderWidth: 0
            },
            label: {
              show: true,
              position: 'inside',
              fontSize: 11,
              color: '#fff',
              fontWeight: 'bold',
              formatter: function(params) {
                return params.value > 0 ? params.value : '';
              }
            }
          },
          {
            name: '中',
            type: 'bar',
            stack: 'total',
            data: sortedData.map(item => item.total3 || 0),
            itemStyle: { 
              color: '#fadb14',
              borderWidth: 0
            },
            label: {
              show: true,
              position: 'inside',
              fontSize: 11,
              color: '#333',
              fontWeight: 'bold',
              formatter: function(params) {
                return params.value > 0 ? params.value : '';
              }
            }
          },
          {
            name: '低',
            type: 'bar',
            stack: 'total',
            data: sortedData.map(item => item.total2 || 0),
            itemStyle: { 
              color: '#87d068',
              borderWidth: 0
            },
            label: {
              show: true,
              position: 'inside',
              fontSize: 11,
              color: '#fff',
              fontWeight: 'bold',
              formatter: function(params) {
                return params.value > 0 ? params.value : '';
              }
            }
          },
          {
            name: '轻微',
            type: 'bar',
            stack: 'total',
            data: sortedData.map(item => item.total1 || 0),
            itemStyle: { 
              color: '#52c41a',
              borderWidth: 0
            },
            label: {
              show: true,
              position: 'inside',
              fontSize: 11,
              color: '#fff',
              fontWeight: 'bold',
              formatter: function(params) {
                return params.value > 0 ? params.value : '';
              }
            }
          }
        ]
      };
      
      chart.setOption(option);
      
      setTimeout(() => {
        try {
          const dataURL = chart.getDataURL({
            type: 'png',
            pixelRatio: 2,
            backgroundColor: '#fff'
          });
          
          chart.dispose();
          document.body.removeChild(tempContainer);
          
          const chartHtml = `<div style="text-align: center; margin: 20px 0;">
            <img src="${dataURL}" alt="${title}" style="max-width: 100%; border: 1px solid #eee;" />
          </div>`;
          
          resolve(chartHtml);
        } catch (error) {
          console.error('部门风险图表转换失败:', error);
          chart.dispose();
          document.body.removeChild(tempContainer);
          resolve(`<p>部门风险图表生成失败: ${title}</p>`);
        }
      }, 500);
      
    } catch (error) {
      console.error('部门风险图表初始化失败:', error);
      document.body.removeChild(tempContainer);
      resolve(`<p>部门风险图表生成失败</p>`);
    }
  });
}



function generateMatrixChartEChart(data) {
  if (!data || data.length === 0) return Promise.resolve('');
  
  return new Promise((resolve) => {
    // 创建临时容器用于ECharts渲染
    const tempContainer = document.createElement('div');
    tempContainer.style.width = '1000px';
    tempContainer.style.height = '500px';
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    document.body.appendChild(tempContainer);
    
    try {
      // 初始化ECharts实例
      const chart = echarts.init(tempContainer);
      
      // 获取标题和排序数据
      const title = data[0]?.tablename || '风险评估结果的矩阵分布图（按数量排序）';
      const sortedData = [...data].sort((a, b) => (b.total || 0) - (a.total || 0));
      
      // 准备Y轴数据（矩阵名称）
      const categories = sortedData.map(item => item.matrixname || '');
      
      // 配置ECharts选项
      const option = {
        title: {
          text: title,
          left: 'center',
          top: 10,
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>';
            let total = 0;
            params.forEach(param => {
              if (param.value > 0) {
                result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
                total += param.value;
              }
            });
            result += '<b>总计: ' + total + '</b>';
            return result;
          }
        },
        legend: {
          data: ['很高', '高', '中', '低', '轻微'],
          orient: 'vertical', // 垂直布局
          right: 10, // 放在右侧
          top: 'center', // 垂直居中
          itemWidth: 15,
          itemHeight: 15,
          textStyle: {
            fontSize: 14
          },
          itemGap: 12 // 图例项之间的间距
        },
        grid: {
          left: '15%',
          right: '20%', // 为右侧图例留出空间
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          name: '',
          nameLocation: 'middle',
          nameGap: 30,
          axisLine: { 
            show: true,
            lineStyle: { color: '#333' }
          },
          axisTick: { 
            show: true,
            lineStyle: { color: '#333' }
          },
          axisLabel: {
            fontSize: 12,
            color: '#333'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#e0e0e0',
              type: 'dashed'
            }
          }
        },
        yAxis: {
          type: 'category',
          data: categories,
          axisLine: { 
            show: true,
            lineStyle: { color: '#333' }
          },
          axisTick: { 
            show: true,
            lineStyle: { color: '#333' }
          },
          axisLabel: {
            fontSize: 12,
            color: '#333',
            width: 120,
            overflow: 'truncate'
          }
        },
        series: [
  {
    name: '很高',
    type: 'bar',
    stack: 'total',
    data: sortedData.map(item => item.total5 > 0 ? item.total5 : null),
    itemStyle: { 
      color: '#ff4d4f',
      borderColor: '#d32f2f',
      borderWidth: 1
    },
    label: {
      show: true,
      position: 'inside',
      fontSize: 12,
      color: '#fff',
      fontWeight: 'bold',
      formatter: function(params) {
        return params.value > 0 ? params.value : '';
      }
    }
  },
  {
    name: '高',
    type: 'bar',
    stack: 'total',
    data: sortedData.map(item => item.total4 > 0 ? item.total4 : null),
    itemStyle: { 
      color: '#faad14',
      borderColor: '#d68910',
      borderWidth: 1
    },
    label: {
      show: true,
      position: 'inside',
      fontSize: 12,
      color: '#fff',
      fontWeight: 'bold',
      formatter: function(params) {
        return params.value > 0 ? params.value : '';
      }
    }
  },
  {
    name: '中',
    type: 'bar',
    stack: 'total',
    data: sortedData.map(item => item.total3 > 0 ? item.total3 : null),
    itemStyle: { 
      color: '#fadb14',
      borderColor: '#d4b106',
      borderWidth: 1
    },
    label: {
      show: true,
      position: 'inside',
      fontSize: 12,
      color: '#000',
      fontWeight: 'bold',
      formatter: function(params) {
        return params.value > 0 ? params.value : '';
      }
    }
  },
  {
    name: '低',
    type: 'bar',
    stack: 'total',
    data: sortedData.map(item => item.total2 > 0 ? item.total2 : null),
    itemStyle: { 
      color: '#a0d911',
      borderColor: '#7cb305',
      borderWidth: 1
    },
    label: {
      show: true,
      position: 'inside',
      fontSize: 12,
      color: '#000',
      fontWeight: 'bold',
      formatter: function(params) {
        return params.value > 0 ? params.value : '';
      }
    }
  },
  {
    name: '轻微',
    type: 'bar',
    stack: 'total',
    data: sortedData.map(item => item.total1 > 0 ? item.total1 : null),
    itemStyle: { 
      color: '#52c41a',
      borderColor: '#389e0d',
      borderWidth: 1
    },
    label: {
      show: true,
      position: 'inside',
      fontSize: 12,
      color: '#fff',
      fontWeight: 'bold',
      formatter: function(params) {
        return params.value > 0 ? params.value : '';
      }
    }
  }
]
      };
      
      // 设置配置
      chart.setOption(option);
      
      // 等待渲染完成后获取图片
      setTimeout(() => {
        try {
          const dataURL = chart.getDataURL({
            type: 'png',
            pixelRatio: 2,
            backgroundColor: '#fff'
          });
          
          // 清理资源
          chart.dispose();
          document.body.removeChild(tempContainer);
          
          // 返回图片HTML
          const chartHtml = `<div style="text-align: center; margin: 20px 0;">
            <img src="${dataURL}" alt="${title}" style="max-width: 100%; border: 1px solid #eee;" />
          </div>`;
          
          resolve(chartHtml);
        } catch (error) {
          console.error('矩阵分布图转换失败:', error);
          chart.dispose();
          document.body.removeChild(tempContainer);
          resolve(`<p>矩阵分布图生成失败: ${title}</p>`);
        }
      }, 500);
      
    } catch (error) {
      console.error('矩阵分布图初始化失败:', error);
      document.body.removeChild(tempContainer);
      resolve(`<p>矩阵分布图生成失败</p>`);
    }
  });
}


function generateLossMoneyYoyChart(data) {
  if (!data || data.length === 0) return Promise.resolve('');
  
  return new Promise((resolve) => {
    const tempContainer = document.createElement('div');
    tempContainer.style.width = '1200px';
    tempContainer.style.height = '700px';
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    document.body.appendChild(tempContainer);
    
    try {
      const chart = echarts.init(tempContainer);
      const currentData = data[0];
      const title = currentData?.tablename || '操作风险损失事件同比分析图';
      
      // 使用实际数据，不做单位转换
      const totalLoss = currentData.totalloss || 0;
      const netLoss = currentData.netlosssum || 0;
      const eventCount = currentData.eventcount || 0;
      const majorEventCount = currentData.majoreventcount || 0;
      const year = currentData.eventyear || '2024年';
      
      // 只显示2024年的数据
      const years = [`${year}1月至9月`];
      const totalLossData = [totalLoss];
      const netLossData = [netLoss];
      const eventCountData = [eventCount];
      const majorEventCountData = [majorEventCount];
      
      const option = {
        title: {
          text: title,
          left: 'center',
          top: 20,
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['总损失金额 (万元)', '净损失金额 (万元)', '操作风险损失事件数量', '重大操作风险事件数量'],
          top: 60,
          itemGap: 20
        },
        grid: {
          left: '8%',
          right: '8%',
          bottom: '35%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: years,
          axisLabel: {
            fontSize: 12,
            color: '#333'
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '万元',
            position: 'left',
            axisLabel: {
              formatter: '{value}',
              fontSize: 12,
              color: '#333'
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#e0e0e0',
                type: 'dashed'
              }
            }
          },
          {
            type: 'value',
            name: '个数',
            position: 'right',
            axisLabel: {
              formatter: '{value}',
              fontSize: 12,
              color: '#333'
            }
          }
        ],
        series: [
          {
            name: '总损失金额 (万元)',
            type: 'bar',
            yAxisIndex: 0,
            data: totalLossData,
            itemStyle: {
              color: '#8884d8'
            }
          },
          {
            name: '净损失金额 (万元)',
            type: 'bar',
            yAxisIndex: 0,
            data: netLossData,
            itemStyle: {
              color: '#ffc658'
            }
          },
          {
            name: '操作风险损失事件数量',
            type: 'line',
            yAxisIndex: 1,
            data: eventCountData,
            lineStyle: {
              color: '#ff7300',
              width: 2
            },
            itemStyle: {
              color: '#ff7300'
            },
            symbol: 'circle',
            symbolSize: 6
          },
          {
            name: '重大操作风险事件数量',
            type: 'line',
            yAxisIndex: 1,
            data: majorEventCountData,
            lineStyle: {
              color: '#387908',
              width: 2
            },
            itemStyle: {
              color: '#387908'
            },
            symbol: 'circle',
            symbolSize: 6
          }
        ]
      };
      
      chart.setOption(option);
      
      setTimeout(() => {
        try {
          const dataURL = chart.getDataURL({
            type: 'png',
            pixelRatio: 2,
            backgroundColor: '#fff'
          });
          
          chart.dispose();
          document.body.removeChild(tempContainer);
          
          const tableHtml = `
            <table border="1" cellpadding="5" cellspacing="0" style="width: 100%; border-collapse: collapse; margin-top: 10px;">
              <tr style="background-color: #f5f5f5;">
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;"></td>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${year}1月至9月</td>
              </tr>
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">总损失金额 (万元)</td>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${totalLoss}</td>
              </tr>
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">净损失金额 (万元)</td>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${netLoss}</td>
              </tr>
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">操作风险损失事件数量 (个)</td>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${eventCount}</td>
              </tr>
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">重大操作风险事件数量 (个)</td>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${majorEventCount}</td>
              </tr>
            </table>
          `;
          
          const chartHtml = `
            <div style="text-align: center; margin: 20px 0;">
              <img src="${dataURL}" alt="${title}" style="max-width: 100%; border: 1px solid #eee;" />
              ${tableHtml}
            </div>
          `;
          
          resolve(chartHtml);
        } catch (error) {
          console.error('损失事件图表转换失败:', error);
          chart.dispose();
          document.body.removeChild(tempContainer);
          resolve(`<p>损失事件图表生成失败: ${title}</p>`);
        }
      }, 500);
      
    } catch (error) {
      console.error('损失事件图表初始化失败:', error);
      document.body.removeChild(tempContainer);
      resolve(`<p>损失事件图表生成失败</p>`);
    }
  });
}


function generateDepartLossChart(data) {
  if (!data || data.length === 0) return Promise.resolve('');
  
  return new Promise((resolve) => {
    const tempContainer = document.createElement('div');
    // 大幅增加宽度，每个部门150px宽度，最小2000px
    const dynamicWidth = Math.max(2000, data.length * 150);
    tempContainer.style.width = `${dynamicWidth}px`;
    tempContainer.style.height = '1000px'; // 增加高度
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    document.body.appendChild(tempContainer);
    
    try {
      const chart = echarts.init(tempContainer);
      const title = data[0]?.tablename || '操作风险损失事件的发生单位分布图';
      
      // 处理数据，过滤掉departname为null的数据
      const validData = data.filter(item => item.departname);
      
      // 准备图表数据
      const departments = validData.map(item => item.departname || '未知单位');
      const totalLossData = validData.map(item => item.totalloss || 0);
      const netLossData = validData.map(item => item.netlosssum || 0);
      const eventCountData = validData.map(item => item.eventcount || 0);
      const majorEventCountData = validData.map(item => item.majoreventcount || 0);

      const option = {
        title: {
          text: title,
          left: 'center',
          top: 20,
          textStyle: {
            fontSize: 24, // 增大标题字体
            fontWeight: 'bold',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          textStyle: {
            fontSize: 14
          }
        },
        legend: {
          data: ['总损失金额 (万元)', '净损失金额 (万元)', '操作风险损失事件数量', '重大操作风险事件数量'],
          top: 70,
          textStyle: {
            fontSize: 16 // 增大图例字体
          },
          itemWidth: 25,
          itemHeight: 14
        },
        grid: {
          top: 150,
          bottom: 200, // 增加底部空间
          left: 100,
          right: 100
        },
        xAxis: {
          type: 'category',
          data: departments,
          axisLabel: {
            rotate: 45,
            fontSize: 12, // 增大X轴标签字体
            interval: 0,
            margin: 20
          },
          axisTick: {
            alignWithLabel: true
          },
          axisLine: {
            lineStyle: {
              width: 2
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '金额 (万元)',
            position: 'left',
            nameTextStyle: {
              fontSize: 14
            },
            axisLabel: {
              formatter: '{value}',
              fontSize: 12
            }
          },
          {
            type: 'value',
            name: '数量 (个)',
            position: 'right',
            nameTextStyle: {
              fontSize: 14
            },
            axisLabel: {
              formatter: '{value}',
              fontSize: 12
            }
          }
        ],
        series: [
          {
            name: '总损失金额 (万元)',
            type: 'bar',
            yAxisIndex: 0,
            data: totalLossData,
            itemStyle: {
              color: '#ff4d4f'
            },
            barWidth: 20, // 固定柱子宽度
            label: {
              show: false
            }
          },
          {
            name: '净损失金额 (万元)',
            type: 'bar',
            yAxisIndex: 0,
            data: netLossData,
            itemStyle: {
              color: '#722ed1'
            },
            barWidth: 20,
            label: {
              show: false
            }
          },
          {
            name: '操作风险损失事件数量',
            type: 'line',
            yAxisIndex: 1,
            data: eventCountData,
            lineStyle: {
              color: '#faad14',
              width: 4 // 增加线条宽度
            },
            itemStyle: {
              color: '#faad14'
            },
            symbol: 'circle',
            symbolSize: 10 // 增大点的大小
          },
          {
            name: '重大操作风险事件数量',
            type: 'line',
            yAxisIndex: 1,
            data: majorEventCountData,
            lineStyle: {
              color: '#52c41a',
              width: 4
            },
            itemStyle: {
              color: '#52c41a'
            },
            symbol: 'circle',
            symbolSize: 10
          }
        ]
      };
      
      chart.setOption(option);
      
      setTimeout(() => {
        try {
          const dataURL = chart.getDataURL({
            type: 'png',
            pixelRatio: 1.5, // 提高图片质量
            backgroundColor: '#fff'
          });
          
          chart.dispose();
          document.body.removeChild(tempContainer);
          
          // 保持原有的表格HTML生成逻辑不变
           // 生成表格HTML
          const tableRows = validData.map((item, index) => `
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${index + 1}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.totalloss}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.netlosssum}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.eventcount}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.majoreventcount}</td>
            </tr>
          `).join('');
          
         // 生成表格HTML
const tableHtml = `
  <table border="1" cellpadding="5" cellspacing="0" style="width: 100%; border-collapse: collapse; margin-top: 10px;">
    <tr style="background-color: #f5f5f5;">
      <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;"></td>
      ${validData.map(item => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">${item.departname}</td>`).join('')}
    </tr>
    <tr>
      <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold; color: black;"> 总损失金额 (万元)</td>
      ${validData.map(item => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.totalloss}</td>`).join('')}
    </tr>
    <tr>
      <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold; color: black;">净损失金额 (万元)</td>
      ${validData.map(item => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.netlosssum}</td>`).join('')}
    </tr>
    <tr>
      <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold; color: black;"> 操作风险损失事件数量 (个)</td>
      ${validData.map(item => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.eventcount}</td>`).join('')}
    </tr>
    <tr>
      <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold; color: black;">重大操作风险事件数量 (个)</td>
      ${validData.map(item => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.majoreventcount}</td>`).join('')}
    </tr>
  </table>
`;
          
          const chartHtml = `
            <div style="text-align: center; margin: 20px 0;">
              <img src="${dataURL}" alt="${title}" style="max-width: 100%; border: 1px solid #eee;" />
              ${tableHtml}
            </div>
          `;
          
          resolve(chartHtml);
        } catch (error) {
          console.error('部门损失图表转换失败:', error);
          chart.dispose();
          document.body.removeChild(tempContainer);
          resolve(`<p>部门损失图表生成失败: ${title}</p>`);
        }
      }, 800);
      
    } catch (error) {
      console.error('部门损失图表初始化失败:', error);
      document.body.removeChild(tempContainer);
      resolve(`<p>部门损失图表生成失败</p>`);
    }
  });
}



function generateEventTypeChart(data) {
  if (!data || data.length === 0) return Promise.resolve('');
  
  return new Promise((resolve) => {
    const tempContainer = document.createElement('div');
    tempContainer.style.width = '1400px'; // 稍微增大宽度
    tempContainer.style.height = '800px'; // 稍微增大高度
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    document.body.appendChild(tempContainer);
    
    try {
      const chart = echarts.init(tempContainer);
      const title = data[0]?.tablename || '操作风险损失事件的事件分类分布图';
      
      // 准备图表数据
      const eventTypes = data.map(item => item.eventtypefirst || '未知类型');
      const totalLossData = data.map(item => item.totalloss || 0);
      const netLossData = data.map(item => item.netlosssum || 0);
      const eventCountData = data.map(item => item.eventcount || 0);
      const majorEventCountData = data.map(item => item.majoreventcount || 0);
      
      const option = {
        title: {
          text: title,
          left: 'center',
          top: 20,
          textStyle: {
            fontSize: 20, // 稍微增大标题字体
            fontWeight: 'bold',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['总损失金额 (万元)', '净损失金额 (万元)', '操作风险损失事件数量', '重大操作风险事件数量'],
          top: 60,
          itemGap: 20
        },
        grid: {
          left: '8%',
          right: '8%',
          bottom: '25%', // 增加底部空间给X轴标签
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: eventTypes,
          axisLabel: {
            fontSize: 14, // 增大X轴标签字体
            color: '#333',
            rotate: 30, // 轻微旋转避免重叠
            interval: 0, // 显示所有标签
            margin: 15 // 增加标签与轴的距离
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '万元',
            position: 'left',
            axisLabel: {
              formatter: '{value}',
              fontSize: 12,
              color: '#333'
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#e0e0e0',
                type: 'dashed'
              }
            }
          },
          {
            type: 'value',
            name: '个',
            position: 'right',
            axisLabel: {
              formatter: '{value}',
              fontSize: 12,
              color: '#333'
            }
          }
        ],
        series: [
          {
            name: '总损失金额 (万元)',
            type: 'bar',
            yAxisIndex: 0,
            data: totalLossData,
            itemStyle: {
              color: '#ff4d4f'
            }
          },
          {
            name: '净损失金额 (万元)',
            type: 'bar',
            yAxisIndex: 0,
            data: netLossData,
            itemStyle: {
              color: '#722ed1'
            }
          },
          {
            name: '操作风险损失事件数量',
            type: 'line',
            yAxisIndex: 1,
            data: eventCountData,
            lineStyle: {
              color: '#faad14',
              width: 3
            },
            itemStyle: {
              color: '#faad14'
            },
            symbol: 'circle',
            symbolSize: 8
          },
          {
            name: '重大操作风险事件数量',
            type: 'line',
            yAxisIndex: 1,
            data: majorEventCountData,
            lineStyle: {
              color: '#52c41a',
              width: 3
            },
            itemStyle: {
              color: '#52c41a'
            },
            symbol: 'circle',
            symbolSize: 8
          }
        ]
      };
      
      chart.setOption(option);
      
      setTimeout(() => {
        try {
          const dataURL = chart.getDataURL({
            type: 'png',
            pixelRatio: 2,
            backgroundColor: '#fff'
          });
          
          chart.dispose();
          document.body.removeChild(tempContainer);
          
          // 生成表格HTML - 保持不变
          const tableHtml = `
            <table border="1" cellpadding="5" cellspacing="0" style="width: 100%; border-collapse: collapse; margin-top: 10px;">
              <tr style="background-color: #f5f5f5;">
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;"></td>
                ${data.map((item, index) => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">${item.eventtypefirst}</td>`).join('')}
              </tr>
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold; color: black;"> 总损失金额 (万元)</td>
                ${data.map(item => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.totalloss}</td>`).join('')}
              </tr>
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold; color: black;">净损失金额 (万元)</td>
                ${data.map(item => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.netlosssum}</td>`).join('')}
              </tr>
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold; color: black;"> 操作风险损失事件数量 (个)</td>
                ${data.map(item => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.eventcount}</td>`).join('')}
              </tr>
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold; color: black;">重大操作风险事件数量 (个)</td>
                ${data.map(item => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.majoreventcount}</td>`).join('')}
              </tr>
            </table>
          `;
          
          const chartHtml = `
            <div style="text-align: center; margin: 20px 0;">
              <img src="${dataURL}" alt="${title}" style="max-width: 100%; border: 1px solid #eee;" />
              ${tableHtml}
            </div>
          `;
          
          resolve(chartHtml);
        } catch (error) {
          console.error('事件分类图表转换失败:', error);
          chart.dispose();
          document.body.removeChild(tempContainer);
          resolve(`<p>事件分类图表生成失败: ${title}</p>`);
        }
      }, 500);
      
    } catch (error) {
      console.error('事件分类图表初始化失败:', error);
      document.body.removeChild(tempContainer);
      resolve(`<p>事件分类图表生成失败</p>`);
    }
  });
}

function generateReasonChart(data) {
  if (!data || data.length === 0) return Promise.resolve('');
  
  return new Promise((resolve) => {
    const tempContainer = document.createElement('div');
    tempContainer.style.width = '1200px';
    tempContainer.style.height = '700px';
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    document.body.appendChild(tempContainer);
    
    try {
      const chart = echarts.init(tempContainer);
      const title = data[0]?.tablename || '操作风险损失事件的原因分布图';
      
      // 准备图表数据
      const reasons = data.map(item => item.eventtypefirst || '未知原因');
      const totalLossData = data.map(item => item.totalloss || 0);
      const netLossData = data.map(item => item.netlosssum || 0);
      const eventCountData = data.map(item => item.eventcount || 0);
      const majorEventCountData = data.map(item => item.majoreventcount || 0);
      
      const option = {
        title: {
          text: title,
          left: 'center',
          top: 20,
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['总损失金额 (万元)', '净损失金额 (万元)', '操作风险损失事件数量', '重大操作风险事件数量'],
          top: 60,
          itemGap: 20
        },
        grid: {
          left: '8%',
          right: '8%',
          bottom: '35%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: reasons,
          axisLabel: {
            fontSize: 12,
            color: '#333',
            rotate: 0,
            interval: 0
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '万元',
            position: 'left',
            axisLabel: {
              formatter: '{value}',
              fontSize: 12,
              color: '#333'
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#e0e0e0',
                type: 'dashed'
              }
            }
          },
          {
            type: 'value',
            name: '个',
            position: 'right',
            axisLabel: {
              formatter: '{value}',
              fontSize: 12,
              color: '#333'
            }
          }
        ],
        series: [
          {
            name: '总损失金额 (万元)',
            type: 'bar',
            yAxisIndex: 0,
            data: totalLossData,
            itemStyle: {
              color: '#ff4d4f'
            }
          },
          {
            name: '净损失金额 (万元)',
            type: 'bar',
            yAxisIndex: 0,
            data: netLossData,
            itemStyle: {
              color: '#722ed1'
            }
          },
          {
            name: '操作风险损失事件数量',
            type: 'line',
            yAxisIndex: 1,
            data: eventCountData,
            lineStyle: {
              color: '#faad14',
              width: 3
            },
            itemStyle: {
              color: '#faad14'
            },
            symbol: 'circle',
            symbolSize: 8
          },
          {
            name: '重大操作风险事件数量',
            type: 'line',
            yAxisIndex: 1,
            data: majorEventCountData,
            lineStyle: {
              color: '#52c41a',
              width: 3
            },
            itemStyle: {
              color: '#52c41a'
            },
            symbol: 'circle',
            symbolSize: 8
          }
        ]
      };
      
      chart.setOption(option);
      
      setTimeout(() => {
        try {
          const dataURL = chart.getDataURL({
            type: 'png',
            pixelRatio: 2,
            backgroundColor: '#fff'
          });
          
          chart.dispose();
          document.body.removeChild(tempContainer);
          
          // 生成表格HTML - 按照截图样式
          const tableHtml = `
            <table border="1" cellpadding="5" cellspacing="0" style="width: 100%; border-collapse: collapse; margin-top: 10px;">
              <tr style="background-color: #f5f5f5;">
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;"></td>
                ${data.map((item, index) => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">${item.eventtypefirst}</td>`).join('')}
              </tr>
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold; color: black;">总损失金额 (万元)</td>
                ${data.map(item => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.totalloss}</td>`).join('')}
              </tr>
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold; color: black;"> 净损失金额 (万元)</td>
                ${data.map(item => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.netlosssum}</td>`).join('')}
              </tr>
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold; color: black;"> 操作风险损失事件数量 (个)</td>
                ${data.map(item => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.eventcount}</td>`).join('')}
              </tr>
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold; color: black;"> 重大操作风险事件数量 (个)</td>
                ${data.map(item => `<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.majoreventcount}</td>`).join('')}
              </tr>
            </table>
          `;
          
          const chartHtml = `
            <div style="text-align: center; margin: 20px 0;">
              <img src="${dataURL}" alt="${title}" style="max-width: 100%; border: 1px solid #eee;" />
              ${tableHtml}
            </div>
          `;
          
          resolve(chartHtml);
        } catch (error) {
          console.error('原因分布图表转换失败:', error);
          chart.dispose();
          document.body.removeChild(tempContainer);
          resolve(`<p>原因分布图表生成失败: ${title}</p>`);
        }
      }, 500);
      
    } catch (error) {
      console.error('原因分布图表初始化失败:', error);
      document.body.removeChild(tempContainer);
      resolve(`<p>原因分布图表生成失败</p>`);
    }
  });
}


function generateSeparateTables(data, title) {
  if (!data || data.length === 0) return '';

  // 按监测频率分组数据
  const groupedData = {};
  data.forEach(item => {
    const frequency = item.monitoringfrequency || '未知';
    if (!groupedData[frequency]) {
      groupedData[frequency] = [];
    }
    groupedData[frequency].push(item);
  });

  let allTablesHtml = '';

  // 为每个频率生成单独的表格
  Object.keys(groupedData).forEach(frequency => {
    const frequencyData = groupedData[frequency];
    const tableHtml = generateTableByFrequency(frequencyData, frequency, title);
    allTablesHtml += tableHtml;
  });

  return allTablesHtml;
}


function generateTableByFrequency(data, frequency, title) {
  if (!data || data.length === 0) return '';

  // 根据频率确定表头
  let periodHeaders = [];
  let frequencyText = '';
  
  switch (frequency) {
    case '季':
      periodHeaders = ['1季度', '2季度', '3季度', '4季度'];
      frequencyText = '季度监测';
      break;
    case '年':
      periodHeaders = ['上半年', '下半年'];
      frequencyText = '年度监测';
      break;
    case '半年':
      periodHeaders = ['上半年', '下半年'];
      frequencyText = '半年度监测';
      break;
    case '月':
      periodHeaders = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
      frequencyText = '月度监测';
      break;
    default:
      periodHeaders = ['数据'];
      frequencyText = '监测数据';
  }

  // 构建表头
  const headers = ['指标名称', '监测频率', '阈值区间', ...periodHeaders];

  let tableHtml = `
    <table border="1" cellpadding="5" cellspacing="0" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
      <caption style="font-weight: bold; margin-bottom: 10px; font-size: 16px;">异常关键风险指标检测结果 - ${frequencyText}</caption>
      <thead>
        <tr style="background-color: #f5f5f5;">
          ${headers.map(header => `<th style="border: 1px solid #ddd; padding: 8px; text-align: center;">${header}</th>`).join('')}
        </tr>
      </thead>
      <tbody>
  `;

  // 表格数据行
  data.forEach((item, index) => {
    const thresholdIntervals = parseThresholds(item.thresholds);
    const periodData = parsePeriodData(item.metricvalueswithdates, frequency);

    tableHtml += `
      <tr>
        <td style="border: 1px solid #ddd; padding: 8px;">${item.indicatorname || ''}</td>
        <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.monitoringfrequency || ''}</td>
        <td style="border: 1px solid #ddd; padding: 0;">
          <table style="width: 100%; border-collapse: collapse;">
            <tbody>
              ${thresholdIntervals}
            </tbody>
          </table>
        </td>
        ${periodHeaders.map(header => {
          const dataValue = periodData[header] || '';
          return `<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${dataValue}</td>`;
        }).join('')}
      </tr>
    `;
  });

  tableHtml += `
      </tbody>
    </table>
  `;

  return tableHtml;
}


function parsePeriodData(metricvalueswithdates, frequency) {
  if (!metricvalueswithdates) return {};
  
  const result = {};
  const dataPairs = metricvalueswithdates.split(';');
  
  dataPairs.forEach(pair => {
    const [month, value] = pair.split('|');
    const monthNum = parseInt(month);
    
    if (frequency === '季') {
      // 季度映射：1-3月为1季度，4-6月为2季度，7-9月为3季度，10-12月为4季度
      if (monthNum >= 1 && monthNum <= 3) {
        result['1季度'] = result['1季度'] ? `${result['1季度']}; ${value}` : value;
      } else if (monthNum >= 4 && monthNum <= 6) {
        result['2季度'] = result['2季度'] ? `${result['2季度']}; ${value}` : value;
      } else if (monthNum >= 7 && monthNum <= 9) {
        result['3季度'] = result['3季度'] ? `${result['3季度']}; ${value}` : value;
      } else if (monthNum >= 10 && monthNum <= 12) {
        result['4季度'] = result['4季度'] ? `${result['4季度']}; ${value}` : value;
      }
    } else if (frequency === '年' || frequency === '半年') {
      // 半年映射：1-6月为上半年，7-12月为下半年
      if (monthNum >= 1 && monthNum <= 6) {
        result['上半年'] = result['上半年'] ? `${result['上半年']}; ${value}` : value;
      } else if (monthNum >= 7 && monthNum <= 12) {
        result['下半年'] = result['下半年'] ? `${result['下半年']}; ${value}` : value;
      }
    } else if (frequency === '月') {
      // 月度映射：直接按月份
      const monthKey = `${monthNum}月`;
      result[monthKey] = result[monthKey] ? `${result[monthKey]}; ${value}` : value;
    } else {
      // 默认情况
      result['数据'] = result['数据'] ? `${result['数据']}; ${value}` : value;
    }
  });
  
  return result;
}




/**
 * 线索派发
 */
function handleClueDistribution() {
  let content = regularTinyMce.value.handleGetSelectedText();
  if (!content) {
    createMessage.warn("请选中文档中的文字！");
    return;
  }

  // 获取锚点信息
  const anchorId = regularTinyMce.value.insertAnchor();

  let handleGetContent = regularTinyMce.value.handleGetContent();

  registerModal.value.disableSubmit = false;
  registerModal.value.add(id.value, content, anchorId, handleGetContent);
}

/**
 * 重置
 */
async function handleReset() {

  confirmLoading.value = true;
  await resetReport({id: id.value})
    .then((res) => {
      if (res.success) {
        createMessage.success("重置成功！");
        regularTinyMce.value.handleSetContent(res.result.reportHtml)
      } else {
        createMessage.warning("重置失败！");
      }
    })
    .finally(() => {
      confirmLoading.value = false;
    });
}

/**
 * 保存
 */
async function handleSave() {
  const data = {
    id: id.value,
    reportHtml: regularTinyMce.value.handleGetContent()
  }

  confirmLoading.value = true;
  await saveOrUpdate(data, true)
    .then((res) => {
      if (res.success) {
        createMessage.success(res.message);
      } else {
        createMessage.warning(res.message);
      }
    })
    .finally(() => {
      confirmLoading.value = false;
    });
}

</script>

<style lang="less" scoped>

</style>
