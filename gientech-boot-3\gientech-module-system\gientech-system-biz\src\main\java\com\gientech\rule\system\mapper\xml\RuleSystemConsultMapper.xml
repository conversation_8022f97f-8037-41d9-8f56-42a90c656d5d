<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gientech.rule.system.mapper.RuleSystemConsultMapper">

    <select id="selectByRelateId" parameterType="java.lang.String"
            resultType="com.gientech.rule.system.entity.RuleSystemConsult">
        select *
        from rule_system_consult
        where relate_id = #{relateId}
    </select>

    <delete id="deleteByRelateId" parameterType="java.lang.String">
        DELETE
        FROM rule_system_consult
        WHERE relate_id = #{relateId}
    </delete>

    <select id="selectFeedbackVOPage" resultType="com.gientech.rule.system.vo.RuleSystemConsultFeedbackVO">
        select
        rsraa.id systemId,
        rsraa.system_name,
        rsraa.system_issuing_body,
        rsraa.issuing_dept_one,
        rsraa.issuing_dept_two,

        rsc.id as id,
        rsc.relate_clause,
        rsc.consult_description,
        rsc.propose_institution,
        rsc.propose_dept_one,
        rsc.propose_dept_two,
        rsc.propose_date,
        rsc.consult_status,
        rsc.feedback_dept,
        rsc.feedback_opinion,
        rsc.feedback_turns,
        rsc.feedback_person,
        rsc.feedback_time,
        rsc.create_by,
        rsc.create_time,
        rsc.update_by,
        rsc.update_time,
        rsc.sys_org_code
        from rule_system_consult rsc
        inner join rule_system_reformed_and_abolished rsraa
        on rsc.system_id = rsraa.id
        <!-- 处理 MyBatis-Plus 条件构造器 -->
        <if test="ew != null">
            ${ew.customSqlSegment}
        </if>
    </select>
</mapper>