package com.gientech.ldc.param.localeventcrs.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import com.gientech.ldc.param.localeventcrs.entity.LdcParamOrLocaleventcrsVersion;
import com.gientech.ldc.param.localeventcrs.service.ILdcParamOrLocaleventcrsVersionService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 本地化事件分类及填报版本
 * @Author: jeecg-boot
 * @Date:   2025-04-18
 * @Version: V1.0
 */
@Tag(name="本地化事件分类及填报版本")
@RestController
@RequestMapping("/ldc/param/localeventcrs/ldcParamOrLocaleventcrsVersion")
@Slf4j
public class LdcParamOrLocaleventcrsVersionController extends JeecgController<LdcParamOrLocaleventcrsVersion, ILdcParamOrLocaleventcrsVersionService> {
	@Autowired
	private ILdcParamOrLocaleventcrsVersionService ldcParamOrLocaleventcrsVersionService;
	
	/**
	 * 分页列表查询
	 *
	 * @param ldcParamOrLocaleventcrsVersion
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "本地化事件分类及填报版本-分页列表查询")
	@Operation(summary="本地化事件分类及填报版本-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<LdcParamOrLocaleventcrsVersion>> queryPageList(LdcParamOrLocaleventcrsVersion ldcParamOrLocaleventcrsVersion,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<LdcParamOrLocaleventcrsVersion> queryWrapper = QueryGenerator.initQueryWrapper(ldcParamOrLocaleventcrsVersion, req.getParameterMap());
		Page<LdcParamOrLocaleventcrsVersion> page = new Page<LdcParamOrLocaleventcrsVersion>(pageNo, pageSize);
		IPage<LdcParamOrLocaleventcrsVersion> pageList = ldcParamOrLocaleventcrsVersionService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param ldcParamOrLocaleventcrsVersion
	 * @return
	 */
	@AutoLog(value = "本地化事件分类及填报版本-添加")
	@Operation(summary="本地化事件分类及填报版本-添加")
	//@RequiresPermissions("ldc.param.localeventcrs:ldc_param_or_localeventcrs_version:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody LdcParamOrLocaleventcrsVersion ldcParamOrLocaleventcrsVersion) {
		ldcParamOrLocaleventcrsVersionService.save(ldcParamOrLocaleventcrsVersion);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param ldcParamOrLocaleventcrsVersion
	 * @return
	 */
	@AutoLog(value = "本地化事件分类及填报版本-编辑")
	@Operation(summary="本地化事件分类及填报版本-编辑")
	//@RequiresPermissions("ldc.param.localeventcrs:ldc_param_or_localeventcrs_version:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody LdcParamOrLocaleventcrsVersion ldcParamOrLocaleventcrsVersion) {
		ldcParamOrLocaleventcrsVersionService.updateById(ldcParamOrLocaleventcrsVersion);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "本地化事件分类及填报版本-通过id删除")
	@Operation(summary="本地化事件分类及填报版本-通过id删除")
	//@RequiresPermissions("ldc.param.localeventcrs:ldc_param_or_localeventcrs_version:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		ldcParamOrLocaleventcrsVersionService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "本地化事件分类及填报版本-批量删除")
	@Operation(summary="本地化事件分类及填报版本-批量删除")
	//@RequiresPermissions("ldc.param.localeventcrs:ldc_param_or_localeventcrs_version:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.ldcParamOrLocaleventcrsVersionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "本地化事件分类及填报版本-通过id查询")
	@Operation(summary="本地化事件分类及填报版本-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<LdcParamOrLocaleventcrsVersion> queryById(@RequestParam(name="id",required=true) String id) {
		LdcParamOrLocaleventcrsVersion ldcParamOrLocaleventcrsVersion = ldcParamOrLocaleventcrsVersionService.getById(id);
		if(ldcParamOrLocaleventcrsVersion==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(ldcParamOrLocaleventcrsVersion);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param ldcParamOrLocaleventcrsVersion
    */
    //@RequiresPermissions("ldc.param.localeventcrs:ldc_param_or_localeventcrs_version:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, LdcParamOrLocaleventcrsVersion ldcParamOrLocaleventcrsVersion) {
        return super.exportXls(request, ldcParamOrLocaleventcrsVersion, LdcParamOrLocaleventcrsVersion.class, "本地化事件分类及填报版本");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    //@RequiresPermissions("ldc.param.localeventcrs:ldc_param_or_localeventcrs_version:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, LdcParamOrLocaleventcrsVersion.class);
    }


	@Operation(summary="本地化事件分类及填报版本-查询当前列表的所有数据")
	@GetMapping(value = "/queryAllEventClassifyList")
	public Result<List<LdcParamOrLocaleventcrsVersion>> queryAllEventClassifyList() {
		QueryWrapper queryWrapper = new QueryWrapper();
		queryWrapper.orderByAsc("event_level_one", "event_level_two");
		List<LdcParamOrLocaleventcrsVersion> pageList = ldcParamOrLocaleventcrsVersionService.list(queryWrapper);
		return Result.OK(pageList);
	}

}
