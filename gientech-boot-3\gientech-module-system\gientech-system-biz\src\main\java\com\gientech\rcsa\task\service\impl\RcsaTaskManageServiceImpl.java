package com.gientech.rcsa.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gientech.rcsa.history.entity.RcsaPlanManageHistory;
import com.gientech.rcsa.history.mapper.RcsaPlanManageHistoryMapper;
import com.gientech.rcsa.task.entity.RcsaTaskBasicInfo;
import com.gientech.rcsa.task.entity.RcsaTaskControlMeasure;
import com.gientech.rcsa.task.entity.RcsaTaskManage;
import com.gientech.rcsa.task.mapper.RcsaTaskBasicInfoMapper;
import com.gientech.rcsa.task.mapper.RcsaTaskControlMeasureMapper;
import com.gientech.rcsa.task.mapper.RcsaTaskManageMapper;
import com.gientech.rcsa.task.service.IRcsaTaskManageService;
import com.gientech.rcsa.task.vo.RcsaTaskExportAllVo;
import com.gientech.rcsa.task.vo.RcsaTaskExportColumnVo;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.mapper.SysDepartMapper;
import org.jeecg.modules.system.mapper.SysDictItemMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
/**
 * @Description: RCSA评估任务表
 * @Author: jeecg-boot
 * @Date:   2025-07-17
 * @Version: V1.0
 */
@Service
public class RcsaTaskManageServiceImpl extends ServiceImpl<RcsaTaskManageMapper, RcsaTaskManage> implements IRcsaTaskManageService {

    @Autowired
    private RcsaTaskManageMapper taskManageMapper;
    @Autowired
    private RcsaTaskBasicInfoMapper basicInfoMapper;
    @Autowired
    private RcsaPlanManageHistoryMapper historyMapper;
    @Autowired
    private RcsaTaskControlMeasureMapper measureMapper;
    @Autowired
    private SysDictItemMapper sysDictItemMapper;
    @Autowired
    private IWorkflowInstanceService workflowInstanceService;
    @Autowired
    private IWorkflowTaskService workflowTaskService;
    private final String businessKey = "rcsaTaskProcess";
    @Autowired
    private SysDepartMapper departMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> submit(RcsaTaskManage taskManage) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        // 判断所有的基本信息是否都已评估完成
//        RcsaTaskBasicInfo basicInfo = new RcsaTaskBasicInfo();
//        basicInfo.setTaskId(taskManage.getId());
//        basicInfo.setIsFinish("0");
//        int count = basicInfoMapper.selectNonFinishCount(basicInfo);
//        if (count>0) {
//            return Result.error("还有尚未完成的评估，请检查！");
//        }
        // 更新状态为审核中
        taskManage.setTaskState("2");
        int dealCount = taskManageMapper.updateState(taskManage);
        if (dealCount == 0) {
            return Result.error("该评估任务已被处理！");
        }

        // 入库历史表
        // 入库操作记录表
        RcsaPlanManageHistory history = new RcsaPlanManageHistory();
        history.setPlanManageId(taskManage.getId());
        history.setIsAudit("0");
        history.setOperateTime(new Date());
        history.setState("2");
        history.setOperateUser(userName);
        history.setOperateType("提交审核");
        history.setOperateDepart(sysUser.getOrgId());
        historyMapper.insert(history);

        return Result.ok("提交成功");
    }

    @Override
    public void auditPass(RcsaTaskManage taskManage) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        taskManage.setTaskState("3");
        taskManage.setFinishDate(new Date());
        taskManageMapper.updateById(taskManage);
        // 入库操作记录表
        RcsaPlanManageHistory history = new RcsaPlanManageHistory();
        history.setPlanManageId(taskManage.getId());
        history.setIsAudit("1");
        history.setOperateTime(new Date());
        history.setState("3");
        history.setOperateUser(userName);
        history.setOperateType("审核通过");
        history.setOperateDepart(sysUser.getOrgId());
        historyMapper.insert(history);
        // 更新审核通过日期
        QueryWrapper<RcsaTaskBasicInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("task_id", taskManage.getId());
        RcsaTaskBasicInfo basicInfo = new RcsaTaskBasicInfo();
        basicInfo.setAuditPassDate(new Date());
        basicInfoMapper.update(basicInfo, wrapper);

        // 更新措施表审核通过日期
        List<RcsaTaskBasicInfo> basicIdList = basicInfoMapper.selectList(wrapper);
        List<String> idList = new ArrayList<>();
        for (RcsaTaskBasicInfo info : basicIdList) {
            idList.add(info.getId());
        }
        QueryWrapper<RcsaTaskControlMeasure> wrapper1 = new QueryWrapper<>();
        wrapper1.in("basic_id", idList);
        RcsaTaskControlMeasure measure = new RcsaTaskControlMeasure();
        measure.setAuditPassDate(new Date());
        measureMapper.update(measure, wrapper1);
    }

    @Override
    public void auditReject(RcsaTaskManage taskManage) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userName = sysUser.getUsername();
        taskManage.setTaskState("4");
        taskManageMapper.updateById(taskManage);
        // 入库操作记录表
        RcsaPlanManageHistory history = new RcsaPlanManageHistory();
        history.setPlanManageId(taskManage.getId());
        history.setIsAudit("1");
        history.setOperateTime(new Date());
        history.setState("4");
        history.setOperateUser(userName);
        history.setOperateType("审核退回");
        history.setOperateDepart(sysUser.getOrgId());
        historyMapper.insert(history);
    }

    @Override
    public void exportOneXls(HttpServletRequest request,
                             HttpServletResponse response,
                             RcsaTaskExportColumnVo columnVo) throws Exception {

        String taskId = columnVo.getTaskId();
        RcsaTaskManage taskManage = taskManageMapper.selectById(taskId);
//        QueryWrapper wrapper = new QueryWrapper();
//        wrapper.eq("task_id", taskId);
//        wrapper.orderByAsc("sort_num");
        List<RcsaTaskBasicInfo> basicInfoList = basicInfoMapper.queryBasicList(taskId);

        XSSFWorkbook workbook = new XSSFWorkbook();
        CellStyle tableStyle = createTableStyle(workbook);
        // 创建第一个sheet页
        Sheet sheet1 = workbook.createSheet("sheet1");
        List<String> column1 = columnVo.getColumn1() == null ? new ArrayList<>() : columnVo.getColumn1();
        List<String> column2 = columnVo.getColumn2() == null ? new ArrayList<>() : columnVo.getColumn2();
        List<String> column3 = columnVo.getColumn3() == null ? new ArrayList<>() : columnVo.getColumn3();
        List<String> column4 = columnVo.getColumn4() == null ? new ArrayList<>() : columnVo.getColumn4();
        List<String> column5 = columnVo.getColumn5() == null ? new ArrayList<>() : columnVo.getColumn5();
        List<String> column6 = columnVo.getColumn6() == null ? new ArrayList<>() : columnVo.getColumn6();
        List<String> column7 = columnVo.getColumn7() == null ? new ArrayList<>() : columnVo.getColumn7();

        int column1Start = 0;
        int column1End = 0;
        int column2Start = 0;
        int column2End = 0;
        int column3Start = 0;
        int column3End = 0;
        int column4Start = 0;
        int column4End = 0;
        int column5Start = 0;
        int column5End = 0;
        int column6Start = 0;
        int column6End = 0;
        int column7Start = 0;
        int column7End = 0;

        // 放入第一层表头
        Row row = sheet1.createRow(0);
        if (column1 != null && column1.size() > 0) {
            column1End = column1.size() - 1;
            if (column1.size() == 1) {
                Cell cell = row.createCell(column1Start);
                cell.setCellValue("评估主界面信息");
                cell.setCellStyle(tableStyle);
            } else {
                mergeCells(sheet1, 0, 0, 0, column1End, "评估主界面信息", tableStyle);
            }
            column2Start = column1.size();
        }
        if (column2 != null && column2.size() > 0) {
            column2End = column2Start + column2.size() - 1;
            if (column2.size() == 1) {
                Cell cell = row.createCell(column2End);
                cell.setCellValue("基本信息");
                cell.setCellStyle(tableStyle);
            } else {
                mergeCells(sheet1, 0, 0, column2Start, column2End, "基本信息", tableStyle);
            }
            column3Start = column2End + 1;
        } else {
            column3Start = column1.size();
        }
        if (column3 != null && column3.size() > 0) {
            column3End = column3Start + column3.size() - 1;
            if (column3.size() == 1) {
                Cell cell = row.createCell(column3End);
                cell.setCellValue("固有风险评估信息");
                cell.setCellStyle(tableStyle);
            } else {
                mergeCells(sheet1, 0, 0, column3Start, column3End, "固有风险评估信息", tableStyle);
            }
            column4Start = column3End + 1;
        } else {
            column4Start = column2End + 1;
        }
        if (column4 != null && column4.size() > 0) {
            column4End = column4Start + column4.size() - 1;
            if (column4.size() == 1) {
                Cell cell = row.createCell(column4End);
                cell.setCellValue("控制措施评估信息");
                cell.setCellStyle(tableStyle);
            } else {
                mergeCells(sheet1, 0, 0, column4Start, column4End, "控制措施评估信息", tableStyle);
            }
            column5Start = column4End + 1;
        } else {
            column5Start = column3End + 1;
        }
        if (column5 != null && column5.size() > 0) {
            column5End = column5Start + column5.size() - 1;
            if (column5.size() == 1) {
                Cell cell = row.createCell(column4End);
                cell.setCellValue("剩余风险评估信息");
                cell.setCellStyle(tableStyle);
            } else {
                mergeCells(sheet1, 0, 0, column5Start, column5End, "剩余风险评估信息", tableStyle);
            }
            column6Start = column5End + 1;
        } else {
            column6Start = column4End + 1;
        }
        if (column6 != null && column6.size() > 0) {
            column6End = column6Start + column6.size() - 1;
            if (column6.size() == 1) {
                Cell cell = row.createCell(column6End);
                cell.setCellValue("改进建议及涉及问题");
                cell.setCellStyle(tableStyle);
            } else {
                mergeCells(sheet1, 0, 0, column6Start, column6End, "改进建议及涉及问题", tableStyle);
            }
            column7Start = column6End + 1;
        } else {
            column7Start = column5End + 1;
        }
        if (column7 != null && column7.size() > 0) {
            column7End = column7Start + column7.size() - 1;
            if (column7.size() == 1) {
                Cell cell = row.createCell(column7End);
                cell.setCellValue("关联要素信息");
                cell.setCellStyle(tableStyle);
            } else {
                mergeCells(sheet1, 0, 0, column7Start, column7End, "关联要素信息", tableStyle);
            }
        }

        // 查询字典数据
        List<String> dictCodeList = new ArrayList<>();
        Collections.addAll(dictCodeList, "rcsa_inherent_risk_level", "sr_control_classify_one",
                "sr_control_classify_two", "rcsa_non_financial_effect", "rcsa_remian_risk_accept", "sr_control_type",
                "rcsa_control_design_rationality", "rcsa_control_execute_availability", "rcsa_control_rating",
                "rcsa_remain_risk_frequency", "rcsa_remain_finance_effect", "rcsa_non_financial_effect",
                "whether", "rcsa_remian_risk_accept");
        List<SysDictItem> sysDictItemList = sysDictItemMapper.getItemByCode(dictCodeList);

        // 开始放入数据
        List<String> columnOrderList = new ArrayList<>();
        columnOrderList.add("tache_num");
        columnOrderList.add("risk_num");
        columnOrderList.add("control_number");

        CellStyle dataStyle = createDataStyle(workbook);
        int rowIndex = 1;
        for (RcsaTaskBasicInfo basicInfo : basicInfoList) {
            // 定义开始的列数
            int cellColumnIndex = 0;
            int cellDataIndex = 0;
            // 定义措施数据
            List<RcsaTaskControlMeasure> measureList = new ArrayList<>();
            int mergeRowIndex = 1;
            // 判断是否需要合并
            if (column4 != null && column4.size() > 0) {
                String basicId = basicInfo.getId();
                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("basic_id", basicId);
                queryWrapper.orderByAsc(columnOrderList);
                measureList = measureMapper.selectList(queryWrapper);
                // 合并行数=措施的数量
                mergeRowIndex = measureList.size();
            }
            // 放入表头
            if (rowIndex == 1) {
                CellStyle columnStyle = createTableStyle2(workbook);

                putTableColumn(sheet1, cellColumnIndex, column1, column2, column3, column4, column5, column6, column7, columnStyle);
                // 行数+1，开始放入数据
                rowIndex++;
            }

            if (mergeRowIndex == 1) {
                RcsaTaskControlMeasure measure = new RcsaTaskControlMeasure();
                if (measureList.size() > 0) {
                    measure = measureList.get(0);
                }
                // 表示不需要合并或措施只有一行数据
                putTableData(sheet1, rowIndex, basicInfo, measure, cellDataIndex, column1, column2,
                        column3, column4, column5, column6, column7, sysDictItemList, dataStyle);
                rowIndex++;
            } else {
                // 进行合并，必须先合并，否则后续放入数据会覆盖
                // 定义合并的结束行
                int mergeEndRow = rowIndex + mergeRowIndex - 1;
                mergeDataCells(sheet1, rowIndex, mergeEndRow,
                        basicInfo, column1, column2, column3, column5, column6, column7,
                        column1Start, column2Start, column3Start, column5Start, column6Start,
                        column7Start, sysDictItemList, dataStyle);
                // 放入措施数据
                for (int i = 0; i< measureList.size(); i++) {
                    Row measureRow = sheet1.getRow(rowIndex + i) == null ? sheet1.createRow(rowIndex + i) : sheet1.getRow(rowIndex + i);
                    // 放入数据
                    putColumn4Data(column4, measureRow, measureList.get(i), column4Start,
                            sysDictItemList, dataStyle);
                }
                rowIndex = rowIndex + mergeRowIndex;
            }
        }

        int columnsSize = column1.size() + column2.size() + column3.size() + column4.size() +
                column5.size() + column6.size() + column7.size();
        for (int i=0 ; i<columnsSize; i++) {
            sheet1.setColumnWidth(i, 5000);
        }

        response.setHeader("Connection", "close");
        response.setHeader("Content-Type",
                "application/vnd.ms-excel;charset=UTF-8");
        OutputStream out = null;
        String fileName = taskManage.getTaskCode() + ".xlsx";
        fileName = URLEncoder.encode(fileName, "utf-8");
        response.setHeader("Content-Disposition", "attachment;filename="
                + fileName);
        try {
            out = response.getOutputStream();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        if (out != null) {
            try {
                workbook.write(out);
                out.flush();
                out.close();
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
    }

    @Override
    public void exportAllUrl(HttpServletRequest request,
                             HttpServletResponse response,
                             RcsaTaskExportAllVo taskInfo) throws Exception {

        XSSFWorkbook workbook = new XSSFWorkbook();
        // 创建第一个sheet页
        Sheet sheet1 = workbook.createSheet("sheet1");
        Row row = sheet1.createRow(0);
        // 放入表头字段
        List<String> column = taskInfo.getColumn();
        CellStyle tableStyle = createTableStyle(workbook);
        putTaskColumn(row, column, tableStyle);

        // 放入数据
        if (StringUtils.isNotBlank(taskInfo.getTaskState())) {
            taskInfo.setTaskStateList(Arrays.asList(taskInfo.getTaskState().split(",")));
        }
        if (StringUtils.isNotBlank(taskInfo.getPlanType())) {
            taskInfo.setPlanTypeList(Arrays.asList(taskInfo.getPlanType().split(",")));
        }
        if (StringUtils.isNotBlank(taskInfo.getEvaluateDepart())) {
            taskInfo.setEvaluateDepartList(Arrays.asList(taskInfo.getEvaluateDepart().split(",")));
        }
        if (StringUtils.isNotBlank(taskInfo.getPlanTitle())) {
            taskInfo.setPlanTitle(taskInfo.getPlanTitle().replaceAll("\\*", ""));
        }
        if (StringUtils.isNotBlank(taskInfo.getSchemeTitle())) {
            taskInfo.setSchemeTitle(taskInfo.getSchemeTitle().replaceAll("\\*", ""));
        }

        List<String> dictCodeList = new ArrayList<>();
        Collections.addAll(dictCodeList, "rcsa_plan_type", "rcsa_plan_state");
        List<SysDictItem> sysDictItemList = sysDictItemMapper.getItemByCode(dictCodeList);

        for (int i=0 ; i<column.size(); i++) {
            sheet1.setColumnWidth(i, 5000);
        }

        CellStyle dataStyle = createDataStyle(workbook);
        // 查询数据
        List<RcsaTaskManage> resultList = taskManageMapper.queryExportList(taskInfo);
        int dataIndex = 1;
        for (RcsaTaskManage taskManage : resultList) {
            Row dataRow = sheet1.createRow(dataIndex);
            putTaskData(dataRow, column, taskManage, sysDictItemList, dataStyle);
            dataIndex++;
        }

        response.setHeader("Connection", "close");
        response.setHeader("Content-Type",
                "application/vnd.ms-excel;charset=UTF-8");
        OutputStream out = null;
        String fileName = "RCSA评估任务表.xlsx";
        fileName = URLEncoder.encode(fileName, "utf-8");
        response.setHeader("Content-Disposition", "attachment;filename="
                + fileName);
        try {
            out = response.getOutputStream();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        if (out != null) {
            try {
                workbook.write(out);
                out.flush();
                out.close();
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
    }

    @Override
    public Result<String> submitByWorkflow(RcsaTaskManage taskManage) {
        // 判断所有的基本信息是否都已评估完成
        RcsaTaskBasicInfo basicInfo = new RcsaTaskBasicInfo();
        basicInfo.setTaskId(taskManage.getId());
        basicInfo.setIsFinish("0");
        int count = basicInfoMapper.selectNonFinishCount(basicInfo);
        if (count>0) {
            return Result.error("还有尚未完成的评估，请检查！");
        }
        // 获取机构号
        SysDepart sysDepart = departMapper.selectById(taskManage.getEvaluateDepart());
        taskManage.setEvaluateDepartCode(sysDepart.getOrgCode());
        Map<String, Object> variables = new HashMap<>();
        variables.put(businessKey, taskManage);
        workflowInstanceService.createWorkflowInstance(businessKey, taskManage.getId(), variables);
        //processService.saveProcess(businessKey, schemeManage.getId(), "提交审核");
        return Result.OK("提交审核成功!");
    }

    private void putTaskData(Row row, List<String> column,
                             RcsaTaskManage taskManage,
                             List<SysDictItem> sysDictItemList,
                             CellStyle dataStyle) {
        int cellColumnIndex = 0;
        if (column.contains("taskCode")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue(taskManage.getTaskCode());
            cell.setCellStyle(dataStyle);
            cellColumnIndex++;
        }
        if (column.contains("planTitle")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue(taskManage.getPlanTitle());
            cell.setCellStyle(dataStyle);
            cellColumnIndex++;
        }
        if (column.contains("planType")) {
            Cell cell = row.createCell(cellColumnIndex);
            if (StringUtils.isNotBlank(taskManage.getPlanType())) {
                cell.setCellValue(sysDictItemList.stream().filter(o->
                                o.getDictCode().equals("rcsa_plan_type")
                                        && o.getItemValue().equals(taskManage.getPlanType()))
                        .collect(Collectors.toList()).get(0).getItemText());
            }
            cell.setCellStyle(dataStyle);
            cellColumnIndex++;
        }
        if (column.contains("schemeTitle")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue(taskManage.getSchemeTitle());
            cell.setCellStyle(dataStyle);
            cellColumnIndex++;
        }
        if (column.contains("matrixName")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue(taskManage.getMatrixName());
            cell.setCellStyle(dataStyle);
            cellColumnIndex++;
        }
        if (column.contains("evaluateDepart")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue(taskManage.getEvaluateDepartName());
            cell.setCellStyle(dataStyle);
            cellColumnIndex++;
        }
        if (column.contains("evaluateEndDate")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue(DateUtils.formatDate(taskManage.getEvaluateEndDate(), "yyyy-MM-dd"));
            cell.setCellStyle(dataStyle);
            cellColumnIndex++;
        }
        if (column.contains("finishDate")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue(taskManage.getFinishDate()==null?"":DateUtils.formatDate(taskManage.getFinishDate(), "yyyy-MM-dd"));
            cell.setCellStyle(dataStyle);
            cellColumnIndex++;
        }
        if (column.contains("taskState")) {
            Cell cell = row.createCell(cellColumnIndex);
            if (StringUtils.isNotBlank(taskManage.getTaskState())) {
                cell.setCellValue(sysDictItemList.stream().filter(o->
                                o.getDictCode().equals("rcsa_plan_state")
                                        && o.getItemValue().equals(taskManage.getTaskState()))
                        .collect(Collectors.toList()).get(0).getItemText());
            }
            cell.setCellStyle(dataStyle);
        }
    }

    private void putTaskColumn(Row row, List<String> column, CellStyle tableStyle) {

        int cellColumnIndex = 0;
        if (column.contains("taskCode")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue("评估编号");
            cell.setCellStyle(tableStyle);
            cellColumnIndex++;
        }
        if (column.contains("planTitle")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue("计划标题");
            cell.setCellStyle(tableStyle);
            cellColumnIndex++;
        }
        if (column.contains("planType")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue("计划类型");
            cell.setCellStyle(tableStyle);
            cellColumnIndex++;
        }
        if (column.contains("schemeTitle")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue("方案标题");
            cell.setCellStyle(tableStyle);
            cellColumnIndex++;
        }
        if (column.contains("matrixName")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue("矩阵名称");
            cell.setCellStyle(tableStyle);
            cellColumnIndex++;
        }
        if (column.contains("evaluateDepart")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue("评估机构/部门");
            cell.setCellStyle(tableStyle);
            cellColumnIndex++;
        }
        if (column.contains("evaluateEndDate")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue("评估截止日期");
            cell.setCellStyle(tableStyle);
            cellColumnIndex++;
        }
        if (column.contains("finishDate")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue("实际完成日期");
            cell.setCellStyle(tableStyle);
            cellColumnIndex++;
        }
        if (column.contains("taskState")) {
            Cell cell = row.createCell(cellColumnIndex);
            cell.setCellValue("任务状态");
            cell.setCellStyle(tableStyle);
        }
    }

    private void putColumn4Data(List<String> column4, Row tableColumnRow, RcsaTaskControlMeasure measure,
                                int cellDataIndex, List<SysDictItem> sysDictItemList, CellStyle dataStyle) {
        if (column4 != null && column4.size()>0) {
            if (column4.contains("controlNumber")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(measure.getControlNumber());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlDescription")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(measure.getControlDescription());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlClassifyOne")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(measure.getControlClassifyOne())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("sr_control_classify_one")
                                            && o.getItemValue().equals(measure.getControlClassifyOne()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlTypeclassifyTwo")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(measure.getControlTypeclassifyTwo())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("sr_control_classify_two")
                                            && o.getItemValue().equals(measure.getControlTypeclassifyTwo()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cellDataIndex++;
            }
            if (column4.contains("controlType")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(measure.getControlType())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("sr_control_type")
                                            && o.getItemValue().equals(measure.getControlType()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("involvingSystem")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(measure.getInvolvingSystem());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlDesignRationality")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(measure.getControlDesignRationality())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_control_design_rationality")
                                            && o.getItemValue().equals(measure.getControlDesignRationality()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlExecuteAvailability")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(measure.getControlExecuteAvailability())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_control_execute_availability")
                                            && o.getItemValue().equals(measure.getControlExecuteAvailability()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlRating")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(measure.getControlRating())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_control_rating")
                                            && o.getItemValue().equals(measure.getControlRating()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlResult")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(measure.getControlResult());
                cell.setCellStyle(dataStyle);
            }
        }
    }

    private void mergeDataCells(Sheet sheet1, int rowIndex,
                                int mergeEndRow, RcsaTaskBasicInfo basicInfo, List<String> column1,
                                List<String> column2, List<String> column3,
                                List<String> column5, List<String> column6,
                                List<String> column7, int column1Start,
                                int column2Start, int column3Start, int column5Start,
                                int column6Start, int column7Start, List<SysDictItem> sysDictItemList,
                                CellStyle dataStyle) {

        if (column1 != null && column1.size()>0) {
            if (column1.contains("tacheNum")) {
                mergeCells(sheet1, rowIndex, mergeEndRow, column1Start, column1Start, basicInfo.getTacheNum(), dataStyle);
                column1Start++;
            }
            if (column1.contains("tacheName")) {
                mergeCells(sheet1, rowIndex, mergeEndRow, column1Start, column1Start, basicInfo.getTacheName(), dataStyle);
                column1Start++;
            }
            if (column1.contains("riskNum")) {
                mergeCells(sheet1, rowIndex, mergeEndRow, column1Start, column1Start, basicInfo.getRiskNum(), dataStyle);
                column1Start++;
            }
            if (column1.contains("riskDescription")) {
                mergeCells(sheet1, rowIndex, mergeEndRow, column1Start, column1Start, basicInfo.getRiskDescription(), dataStyle);
                column1Start++;
            }
            if (column1.contains("adjustedRemainRiskLevel")) {
                if (StringUtils.isNotBlank(basicInfo.getAdjustedRemainRiskLevel())) {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column1Start, column1Start,
                            sysDictItemList.stream().filter(o->
                                            o.getDictCode().equals("rcsa_non_financial_effect")
                                                    && o.getItemValue().equals(basicInfo.getAdjustedRemainRiskLevel()))
                                    .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column1Start, column1Start, basicInfo.getAdjustedRemainRiskLevel(), dataStyle);
                }
                column1Start++;
            }
            if (column1.contains("remainRiskIsAccept")) {
                if (StringUtils.isNotBlank(basicInfo.getRemainRiskIsAccept())) {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column1Start, column1Start,
                            sysDictItemList.stream().filter(o->
                                            o.getDictCode().equals("rcsa_remian_risk_accept")
                                                    && o.getItemValue().equals(basicInfo.getRemainRiskIsAccept()))
                                    .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column1Start, column1Start, basicInfo.getRemainRiskIsAccept(), dataStyle);
                }
                column1Start++;
            }
            if (column1.contains("lastEvaluateResult")) {
                if (StringUtils.isNotBlank(basicInfo.getLastEvaluateResult())) {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column1Start, column1Start,
                            sysDictItemList.stream().filter(o->
                                            o.getDictCode().equals("rcsa_non_financial_effect")
                                                    && o.getItemValue().equals(basicInfo.getLastEvaluateResult()))
                                    .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column1Start, column1Start, basicInfo.getLastEvaluateResult(), dataStyle);
                }
            }
        }

        if (column2 != null && column2.size()>0) {
            if (column2.contains("evaluateDepart")) {
                mergeCells(sheet1, rowIndex, mergeEndRow, column2Start, column2Start, basicInfo.getEvaluateDepartName(), dataStyle);
                column2Start++;
            }
            if (column2.contains("sector")) {
                mergeCells(sheet1, rowIndex, mergeEndRow, column2Start, column2Start, basicInfo.getSector(), dataStyle);
                column2Start++;
            }
            if (column2.contains("levelOne")) {
                mergeCells(sheet1, rowIndex, mergeEndRow, column2Start, column2Start, basicInfo.getLevelOne(), dataStyle);
                column2Start++;
            }
            if (column2.contains("levelTwo")) {
                mergeCells(sheet1, rowIndex, mergeEndRow, column2Start, column2Start, basicInfo.getLevelTwo(), dataStyle);
                column2Start++;
            }
            if (column2.contains("matrixName")) {
                mergeCells(sheet1, rowIndex, mergeEndRow, column2Start, column2Start, basicInfo.getMatrixName(), dataStyle);
                column2Start++;
            }
            if (column2.contains("riskFactorType")) {
                mergeCells(sheet1, rowIndex, mergeEndRow, column2Start, column2Start, basicInfo.getRiskFactorType(), dataStyle);
                column2Start++;
            }
            if (column2.contains("riskImpactType")) {
                mergeCells(sheet1, rowIndex, mergeEndRow, column2Start, column2Start, basicInfo.getRiskImpactType(), dataStyle);
                column2Start++;
            }
            if (column2.contains("riskEventLabels")) {
                mergeCells(sheet1, rowIndex, mergeEndRow, column2Start, column2Start, basicInfo.getRiskEventLabels(), dataStyle);
            }
        }
        if (column3 != null && column3.size()>0) {
            if (column3.contains("inherentRiskLevel")) {
                if (StringUtils.isNotBlank(basicInfo.getInherentRiskLevel())) {
                    // rcsa_inherent_risk_level
                    mergeCells(sheet1, rowIndex, mergeEndRow, column3Start, column3Start,
                            sysDictItemList.stream().filter(o->o.getDictCode().equals("rcsa_inherent_risk_level") && o.getItemValue().equals(basicInfo.getInherentRiskLevel())).collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column3Start, column3Start, basicInfo.getInherentRiskLevel(), dataStyle);
                }
            }
        }

        if (column5 != null && column5.size()>0) {
            if (column5.contains("riskFrequency")) {
                if (StringUtils.isNotBlank(basicInfo.getRiskFrequency())) {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start,
                            sysDictItemList.stream().filter(o->
                                            o.getDictCode().equals("rcsa_remain_risk_frequency")
                                                    && o.getItemValue().equals(basicInfo.getRiskFrequency()))
                                    .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start, basicInfo.getRiskFrequency(), dataStyle);
                }
                column5Start++;
            }
            if (column5.contains("financeEffect")) {
                if (StringUtils.isNotBlank(basicInfo.getFinanceEffect())) {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start,
                            sysDictItemList.stream().filter(o->
                                            o.getDictCode().equals("rcsa_remain_finance_effect")
                                                    && o.getItemValue().equals(basicInfo.getFinanceEffect()))
                                    .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start, basicInfo.getFinanceEffect(), dataStyle);
                }
                column5Start++;
            }
            if (column5.contains("reputationDamage")) {
                if (StringUtils.isNotBlank(basicInfo.getReputationDamage())) {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start,
                            sysDictItemList.stream().filter(o->
                                            o.getDictCode().equals("rcsa_non_financial_effect")
                                                    && o.getItemValue().equals(basicInfo.getReputationDamage()))
                                    .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start, basicInfo.getReputationDamage(), dataStyle);
                }
                column5Start++;
            }
            if (column5.contains("operationInterruption")) {
                if (StringUtils.isNotBlank(basicInfo.getOperationInterruption())) {

                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start,
                            sysDictItemList.stream().filter(o->
                                            o.getDictCode().equals("rcsa_non_financial_effect")
                                                    && o.getItemValue().equals(basicInfo.getOperationInterruption()))
                                    .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start, basicInfo.getOperationInterruption(), dataStyle);
                }
                column5Start++;
            }
            if (column5.contains("customerServiceQuality")) {
                if (StringUtils.isNotBlank(basicInfo.getCustomerServiceQuality())) {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start,
                            sysDictItemList.stream().filter(o->
                                            o.getDictCode().equals("rcsa_non_financial_effect")
                                                    && o.getItemValue().equals(basicInfo.getCustomerServiceQuality()))
                                    .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start, basicInfo.getCustomerServiceQuality(), dataStyle);
                }
                column5Start++;
            }
            if (column5.contains("regulatoryActions")) {
                if (StringUtils.isNotBlank(basicInfo.getRegulatoryActions())) {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start,
                            sysDictItemList.stream().filter(o->
                                            o.getDictCode().equals("rcsa_non_financial_effect")
                                                    && o.getItemValue().equals(basicInfo.getRegulatoryActions()))
                                    .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start, basicInfo.getRegulatoryActions(), dataStyle);
                }
                column5Start++;
            }
            if (column5.contains("employeeSafety")) {
                if (StringUtils.isNotBlank(basicInfo.getEmployeeSafety())) {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start,
                            sysDictItemList.stream().filter(o->
                                            o.getDictCode().equals("rcsa_non_financial_effect")
                                                    && o.getItemValue().equals(basicInfo.getEmployeeSafety()))
                                    .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start, basicInfo.getEmployeeSafety(), dataStyle);
                }
                column5Start++;
            }
            if (column5.contains("remainRiskLevel")) {
                if (StringUtils.isNotBlank(basicInfo.getRemainRiskLevel())) {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start,
                            sysDictItemList.stream().filter(o->
                                            o.getDictCode().equals("rcsa_non_financial_effect")
                                                    && o.getItemValue().equals(basicInfo.getRemainRiskLevel()))
                                    .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start, basicInfo.getRemainRiskLevel(), dataStyle);
                }
                column5Start++;
            }
            if (column5.contains("isAdjustRemainRiskLevel")) {
                if (StringUtils.isNotBlank(basicInfo.getIsAdjustRemainRiskLevel())) {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start,
                            sysDictItemList.stream().filter(o->
                                            o.getDictCode().equals("whether")
                                                    && o.getItemValue().equals(basicInfo.getIsAdjustRemainRiskLevel()))
                                    .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start, basicInfo.getIsAdjustRemainRiskLevel(), dataStyle);
                }
                column5Start++;
            }
            if (column5.contains("adjustedRemainRiskLevel")) {
                if (StringUtils.isNotBlank(basicInfo.getAdjustedRemainRiskLevel())) {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start,
                            sysDictItemList.stream().filter(o->
                                            o.getDictCode().equals("rcsa_non_financial_effect")
                                                    && o.getItemValue().equals(basicInfo.getAdjustedRemainRiskLevel()))
                                    .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start, basicInfo.getAdjustedRemainRiskLevel(), dataStyle);
                }
                column5Start++;
            }
            if (column5.contains("remainRiskIsAccept")) {
                if (StringUtils.isNotBlank(basicInfo.getRemainRiskIsAccept())) {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start,
                            sysDictItemList.stream().filter(o->
                                            o.getDictCode().equals("rcsa_remian_risk_accept")
                                                    && o.getItemValue().equals(basicInfo.getRemainRiskIsAccept()))
                                    .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                } else {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start, basicInfo.getRemainRiskIsAccept(), dataStyle);
                }
                column5Start++;
            }
            if (column5.contains("remainRiskResult")) {
                mergeCells(sheet1, rowIndex, mergeEndRow, column5Start, column5Start, basicInfo.getRemainRiskResult(), dataStyle);
            }

            if (column6 != null && column6.size()>0) {
                if (column6.contains("improveAdvice")) {
                    mergeCells(sheet1, rowIndex, mergeEndRow, column6Start, column6Start, basicInfo.getImproveAdvice(), dataStyle);
                    column6Start++;
                }
                if (column6.contains("isInvolveProblem")) {
                    if (StringUtils.isNotBlank(basicInfo.getIsInvolveProblem())) {
                        mergeCells(sheet1, rowIndex, mergeEndRow, column6Start, column6Start,
                                sysDictItemList.stream().filter(o->
                                                o.getDictCode().equals("whether")
                                                        && o.getItemValue().equals(basicInfo.getIsInvolveProblem()))
                                        .collect(Collectors.toList()).get(0).getItemText(), dataStyle);
                    } else {
                        mergeCells(sheet1, rowIndex, mergeEndRow, column6Start, column6Start, basicInfo.getIsInvolveProblem(), dataStyle);
                    }
                }
            }
            // TODO:huanjing 未做
        }
    }

    private void putTableData(Sheet sheet1, int rowIndex, RcsaTaskBasicInfo basicInfo,
                              RcsaTaskControlMeasure measure, int cellDataIndex, List<String> column1,
                              List<String> column2, List<String> column3, List<String> column4,
                              List<String> column5, List<String> column6, List<String> column7,
                              List<SysDictItem> sysDictItemList, CellStyle dataStyle) {
        Row tableColumnRow = sheet1.createRow(rowIndex);
        if (column1 != null && column1.size()>0) {
            if (column1.contains("tacheNum")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(basicInfo.getTacheNum());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column1.contains("tacheName")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(basicInfo.getTacheName());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column1.contains("riskNum")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(basicInfo.getRiskNum());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column1.contains("riskDescription")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(basicInfo.getRiskDescription());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column1.contains("adjustedRemainRiskLevel")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getAdjustedRemainRiskLevel())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_non_financial_effect")
                                            && o.getItemValue().equals(basicInfo.getAdjustedRemainRiskLevel()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column1.contains("remainRiskIsAccept")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getRemainRiskIsAccept())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_remian_risk_accept")
                                            && o.getItemValue().equals(basicInfo.getRemainRiskIsAccept()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column1.contains("lastEvaluateResult")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getLastEvaluateResult())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_non_financial_effect")
                                            && o.getItemValue().equals(basicInfo.getLastEvaluateResult()))
                            .collect(Collectors.toList()).get(0).getItemText());
                } else {
                    cell.setCellValue(basicInfo.getLastEvaluateResult());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
        }

        if (column2 != null && column2.size()>0) {
            if (column2.contains("evaluateDepart")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(basicInfo.getEvaluateDepartName());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column2.contains("sector")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(basicInfo.getSector());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column2.contains("levelOne")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(basicInfo.getLevelOne());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column2.contains("levelTwo")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(basicInfo.getLevelTwo());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column2.contains("matrixName")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(basicInfo.getMatrixName());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column2.contains("riskFactorType")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(basicInfo.getRiskFactorType());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column2.contains("riskImpactType")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(basicInfo.getRiskImpactType());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column2.contains("riskEventLabels")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(basicInfo.getRiskEventLabels());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
        }
        if (column3 != null && column3.size()>0) {
            if (column3.contains("inherentRiskLevel")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getInherentRiskLevel())) {
                    // rcsa_inherent_risk_level
                    cell.setCellValue(sysDictItemList.stream().filter(o->o.getDictCode().equals("rcsa_inherent_risk_level") && o.getItemValue().equals(basicInfo.getInherentRiskLevel())).collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
        }
        if (column4 != null && column4.size()>0) {
            if (column4.contains("controlNumber")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(measure.getControlNumber());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlDescription")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(measure.getControlDescription());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlClassifyOne")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(measure.getControlClassifyOne())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                            o.getDictCode().equals("sr_control_classify_one")
                                    && o.getItemValue().equals(measure.getControlClassifyOne()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlTypeclassifyTwo")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(measure.getControlTypeclassifyTwo())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("sr_control_classify_two")
                                            && o.getItemValue().equals(measure.getControlTypeclassifyTwo()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlType")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(measure.getControlType())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("sr_control_type")
                                            && o.getItemValue().equals(measure.getControlType()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("involvingSystem")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(measure.getInvolvingSystem());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlDesignRationality")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(measure.getControlDesignRationality())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_control_design_rationality")
                                            && o.getItemValue().equals(measure.getControlDesignRationality()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlExecuteAvailability")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(measure.getControlExecuteAvailability())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_control_execute_availability")
                                            && o.getItemValue().equals(measure.getControlExecuteAvailability()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlRating")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(measure.getControlRating())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_control_rating")
                                            && o.getItemValue().equals(measure.getControlRating()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column4.contains("controlResult")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(measure.getControlResult());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
        }

        if (column5 != null && column5.size()>0) {
            if (column5.contains("riskFrequency")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getRiskFrequency())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_remain_risk_frequency")
                                            && o.getItemValue().equals(basicInfo.getRiskFrequency()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column5.contains("financeEffect")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getFinanceEffect())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_remain_finance_effect")
                                            && o.getItemValue().equals(basicInfo.getFinanceEffect()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column5.contains("reputationDamage")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getReputationDamage())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_non_financial_effect")
                                            && o.getItemValue().equals(basicInfo.getReputationDamage()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column5.contains("operationInterruption")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getOperationInterruption())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_non_financial_effect")
                                            && o.getItemValue().equals(basicInfo.getOperationInterruption()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column5.contains("customerServiceQuality")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getCustomerServiceQuality())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_non_financial_effect")
                                            && o.getItemValue().equals(basicInfo.getCustomerServiceQuality()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column5.contains("regulatoryActions")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getRegulatoryActions())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_non_financial_effect")
                                            && o.getItemValue().equals(basicInfo.getRegulatoryActions()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column5.contains("employeeSafety")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getEmployeeSafety())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_non_financial_effect")
                                            && o.getItemValue().equals(basicInfo.getEmployeeSafety()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column5.contains("remainRiskLevel")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getRemainRiskLevel())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_non_financial_effect")
                                            && o.getItemValue().equals(basicInfo.getRemainRiskLevel()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column5.contains("isAdjustRemainRiskLevel")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getIsAdjustRemainRiskLevel())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("whether")
                                            && o.getItemValue().equals(basicInfo.getIsAdjustRemainRiskLevel()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column5.contains("adjustedRemainRiskLevel")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getAdjustedRemainRiskLevel())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_non_financial_effect")
                                            && o.getItemValue().equals(basicInfo.getAdjustedRemainRiskLevel()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column5.contains("remainRiskIsAccept")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getRemainRiskIsAccept())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("rcsa_remian_risk_accept")
                                            && o.getItemValue().equals(basicInfo.getRemainRiskIsAccept()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column5.contains("remainRiskResult")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(basicInfo.getRemainRiskResult());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
        }
        if (column6 != null && column6.size()>0) {
            if (column6.contains("improveAdvice")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                cell.setCellValue(basicInfo.getImproveAdvice());
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
            if (column6.contains("isInvolveProblem")) {
                Cell cell = tableColumnRow.createCell(cellDataIndex);
                if (StringUtils.isNotBlank(basicInfo.getIsInvolveProblem())) {
                    cell.setCellValue(sysDictItemList.stream().filter(o->
                                    o.getDictCode().equals("whether")
                                            && o.getItemValue().equals(basicInfo.getIsInvolveProblem()))
                            .collect(Collectors.toList()).get(0).getItemText());
                }
                cell.setCellStyle(dataStyle);
                cellDataIndex++;
            }
        }
        // TODO:huanjing 未做

    }

    private void putTableColumn(Sheet sheet1, int cellColumnIndex,
                                List<String> column1, List<String> column2,
                                List<String> column3, List<String> column4,
                                List<String> column5, List<String> column6,
                                List<String> column7, CellStyle columnStyle) {
        Row tableColumnRow = sheet1.createRow(1);
        if (column1 != null && column1.size()>0) {
            if (column1.contains("tacheNum")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("环节编号");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column1.contains("tacheName")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("环节名称");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column1.contains("riskNum")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("风险编号");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column1.contains("riskDescription")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("风险描述");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column1.contains("adjustedRemainRiskLevel")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("调整后剩余风险等级");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column1.contains("remainRiskIsAccept")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("剩余风险接受程度");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column1.contains("lastEvaluateResult")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("最近一次评估结果");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
        }
        if (column2 != null && column2.size()>0) {
            if (column2.contains("evaluateDepart")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("评估机构/部门");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column2.contains("sector")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("所属板块");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column2.contains("levelOne")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("一级分类");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column2.contains("levelTwo")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("二级分类");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column2.contains("matrixName")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("矩阵名称");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column2.contains("riskFactorType")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("风险因子类型");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column2.contains("riskImpactType")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("风险影响类型");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column2.contains("riskEventLabels")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("风险事件标签");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
        }
        if (column3 != null && column3.size()>0) {
            if (column3.contains("inherentRiskLevel")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("固有风险等级");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
        }
        if (column4 != null && column4.size()>0) {
            if (column4.contains("controlNumber")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("控制编号");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column4.contains("controlDescription")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("控制措施描述");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column4.contains("controlClassifyOne")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("控制分类（一级）");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column4.contains("controlTypeclassifyTwo")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("控制分类（二级）");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column4.contains("controlType")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("控制类型");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column4.contains("involvingSystem")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("涉及信息系统");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column4.contains("controlDesignRationality")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("控制设计合理性");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column4.contains("controlExecuteAvailability")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("控制执行有效性");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column4.contains("controlRating")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("控制评级");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column4.contains("controlResult")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("控制评估结果说明");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
        }

        if (column5 != null && column5.size()>0) {
            if (column5.contains("riskFrequency")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("风险频率");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column5.contains("financeEffect")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("财务影响");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column5.contains("reputationDamage")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("声誉受损");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column5.contains("operationInterruption")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("运营中断");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column5.contains("customerServiceQuality")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("客户服务质量");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column5.contains("regulatoryActions")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("监管行动");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column5.contains("employeeSafety")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("员工安全");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column5.contains("remainRiskLevel")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("剩余风险等级");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column5.contains("isAdjustRemainRiskLevel")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("是否调整剩余风险等级");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column5.contains("adjustedRemainRiskLevel")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("调整后剩余风险等级");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column5.contains("remainRiskIsAccept")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("剩余风险接受度");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column5.contains("remainRiskResult")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("剩余风险评估结果说明");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
        }
        if (column6 != null && column6.size()>0) {
            if (column6.contains("improveAdvice")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("改进建议");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column6.contains("isInvolveProblem")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("是否涉及问题");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
        }
        if (column7 != null && column7.size()>0) {
            if (column7.contains("indexCode")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("指标编号");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
            if (column7.contains("indexName")) {
                Cell cell = tableColumnRow.createCell(cellColumnIndex);
                cell.setCellValue("指标名称");
                cell.setCellStyle(columnStyle);
                cellColumnIndex++;
            }
        }
    }


    private static void mergeCells(Sheet sheet, int firstRow, int lastRow,
                                   int firstCol, int lastCol, String value, CellStyle style) {
        sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));

        // 为合并区域的所有单元格设置边框
        for (int rowNum = firstRow; rowNum <= lastRow; rowNum++) {
            Row row = sheet.getRow(rowNum) == null ? sheet.createRow(rowNum) : sheet.getRow(rowNum);
            row.setHeightInPoints(20);
            for (int colNum = firstCol; colNum <= lastCol; colNum++) {
                Cell cell = row.createCell(colNum);
                if (style != null) {
                    cell.setCellStyle(style);
                }
                if (rowNum == firstRow && colNum == firstCol) {
                    cell.setCellValue(value);
                }
            }
        }
    }

    private CellStyle createTableStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);

        // 设置字体
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short)14);
        font.setBold(true);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置背景色
        style.setFillForegroundColor(IndexedColors.LIGHT_TURQUOISE1.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setFont(font);
        return style;
    }

    private CellStyle createTableStyle2(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);

        // 设置字体
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short)12);
        font.setBold(true);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置背景色
        style.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setFont(font);
        return style;
    }

    private CellStyle createDataStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);

        // 设置字体
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short)12);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置背景色
        style.setFont(font);
        return style;
    }

}
