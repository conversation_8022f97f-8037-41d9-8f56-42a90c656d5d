<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gientech.rule.manage.reformedAndAbolished.mapper.RuleSystemReformedAndAbolishedMapper">

    <select id="selectRuleSystemPage" resultType="com.gientech.rule.manage.reformedAndAbolished.entity.RuleSystem">
        select *
        from rule_system_reformed_and_abolished
        <!-- 处理 MyBatis-Plus 条件构造器 -->
        <if test="ew != null">
            ${ew.customSqlSegment}
        </if>
    </select>

    <select id="selectSuggestVOPage" resultType="com.gientech.rule.system.vo.RuleSystemSuggestVO">
        select rssr.*,
        rsraa.id as system_id,
        rsraa.system_name,
        rsraa.system_issuing_body,
        rsraa.issuing_dept_one,
        rsraa.issuing_dept_two
        from rule_system_reformed_and_abolished rsraa
        inner join rule_system_suggest_relate rssr on rsraa.id = rssr.system_id
        <!-- 处理 MyBatis-Plus 条件构造器 -->
        <if test="ew != null">
            ${ew.customSqlSegment}
        </if>
    </select>

    <select id="selectConsultVOPage" resultType="com.gientech.rule.system.vo.RuleSystemConsultVO">
        select rscr.*,
        rsraa.id as system_id,
        rsraa.system_name,
        rsraa.system_issuing_body,
        rsraa.issuing_dept_one,
        rsraa.issuing_dept_two
        from rule_system_reformed_and_abolished rsraa
        inner join rule_system_consult_relate rscr on rsraa.id = rscr.system_id
        <!-- 处理 MyBatis-Plus 条件构造器 -->
        <if test="ew != null">
            ${ew.customSqlSegment}
        </if>
    </select>

    <select id="queryInterList"
            resultType="com.gientech.rule.manage.reformedAndAbolished.entity.RuleSystemReformedAndAbolished">
        select
        t.*,
        c.ldc_id as "ldcId",
        d.depart_name as "issuingDeptOneName"
        from rule_system_reformed_and_abolished t
        left join ldc_rule_rel c on c.rule_id = t.id
        left join sys_depart d on t.issuing_dept_one = d.org_code
        where
        c.rule_type = '1'
        and c.ldc_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>