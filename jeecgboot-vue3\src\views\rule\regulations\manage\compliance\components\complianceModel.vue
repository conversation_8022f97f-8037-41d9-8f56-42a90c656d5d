<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    @ok="handleOk"
    :maxHeight="10"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <RuleSystemReformedAndAbolishedForm ref="registerForm" @ok="submitCallback" :formDisabled="disableSubmit" :formBpm="false" />
  </j-modal>
</template>

<script lang="ts" setup>
  import { ref, nextTick, defineExpose } from 'vue';
  import RuleSystemReformedAndAbolishedForm from './complianceForm.vue';
  import JModal from '/@/components/Modal/src/JModal/JModal.vue';
  import { cancelDelete } from '@/views/rule/regulations/manage/reformedAndAbolished/components/file/RuleSystemReformedAndAbolishedFile.api';

  const title = ref<string>('合规审核意见');
  const width = ref<number>(800);
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const registerForm = ref();
  const emit = defineEmits(['register', 'success']);

  /**
   * 新增
   */
  function add() {
    title.value = '新增';
    visible.value = true;
    nextTick(() => {
      registerForm.value.add();
      registerForm.value.addorupdate = '1';
    });
  }

  /**
   * 编辑
   * @param record
   */
  function edit(record, flag) {
    visible.value = true;
    nextTick(() => {
      registerForm.value.edit(record, flag);
    });
  }

  /**
   * 确定按钮点击事件
   */
  function handleOk() {
    registerForm.value.submitForm();
  }

  /**
   * form保存回调事件
   */
  function submitCallback() {
    handleCancel();
    emit('success');
  }

  /**
   * 取消按钮回调事件
   */
  async function handleCancel() {
    await cancelDelete();
    visible.value = false;
  }

  defineExpose({
    add,
    edit,
    disableSubmit,
  });
</script>

<style lang="less">
  /**隐藏样式-modal确定按钮 */
  .jee-hidden {
    display: none !important;
  }
</style>
<style lang="less" scoped></style>
