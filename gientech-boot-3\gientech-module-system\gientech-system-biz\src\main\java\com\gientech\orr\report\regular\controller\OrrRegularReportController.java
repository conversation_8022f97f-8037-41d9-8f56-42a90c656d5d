package com.gientech.orr.report.regular.controller;

import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import com.gientech.common.process.enitity.CommonProcess;
import com.gientech.common.process.service.ICommonProcessService;
import com.gientech.orr.report.regular.vo.OrrRegularReportVo;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import net.sf.json.JSONObject;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.oConvertUtils;
import com.gientech.orr.report.regular.entity.OrrRegularReport;
import com.gientech.orr.report.regular.service.IOrrRegularReportService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 操作风险报告-常规报告管理
 * @Author: jeecg-boot
 * @Date: 2025-07-08
 * @Version: V1.0
 */
@Tag(name = "操作风险报告-常规报告管理")
@RestController
@RequestMapping("/orr/report/regular/orrRegularReport")
@Slf4j
public class OrrRegularReportController extends JeecgController<OrrRegularReport, IOrrRegularReportService> {

    private final String businessKey = "orrReport";

    @Autowired
    private IOrrRegularReportService orrRegularReportService;

    @Autowired
    private ICommonProcessService processService;

    /**
     * 分页列表查询
     *
     * @param orrRegularReportVo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "操作风险报告-常规报告管理-分页列表查询")
    @Operation(summary = "操作风险报告-常规报告管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<OrrRegularReport>> queryPageList(OrrRegularReportVo orrRegularReportVo,
                                                         @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                         HttpServletRequest req) {

        OrrRegularReport orrRegularReport = new OrrRegularReport();
        BeanUtils.copyProperties(orrRegularReportVo, orrRegularReport);

        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("reportStatus", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<OrrRegularReport> queryWrapper = QueryGenerator.initQueryWrapper(orrRegularReport, req.getParameterMap(), customeRuleMap);

        // 当前日期
        Date today = new Date();

        // 加入是否逾期的条件（1-是 0-否）
        String isOverdue = orrRegularReportVo.getIsOverdue();
		if ("1".equals(isOverdue)) {
			// 逾期条件：提交日期 > 截止日期 OR (截止日期 < 当前日期 AND 提交日期为空)
			queryWrapper.apply(
					"(report_submit_date > report_deadline_date OR " +
							"(report_deadline_date < {0} AND report_submit_date IS NULL))",
					today
			);
		} else if ("0".equals(isOverdue)) {
			// 非逾期条件：提交日期 <= 截止日期 OR (提交日期为空 AND 截止日期 >= 当前日期)
			queryWrapper.apply(
					"(report_submit_date <= report_deadline_date OR " +
							"(report_submit_date IS NULL AND report_deadline_date >= {0}))",
					today
			);
		}

        Page<OrrRegularReport> page = new Page<OrrRegularReport>(pageNo, pageSize);
        IPage<OrrRegularReport> pageList = orrRegularReportService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param orrRegularReport
     * @return
     */
    @AutoLog(value = "操作风险报告-常规报告管理-添加")
    @Operation(summary = "操作风险报告-常规报告管理-添加")
    @RequiresPermissions("orr.report.regular:orr_regular_report:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody OrrRegularReport orrRegularReport) {
        orrRegularReportService.save(orrRegularReport);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param orrRegularReport
     * @return
     */
    @AutoLog(value = "操作风险报告-常规报告管理-编辑")
    @Operation(summary = "操作风险报告-常规报告管理-编辑")
    @RequiresPermissions("orr.report.regular:orr_regular_report:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody OrrRegularReport orrRegularReport) {
        orrRegularReportService.updateById(orrRegularReport);
        return Result.OK("保存成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "操作风险报告-常规报告管理-通过id删除")
    @Operation(summary = "操作风险报告-常规报告管理-通过id删除")
    @RequiresPermissions("orr.report.regular:orr_regular_report:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        orrRegularReportService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "操作风险报告-常规报告管理-批量删除")
    @Operation(summary = "操作风险报告-常规报告管理-批量删除")
    @RequiresPermissions("orr.report.regular:orr_regular_report:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.orrRegularReportService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "操作风险报告-常规报告管理-通过id查询")
    @Operation(summary = "操作风险报告-常规报告管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<OrrRegularReport> queryById(@RequestParam(name = "id", required = true) String id) {
        OrrRegularReport orrRegularReport = orrRegularReportService.getById(id);
        if (orrRegularReport == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(orrRegularReport);
    }

    /**
     * 通过ids批量查询查询
     *
     * @param ids
     * @return
     */
    //@AutoLog(value = "操作风险报告-常规报告管理-通过ids批量查询查询")
    @Operation(summary = "操作风险报告-常规报告管理-通过ids批量查询查询")
    @PostMapping(value = "/batchQueryById")
    public Result<List<OrrRegularReport>> batchQueryById(@RequestParam(name = "ids", required = true) List<String> ids) {
        return orrRegularReportService.batchQueryById(ids);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param orrRegularReport
     */
    @RequiresPermissions("orr.report.regular:orr_regular_report:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OrrRegularReport orrRegularReport) {
        return super.exportXls(request, orrRegularReport, OrrRegularReport.class, "操作风险报告-常规报告管理");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("orr.report.regular:orr_regular_report:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, OrrRegularReport.class);
    }

    /**
     * 批量提交
     *
     * @param ids
     * @return
     */
    @Operation(summary = "操作风险报告-常规报告管理-批量提交")
//	 @RequiresPermissions("orr.report.regular:orr_regular_report:batchSubmit")
    @RequestMapping(value = "/batchSubmit", method = RequestMethod.POST)
    public Result<String> batchSubmit(@RequestParam(name = "ids", required = true) List<String> ids) {
        return orrRegularReportService.batchSubmit(ids);
    }

    /**
     * 批量撤销
     *
     * @param ids
     * @return
     */
    @Operation(summary = "操作风险报告-常规报告管理-批量撤销")
//	 @RequiresPermissions("orr.report.regular:orr_regular_report:batchRevoke")
    @RequestMapping(value = "/batchRevoke", method = RequestMethod.POST)
    public Result<String> batchRevoke(@RequestParam(name = "ids", required = true) List<String> ids) {
        return orrRegularReportService.batchRevoke(ids);
    }

    /**
     * 批量通过
     *
     * @param ids
     * @return
     */
    @Operation(summary = "操作风险报告-常规报告管理-批量通过")
//	 @RequiresPermissions("orr.report.regular:orr_regular_report:passBatch")
    @RequestMapping(value = "/passBatch", method = RequestMethod.POST)
    public Result<String> passBatch(@RequestParam(name = "ids", required = true) List<String> ids) {
        return orrRegularReportService.passBatch(ids);
    }

    /**
     * 批量退回
     *
     * @param ids
     * @return
     */
    @Operation(summary = "操作风险报告-常规报告管理-批量退回")
//	 @RequiresPermissions("orr.report.regular:orr_regular_report:returnBatch")
    @RequestMapping(value = "/returnBatch", method = RequestMethod.POST)
    public Result<String> returnBatch(@RequestParam(name = "ids", required = true) List<String> ids) {
        return orrRegularReportService.returnBatch(ids);
    }

    /**
     * 重置模板
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "操作风险报告-常规报告管理-重置模板")
    @Operation(summary = "操作风险报告-常规报告管理-重置模板")
    @GetMapping(value = "/resetReport")
    public Result<OrrRegularReport> resetReport(@RequestParam(name = "id", required = true) String id) {
        return orrRegularReportService.resetReport(id);
    }

    /**
     * 数据导入
     *
     * @param orrRegularReport
     * @return
     */
    //@AutoLog(value = "操作风险报告-常规报告管理-数据导入")
    @Operation(summary = "操作风险报告-常规报告管理-数据导入")
    @PostMapping(value = "/dataImport")
    public Result<List<HashMap<String, Object>>> dataImport(@RequestBody OrrRegularReport orrRegularReport) {
        return orrRegularReportService.dataImport(orrRegularReport);
    }

    /**
     * 处理过程
     *
     * @param id 数据录入对象id
     * @return 处理过程列表
     */
    @AutoLog(value = "操作风险报告-常规报告管理-处理过程")
    @Operation(summary = "操作风险报告-常规报告管理-处理过程")
    @GetMapping(value = "/process")
//    @RequiresPermissions("orr.report.regular:orr_regular_report:process")
    public Result<IPage<CommonProcess>> process(@RequestParam(name = "id", required = true) String id) {
        // 借用分页的字典翻译
        IPage<CommonProcess> page = new Page<>();
        page.setRecords(processService.getProcess(businessKey, id));
        return Result.ok(page);
    }

}
