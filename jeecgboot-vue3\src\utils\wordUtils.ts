// src/utils/wordUtils.ts
// import mammoth from 'mammoth';
import { renderAsync } from 'docx-preview';
/**
 * 导出HTML内容为Word文档
 * @param reportName 报告名称
 * @param content HTML内容
 */
export const exportToWord = (reportName: string, content: string) => {
  try {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;

    // 处理图片转换
    const images = tempDiv.querySelectorAll('img');
    images.forEach(img => {
      if (!img.src.startsWith('data:image')) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (ctx) {
          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);
          img.src = canvas.toDataURL('image/png');
        }
      }
    });

    // 创建完整的HTML文档结构
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${reportName}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; }
          h1, h2, h3, h4, h5, h6 { color: #1a3353; }
          table { border-collapse: collapse; width: 100%; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
          img { max-width: 100%; height: auto; }
          blockquote { border-left: 4px solid #ddd; padding-left: 15px; margin-left: 0; }
        </style>
      </head>
      <body>
        ${tempDiv.innerHTML}
      </body>
      </html>
    `;

    // 创建并下载Word文档
    const blob = new Blob([htmlContent], { type: 'application/msword' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${reportName.replace(/[^\w\u4e00-\u9fa5]/g, '_')}.doc`;
    document.body.appendChild(link);
    link.click();

    // 清理
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    }, 100);

  } catch (error) {
    console.error('导出Word文档失败:', error);
  }
};

/**
 * 将Word文档转换为HTML
 * @param file Word文件对象
 * @returns Promise解析为HTML字符串
 */
// export const convertWordToHtml = (file: File): Promise<string> => {
//   return new Promise((resolve, reject) => {
//     if (!file) {
//       reject(new Error("未提供上传文件"));
//       return;
//     }

//     // 检查文件格式
//     const fileExtension = file.name.split('.').pop()?.toLowerCase();
//     if (fileExtension !== 'docx') {
//       reject(new Error("不支持的文件格式"));
//       return;
//     }

//     const reader = new FileReader();

//     reader.onload = (event) => {
//       try {
//         const arrayBuffer = event.target?.result as ArrayBuffer;

//         mammoth.convertToHtml({ arrayBuffer })
//           .then((result) => resolve(result.value))
//           .catch((err) => {
//             console.error('文件转换失败:', err);
//             reject(new Error(`文件转换失败: ${err.message}`));
//           });
//       } catch (error) {
//         console.error('文件处理失败:', error);
//         reject(new Error(`文件处理失败: ${error instanceof Error ? error.message : String(error)}`));
//       }
//     };

//     reader.onerror = (error) => {
//       console.error('文件读取错误:', error);
//       reject(new Error("文件读取失败"));
//     };

//     reader.readAsArrayBuffer(file);
//   });
// };
export class DocxConverter {
  /**
   * 将 DOCX 文件转换为 HTML 字符串
   * @param file - 选择的 .docx 文件
   * @returns 转换后的 HTML 内容
   */
  static async convertToHtml(file: File): Promise<string> {
    // 校验文件格式
    if (!file.name.endsWith('.docx')) {
      throw new Error('仅支持 .docx 格式文件');
    }

    try {
      // 创建临时容器
      const tempContainer = document.createElement('div');
      document.body.appendChild(tempContainer);

      // 读取文件为 ArrayBuffer
      const arrayBuffer = await this.readFileAsArrayBuffer(file);

      // 执行转换（关键：Base64 嵌入图片）
      await renderAsync(
        arrayBuffer, 
        tempContainer, 
        null, 
        { useBase64URL: true } // 图片转 Base64 避免路径问题
      );

      // 提取 HTML 并清理临时容器
      const html = tempContainer.innerHTML;
      document.body.removeChild(tempContainer);

      return html;
    } catch (error) {
      console.error('DOCX 转换失败:', error);
      throw new Error('转换过程发生错误，请重试');
    }
  }

  /**
   * 工具方法：将 File 转为 ArrayBuffer
   */
  private static readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as ArrayBuffer);
      reader.onerror = reject;
      reader.readAsArrayBuffer(file);
    });
  }
}
