<template>
  <div class="p-2">
    
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <!-- <div style="color: #262626; font-family: '黑体', sans-serif; font-weight: bold; font-size: 18px;
            display: flex; justify-content: center; align-items: center; margin-left: 42%;">
           {{title}}
        </div> -->
      </template>
      <!--操作栏-->
      <!-- <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"/>
      </template> -->
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <RegulatoryVersionModal ref="registerModal" @success="handleSuccess"></RegulatoryVersionModal>
  </div>
</template>

<script lang="ts" name="ldc.param.regulatory-regulatoryVersion" setup>
  import { ref, reactive } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, superQuerySchema } from './RegulatoryVersion.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './RegulatoryVersion.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import RegulatoryVersionModal from './components/RegulatoryVersionModal.vue'
  import { useUserStore } from '/@/store/modules/user';
  import JInput from "/@/components/Form/src/jeecg/components/JInput.vue";
  import { message } from 'ant-design-vue';

  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const userStore = useUserStore();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '操作风险损失事件类型（监管分类）',
      api: list,
      columns,
      canResize:false,
      useSearchForm: false,
      showActionColumn: false,
      showHeader: true,
      showTableSetting: false,
      showIndexColumn: true,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      rowSelection:{
        type: "radio"
      },
      beforeFetch: async (params) => {
        return Object.assign(params, queryParam);
      },
      defSort: {
        column: ["firstLevelName", "secondLevelName"],
        order: "asc"
      },
    },
    exportConfig: {
      name: "操作风险损失事件类型（监管分类）（正式表）",
      url: getExportUrl,
      params: queryParam,
    },
	  importConfig: {
	    url: getImportUrl,
	    success: handleSuccess
	  },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] = tableContext;
  const labelCol = reactive({
    xs:24,
    sm:4,
    xl:6,
    xxl:4
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

   


  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }

  // 返回
  const emit = defineEmits(['ok']);
  function choose() {
    
  }

  function submitForm() {
    let selectedLength = rowSelection.selectedRows.length;
    if (selectedLength == 0 ) {
      message.warning("请选择一条数据");
      return;
    }
    let regulatory = rowSelection.selectedRows[0];
    // 将数据返回
    emit('ok', regulatory);
  }
  

  defineExpose({
    choose,
    submitForm
  });


</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust{
      min-width: 100px !important;
    }
    .query-group-split-cust{
      width: 30px;
      display: inline-block;
      text-align: center
    }
    .ant-form-item:not(.ant-form-item-with-help){
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),:deep(.ant-input-number){
      width: 100%;
    }
  }
</style>
