package com.gientech.rule.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.rule.system.entity.RuleSystemConsult;
import com.gientech.rule.system.service.IRuleSystemConsultService;
import com.gientech.rule.system.vo.RuleSystemConsultFeedbackVO;
import com.gientech.rule.system.vo.RuleSystemConsultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 内外规模块-制度咨询表
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-21
 */
@Tag(name = "内外规模块-制度咨询表")
@RestController
@RequestMapping("/rule/system/consult")
@Slf4j
public class RuleSystemConsultController extends JeecgController<RuleSystemConsult, IRuleSystemConsultService> {

    private IRuleSystemConsultService ruleSystemConsultService;

    @Autowired
    public void setRuleSystemConsultService(IRuleSystemConsultService ruleSystemConsultService) {
        this.ruleSystemConsultService = ruleSystemConsultService;
    }

    /**
     * 分页列表查询
     *
     * @param ruleSystemConsult 实体参数
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @param req 请求参数
     * @return 查询结果
     */
    @GetMapping(value = "/list")
    @Operation(summary = "分页列表查询")
    public Result<IPage<RuleSystemConsult>> queryPageList(RuleSystemConsult ruleSystemConsult,
                                                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                          HttpServletRequest req) {
        QueryWrapper<RuleSystemConsult> queryWrapper = QueryGenerator.initQueryWrapper(ruleSystemConsult, req.getParameterMap());
        Page<RuleSystemConsult> page = new Page<RuleSystemConsult>(pageNo, pageSize);
        IPage<RuleSystemConsult> pageList = ruleSystemConsultService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 分页列表查询
     *
     * @param ruleSystemConsultVO 实体参数
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @param req 请求参数
     * @return 查询结果
     */
    @GetMapping(value = "/voList")
    @Operation(summary = "分页列表查询")
    public Result<IPage<RuleSystemConsultVO>> queryVOList(RuleSystemConsultVO ruleSystemConsultVO,
                                                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                          HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：IN
        customeRuleMap.put("systemIssuingBody", QueryRuleEnum.IN);
        customeRuleMap.put("issuingDeptOne", QueryRuleEnum.IN);
        customeRuleMap.put("issuingDeptTwo", QueryRuleEnum.IN);
        QueryWrapper<RuleSystemConsultVO> queryWrapper = QueryGenerator.initQueryWrapper(ruleSystemConsultVO, req.getParameterMap(), customeRuleMap);
        Page<RuleSystemConsultVO> page = new Page<>(pageNo, pageSize);
        IPage<RuleSystemConsultVO> pageList = ruleSystemConsultService.selectVOPage(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 分页列表查询(反馈)
     *
     * @param ruleSystemConsultFeedbackVO 实体参数
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @param req 请求参数
     * @return 查询结果
     */
    @GetMapping(value = "/feedbackList")
    @Operation(summary = "分页列表查询")
    public Result<IPage<RuleSystemConsultFeedbackVO>> queryFeedbackList(RuleSystemConsultFeedbackVO ruleSystemConsultFeedbackVO,
                                                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                                        HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：IN
        customeRuleMap.put("systemIssuingBody", QueryRuleEnum.IN);
        customeRuleMap.put("issuingDeptOne", QueryRuleEnum.IN);
        customeRuleMap.put("issuingDeptTwo", QueryRuleEnum.IN);
        QueryWrapper<RuleSystemConsultFeedbackVO> queryWrapper = QueryGenerator.initQueryWrapper(ruleSystemConsultFeedbackVO, req.getParameterMap(), customeRuleMap);
        Page<RuleSystemConsultFeedbackVO> page = new Page<>(pageNo, pageSize);
        IPage<RuleSystemConsultFeedbackVO> pageList = service.selectFeedbackVOPage(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 根据制度主键查询咨询信息
     *
     * @param systemId 制度主键
     * @return 咨询信息
     */
    @GetMapping(value = "/queryBySystemId")
    @Operation(summary = "根据制度主键查询")
    public Result<IPage<RuleSystemConsult>> queryConsultListBySystemId(@RequestParam(name = "systemId", required = true) String systemId) {
        QueryWrapper<RuleSystemConsult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("system_id", systemId);
        List<RuleSystemConsult> list = service.list(queryWrapper);
        Page<RuleSystemConsult> page = new Page<>();
        page.setRecords(list);
        return Result.OK(page);
    }

    /**
     * 根据制度主键查询咨询信息数量
     *
     * @param systemId 制度主键
     * @return 咨询信息
     */
    @GetMapping(value = "/countBySystemId")
    @Operation(summary = "根据制度主键查询咨询信息数量")
    public Result<Integer> countBySystemId(@RequestParam(name = "systemId", required = true) String systemId) {
        QueryWrapper<RuleSystemConsult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("system_id", systemId);
        long count = service.count(queryWrapper);
        return Result.OK((int) count);
    }

    /**
     * 添加
     *
     * @param ruleSystemConsultVO 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/add")
    @AutoLog(value = "内外规模块-制度咨询表-添加")
    @RequiresPermissions("rule.system:rule_system_consult:add")
    public Result<String> add(@RequestBody RuleSystemConsultVO ruleSystemConsultVO) {
        if (service.saveVO(ruleSystemConsultVO)) {
            return Result.OK("新增成功！");
        } else {
            return Result.error("新增失败！");
        }
    }

    /**
     * 编辑
     *
     * @param ruleSystemConsultVO 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "编辑")
    @AutoLog(value = "内外规模块-制度咨询表-编辑")
    @RequiresPermissions("rule.system:rule_system_consult:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody RuleSystemConsultVO ruleSystemConsultVO) {
        if (service.updateVO(ruleSystemConsultVO)) {
            return Result.OK("保存成功！");
        } else {
            return Result.error("保存失败！");
        }
    }

    /**
     * 反馈
     *
     * @param ruleSystemConsultFeedbackVO 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "反馈")
    @AutoLog(value = "内外规模块-制度咨询表-编辑")
    @RequiresPermissions("rule.system:rule_system_consult:feedback")
    @RequestMapping(value = "/feedback", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> feedback(@RequestBody RuleSystemConsultFeedbackVO ruleSystemConsultFeedbackVO) {
        if (service.feedback(ruleSystemConsultFeedbackVO)) {
            return Result.OK("保存成功!");
        } else {
            return Result.error("保存失败!");
        }
    }

    /**
     * 通过id删除
     *
     * @param id 实体对象主键
     * @return 是否成功
     */
    @Operation(summary = "通过id删除")
    @DeleteMapping(value = "/delete")
    @AutoLog(value = "内外规模块-制度咨询表-通过id删除")
    @RequiresPermissions("rule.system:rule_system_consult:delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        ruleSystemConsultService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 多个实体对象主键
     * @return 是否成功
     */
    @Operation(summary = "批量删除")
    @DeleteMapping(value = "/deleteBatch")
    @AutoLog(value = "内外规模块-制度咨询表-批量删除")
    @RequiresPermissions("rule.system:rule_system_consult:deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.ruleSystemConsultService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id 实体对象主键
     * @return 查询结果
     */
    @GetMapping(value = "/queryById")
    @Operation(summary = "通过id查询")
    public Result<RuleSystemConsult> queryById(@RequestParam(name = "id", required = true) String id) {
        RuleSystemConsult ruleSystemConsult = ruleSystemConsultService.getById(id);
        if (ruleSystemConsult == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(ruleSystemConsult);
    }

    /**
     * 导出excel
     *
     * @param request 请求参数
     * @param ruleSystemConsult 实体表单参数
     */
    @RequestMapping(value = "/exportXls")
    @RequiresPermissions("rule.system:rule_system_consult:exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RuleSystemConsult ruleSystemConsult) {
        return super.exportXls(request, ruleSystemConsult, RuleSystemConsult.class, "内外规模块-制度咨询表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request 请求参数
     * @return 是否成功
     */
    @RequiresPermissions("rule.system:rule_system_consult:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RuleSystemConsult.class);
    }

}
