<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="RuleAssessYearForm">
          <a-row>
            <a-col>
              <div class="title-container">
                <div class="title-bar"></div>
                <h1 class="title-text">评估任务基本信息</h1>
              </div>
            </a-col>
            <a-col :span="12">
              <a-form-item label="任务编号" v-bind="validateInfos.taskCode" id="RuleAssessYearForm-taskCode" name="taskCode">
                <a-input v-model:value="formData.taskCode" placeholder="请输入任务编号" disabled allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="任务名称" v-bind="validateInfos.taskName" id="RuleAssessYearForm-taskName" name="taskName">
                <a-input v-model:value="formData.taskName" placeholder="请输入任务名称" disabled allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="任务层级" v-bind="validateInfos.taskLevel" id="RuleAssessYearForm-taskLevel" name="taskLevel">
                <j-dict-select-tag
                  v-model:value="formData.taskLevel"
                  dictCode="rule_assess_year_level"
                  placeholder="请选择任务层级"
                  @change="handleTaskLevelChange"
                  :disabled="disabled"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="评估类型" v-bind="validateInfos.assessType" id="RuleAssessYearForm-assessType" name="assessType">
                <j-dict-select-tag
                  v-model:value="formData.assessType"
                  dictCode="rule_assess_pilot_assess_type"
                  placeholder="请选择评估类型"
                  disabled
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="结束时间" v-bind="validateInfos.endTime" id="RuleAssessYearForm-endTime" name="endTime">
                <a-date-picker
                  placeholder="请选择结束时间"
                  v-model:value="formData.endTime"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="参与评估机构/部门" v-bind="validateInfos.assessOrgCode" id="RuleAssessYearForm-assessOrgCode" name="assessOrgCode">
                <j-select-dept
                  v-model:value="formData.assessOrgCode"
                  :multiple="true"
                  checkStrictly
                  allow-clear
                  :disabled="disabled"
                  rowKey="orgCode"
                  placeholder="请选择参与评估的部门（支持全选）"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="任务状态" v-bind="validateInfos.taskStatus" id="RuleAssessYearForm-taskStatus" name="taskStatus">
                <j-dict-select-tag
                  v-model:value="formData.taskStatus"
                  dictCode="rule_assess_year_task_status"
                  placeholder="请选择任务状态"
                  disabled
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="流程状态" v-bind="validateInfos.processStatus" id="RuleAssessYearForm-processStatus" name="processStatus">
                <j-dict-select-tag
                  v-model:value="formData.processStatus"
                  dictCode="rule_assess_year_sub_process_status"
                  placeholder="请选择流程状态"
                  disabled
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, computed } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate, generateTaskCode } from '../RuleAssessYear.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';

  // 组件属性
  defineOptions({ name: 'RuleAssessYearForm' });

  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({}) },
    formBpm: { type: Boolean, default: true },
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    taskCode: '',
    taskName: '',
    taskLevel: '',
    assessType: '',
    endTime: '',
    assessOrgCode: '',
    taskStatus: '',
    processStatus: '',
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    taskCode: [{ required: true, message: '请输入任务编号!' }],
    taskName: [{ required: true, message: '请输入任务名称!' }],
    taskLevel: [{ required: true, message: '请输入任务层级!' }],
    assessType: [{ required: true, message: '请输入评估类型!' }],
    endTime: [{ required: true, message: '请输入结束时间!' }],
    assessOrgCode: [{ required: true, message: '请输入参与评估部门!' }],
    taskStatus: [{ required: true, message: '请输入任务状态!' }],
    processStatus: [{ required: true, message: '请输入流程状态!' }],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  /**
   * 新增
   */
  async function add() {
    edit({});
    // 自动生成任务编号
    try {
      const taskCodeRes = await generateTaskCode();
      if (taskCodeRes.success) {
        formData.taskCode = taskCodeRes.result;
      }
    } catch (error) {
      console.error('生成任务编号失败', error);
    }

    // 设置默认值
    formData.taskLevel = '1'; // 默认总行
    formData.assessType = '2'; // 默认存量制度年度评估
    formData.taskStatus = '1'; // 默认待发起
    formData.processStatus = '1'; // 默认草稿

    // 生成任务名称
    generateTaskNameByLevel();
  }

  /**
   * 根据任务层级生成任务名称
   */
  function generateTaskNameByLevel() {
    if (formData.taskLevel) {
      const currentYear = new Date().getFullYear();
      if (formData.taskLevel === '1') {
        // 总行
        formData.taskName = `${currentYear}年总行存量制度全面评估任务`;
      } else if (formData.taskLevel === '2') {
        // 分行
        formData.taskName = `${currentYear}年分行存量制度全面评估任务`;
      } else {
        // 兼容其他情况
        formData.taskName = `${currentYear}年存量制度全面评估任务`;
      }
    }
  }

  /**
   * 任务层级变化处理
   */
  function handleTaskLevelChange() {
    generateTaskNameByLevel();
  }

  /**
   * 编辑
   */
  function edit(record: any) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if (record.hasOwnProperty(key)) {
          tmpData[key] = record[key];
        }
      });
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }

  .title-container {
    display: flex;
    align-items: center;
    margin-left: 10px;
    margin-top: 10px;
    margin-bottom: 20px;
  }

  .title-bar {
    width: 5px;
    height: 22px;
    background: #ffc53d;
    border-radius: 3px;
    margin-right: 12px;
    box-shadow: 0 2px 4px rgba(255, 197, 61, 0.3);
  }

  .title-text {
    font-size: 16px;
    font-weight: 600;
    color: #1f1f1f;
    margin: 0;
    letter-spacing: 0.5px;
  }
</style>
