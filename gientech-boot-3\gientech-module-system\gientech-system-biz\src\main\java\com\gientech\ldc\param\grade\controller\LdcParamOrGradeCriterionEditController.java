package com.gientech.ldc.param.grade.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import com.gientech.ldc.param.lossform.entity.LdcParamOrLossFormEdit;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import com.gientech.ldc.param.grade.entity.LdcParamOrGradeCriterionEdit;
import com.gientech.ldc.param.grade.service.ILdcParamOrGradeCriterionEditService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 损失事件严重度划分标准-编辑数据
 * @Author: jeecg-boot
 * @Date: 2025-04-21
 * @Version: V1.0
 */
@Tag(name = "损失事件严重度划分标准-编辑数据")
@RestController
@RequestMapping("/ldc/param/grade/edit")
@Slf4j
public class LdcParamOrGradeCriterionEditController extends JeecgController<LdcParamOrGradeCriterionEdit, ILdcParamOrGradeCriterionEditService> {
    @Autowired
    private ILdcParamOrGradeCriterionEditService ldcParamOrGradeCriterionEditService;

    /**
     * 分页列表查询
     *
     * @param ldcParamOrGradeCriterionEdit 参数
     * @param pageNo                       页码
     * @param pageSize                     分页大小
     * @param req                          请求参数1
     * @return 查询结果
     */
    //@AutoLog(value = "损失事件严重度划分标准-编辑数据-分页列表查询")
    @Operation(summary = "损失事件严重度划分标准-编辑数据-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<LdcParamOrGradeCriterionEdit>> queryPageList(LdcParamOrGradeCriterionEdit ldcParamOrGradeCriterionEdit,
                                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                                     HttpServletRequest req) {
        QueryWrapper<LdcParamOrGradeCriterionEdit> queryWrapper = QueryGenerator.initQueryWrapper(ldcParamOrGradeCriterionEdit, req.getParameterMap());
        // 只查询当前用户的编辑数据
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.eq("create_id", loginUser.getId());
        Page<LdcParamOrGradeCriterionEdit> page = new Page<LdcParamOrGradeCriterionEdit>(pageNo, pageSize);
        IPage<LdcParamOrGradeCriterionEdit> pageList = ldcParamOrGradeCriterionEditService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 初始化/重置编辑数据
     *
     * @return 成功：编辑数据列表   失败：错误信息
     */
    @AutoLog(value = "损失事件严重度划分标准-初始化/重置编辑数据")
    @Operation(summary = "损失事件严重度划分标准-初始化/重置编辑数据")
//    @RequiresPermissions("ldc.param.grade:ldc_param_or_grade_criterion_edit:edit")
    @PostMapping(value = "/resetEditList")
    public Result<List<LdcParamOrGradeCriterionEdit>> reset() {
        // service内实现了 根据操作人员id执行业务
        List<LdcParamOrGradeCriterionEdit> editList = service.resetEditList();
        if (editList.isEmpty()) {
            return Result.error("创建草稿失败！");
        }
        return Result.OK(editList);
    }

    /**
     * 添加
     *
     * @param ldcParamOrGradeCriterionEdit 分级标准
     * @return 是否成功
     */
    @AutoLog(value = "损失事件严重度划分标准-编辑数据-添加")
    @Operation(summary = "损失事件严重度划分标准-编辑数据-添加")
    //@RequiresPermissions("ldc.param.grade:ldc_param_or_grade_criterion_edit:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody LdcParamOrGradeCriterionEdit ldcParamOrGradeCriterionEdit) {
        ldcParamOrGradeCriterionEditService.addEdit(ldcParamOrGradeCriterionEdit);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param ldcParamOrGradeCriterionEdit 分级标准
     * @return 是否成功
     */
    @AutoLog(value = "损失事件严重度划分标准-编辑数据-编辑")
    @Operation(summary = "损失事件严重度划分标准-编辑数据-编辑")
    //@RequiresPermissions("ldc.param.grade:ldc_param_or_grade_criterion_edit:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody LdcParamOrGradeCriterionEdit ldcParamOrGradeCriterionEdit) {
        ldcParamOrGradeCriterionEditService.updateById(ldcParamOrGradeCriterionEdit);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id 要删除的分级标准id
     * @return 是否成功
     */
    @AutoLog(value = "损失事件严重度划分标准-编辑数据-通过id删除")
    @Operation(summary = "损失事件严重度划分标准-编辑数据-通过id删除")
    //@RequiresPermissions("ldc.param.grade:ldc_param_or_grade_criterion_edit:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        ldcParamOrGradeCriterionEditService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "损失事件严重度划分标准-编辑数据-批量删除")
    @Operation(summary = "损失事件严重度划分标准-编辑数据-批量删除")
    //@RequiresPermissions("ldc.param.grade:ldc_param_or_grade_criterion_edit:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.ldcParamOrGradeCriterionEditService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id 查询的id
     * @return 查询结果
     */
    //@AutoLog(value = "损失事件严重度划分标准-编辑数据-通过id查询")
    @Operation(summary = "损失事件严重度划分标准-编辑数据-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<LdcParamOrGradeCriterionEdit> queryById(@RequestParam(name = "id", required = true) String id) {
        LdcParamOrGradeCriterionEdit ldcParamOrGradeCriterionEdit = ldcParamOrGradeCriterionEditService.getById(id);
        if (ldcParamOrGradeCriterionEdit == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(ldcParamOrGradeCriterionEdit);
    }

    /**
     * 导出excel
     * TODO 根据用户id导出
     * @param request                      请求参数
     * @param ldcParamOrGradeCriterionEdit 请求
     */
    //@RequiresPermissions("ldc.param.grade:ldc_param_or_grade_criterion_edit:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, LdcParamOrGradeCriterionEdit ldcParamOrGradeCriterionEdit) {
        return super.exportXls(request, ldcParamOrGradeCriterionEdit, LdcParamOrGradeCriterionEdit.class, "损失事件严重度划分标准-编辑数据");
    }

    /**
     * 通过excel导入数据
     * TODO 更改导入逻辑， 保存版本号后 清空编辑表，再导入新数据（需要判断操作人员id）
     *
     * @param request
     * @param response
     * @return
     */
    //@RequiresPermissions("ldc.param.grade:ldc_param_or_grade_criterion_edit:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, LdcParamOrGradeCriterionEdit.class);
    }

}
