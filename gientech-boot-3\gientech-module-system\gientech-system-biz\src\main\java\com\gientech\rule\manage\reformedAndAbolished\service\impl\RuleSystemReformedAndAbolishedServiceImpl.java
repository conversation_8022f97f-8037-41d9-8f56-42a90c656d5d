package com.gientech.rule.manage.reformedAndAbolished.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gientech.rule.manage.reformedAndAbolished.entity.*;
import com.gientech.rule.manage.reformedAndAbolished.mapper.*;
import com.gientech.rule.manage.reformedAndAbolished.service.IRuleSystemReformedAndAbolishedService;
import com.gientech.rule.manage.utils.GenerateTaskNum;
import com.gientech.rule.manage.utils.GetVersion;
import com.gientech.rule.manage.utils.SaveFileHistory;
import com.gientech.rule.regulations.deptSystemPlan.entity.RuleDeptSystemManagement;
import com.gientech.rule.regulations.deptSystemPlan.entity.RuleDeptSystemManagementDetail;
import com.gientech.rule.regulations.deptSystemPlan.mapper.RuleDeptSystemManagementDetailMapper;
import com.gientech.rule.regulations.deptSystemPlan.mapper.RuleDeptSystemManagementMapper;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * @Description: 内外规-制度立改废任务管理
 * @Author: jeecg-boot
 * @Date:   2025-07-11
 * @Version: V1.0
 */
@Service
public class RuleSystemReformedAndAbolishedServiceImpl extends ServiceImpl<RuleSystemReformedAndAbolishedMapper, RuleSystemReformedAndAbolished> implements IRuleSystemReformedAndAbolishedService {
    @Autowired
    private RuleSystemReformedAndAbolishedMapper mapper;
    @Autowired
    private RuleSystemReformedAndAbolishedAmendmentMapper amendmentMapper;
    @Autowired
    private RuleSystemReformedAndAbolishedAskMapper askMapper;
    @Autowired
    private RuleSystemReformedAndAbolishedFileMapper fileMapper;
    @Autowired
    private FileHistoryMapper fileHistoryMapper;
    @Autowired
    private RuleSystemReformedAndAbolishedExternalMapper externalMapper;
    @Autowired
    private RuleSystemReformedAndAbolishedInteriorMapper interiorMapper;
    @Autowired
    private AbolitionMapper abolitionMapper;
    @Autowired
    private ConnectMapper connectMapper;
    @Autowired
    private GenerateTaskNum generateTaskNum;
    @Autowired
    private SaveFileHistory saveFileHistory;
    @Autowired
    private GetVersion getVersion;
    @Autowired
    private RuleDeptSystemManagementMapper deptMapper;
    @Autowired
    private RuleDeptSystemManagementDetailMapper detailMapper;


    @Override
    public IPage<RuleSystemReformedAndAbolished> queryChildDetail(IPage<RuleSystemReformedAndAbolished> pageList) {
        // 获取分页记录列表
        List<RuleSystemReformedAndAbolished> records = pageList.getRecords();
        if(records.size()>0){
            // 遍历每条记录
            for (RuleSystemReformedAndAbolished record : records) {
                //查询制度修订表
                if("1".equals(record.getTaskType())||"2".equals(record.getTaskType())){
                    //外规
                    QueryWrapper<RuleSystemReformedAndAbolishedExternal> externalWrapper = new QueryWrapper();
                    externalWrapper.eq("relevancy_id", record.getId());
                    List<RuleSystemReformedAndAbolishedExternal> exteriors=externalMapper.selectList(externalWrapper);
                    if(exteriors.size()>0){
                        for(RuleSystemReformedAndAbolishedExternal exterior : exteriors){
                            exterior.setKey(exterior.getNewkey());
                        }
                    }
                    record.setExteriors(exteriors);
                    //外规
                    QueryWrapper<RuleSystemReformedAndAbolishedInterior> interiorWrapper = new QueryWrapper();
                    interiorWrapper.eq("relevancy_id", record.getId());
                    List<RuleSystemReformedAndAbolishedInterior> interiors=interiorMapper.selectList(interiorWrapper);
                    if(exteriors.size()>0){
                        for(RuleSystemReformedAndAbolishedInterior interior : interiors){
                            interior.setKey(interior.getNewkey());
                        }
                    }
                    record.setInteriors(interiors);
                    //制度修订
                    QueryWrapper<RuleSystemReformedAndAbolishedAmendment> amendmentWrapper = new QueryWrapper();
                    amendmentWrapper.eq("relevancy_id", record.getId());
                    record.setAmendments(amendmentMapper.selectList(amendmentWrapper));
                }
                if("4".equals(record.getTaskType())){
                    QueryWrapper<Connect> connectWrapper = new QueryWrapper();
                    connectWrapper.eq("relevancy_id", record.getId());
                    record.setConnects(connectMapper.selectList(connectWrapper));
                }
                //查询征求意见表
                if("1".equals(record.getAskComments())){
                    QueryWrapper<RuleSystemReformedAndAbolishedAsk> askWrapper = new QueryWrapper();
                    askWrapper.eq("relevancy_id", record.getId());
                    record.setMeasures(askMapper.selectList(askWrapper));
                }
            }
        }
        return pageList;
    }

    @Override
    public String newsave(RuleSystemReformedAndAbolished ruleSystemReformedAndAbolished) {
        //生成任务编号
        String num=generateTaskNum.generateTaskNum();
        ruleSystemReformedAndAbolished.setTaskNum(num);
        ruleSystemReformedAndAbolished.setTaskStatus("1");//草稿
        ruleSystemReformedAndAbolished.setProcessStatus("1");//待提交
        ruleSystemReformedAndAbolished.setTimeliness("0");//未生效
        ruleSystemReformedAndAbolished.setVersion(LocalDate.now().getYear()+"年版");
        //判断待废止制度名称和新建制度名称是否相同，相同则版本号变更(如果待废止制度如果是同一个年份版本，则版本号生成规则为：年份+阿拉伯数字顺序编号，例如2025年第二版、2025年第三版)
        if("1".equals(ruleSystemReformedAndAbolished.getAbolitionSystem())){
            ruleSystemReformedAndAbolished.setVersion(getVersion.Version(ruleSystemReformedAndAbolished));
        }
        boolean flag=true;
        //如果id有值则是更新，id没有值则是新增
        if(ruleSystemReformedAndAbolished.getId()!=null&&!"".equals(ruleSystemReformedAndAbolished.getId())){
            //修改时将文件存入历史表中
            saveFileHistory.toSaveHistory(ruleSystemReformedAndAbolished);
            //先删除子表在插入
            newdelete(ruleSystemReformedAndAbolished.getId(),"save");
            flag=this.updateById(ruleSystemReformedAndAbolished);
        }else{
            flag=this.save(ruleSystemReformedAndAbolished);
        }
        //制度新建和制度维护
        if("1".equals(ruleSystemReformedAndAbolished.getTaskType())||"2".equals(ruleSystemReformedAndAbolished.getTaskType())){//维护任务类型
            //外规
            List<RuleSystemReformedAndAbolishedExternal> exterios=ruleSystemReformedAndAbolished.getExteriors();
            if(exterios!=null&&exterios.size()>0){
                for(RuleSystemReformedAndAbolishedExternal external:exterios){
                    external.setRelevancyId(ruleSystemReformedAndAbolished.getId());
                    external.setNewkey(external.getKey());
                    externalMapper.insert(external);
                }
            }
            //内规
            List<RuleSystemReformedAndAbolishedInterior> interiors=ruleSystemReformedAndAbolished.getInteriors();
            if(interiors!=null&&interiors.size()>0){
                for(RuleSystemReformedAndAbolishedInterior interior:interiors){
                    interior.setRelevancyId(ruleSystemReformedAndAbolished.getId());
                    interior.setNewkey(interior.getKey());
                    interiorMapper.insert(interior);
                }
            }
            //制度修订
            List<RuleSystemReformedAndAbolishedAmendment> amendmens=ruleSystemReformedAndAbolished.getAmendments();
            if(amendmens!=null&&amendmens.size()>0){
                for(RuleSystemReformedAndAbolishedAmendment amendmen:amendmens){
                    amendmen.setRelevancyId(ruleSystemReformedAndAbolished.getId());
                    amendmentMapper.insert(amendmen);
                }
            }
            //制度及附件
            List<String> fileIds= Arrays.asList(ruleSystemReformedAndAbolished.getFileIds().split(","));
            if(!fileIds.isEmpty()){
                for(String fileId:fileIds){
                    if(fileId!=null&&!"".equals(fileId)){
                        RuleSystemReformedAndAbolishedFile file=fileMapper.selectById(fileId);
                        if("3".equals(file.getTempFlag())){
                            fileMapper.deleteById(fileId);
                        }else{
                            file.setRelevancyId(ruleSystemReformedAndAbolished.getId());
                            file.setTempFlag("1");//正常
                            fileMapper.updateById(file);
                        }
                    }
                }
            }
        }
        //制度废止
        if("3".equals(ruleSystemReformedAndAbolished.getTaskType())){
            String AbolitionIds=ruleSystemReformedAndAbolished.getAbolitionIds();
            for(String abolitionId:AbolitionIds.split(",")){
                Abolition abolition=new Abolition();
                abolition.setAbolitionId(abolitionId);
                abolition.setRelevancyId(ruleSystemReformedAndAbolished.getId());
                abolition.setDept(ruleSystemReformedAndAbolished.getSystemIssuingBody());
                abolition.setDeptOne(ruleSystemReformedAndAbolished.getIssuingDeptOne());
                abolition.setDeptTwo(ruleSystemReformedAndAbolished.getIssuingDeptTwo());
                abolitionMapper.insert(abolition);
            }
        }
        //制度废止
        if("4".equals(ruleSystemReformedAndAbolished.getTaskType())){
            List<Connect> connects=ruleSystemReformedAndAbolished.getConnects();
            if(connects.size()>0){
                for(Connect connect:connects){
                    connect.setRelevancyId(ruleSystemReformedAndAbolished.getId());
                    connectMapper.insert(connect);
                }
            }
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            //当交接时，查询这个用户创建（因为用户同时只有一位操作，所以所有临时的都是他弄的）的待新增和待删除文件记录，
            QueryWrapper<RuleSystemReformedAndAbolishedFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("create_by",loginUser.getUsername());
            String str="2,3";
            queryWrapper.in("temp_flag",Arrays.asList(str.split(",")));
            List<RuleSystemReformedAndAbolishedFile> files=fileMapper.selectList(queryWrapper);
            if(files!=null&&files.size()>0){
                for(RuleSystemReformedAndAbolishedFile file:files){
                    if("2".equals(file.getTempFlag())){
                        file.setTempFlag("1");
                        fileMapper.updateById(file);
                    }
                    if("3".equals(file.getTempFlag())){
                        fileMapper.deleteById(file.getId());
                    }
                }
            }
        }
        //存入征求意见表
        if("1".equals(ruleSystemReformedAndAbolished.getAskComments())){//是否需要征求意见
            List<RuleSystemReformedAndAbolishedAsk> asks=ruleSystemReformedAndAbolished.getMeasures();
            if(asks!=null&&asks.size()>0){
                for(RuleSystemReformedAndAbolishedAsk ask:asks){
                    ask.setRelevancyId(ruleSystemReformedAndAbolished.getId());
                    askMapper.insert(ask);
                }
            }
        }
        //判断是否关联计划
        if("1".equals(ruleSystemReformedAndAbolished.getIsPlaned())){
            //关联部门制度规划明细-新建
            QueryWrapper<RuleDeptSystemManagement> deptWrapper = new QueryWrapper<>();
            deptWrapper.eq("relevancy_num", ruleSystemReformedAndAbolished.getAssociatedPlanNumber());
            deptWrapper.eq("plan_dept", ruleSystemReformedAndAbolished.getIssuingDeptOne());
            RuleDeptSystemManagement dept=deptMapper.selectOne(deptWrapper);
            RuleDeptSystemManagementDetail detail =new RuleDeptSystemManagementDetail();
            detail.setReationalId(dept.getId());
            detail.setSystemId(ruleSystemReformedAndAbolished.getId());
            detail.setReationalNum(num);
            detail.setTaskNum(generateTaskNum.generatePlanNum(dept.getRelevancyNum()));
            detail.setTaskName(ruleSystemReformedAndAbolished.getSystemName());
            detail.setTaskType(ruleSystemReformedAndAbolished.getTaskType());
            detail.setProcessStatus("1");
            detail.setDeleteStatus("1");
            detail.setTaskStatus("2");
            detailMapper.insert(detail);
        }
        if(flag){
            return "000000";
        }else {
            return "保存失败!";
        }
    }

    @Override
    public void newdelete(String ids,String str) {
        List<String> fileIds= Arrays.asList(ids.split(","));
        if(!fileIds.isEmpty()){
            for(String fileId:fileIds){
                QueryWrapper<RuleSystemReformedAndAbolishedAmendment> amendmentWrapper = new QueryWrapper();
                amendmentWrapper.eq("relevancy_id",fileId);
                amendmentMapper.delete(amendmentWrapper);
                QueryWrapper<RuleSystemReformedAndAbolishedInterior> interiorWrapper = new QueryWrapper();
                interiorWrapper.eq("relevancy_id", fileId);
                interiorMapper.delete(interiorWrapper);
                QueryWrapper<RuleSystemReformedAndAbolishedExternal> externalWrapper = new QueryWrapper();
                externalWrapper.eq("relevancy_id", fileId);
                externalMapper.delete(externalWrapper);
                QueryWrapper<RuleSystemReformedAndAbolishedAsk> askWrapper = new QueryWrapper();
                askWrapper.eq("relevancy_id", fileId);
                askMapper.delete(askWrapper);
                QueryWrapper<Connect> connectWrapper = new QueryWrapper();
                connectWrapper.eq("relevancy_id", fileId);
                connectMapper.delete(connectWrapper);
                if("delete".equals(str)){
                    QueryWrapper<RuleSystemReformedAndAbolishedFile> fileWrapper = new QueryWrapper();
                    fileWrapper.eq("relevancy_id", fileId);
                    fileMapper.delete(fileWrapper);
                }
            }
        }
        if("delete".equals(str)){
            this.removeByIds(fileIds);
        }
    }


}
