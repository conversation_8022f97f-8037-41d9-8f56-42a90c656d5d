package com.gientech.orr.clue;

import com.gientech.orr.clue.entity.OrrReportClue;
import com.gientech.orr.clue.mapper.OrrReportClueMapper;
import com.gientech.orr.report.regular.entity.OrrRegularReport;
import com.gientech.orr.report.regular.mapper.OrrRegularReportMapper;
import com.gientech.workflow.updater.BusinessDataUpdater;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025年07月03日 13:45
 */
@Slf4j
@Service
public class OrrReportClueUpdater implements BusinessDataUpdater {

    private OrrReportClueMapper orrReportClueMapper;

    private final String businessKey = "orrReportClue";

    @Autowired
    public void setOrrReportClueMapper(OrrReportClueMapper orrReportClueMapper) {
        this.orrReportClueMapper = orrReportClueMapper;
    }

    @Override
    public String getBusinessKey() {
        return this.businessKey;
    }

    @Override
    public Class<?> getBusinessDataType() {
        return OrrReportClue.class;
    }

    @Override
    public void beforeProcessTask(Map<String, Object> businessData) {
        Object data = businessData.get(businessKey);
        if (data instanceof OrrReportClue orrReportClue) {
            String id = orrReportClue.getId();
            String clueStatus = orrReportClue.getClueStatus();
            Date clueFeedbackDate = orrReportClue.getClueFeedbackDate();
            orrReportClue = orrReportClueMapper.selectById(id);
            orrReportClue.setClueStatus(clueStatus);
            orrReportClue.setClueFeedbackDate(clueFeedbackDate);
            orrReportClueMapper.updateById(orrReportClue);
        } else {
            log.error("{} is not a OrrReportClue", data.getClass().getName());
        }
    }
}
