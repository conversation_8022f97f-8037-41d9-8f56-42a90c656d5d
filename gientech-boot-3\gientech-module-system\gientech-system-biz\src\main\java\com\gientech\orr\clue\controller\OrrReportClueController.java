package com.gientech.orr.clue.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import com.gientech.common.process.enitity.CommonProcess;
import com.gientech.common.process.service.ICommonProcessService;
import com.gientech.orr.clue.vo.OrrReportClueVo;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import com.gientech.orr.clue.entity.OrrReportClue;
import com.gientech.orr.clue.service.IOrrReportClueService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 操作风险报告-线索表
 * @Author: jeecg-boot
 * @Date: 2025-07-10
 * @Version: V1.0
 */
@Tag(name = "操作风险报告-线索表")
@RestController
@RequestMapping("/orr/clue/orrReportClue")
@Slf4j
public class OrrReportClueController extends JeecgController<OrrReportClue, IOrrReportClueService> {

    public static final String businessKey = "orrReportClue";

    @Autowired
    private IOrrReportClueService orrReportClueService;

    @Autowired
    private ICommonProcessService processService;

    /**
     * 分页列表查询
     *
     * @param orrReportClue
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "操作风险报告-线索表-分页列表查询")
    @Operation(summary = "操作风险报告-线索表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<OrrReportClue>> queryPageList(OrrReportClue orrReportClue,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("clueStatus", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<OrrReportClue> queryWrapper = QueryGenerator.initQueryWrapper(orrReportClue, req.getParameterMap(), customeRuleMap);
        Page<OrrReportClue> page = new Page<OrrReportClue>(pageNo, pageSize);

        // 加入部门权限
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.likeRight("clue_receiving_department", sysUser.getOrgCode());

        IPage<OrrReportClue> pageList = orrReportClueService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加线索
     *
     * @param orrReportClueVo
     * @return
     */
    @AutoLog(value = "操作风险报告-线索表-添加")
    @Operation(summary = "操作风险报告-线索表-添加")
    @RequiresPermissions("orr.clue:orr_report_clue:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody OrrReportClueVo orrReportClueVo) {
        return orrReportClueService.add(orrReportClueVo);
    }

    /**
     * 编辑
     *
     * @param orrReportClue
     * @return
     */
    @AutoLog(value = "操作风险报告-线索表-编辑")
    @Operation(summary = "操作风险报告-线索表-编辑")
    @RequiresPermissions("orr.clue:orr_report_clue:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody OrrReportClue orrReportClue) {
        orrReportClueService.updateById(orrReportClue);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "操作风险报告-线索表-通过id删除")
    @Operation(summary = "操作风险报告-线索表-通过id删除")
    @RequiresPermissions("orr.clue:orr_report_clue:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        orrReportClueService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "操作风险报告-线索表-批量删除")
    @Operation(summary = "操作风险报告-线索表-批量删除")
    @RequiresPermissions("orr.clue:orr_report_clue:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.orrReportClueService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "操作风险报告-线索表-通过id查询")
    @Operation(summary = "操作风险报告-线索表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<OrrReportClue> queryById(@RequestParam(name = "id", required = true) String id) {
        OrrReportClue orrReportClue = orrReportClueService.getById(id);
        if (orrReportClue == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(orrReportClue);
    }

    /**
     * 通过reportId查询
     *
     * @param reportId
     * @return
     */
    //@AutoLog(value = "操作风险报告-线索表-通过reportId查询")
    @Operation(summary = "操作风险报告-线索表-通过reportId查询")
    @GetMapping(value = "/queryByReportId")
    public Result<IPage<OrrReportClue>> queryByReportId(@RequestParam(name = "reportId", required = true) String reportId) {

        QueryWrapper<OrrReportClue> clueQueryWrapper = new QueryWrapper<>();
        clueQueryWrapper.eq("report_id", reportId);
        clueQueryWrapper.eq("clue_status", "3");

        IPage<OrrReportClue> pageList = orrReportClueService.page(new Page<OrrReportClue>(1, 100000), clueQueryWrapper);

        return Result.ok(pageList);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param orrReportClue
     */
    @RequiresPermissions("orr.clue:orr_report_clue:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OrrReportClue orrReportClue) {
        return super.exportXls(request, orrReportClue, OrrReportClue.class, "操作风险报告-线索表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("orr.clue:orr_report_clue:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, OrrReportClue.class);
    }

    /**
     * 批量提交
     *
     * @param ids
     * @return
     */
    @Operation(summary = "操作风险报告-线索表-批量提交")
//	 @RequiresPermissions("orr.clue:orr_report_clue:batchSubmit")
    @RequestMapping(value = "/batchSubmit", method = RequestMethod.POST)
    public Result<String> batchSubmit(@RequestParam(name = "ids", required = true) List<String> ids) {
        return orrReportClueService.batchSubmit(ids);
    }

    /**
     * 批量撤销
     *
     * @param ids
     * @return
     */
    @Operation(summary = "操作风险报告-线索表-批量撤销")
//	 @RequiresPermissions("orr.clue:orr_report_clue:batchRevoke")
    @RequestMapping(value = "/batchRevoke", method = RequestMethod.POST)
    public Result<String> batchRevoke(@RequestParam(name = "ids", required = true) List<String> ids) {
        return orrReportClueService.batchRevoke(ids);
    }

    /**
     * 批量通过
     *
     * @param ids
     * @return
     */
    @Operation(summary = "操作风险报告-线索表-批量通过")
//	 @RequiresPermissions("orr.clue:orr_report_clue:passBatch")
    @RequestMapping(value = "/passBatch", method = RequestMethod.POST)
    public Result<String> passBatch(@RequestParam(name = "ids", required = true) List<String> ids) {
        return orrReportClueService.passBatch(ids);
    }

    /**
     * 批量退回
     *
     * @param ids
     * @return
     */
    @Operation(summary = "操作风险报告-线索表-批量退回")
//	 @RequiresPermissions("orr.clue:orr_report_clue:returnBatch")
    @RequestMapping(value = "/returnBatch", method = RequestMethod.POST)
    public Result<String> returnBatch(@RequestParam(name = "ids", required = true) List<String> ids) {
        return orrReportClueService.returnBatch(ids);
    }

    /**
     * 处理过程
     *
     * @param id 数据录入对象id
     * @return 处理过程列表
     */
    @AutoLog(value = "操作风险报告-线索表-处理过程")
    @Operation(summary = "操作风险报告-线索表-处理过程")
    @GetMapping(value = "/process")
//    @RequiresPermissions("orr.clue:orr_report_clue:process")
    public Result<IPage<CommonProcess>> process(@RequestParam(name = "id", required = true) String id) {
        // 借用分页的字典翻译
        IPage<CommonProcess> page = new Page<>();
        page.setRecords(processService.getProcess(businessKey, id));
        return Result.ok(page);
    }

}
