# 工作流任务查询优化说明

## 概述

本次优化主要针对 `getWorkflowTaskBusinessIdList` 方法进行性能提升，通过以下方式实现：

1. **设计新的DTO类**：创建了查询参数和结果DTO，提供更灵活的查询接口
2. **优化SQL查询**：使用联表查询替代多次单表查询，支持批量委托人查询
3. **性能测试工具**：提供性能对比测试，验证优化效果
4. **向后兼容**：保留原有方法，确保现有代码不受影响

## 新增文件

### DTO类
- `WorkflowTaskQueryDTO.java` - 查询结果DTO
- `WorkflowTaskQueryParam.java` - 查询参数DTO

### 工具类
- `WorkflowTaskPerformanceTest.java` - 性能测试工具

### 测试类
- `WorkflowTaskServiceOptimizationTest.java` - 优化功能测试

## 主要优化点

### 1. 批量查询支持
原来的方法需要循环调用多次数据库查询：
```java
// 原始方式 - 多次数据库访问
for (String assignee : assigneeList) {
    List<String> result = workflowTaskService.getWorkflowTaskBusinessIdList(
        businessKey, assignee, orgCode, isComplete);
    idList.addAll(result);
}
```

优化后使用单次批量查询：
```java
// 优化方式 - 单次数据库访问
WorkflowTaskQueryParam param = new WorkflowTaskQueryParam()
    .setBusinessKey(businessKey)
    .setAssigneeList(assigneeList)
    .setAssigneeOrgCodeList(orgCodeList)
    .setIsComplete(isComplete);
List<String> result = workflowTaskService.getWorkflowTaskBusinessIdListOptimized(param);
```

### 2. SQL优化
使用联表查询替代多次单表查询：
```sql
SELECT DISTINCT wi.business_id
FROM workflow_task wt
INNER JOIN workflow_instance wi ON wt.workflow_instance_id = wi.id
INNER JOIN workflow_define wd ON wi.workflow_define_id = wd.id
WHERE wd.business_key = #{param.businessKey}
  AND wt.assignee IN (#{assigneeList})
  AND wt.assignee_org_code IN (#{orgCodeList})
  AND wt.status = #{taskStatus}
```

### 3. 灵活的查询参数
新的查询参数支持：
- 单个或批量委托人查询
- 单个或批量机构代码查询
- 任务状态过滤
- 实例状态过滤
- 分页支持

## 使用方法

### 1. 批量委托人查询
```java
WorkflowTaskQueryParam param = new WorkflowTaskQueryParam()
    .setBusinessKey("kri_indicator_info")
    .setAssigneeList(Arrays.asList("zhgbm_cfshg", "zh_kri_glg", "zh_kri_shg"))
    .setAssigneeOrgCodeList(Arrays.asList("001", "001", "001"))
    .setIsComplete(false);

List<String> businessIds = workflowTaskService.getWorkflowTaskBusinessIdListOptimized(param);
```

### 2. 查询详细信息
```java
WorkflowTaskQueryParam param = new WorkflowTaskQueryParam()
    .setBusinessKey("kri_indicator_info")
    .setAssignee("zhgbm_cfshg")
    .setAssigneeOrgCode("001")
    .setIsComplete(false);

List<WorkflowTaskQueryDTO> taskDetails = workflowTaskService.getWorkflowTaskWithInstance(param);
```

### 3. 统计任务数量
```java
Long count = workflowTaskService.countWorkflowTask(param);
```

## 性能测试

使用 `WorkflowTaskPerformanceTest` 类进行性能测试：

```java
@Autowired
private WorkflowTaskPerformanceTest performanceTest;

public void testPerformance() {
    String businessKey = "kri_indicator_info";
    List<String> assigneeList = Arrays.asList("zhgbm_cfshg", "zh_kri_glg", "zh_kri_shg");
    String orgCode = "001";
    boolean isComplete = false;
    
    performanceTest.runPerformanceTest(businessKey, assigneeList, orgCode, isComplete);
}
```

## 已重构的控制器

### KriIndicatorInfoController.examineList
已将该方法重构为使用优化后的批量查询：

**重构前**：
```java
if (sysRole.contains("zhgbm_cfshg")) {
    idList.addAll(workflowTaskService.getWorkflowTaskBusinessIdList(
        businessKey, "zhgbm_cfshg", sysUser.getOrgCode(), isComplete));
}
if (sysRole.contains("zh_kri_glg")) {
    idList.addAll(workflowTaskService.getWorkflowTaskBusinessIdList(
        businessKey, "zh_kri_glg", sysUser.getOrgCode().substring(0, 3), isComplete));
}
// ... 更多类似的调用
```

**重构后**：
```java
List<String> assigneeList = new ArrayList<>();
List<String> orgCodeList = new ArrayList<>();

if (sysRole.contains("zhgbm_cfshg")) {
    assigneeList.add("zhgbm_cfshg");
    orgCodeList.add(sysUser.getOrgCode());
}
if (sysRole.contains("zh_kri_glg")) {
    assigneeList.add("zh_kri_glg");
    orgCodeList.add(sysUser.getOrgCode().substring(0, 3));
}
// ... 构建完整列表

WorkflowTaskQueryParam param = new WorkflowTaskQueryParam()
    .setBusinessKey(businessKey)
    .setAssigneeList(assigneeList)
    .setAssigneeOrgCodeList(orgCodeList)
    .setIsComplete(isComplete);
List<String> idList = workflowTaskService.getWorkflowTaskBusinessIdListOptimized(param);
```

## 向后兼容性

原有的 `getWorkflowTaskBusinessIdList(String, String, String, boolean)` 方法保持不变，确保现有代码继续正常工作。

## 建议

1. **新开发的功能**：建议使用新的优化方法
2. **现有功能重构**：可以逐步将现有的多次查询重构为批量查询
3. **性能监控**：在生产环境中监控查询性能，根据实际情况调整
4. **索引优化**：确保数据库表有适当的索引支持新的查询模式

## 注意事项

1. 确保数据库连接池配置足够支持并发查询
2. 监控SQL执行计划，确保查询使用了正确的索引
3. 在大数据量情况下，考虑添加分页支持
4. 定期进行性能测试，确保优化效果持续有效
