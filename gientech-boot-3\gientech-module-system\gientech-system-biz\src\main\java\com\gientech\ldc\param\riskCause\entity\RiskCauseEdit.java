package com.gientech.ldc.param.riskCause.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 操作风险原因分类表（编辑）
 * @Author: jeecg-boot
 * @Date:   2025-04-18
 * @Version: V1.0
 */
@Data
@TableName("ldc_param_or_risk_cause_edit")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="操作风险原因分类表（编辑）")
public class RiskCauseEdit implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**序号*/
//	@Excel(name = "序号", width = 15)
    @Schema(description = "序号")
    private Integer serialNumber;
	/**一级分类*/
	@Excel(name = "一级分类", width = 15)
    @Schema(description = "一级分类")
    private String firstLevelName;
	/**说明*/
	@Excel(name = "说明", width = 15)
    @Schema(description = "说明")
    private String explanation;
	/**表单类型*/
//	@Excel(name = "表单类型", width = 15)
    @Schema(description = "表单类型")
    private Integer type;
	/**版本号*/
	@Excel(name = "版本号", width = 15)
    @Schema(description = "版本号")
    private String version;
	/**创建人id*/
//	@Excel(name = "创建人id", width = 15)
    @Schema(description = "创建人id")
    private String createId;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
}
