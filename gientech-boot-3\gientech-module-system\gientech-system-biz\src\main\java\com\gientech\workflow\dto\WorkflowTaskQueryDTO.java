package com.gientech.workflow.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 工作流任务查询DTO
 * 用于优化工作流任务查询，包含业务信息和任务信息
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Data
@Accessors(chain = true)
@Schema(description = "工作流任务查询DTO")
public class WorkflowTaskQueryDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务键
     */
    @Schema(description = "业务键")
    private String businessKey;

    /**
     * 业务ID
     */
    @Schema(description = "业务ID")
    private String businessId;

    /**
     * 工作流实例ID
     */
    @Schema(description = "工作流实例ID")
    private String workflowInstanceId;

    /**
     * 任务ID
     */
    @Schema(description = "任务ID")
    private String taskId;

    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    private String taskName;

    /**
     * 任务委托人
     */
    @Schema(description = "任务委托人")
    private String assignee;

    /**
     * 任务委托人机构
     */
    @Schema(description = "任务委托人机构")
    private String assigneeOrgCode;

    /**
     * 任务状态
     */
    @Schema(description = "任务状态")
    private String taskStatus;

    /**
     * 实例状态
     */
    @Schema(description = "实例状态")
    private String instanceStatus;

    /**
     * 任务创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "任务创建时间")
    private Date taskCreateTime;

    /**
     * 任务更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "任务更新时间")
    private Date taskUpdateTime;

    /**
     * 实例创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "实例创建时间")
    private Date instanceCreateTime;

    /**
     * 工作流定义ID
     */
    @Schema(description = "工作流定义ID")
    private String workflowDefineId;

    /**
     * 当前节点ID
     */
    @Schema(description = "当前节点ID")
    private String currentNodeId;
}
