package com.gientech.ldc.param.regulatory.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import com.gientech.ldc.param.regulatory.entity.RegulatoryEdit;
import com.gientech.ldc.param.regulatory.service.IRegulatoryEditService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 操作风险损失事件类型（监管分类）（编辑表）
 * @Author: jeecg-boot
 * @Date:   2025-04-18
 * @Version: V1.0
 */
@Tag(name="操作风险损失事件类型（监管分类）（编辑表）")
@RestController
@RequestMapping("/regulatory/regulatoryEdit")
@Slf4j
public class RegulatoryEditController extends JeecgController<RegulatoryEdit, IRegulatoryEditService> {

	@Autowired
	private IRegulatoryEditService regulatoryEditService;
	
	/**
	 * 分页列表查询
	 *
	 * @param regulatoryEdit
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "操作风险损失事件类型（监管分类）（编辑表）-分页列表查询")
	@Operation(summary="操作风险损失事件类型（监管分类）（编辑表）-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<RegulatoryEdit>> queryPageList(RegulatoryEdit regulatoryEdit,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {

		// 正式表的数据复制到编辑表中
		regulatoryEditService.fromVersionToEdit();

        QueryWrapper<RegulatoryEdit> queryWrapper = QueryGenerator.initQueryWrapper(regulatoryEdit, req.getParameterMap());
		Page<RegulatoryEdit> page = new Page<RegulatoryEdit>(pageNo, pageSize);
		IPage<RegulatoryEdit> pageList = regulatoryEditService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param regulatoryEdit
	 * @return
	 */
	@AutoLog(value = "操作风险损失事件类型（监管分类）（编辑表）-添加")
	@Operation(summary="操作风险损失事件类型（监管分类）（编辑表）-添加")
//	@RequiresPermissions("ldc.param.regulatory:ldc_param_or_regulatory_edit:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody RegulatoryEdit regulatoryEdit) {

		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String id = loginUser.getId();

		regulatoryEdit.setCreateId(id);
		regulatoryEditService.save(regulatoryEdit);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param regulatoryEdit
	 * @return
	 */
	@AutoLog(value = "操作风险损失事件类型（监管分类）（编辑表）-编辑")
	@Operation(summary="操作风险损失事件类型（监管分类）（编辑表）-编辑")
//	@RequiresPermissions("ldc.param.regulatory:ldc_param_or_regulatory_edit:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody RegulatoryEdit regulatoryEdit) {

		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String id = loginUser.getId();

		regulatoryEdit.setCreateId(id);
		regulatoryEditService.updateById(regulatoryEdit);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "操作风险损失事件类型（监管分类）（编辑表）-通过id删除")
	@Operation(summary="操作风险损失事件类型（监管分类）（编辑表）-通过id删除")
//	@RequiresPermissions("ldc.param.regulatory:ldc_param_or_regulatory_edit:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		regulatoryEditService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "操作风险损失事件类型（监管分类）（编辑表）-批量删除")
	@Operation(summary="操作风险损失事件类型（监管分类）（编辑表）-批量删除")
//	@RequiresPermissions("ldc.param.regulatory:ldc_param_or_regulatory_edit:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.regulatoryEditService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "操作风险损失事件类型（监管分类）（编辑表）-通过id查询")
	@Operation(summary="操作风险损失事件类型（监管分类）（编辑表）-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<RegulatoryEdit> queryById(@RequestParam(name="id",required=true) String id) {
		RegulatoryEdit regulatoryEdit = regulatoryEditService.getById(id);
		if(regulatoryEdit==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(regulatoryEdit);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param regulatoryEdit
    */
//    @RequiresPermissions("ldc.param.regulatory:ldc_param_or_regulatory_edit:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RegulatoryEdit regulatoryEdit) {
        return super.exportXls(request, regulatoryEdit, RegulatoryEdit.class, "操作风险损失事件类型（监管分类）（编辑表）");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
//    @RequiresPermissions("ldc.param.regulatory:ldc_param_or_regulatory_edit:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RegulatoryEdit.class);
    }

	 /**
	  * 重置编辑表
	  * @return
	  */
	 @RequestMapping(value = "/resetEdit", method = RequestMethod.GET)
	 public Result<String> resetEdit() {
		 regulatoryEditService.resetEdit();
		 return Result.OK("重置成功");
	 }

}
