<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gientech.workflow.mapper.WorkflowTaskMapper">

    <!-- 工作流任务查询结果映射 -->
    <resultMap id="WorkflowTaskQueryDTOMap" type="com.gientech.workflow.dto.WorkflowTaskQueryDTO">
        <result column="business_key" property="businessKey"/>
        <result column="business_id" property="businessId"/>
        <result column="workflow_instance_id" property="workflowInstanceId"/>
        <result column="task_id" property="taskId"/>
        <result column="task_name" property="taskName"/>
        <result column="assignee" property="assignee"/>
        <result column="assignee_org_code" property="assigneeOrgCode"/>
        <result column="task_status" property="taskStatus"/>
        <result column="instance_status" property="instanceStatus"/>
        <result column="task_create_time" property="taskCreateTime"/>
        <result column="task_update_time" property="taskUpdateTime"/>
        <result column="instance_create_time" property="instanceCreateTime"/>
        <result column="workflow_define_id" property="workflowDefineId"/>
        <result column="current_node_id" property="currentNodeId"/>
    </resultMap>

    <!-- 通用查询条件 -->
    <sql id="commonWhereCondition">
        <where>
            <if test="param.businessKey != null and param.businessKey != ''">
                AND wd.business_key = #{param.businessKey}
            </if>
            <if test="param.assignee != null and param.assignee != ''">
                AND wt.assignee = #{param.assignee}
            </if>
            <if test="param.assigneeList != null and param.assigneeList.size() > 0">
                AND wt.assignee IN
                <foreach collection="param.assigneeList" item="assignee" open="(" separator="," close=")">
                    #{assignee}
                </foreach>
            </if>
            <if test="param.assigneeOrgCode != null and param.assigneeOrgCode != ''">
                AND wt.assignee_org_code = #{param.assigneeOrgCode}
            </if>
            <if test="param.assigneeOrgCodeList != null and param.assigneeOrgCodeList.size() > 0">
                AND wt.assignee_org_code IN
                <foreach collection="param.assigneeOrgCodeList" item="orgCode" open="(" separator="," close=")">
                    #{orgCode}
                </foreach>
            </if>
            <if test="param.taskStatusList != null and param.taskStatusList.size() > 0">
                AND wt.status IN
                <foreach collection="param.taskStatusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="param.instanceStatusList != null and param.instanceStatusList.size() > 0">
                AND wi.status IN
                <foreach collection="param.instanceStatusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="param.isComplete != null">
                <choose>
                    <when test="param.isComplete == true">
                        AND wt.status = 'completed'
                    </when>
                    <otherwise>
                        AND wt.status = 'created'
                    </otherwise>
                </choose>
            </if>
            AND wt.del_flag = 0
            AND wi.del_flag = 0
        </where>
    </sql>

    <!-- 查询工作流任务详细信息 -->
    <select id="selectWorkflowTaskWithInstance" resultMap="WorkflowTaskQueryDTOMap">
        SELECT
            wd.business_key,
            wi.business_id,
            wi.id as workflow_instance_id,
            wt.id as task_id,
            wt.name as task_name,
            wt.assignee,
            wt.assignee_org_code,
            wt.status as task_status,
            wi.status as instance_status,
            wt.create_time as task_create_time,
            wt.update_time as task_update_time,
            wi.create_time as instance_create_time,
            wi.workflow_define_id,
            wi.current_node_id
        FROM workflow_task wt
        INNER JOIN workflow_instance wi ON wt.workflow_instance_id = wi.id
        INNER JOIN workflow_define wd ON wi.workflow_define_id = wd.id
        <include refid="commonWhereCondition"/>
        ORDER BY wt.create_time DESC
        <if test="param.pageNo != null and param.pageSize != null">
            LIMIT #{param.pageSize} OFFSET #{param.pageNo}
        </if>
    </select>

    <!-- 查询工作流任务对应的业务ID列表 -->
    <select id="selectWorkflowTaskBusinessIds" resultType="java.lang.String">
        SELECT DISTINCT wi.business_id
        FROM workflow_task wt
        INNER JOIN workflow_instance wi ON wt.workflow_instance_id = wi.id
        INNER JOIN workflow_define wd ON wi.workflow_define_id = wd.id
        <include refid="commonWhereCondition"/>
        ORDER BY wi.business_id
    </select>

    <!-- 统计工作流任务数量 -->
    <select id="countWorkflowTask" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT wt.id)
        FROM workflow_task wt
        INNER JOIN workflow_instance wi ON wt.workflow_instance_id = wi.id
        INNER JOIN workflow_define wd ON wi.workflow_define_id = wd.id
        <include refid="commonWhereCondition"/>
    </select>

</mapper>