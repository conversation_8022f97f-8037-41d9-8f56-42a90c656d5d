package com.gientech.rule.assess.year.updater;

import com.gientech.kri.input.entity.KriIndicatorDataInput;
import com.gientech.rule.assess.year.entity.RuleAssessYearSub;
import com.gientech.rule.assess.year.mapper.RuleAssessYearSubMapper;
import com.gientech.workflow.updater.BusinessDataUpdater;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2025年08月07日 22:35
 */
@Slf4j
@Service
public class RuleAssessYearSubUpdater implements BusinessDataUpdater {

    @Autowired
    private RuleAssessYearSubMapper  ruleAssessYearSubMapper;

    private final String businessKey = "ruleAssessYearSub";

    @Override
    public String getBusinessKey() {
        return this.businessKey;
    }

    @Override
    public Class<?> getBusinessDataType() {
        return RuleAssessYearSub.class;
    }

    @Override
    public void beforeProcessTask(Map<String, Object> businessData) {
        Object data = businessData.get(businessKey);
        if (data instanceof RuleAssessYearSub ruleAssessYearSub) {
            String id = ruleAssessYearSub.getId();
            String processStatus = ruleAssessYearSub.getProcessStatus();

            ruleAssessYearSub = ruleAssessYearSubMapper.selectById(id);
            ruleAssessYearSub.setProcessStatus(processStatus);
            ruleAssessYearSubMapper.updateById(ruleAssessYearSub);
        } else {
            log.error("{} is not a RuleAssessYearSub", data.getClass().getName());
        }
    }
}
