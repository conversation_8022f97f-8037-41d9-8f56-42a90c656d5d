package com.gientech.rule.assess.pilot.updater;

import com.gientech.rule.assess.pilot.entity.RuleAssessPilotSub;
import com.gientech.rule.assess.pilot.mapper.RuleAssessPilotSubMapper;
import com.gientech.workflow.updater.BusinessDataUpdater;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2025年08月08日 08:30
 */
@Slf4j
@Service
public class RuleAssessPilotSubUpdater implements BusinessDataUpdater {

    @Autowired
    private RuleAssessPilotSubMapper ruleAssessPilotSubMapper;

    private final String businessKey = "ruleAssessPilotSub";

    @Override
    public String getBusinessKey() {
        return this.businessKey;
    }

    @Override
    public Class<?> getBusinessDataType() {
        return RuleAssessPilotSub.class;
    }

    @Override
    public void beforeProcessTask(Map<String, Object> businessData) {
        Object data = businessData.get(businessKey);
        if (data instanceof RuleAssessPilotSub ruleAssessPilotSub) {
            String id = ruleAssessPilotSub.getId();
            String processStatus = ruleAssessPilotSub.getProcessStatus();

            ruleAssessPilotSub = ruleAssessPilotSubMapper.selectById(id);
            ruleAssessPilotSub.setProcessStatus(processStatus);
            ruleAssessPilotSubMapper.updateById(ruleAssessPilotSub);
        } else {
            log.error("{} is not a RuleAssessPilotSub", data.getClass().getName());
        }
    }
}
