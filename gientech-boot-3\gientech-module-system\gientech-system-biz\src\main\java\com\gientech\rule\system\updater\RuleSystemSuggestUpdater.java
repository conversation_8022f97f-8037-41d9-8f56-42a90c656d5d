package com.gientech.rule.system.updater;


import com.gientech.rule.system.entity.RuleSystemSuggest;
import com.gientech.rule.system.mapper.RuleSystemSuggestMapper;
import com.gientech.workflow.updater.BusinessDataUpdater;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2025年08月08日 10:42
 */
@Slf4j
@Service
public class RuleSystemSuggestUpdater implements BusinessDataUpdater {

    @Autowired
    private RuleSystemSuggestMapper ruleSystemSuggestMapper;

    private final String businessKey = "ruleSystemSuggest";

    @Override
    public String getBusinessKey() {
        return this.businessKey;
    }

    @Override
    public Class<?> getBusinessDataType() {
        return RuleSystemSuggest.class;
    }

    @Override
    public void beforeProcessTask(Map<String, Object> businessData) {
        Object data = businessData.get(businessKey);
        if (data instanceof RuleSystemSuggest ruleSystemSuggest) {
            String id = ruleSystemSuggest.getId();
            String processStatus = ruleSystemSuggest.getProcessStatus();

            ruleSystemSuggest = ruleSystemSuggestMapper.selectById(id);
            ruleSystemSuggest.setProcessStatus(processStatus);
            ruleSystemSuggestMapper.updateById(ruleSystemSuggest);
        } else {
            log.error("{} is not a RuleSystemSuggest", data.getClass().getName());
        }
    }
}
