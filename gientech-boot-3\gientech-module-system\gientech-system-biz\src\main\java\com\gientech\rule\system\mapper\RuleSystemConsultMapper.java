package com.gientech.rule.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.rule.system.entity.RuleSystemConsult;
import com.gientech.rule.system.vo.RuleSystemConsultFeedbackVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 内外规模块-制度咨询表
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-21
 */
public interface RuleSystemConsultMapper extends BaseMapper<RuleSystemConsult> {

    /**
     * 通过关联主键id查询子表数据
     *
     * @param relateId 关联主键id
     * @return {@link RuleSystemConsult} 子表查询结果
     */
    List<RuleSystemConsult> selectByRelateId(@Param("relateId") String relateId);

    /**
     * 通过关联主键id删除子表数据
     *
     * @param relateId 关联主键id
     * @return 是否成功
     */
    boolean deleteByRelateId(@Param("relateId") String relateId);

    /**
     * 查询制度咨询信息-反馈（分页）
     *
     * @param page 分页
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    Page<RuleSystemConsultFeedbackVO> selectFeedbackVOPage(
            Page<RuleSystemConsultFeedbackVO> page,
            @Param(Constants.WRAPPER) Wrapper<RuleSystemConsultFeedbackVO> queryWrapper);
}
