package com.gientech.rule.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gientech.rule.system.entity.RuleSystemConsult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 制度咨询详情
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2025年07月21日 13:43
 */
@Data
@Accessors(chain = true)
@Schema(description = "制度咨询详情")
public class RuleSystemConsultVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联主键
     */
    @Schema(description = "主键")
    private String id;

    /**
     * 制度主键
     */
    @Schema(description = "制度主键")
    private String systemId;

    /**
     * 制度名称
     */
    @Excel(name = "制度名称", width = 15)
    @Schema(description = "制度名称")
    private String systemName;

    /**
     * 制度发文机构
     */
    @Excel(name = "制度发文机构", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "制度发文机构")
    private String systemIssuingBody;

    /**
     * 制度发文部门（一级）
     */
    @Excel(name = "制度发文部门（一级）", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "制度发文部门（一级）")
    private String issuingDeptOne;

    /**
     * 制度发文部门（二级）
     */
    @Excel(name = "制度发文部门（二级）", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "制度发文部门（二级）")
    private String issuingDeptTwo;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;

    /**
     * 所属部门
     */
    @Schema(description = "所属部门")
    private String sysOrgCode;

    /**
     * 内外规管理-制度咨询列表
     */
    @ExcelCollection(name = "内外规管理-制度咨询列表")
    @Schema(description = "内外规管理-制度咨询列表")
    private List<RuleSystemConsult> consultList;
}
