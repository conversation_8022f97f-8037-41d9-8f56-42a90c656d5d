package com.gientech.rcsa.task.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: RCSA评估任务表
 * @Author: jeecg-boot
 * @Date:   2025-07-17
 * @Version: V1.0
 */
@Data
@TableName("rcsa_task_manage")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="RCSA评估任务表")
public class RcsaTaskManage implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**评估编号*/
	@Excel(name = "评估编号", width = 15)
    @Schema(description = "评估编号")
    private String taskCode;
	/**计划表主键*/
	@Excel(name = "计划表主键", width = 15)
    @Schema(description = "计划表主键")
    private String planId;
	/**方案表主键*/
	@Excel(name = "方案表主键", width = 15)
    @Schema(description = "方案表主键")
    private String schemeId;
	/**关联表主键*/
	@Excel(name = "关联表主键", width = 15)
    @Schema(description = "关联表主键")
    private String relId;
	/**计划标题*/
	@Excel(name = "计划标题", width = 15)
    @Schema(description = "计划标题")
    private String planTitle;
	/**计划类型*/
	@Excel(name = "计划类型", width = 15, dicCode = "rcsa_plan_type")
	@Dict(dicCode = "rcsa_plan_type")
    @Schema(description = "计划类型")
    private String planType;
	/**方案标题*/
	@Excel(name = "方案标题", width = 15)
    @Schema(description = "方案标题")
    private String schemeTitle;
	/**矩阵/流程表主键*/
	@Excel(name = "矩阵/流程表主键", width = 15)
    @Schema(description = "矩阵/流程表主键")
    private String matrixId;
    /**矩阵编号*/
    @Excel(name = "矩阵编号", width = 15)
    @Schema(description = "矩阵编号")
    private String matrixNum;
	/**矩阵名称*/
	@Excel(name = "矩阵名称", width = 15)
    @Schema(description = "矩阵名称")
    private String matrixName;
	/**评估部门*/
	@Excel(name = "评估部门", width = 15)
    @Schema(description = "评估部门")
    @Dict(dictTable = "sys_depart", dicCode = "id", dicText = "depart_name")
    private String evaluateDepart;
	/**评估截止日期*/
	@Excel(name = "评估截止日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "评估截止日期")
    private Date evaluateEndDate;
	/**实际完成日期*/
	@Excel(name = "实际完成日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "实际完成日期")
    private Date finishDate;
	/**任务状态*/
	@Excel(name = "任务状态", width = 15, dicCode = "rcsa_plan_state")
	@Dict(dicCode = "rcsa_plan_state")
    @Schema(description = "任务状态")
    private String taskState;
	/**编号下标*/
	@Excel(name = "编号下标", width = 15)
    @Schema(description = "编号下标")
    private String codeIndex;
	/**流程类型 1-牵头部门 2-总行部门 3-分行机构*/
	@Excel(name = "流程类型 1-牵头部门 2-总行部门 3-分行机构", width = 15)
    @Schema(description = "流程类型 1-牵头部门 2-总行部门 3-分行机构")
    private String processFlag;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;

    /**牵头部门*/
    private String leadDepart;
    private String distributeDepart;
    private Integer sortNum;

    @TableField(exist = false)
    private String pageFlag;
    @TableField(exist = false)
    private String evaluateDepartName;
    @TableField(exist = false)
    private java.lang.String evaluateDepartCode;
}
