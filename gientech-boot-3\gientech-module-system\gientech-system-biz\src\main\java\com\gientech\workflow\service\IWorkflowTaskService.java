package com.gientech.workflow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gientech.workflow.define.WorkflowDefine;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.dto.WorkflowTaskQueryDTO;
import com.gientech.workflow.dto.WorkflowTaskQueryParam;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;

import java.util.List;
import java.util.Map;

/**
 * 工作流任务 服务接口
 *
 * <AUTHOR>
 * @date 2025年05月08日 13:46
 */
public interface IWorkflowTaskService extends IService<WorkflowTask> {
    /**
     * 查询工作流任务
     *
     * @param businessKey     业务key
     * @param assignee        委托人
     * @param assigneeOrgCode 委托人机构
     * @return 工作流任务
     */
    List<WorkflowTask> getWorkflowTask(String businessKey, String assignee, String assigneeOrgCode);

    /**
     * 查询工作流任务
     * @param businessKey 业务key
     * @param assignee 委托人
     * @param assigneeOrgCode 委托人机构
     * @return 工作流任务
     */
    List<String> getWorkflowTaskBusinessIdList(String businessKey, String assignee, String assigneeOrgCode, boolean isComplete);

    /**
     * 获取指定业务数据对应的当前工作流任务
     *
     * @param businessKey 业务key
     * @param businessId  业务主键
     * @return 工作流任务
     */
    WorkflowTask getWorkflowTask(String businessKey, String businessId);

    /**
     * 完成工作流任务
     * 是否可以直接传入Task对象
     * @param taskId    任务Id
     * @param variables 参数
     */
    void completeTask(String taskId, Map<String, Object> variables);

    /**
     * 查询工作流任务详细信息（优化版本）
     *
     * @param param 查询参数
     * @return 工作流任务查询结果列表
     */
    List<WorkflowTaskQueryDTO> getWorkflowTaskWithInstance(WorkflowTaskQueryParam param);

    /**
     * 查询工作流任务对应的业务ID列表（优化版本）
     * 支持单个和批量委托人查询
     *
     * @param param 查询参数
     * @return 业务ID列表
     */
    List<String> getWorkflowTaskBusinessIdListOptimized(WorkflowTaskQueryParam param);

    /**
     * 统计工作流任务数量
     *
     * @param param 查询参数
     * @return 任务数量
     */
    Long countWorkflowTask(WorkflowTaskQueryParam param);
}
