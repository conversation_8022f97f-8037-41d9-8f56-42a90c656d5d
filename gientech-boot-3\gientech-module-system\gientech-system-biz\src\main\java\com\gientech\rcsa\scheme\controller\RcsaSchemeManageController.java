package com.gientech.rcsa.scheme.controller;

import java.util.*;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import com.gientech.rcsa.scheme.entity.RcsaSchemeManage;
import com.gientech.rcsa.scheme.service.IRcsaSchemeManageService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: RCSA评估方案表
 * @Author: jeecg-boot
 * @Date:   2025-07-16
 * @Version: V1.0
 */
@Tag(name="RCSA评估方案表")
@RestController
@RequestMapping("/rcsa/scheme/rcsaSchemeManage")
@Slf4j
public class RcsaSchemeManageController extends JeecgController<RcsaSchemeManage, IRcsaSchemeManageService> {
	@Autowired
	private IRcsaSchemeManageService rcsaSchemeManageService;
	 @Autowired
	 private IWorkflowInstanceService workflowInstanceService;
	 @Autowired
	 private IWorkflowTaskService workflowTaskService;
	 private final String businessKey = "rcsaSchemeProcess";
	
	/**
	 * 分页列表查询
	 *
	 * @param rcsaSchemeManage
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "RCSA评估方案表-分页列表查询")
	@Operation(summary="RCSA评估方案表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<RcsaSchemeManage>> queryPageList(RcsaSchemeManage rcsaSchemeManage,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {

		LoginUser sysUser =(LoginUser) SecurityUtils.getSubject().getPrincipal();

		// 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("planType", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("schemeState", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<RcsaSchemeManage> queryWrapper = QueryGenerator.initQueryWrapper(rcsaSchemeManage, req.getParameterMap(),customeRuleMap);
		Page<RcsaSchemeManage> page = new Page<RcsaSchemeManage>(pageNo, pageSize);

		List<String> sysRole = Arrays.asList(sysUser.getRoleCode().split(","));
		if (sysRole.contains("zh_rcsashg") || sysRole.contains("zhgbm_cfshg") || sysRole.contains("fh_cfshg")
			|| sysRole.contains("zh_rcsaglg") || sysRole.contains("zhgbm_cfglh") || sysRole.contains("fh_cfzg")) {
			queryWrapper.eq("evaluate_depart", sysUser.getOrgId());
		} else {
			return Result.ok(page);
		}

		IPage<RcsaSchemeManage> pageList = rcsaSchemeManageService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param rcsaSchemeManage
	 * @return
	 */
	@AutoLog(value = "RCSA评估方案表-添加")
	@Operation(summary="RCSA评估方案表-添加")
	@RequiresPermissions("rcsa.scheme:rcsa_scheme_manage:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody RcsaSchemeManage rcsaSchemeManage) {
		rcsaSchemeManageService.save(rcsaSchemeManage);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param rcsaSchemeManage
	 * @return
	 */
	@AutoLog(value = "RCSA评估方案表-编辑")
	@Operation(summary="RCSA评估方案表-编辑")
	@RequiresPermissions("rcsa.scheme:rcsa_scheme_manage:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody RcsaSchemeManage rcsaSchemeManage) {
		try {
			return rcsaSchemeManageService.edit(rcsaSchemeManage);
		} catch (Exception e) {
			log.error("方案制定异常：", e);
			return Result.error("方案制定异常!");
		}
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "RCSA评估方案表-通过id删除")
	@Operation(summary="RCSA评估方案表-通过id删除")
	@RequiresPermissions("rcsa.scheme:rcsa_scheme_manage:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		rcsaSchemeManageService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "RCSA评估方案表-批量删除")
	@Operation(summary="RCSA评估方案表-批量删除")
	@RequiresPermissions("rcsa.scheme:rcsa_scheme_manage:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.rcsaSchemeManageService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "RCSA评估方案表-通过id查询")
	@Operation(summary="RCSA评估方案表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<RcsaSchemeManage> queryById(@RequestParam(name="id",required=true) String id) {
		RcsaSchemeManage rcsaSchemeManage = rcsaSchemeManageService.getById(id);
		if(rcsaSchemeManage==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(rcsaSchemeManage);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param rcsaSchemeManage
    */
    @RequiresPermissions("rcsa.scheme:rcsa_scheme_manage:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RcsaSchemeManage rcsaSchemeManage) {
        return super.exportXls(request, rcsaSchemeManage, RcsaSchemeManage.class, "RCSA评估方案表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("rcsa.scheme:rcsa_scheme_manage:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RcsaSchemeManage.class);
    }

	 /**
	  * 提交
	  * @param schemeManage
	  * @return
	  */
	 @AutoLog(value = "RCSA评估方案表-提交")
	 @PostMapping(value = "/submit")
	 @RequiresPermissions("rcsa.scheme:rcsa_scheme_manage:submit")
	 public Result<String> submit(@RequestBody RcsaSchemeManage schemeManage) {
//		 try {
//			 return rcsaSchemeManageService.submit(schemeManage);
//		 } catch (Exception e) {
//			 log.error("RCSA评估方案提交异常！", e);
//			 return Result.error("提交异常！");
//		 }

		 try {
			 return rcsaSchemeManageService.submitByWorkflow(schemeManage);
		 } catch (Exception e) {
			 log.error("RCSA评估方案提交异常！", e);
			 return Result.error("提交异常！");
		 }
	 }

	 /**
	  * 审核通过
	  * @param schemeManage
	  * @return
	  * @throws Exception
	  */
	 @AutoLog(value = "RCSA评估方案表-审核通过")
	 @PostMapping(value = "/auditPass")
	 @RequiresPermissions("rcsa.scheme:rcsa_scheme_manage:pass")
	 public Result<String> auditPass(@RequestBody RcsaSchemeManage schemeManage) {
//		 try {
//			 rcsaSchemeManageService.auditPass(schemeManage);
//			 return Result.ok("审核通过成功！");
//		 } catch (Exception e) {
//			 log.error("RCSA评估方案审核通过异常！", e);
//			 return Result.error("审核通过异常！");
//		 }

		 RcsaSchemeManage manage = rcsaSchemeManageService.getById(schemeManage.getId());
		 WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, manage.getId());
		 if (workflowTask != null) {
			 Map<String, Object> executeVariables = new HashMap<>();
			 executeVariables.put("approval", true);
			 executeVariables.put(businessKey, manage);
			 workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
		 }
		 return Result.ok("审核通过成功!");
	 }

	 /**
	  * 审核退回
	  * @param schemeManage
	  * @return
	  * @throws Exception
	  */
	 @AutoLog(value = "RCSA评估方案表-审核退回")
	 @PostMapping(value = "/auditReject")
	 @RequiresPermissions("rcsa.scheme:rcsa_scheme_manage:reject")
	 public Result<String> auditReject(@RequestBody RcsaSchemeManage schemeManage) {
//		 try {
//			 rcsaSchemeManageService.auditReject(schemeManage);
//			 return Result.ok("审核退回成功！");
//		 } catch (Exception e) {
//			 log.error("RCSA评估方案审核退回异常！", e);
//			 return Result.error("审核退回异常！");
//		 }

		 RcsaSchemeManage manage = rcsaSchemeManageService.getById(schemeManage.getId());
		 WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, manage.getId());
		 if (workflowTask != null) {
			 Map<String, Object> executeVariables = new HashMap<>();
			 executeVariables.put("approval", false);
			 executeVariables.put(businessKey, manage);
			 workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
		 }
		 return Result.ok("审核退回成功!");
	 }

}
