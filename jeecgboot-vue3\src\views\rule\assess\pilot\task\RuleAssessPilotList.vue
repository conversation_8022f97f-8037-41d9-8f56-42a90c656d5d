<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="reload" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item name="taskCode">
              <template #label><span title="任务编号">任务编号</span></template>
              <a-input placeholder="请输入任务编号" v-model:value="queryParam.taskCode" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="taskName">
              <template #label><span title="任务名称">任务名称</span></template>
              <a-input placeholder="请输入任务名称" v-model:value="queryParam.taskName" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="assessType">
              <template #label><span title="评估类型">评估类型</span></template>
              <j-dict-select-tag
                v-model:value="queryParam.assessType"
                dictCode="rule_assess_pilot_assess_type"
                placeholder="请选择评估类型"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="endTime">
              <template #label><span title="结束时间">结束时间</span></template>
              <a-date-picker
                placeholder="请选择结束时间"
                v-model:value="queryParam.endTime"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="assessOrgCode">
              <template #label><span title="参与评估机构">参与评估机构</span></template>
              <j-select-dept v-model:value="queryParam.assessOrgCode" placeholder="请选择参与评估机构" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <span style="float: right; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
              <a-button preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'rule.assess:rule_assess_pilot:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增 </a-button>
        <a-button
          type="default"
          v-auth="'rule.assess:rule_assess_pilot:edit'"
          @click="handleInitiate"
          :disabled="!canInitiate"
          preIcon="ant-design:plus-outlined"
        >
          发起评估任务
        </a-button>
        <a-button
          type="default"
          v-auth="'rule.assess:rule_assess_pilot:append'"
          @click="handleAppend"
          :disabled="!canAppend"
          preIcon="ant-design:plus-outlined"
        >
          评估任务追加
        </a-button>
        <a-button
          type="default"
          v-auth="'rule.assess:rule_assess_pilot:conclusion'"
          @click="handleConclusion"
          :disabled="selectedRowKeys.length !== 1"
          preIcon="ant-design:edit-outlined"
        >
          评估结论录入
        </a-button>
        <!-- 以下按钮未开发功能 -->
        <a-button
          type="default"
          v-auth="'rule.assess:rule_assess_pilot:edit'"
          @click="handleSubmit"
          :disabled="selectedRowKeys.length !== 1"
          preIcon="ant-design:edit-outlined"
        >
          提交评估结论
        </a-button>
        <a-button
          type="default"
          v-auth="'rule.assess:rule_assess_pilot:edit'"
          @click="handleConclusion"
          :disabled="selectedRowKeys.length !== 1"
          preIcon="ant-design:edit-outlined"
        >
          撤回
        </a-button>
        <!-- 以上 -->
        <a-button
          type="default"
          v-auth="'rule.assess:rule_assess_pilot:deleteBatch'"
          @click="batchHandleDelete"
          :disabled="selectedRowKeys.length === 0"
          preIcon="ant-design:edit-outlined"
        >
          删除
        </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <RuleAssessPilotModal @register="registerModal" @success="handleSuccess" />

    <!-- 追加弹窗 -->
    <RuleAssessPilotAppendModal @register="registerAppendModal" @success="handleSuccess" />
    <!-- 详情/评估结论录入弹窗 -->
    <RuleAssessPilotDetailModal @register="registerDetailModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="rule.assess-ruleAssessPilot" setup>
  import { reactive, ref, computed } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useModal } from '/@/components/Modal';
  import RuleAssessPilotModal from './components/RuleAssessPilotModal.vue';

  import RuleAssessPilotAppendModal from './components/RuleAssessPilotAppendModal.vue';
  import RuleAssessPilotDetailModal from './components/RuleAssessPilotDetailModal.vue';
  import { columns } from './RuleAssessPilot.data';
  import { batchDelete, deleteOne, initiate, list, submitRequest } from './RuleAssessPilot.api';
  import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { useMessage } from '@/hooks/web/useMessage';

  const formRef = ref();
  const queryParam = reactive<any>({
    taskCode: '',
    taskName: '',
    assessType: '',
    endTime: '',
    assessOrgCode: '',
  });
  //注册model
  const [registerModal, { openModal }] = useModal();
  const [registerAppendModal, { openModal: openAppendModal }] = useModal();
  const [registerDetailModal, { openModal: openDetailModal }] = useModal();
  const { createMessage } = useMessage();

  // const userStore = useUserStore();
  //注册table数据
  const { tableContext } = useListPage({
    tableProps: {
      title: '内外规库管理-制度试运行评估任务',
      api: list,
      columns,
      canResize: false,
      useSearchForm: false,
      actionColumn: {
        title: '操作',
        width: 160,
        fixed: 'right',
      },
      tableSetting: {
        redo: false,
        size: false,
        setting: false,
      },
      beforeFetch: async (params) => {
        return Object.assign(params, queryParam);
      },
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  // 计算属性：控制发起按钮状态 - 只能对任务状态为待发起('1')的任务进行操作
  const canInitiate = computed(() => {
    if (selectedRowKeys.value.length === 0) return false;
    return selectedRows.value.every((row) => row.taskStatus === '1');
  });

  // 计算属性：控制评估任务追加按钮状态 - 只能对任务状态为待评价('2')的任务进行操作
  const canAppend = computed(() => {
    if (selectedRowKeys.value.length !== 1) return false;
    return selectedRows.value[0]?.taskStatus === '2';
  });

  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openDetailModal(true, {
      record,
      isEditMode: false, // 查看模式
    });
  }

  /**
   * 评估任务追加
   */
  function handleAppend() {
    let rows = selectedRows.value;
    if (rows.length !== 1) {
      createMessage.warning('请选择一条评估任务数据!');
      return;
    }
    // 只能对任务状态为待评价('2')的任务进行追加操作
    if (rows[0].taskStatus !== '2') {
      createMessage.warning('只能对状态为待评价的评估任务进行追加操作!');
      return;
    }
    openAppendModal(true, {
      record: rows[0],
      isUpdate: false,
    });
  }

  /**
   * 删除事件
   */
  function handleDelete(record: Recordable) {
    if (record.taskStatus !== '1') {
      createMessage.warning('只能删除状态为待发起的评估任务！');
      return;
    }
    deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 评估结论录入
   */
  function handleConclusion() {
    let rows = selectedRows.value;
    if (rows.length !== 1) {
      createMessage.warning('请选择一条评估任务数据!');
      return;
    }
    openDetailModal(true, {
      record: rows[0],
      isEditMode: true, // 编辑模式
    });
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    // 检查选中的记录是否都是待发起状态（字典值为1）
    const canDeleteRows = selectedRows.value.filter((row) => row.taskStatus === '1');

    if (canDeleteRows.length === 0) {
      createMessage.warning('只能删除状态为待发起的评估任务！');
      return;
    }

    if (canDeleteRows.length < selectedRows.value.length) {
      createMessage.warning('选中的记录中包含非待发起状态的任务，只能删除待发起状态的任务！');
    }

    const canDeleteIds = canDeleteRows.map((row) => row.id);
    batchDelete({ ids: canDeleteIds }, handleSuccess);
  }

  /**
   * 发起评估任务
   */
  async function handleInitiate() {
    // 检查选中的记录是否都是待发起状态（字典值为1）
    const canInitiateRows = selectedRows.value.filter((row) => row.taskStatus === '1');

    if (canInitiateRows.length === 0) {
      createMessage.warning('只能发起状态为待发起的评估任务！');
      return;
    }

    if (canInitiateRows.length < selectedRows.value.length) {
      createMessage.warning('选中的记录中包含非待发起状态的任务，只能发起待发起状态的任务！');
      return;
    }

    initiate({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 提交
   */
  function handleSubmit() {
    let ids: any[] = [];
    let rows = selectedRows.value;
    let findRow = rows.find((row) => {
      return row.taskStatus !== '2' || row.processStatus !== '1';
    });
    if (findRow) {
      createMessage.warning('请选择完成录入的草稿数据!');
      return;
    }
    ids = selectedRowKeys.value;
    submitRequest({ ids: ids }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record: Recordable) {
    return [
      {
        label: '查看',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '修改',
        onClick: handleEdit.bind(null, record),
        auth: 'rule.assess:rule_assess_pilot:edit',
        disabled: record.taskStatus !== '1', // 只能对任务状态为待发起('1')的任务进行修改
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'rule.assess:rule_assess_pilot:delete',
        disabled: record.taskStatus !== '1',
      },
    ];
  }

  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 8,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 16,
  });

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }
</script>
<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;

    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }

    .query-group-cust {
      min-width: 100px !important;
    }

    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }

    .ant-form-item:not(.ant-form-item-with-help) {
      margin-bottom: 16px;
      height: 32px;
    }

    :deep(.ant-picker),
    :deep(.ant-input-number) {
      width: 100%;
    }
  }
</style>
