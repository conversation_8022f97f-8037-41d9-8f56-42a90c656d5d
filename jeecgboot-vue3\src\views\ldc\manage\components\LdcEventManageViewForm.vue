<template>
	<a-modal v-model:open="visible" :okButtonProps="{ class: { 'jee-hidden': true } }" title="重大操作风险认定标准" cancel-text="关闭">
		<a-table ref="table" 
		  	:columns="columns" 
			:pagination="false"
			:data-source="tableData1" bordered size="middle" 
          	rowKey="id">
        </a-table>
    </a-modal>

	<a-modal style="width:800px" v-model:open="visible2" :okButtonProps="{ class: { 'jee-hidden': true } }" title="关联台账" cancel-text="关闭">
		<a-table ref="table" 
		  	:columns="columnsTZ" 
			:pagination="false"
			:data-source="tableData2" bordered size="middle" 
			:scroll="{ x: 'calc(700px + 50%)'}" 
			rowKey="id">
        </a-table>
    </a-modal>

	<a-modal style="width:800px" v-model:open="visible3" :okButtonProps="{ class: { 'jee-hidden': true } }" title="主要成因分类" cancel-text="关闭">
		<a-table ref="table" 
		  	:columns="columnsCYFL" 
			:pagination="false"
			:data-source="tableData3" bordered size="middle" 
			rowKey="id">
        </a-table>
    </a-modal>

	<a-modal style="width:800px" v-model:open="visible4" :okButtonProps="{ class: { 'jee-hidden': true } }" title="操作风险损失事件分类" cancel-text="关闭">
		<a-table ref="table" 
		  	:columns="columnsSSSJFL" 
			:pagination="false"
			:data-source="tableData4" bordered size="middle" 
			rowKey="id">
        </a-table>
    </a-modal>

	
	<!-- 锚点导航 -->

	<div> 

		<div class="page-container" style="background-color: #fff6f0" >
		<div style="text-align: center; ">
			<span style="font-weight: bold; font-size: 28px;font-family: '黑体', sans-serif;">操作风险损失事件记录表</span>
		</div>
		<div class="scroll-wrapper" style="background-color: #fff6f0; margin-top: 5px;">
			<a-menu mode="horizontal" calss="scroll-menu">
				<a-menu-item v-for="item in anchorItems" :key="item.id" :key-path="item.id">
				<a :href="`#${item.id}`" @click.prevent="scrollToSection(item.id)">{{ item.title }}</a>
				</a-menu-item>
			</a-menu>
		</div>
				

			<div class="form-container" style="background-color: #fff6f0">
				<!-- <a-spin :spinning="confirmLoading"> -->
				<a-form ref="formRef" :model="formData"  class="antd-modal-form" :label-col="{ span: 12 }" :wrapper-col="{ span: 16 }"
					name="LdcEventManageForm">
					 
					<a-card title="基本信息" :id="anchorItems[0].id">
						<a-row>
							<a-col :span="12">
								<a-form-item label="事件编号" v-bind="validateInfos.eventCode"
									id="LdcEventManageForm-eventCode" name="eventCode">
										{{ formData.eventCode }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="事件分类(一级)" v-bind="validateInfos.eventClassifyFirst"
									id="LdcEventManageForm-eventClassifyFirst" name="eventClassifyFirst">
									{{ formData.eventClassifyFirst }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="事件分类(二级)" v-bind="validateInfos.eventClassifySecond"
									id="LdcEventManageForm-eventClassifySecond" name="eventClassifySecond">
                                     {{ formData.eventClassifySecond }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="事件名称" v-bind="validateInfos.eventName"
									id="LdcEventManageForm-eventName" name="eventName" :tooltip="nameStandard">
										{{ formData.eventName }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="事件描述" v-bind="validateInfos.eventDescrption"
									id="LdcEventManageForm-eventDescrption" name="eventDescrption" :tooltip="describeCriteria">
										<!-- {{ formData.eventDescrption }} -->
										<a-tooltip placement="top" :title="formData.eventDescrption">
            <div class="truncate">
              {{ truncatedDescription(formData.eventDescrption) }}
            </div>
          </a-tooltip>
										<!-- <div @click="toggleExpand('eventDescrption')" class="truncate" :class="{ 'expanded': isExpanded.eventDescrption }">
            {{ isExpanded.eventDescrption ? formData.eventDescrption : truncatedDescription(formData.eventDescrption) }}
          </div> -->
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item  label="事件原因描述" v-bind="validateInfos.eventReasonDescription"
									id="LdcEventManageForm-eventReasonDescription" name="eventReasonDescription" :tooltip="eventCauseStandard">
									<a-tooltip placement="top" :title="formData.eventReasonDescription">
            <div class="truncate">
              {{ truncatedDescription(formData.eventReasonDescription) }}
            </div>
          </a-tooltip>
										<!-- {{ formData.eventReasonDescription }} -->
										<!-- <div @click="toggleExpand('eventReasonDescription')" class="truncate" :class="{ 'expanded': isExpanded.eventReasonDescription }">
            {{ isExpanded.eventReasonDescription ? formData.eventReasonDescription : truncatedDescription(formData.eventReasonDescription) }}
          </div> -->
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item  label="第几次填报"
									id="LdcEventManageForm-fillCount" name="fillCount">
										{{ formData.fillCount }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="是否结束" id="LdcEventManageForm-isEnd_dictText"
									name="isEnd_dictText" :tooltip="eventEndstutsCognizance">
										{{ formData.isEnd_dictText }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="状态" id="LdcEventManageForm-status_dictText"
									name="status_dictText" >
									{{ formData.status_dictText }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="是否为损失事件"
									id="LdcEventManageForm-isLossEvent_dictText" name="isLossEvent_dictText">
										{{ formData.isLossEvent_dictText }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="是否废止"
									id="LdcEventManageForm-isNullify_dictText" name="isNullify_dictText">
										{{ formData.isNullify_dictText }}
								</a-form-item>
							</a-col>
							<a-col :span="12" v-if="isZHSHG">
								<a-form-item label="是否经监管认可剔除" v-bind="validateInfos.isRemove_dictText"
									id="LdcEventManageForm-isRemove_dictText" name="isRemove_dictText">
									{{formData.isRemove_dictText}}
								</a-form-item>
							</a-col>
						</a-row>
					</a-card>
					<a-card title="时间信息" :id="anchorItems[1].id" >
						<a-row>
							<a-col :span="12">
								<a-form-item label="初次填报日期" v-bind="validateInfos.firstFillDate"
									id="LdcEventManageForm-firstFillDate" name="firstFillDate">
									
										{{ formData.firstFillDate }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="本次填报日期" v-bind="validateInfos.thisFillDate"
									id="LdcEventManageForm-thisFillDate" name="thisFillDate">
									
										{{ formData.thisFillDate }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="事件初始发生日期" v-bind="validateInfos.initialHappenDate"
									id="LdcEventManageForm-initialHappenDate" name="initialHappenDate" :tooltip="eventFirstDate">
								
										{{ formData.initialHappenDate }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="事件发现日期" v-bind="validateInfos.findDate"
									id="LdcEventManageForm-findDate" name="findDate" :tooltip="eventFoundDate">
									
										{{ formData.findDate }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="首笔损失入账(或确认)日期" v-bind="validateInfos.firstEntryDate"
									id="LdcEventManageForm-firstEntryDate" name="firstEntryDate">
									
										{{ formData.firstEntryDate }}
								</a-form-item>
							</a-col>
						</a-row>
					</a-card>
					<a-card title="机构/人员信息" :id="anchorItems[2].id">
						<a-row>
							<a-col :span="12">
								<a-form-item label="事件填报机构"
									id="LdcEventManageForm-fillOrgan_dictText" name="fillOrgan_dictText">
										{{ formData.fillOrgan_dictText }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="事件填报部门"
									id="LdcEventManageForm-fillDepart_dictText " name="fillDepart_dictText ">
										{{ formData.fillDepart_dictText  }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="事件填报人"
									id="LdcEventManageForm-fillUser_dictText " name="fillUser_dictText ">
									{{ formData.fillUser_dictText  }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="填报人联系方式" v-bind="validateInfos.fillUserContact"
									id="LdcEventManageForm-fillUserContact" name="fillUserContact">
										{{ formData.fillUserContact }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="总行对口管理部门"
									id="LdcEventManageForm-duetManageDepart_dictText " name="duetManageDepart_dictText ">
										{{ formData.duetManageDepart_dictText  }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="发生机构"
									id="LdcEventManageForm-happenOrgan_dictText " name="happenOrgan_dictText ">
										{{ formData.happenOrgan_dictText  }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="发生部门"
									id="LdcEventManageForm-happenDepart_dictText " name="happenDepart_dictText ">
										{{ formData.happenDepart_dictText  }}
								</a-form-item>
							</a-col>
						</a-row>
					</a-card>
					<a-card title="损失和影响信息" :id="anchorItems[3].id">
						<legend>财务影响信息</legend>
						<a-row>
							<a-col :span="12">
								<a-form-item label="最大预估损失金额" v-bind="validateInfos.maxEstimateLossMoney"
									id="LdcEventManageForm-maxEstimateLossMoney" name="maxEstimateLossMoney">
									
										{{ formData.maxEstimateLossMoney }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="总损失金额" v-bind="validateInfos.totalLossMoney"
									id="LdcEventManageForm-totalLossMoney" name="totalLossMoney">
									
										{{ formData.totalLossMoney }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="已确认的回收总金额" v-bind="validateInfos.cfmdRecycleMoney"
									id="LdcEventManageForm-cfmdRecycleMoney" name="cfmdRecycleMoney">
									
										{{ formData.cfmdRecycleMoney }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="净损失金额" v-bind="validateInfos.netLossMoney"
									id="LdcEventManageForm-netLossMoney" name="netLossMoney">
								
										{{ formData.netLossMoney }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="其中：已确认的保险赔付" v-bind="validateInfos.cfmdIndemnity"
									id="LdcEventManageForm-cfmdIndemnity" name="cfmdIndemnity">
									
										{{ formData.cfmdIndemnity }}
								</a-form-item>
							</a-col>
						</a-row>
						<legend>损失回收明细</legend>
						<a-tabs v-model:activeKey="activeKey" animated style="overflow:hidden;">
							<a-tab-pane key="recycleDetails" :forceRender="true">
								<j-vxe-table
								:keep-source="true"
								resizable
								ref="ldcEventRecycleDetailsTableRef"
								:loading="recycleDetailsTable.loading"
								:columns="recycleDetailsColumns"
								:dataSource="recycleDetailsTable.dataSource"
								:height="200"
								:rowNumber="true"
								:rowSelection="false"
								:toolbar="false"
								@removed="handleDeleteDetail"
								@added="handleAddDetail">
								<template #myAction="record">
									
									<a @click="clueRelList(record)">入账线索详情</a>
								</template>
								</j-vxe-table>
							</a-tab-pane>
						</a-tabs>
					</a-card>
					<a-card title="非财务影响信息" :id="anchorItems[4].id">
						<a-row>
							<a-col :span="12">
								<a-form-item label="声誉受损"
									id="LdcEventManageForm-reputationDamage_dictText" name="reputationDamage_dictText">
										{{ formData.reputationDamage_dictText }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="运营中断"
									id="LdcEventManageForm-operationInterruption_dictText" name="operationInterruption_dictText">
										{{ formData.operationInterruption_dictText }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="广大客户服务质量"
									id="LdcEventManageForm-customerServiceQuality_dictText" name="customerServiceQuality_dictText">
										{{ formData.customerServiceQuality_dictText }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="监管行动"
									id="LdcEventManageForm-regulatoryActions_dictText" name="regulatoryActions_dictText">
										{{ formData.regulatoryActions_dictText }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="员工安全"
									id="LdcEventManageForm-employeeSafety_dictText" name="employeeSafety_dictText">
										{{ formData.employeeSafety_dictText }}
								</a-form-item>
							</a-col>
						</a-row>
					</a-card>
					<a-card title="事件分段信息" :id="anchorItems[5].id">
						<a-row>
							<a-col :span="12">
								<a-form-item label="事件严重度分级"
									id="LdcEventManageForm-severityClassification_dictText" name="severityClassification_dictText">
										{{ formData.severityClassification_dictText }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item 
									id="LdcEventManageForm-isMajorRiskEvent" name="isMajorRiskEvent" style="width: 120%;">
									<template #label>
										<a @click="showImportOrec">是否重大操作风险事件</a>
									</template>
										{{ formData.isMajorRiskEvent_dictText }}
								</a-form-item>
							</a-col>
						</a-row>
					</a-card>
					<a-card title="事件属性" :id="anchorItems[6].id">
						<a-row>
							<a-col :span="12">
								<a-form-item
									id="LdcEventManageForm-mainCauseClassification" name="mainCauseClassification">
									<template #label>
										<a @click="showMainCause">主要成因分类</a>
									</template>
										{{ formData.mainCauseClassification }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item v-bind="validateInfos.eventTypeFirst"
									id="LdcEventManageForm-eventTypeFirst" name="eventTypeFirst">
									<template #label>
										<a @click="showEventType">损失事件类型(一级)</a>
									</template>
										{{ formData.eventTypeFirst }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="与信用/市场风险相关"
									id="LdcEventManageForm-creditRiskRelevant_dictText" name="creditRiskRelevant_dictText">
										{{ formData.creditRiskRelevant_dictText }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item v-bind="validateInfos.eventTypeSecond"
									id="LdcEventManageForm-eventTypeSecond" name="eventTypeSecond">
									<template #label>
										<a @click="showEventType">损失事件类型(二级)</a>
									</template>
									{{ formData.eventTypeSecond }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="是否纳入计量"
									id="LdcEventManageForm-isIntoMetering_dictText" name="isIntoMetering_dictText">
										{{ formData.isIntoMetering_dictText }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item v-bind="validateInfos.eventTypeThird"
									id="LdcEventManageForm-eventTypeThird" name="eventTypeThird">
									<template #label>
										<a @click="showEventType">损失事件类型(三级)</a>
									</template>
									{{ formData.eventTypeThird }}
								</a-form-item>
							</a-col>
							
							
						</a-row>
					</a-card>
					<a-card title="关联台账" :id="anchorItems[7].id">
						<a-row>
							<a-col :span="12">
								<a-form-item v-bind="validateInfos.relLedgerType"
									id="LdcEventManageForm-relLedgerType" name="relLedgerType">
									<template #label>
										<a @click="showGLTZ">关联台账类型</a>
									</template>
									<a-tooltip placement="top" :title="formData.relLedgerType_dictText">
										<div class="truncate">
										{{ truncatedDescription(formData.relLedgerType_dictText) }}
										</div>
									</a-tooltip>
										<!-- {{ formData.relLedgerType }} -->
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item v-bind="validateInfos.relLedgerCode"
									id="LdcEventManageForm-relLedgerCode" name="relLedgerCode">
										<template #label>
											<a @click="showGLTZ">关联台账编号</a>
										</template>
										<a-tooltip placement="top" :title="formData.relLedgerCode">
											<div class="truncate">
											{{ truncatedDescription(formData.relLedgerCode) }}
											</div>
										</a-tooltip>
										<!-- {{ formData.relLedgerCode }} -->
								</a-form-item>
							</a-col>
						</a-row>
					</a-card>
					<a-card title="事件关联RCSA" :id="anchorItems[8].id">
						<a-row>
							<a-col :span="12">
								<a-form-item label="发生操作风险事件的主要流程" v-bind="validateInfos.mainProcess"
									id="LdcEventManageForm-mainProcess" name="mainProcess">
									
										{{ formData.mainProcess }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="是否发起触发式评估计划"
									id="LdcEventManageForm-isInitiateRcsa_dictText" name="isInitiateRcsa_dictText">
										{{ formData.isInitiateRcsa_dictText }}
								</a-form-item>
							</a-col>
						</a-row>
						<a-row v-for="item in rcsaRelInfoList">
              				<a-col :span="8">
								<a-form-item label="计划编号" v-bind="validateInfos.rcsaPlanCode"
									id="LdcEventManageForm-rcsaPlanCode" name="rcsaPlanCode">
										{{ item.planCode }}
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item label="计划名称" v-bind="validateInfos.rcsaPlanName"
									id="LdcEventManageForm-rcsaPlanName" name="rcsaPlanName">									
										{{ item.planTitle }}
								</a-form-item>
							</a-col>
							<a-col :span="8">
								<a-form-item label="计划状态" v-bind="validateInfos.rcsaPlanState"
									id="LdcEventManageForm-rcsaPlanState" name="rcsaPlanState">									
										{{ item.stateName }}
								</a-form-item>
							</a-col>
            			</a-row>
					</a-card>
					<a-card title="事件反映出的问题与整改措施" :id="anchorItems[9].id">
						<a-row>
							<a-col :span="12">
								<a-form-item label="是否录入操作风险管理系统-问题收集模块"
									id="LdcEventManageForm-isIntoHistory_dictText" name="isIntoHistory_dictText">
										{{ formData.isIntoHistory_dictText }}
								</a-form-item>
							</a-col>
							<a-col :span="12">
								<a-form-item label="问题编号" v-bind="validateInfos.historyCode"
									id="LdcEventManageForm-historyCode" name="historyCode">
									
										{{ formData.historyCode }}
								</a-form-item>
							</a-col>
						</a-row>
					</a-card>
					<a-card title="事件关联内外规" :id="anchorItems[10].id">
						<a-row>
							<a-col :span="12">
								<a-form-item label="是否违反内外规"
									id="LdcEventManageForm-isViolateRegulations_dictText" name="isViolateRegulations_dictText">
										{{ formData.isViolateRegulations_dictText }}
								</a-form-item>
							</a-col>
						</a-row>
						<a-row >
							<a-col style="color: orange; display: flex; font-weight: 800">
							|
								<a-col style="color: black; padding-left: 5px"> 关联外规 </a-col>
							</a-col>
							<div style="margin: 10px; background: #fff; padding: 10px; border: 1px solid #e8e8e8">
								<div style="margin-bottom: 10px">
								</div>
								<a-table
								:dataSource="ruleExternalLawDetailTable.dataSource"
								:columns="externalLawColumns"
								:rowKey="(record, index) => index"
								bordered
								size="small"
								>
								</a-table>
							</div>
							<!-- 关联内规 - 替换为自定义表格 -->
							<a-col style="color: orange; display: flex; font-weight: 800">
							|
								<a-col style="color: black; padding-left: 5px"> 关联内规 </a-col>
							</a-col>
							<div style="margin: 10px; background: #fff; padding: 10px; border: 1px solid #e8e8e8">
								<a-table
								:dataSource="ruleExternalInternalDetailTable.dataSource"
								:columns="externalInternalColumns"
								:rowKey="(record, index) => index"
								bordered
								size="small"
								>
								</a-table>
							</div>
						</a-row>
					</a-card>

					<a-card title="事件合并" :id="anchorItems[11].id">
						<a-row>
							<a-col :span="12">
								<a-form-item label="合并状态"
									id="LdcEventManageForm-mergeState_dictText" name="mergeState_dictText">
										{{ formData.mergeState_dictText }}
								</a-form-item>
							</a-col>
						</a-row>
					</a-card>
					<a-card title="附件">
						<j-vxe-table
								:keep-source="true"
								resizable
								ref="ldcEventDocumentTableRef"
								:loading="ldcEventDocumentTable.loading"
								:columns="ldcEventDocumentTable.columns"
								:dataSource="ldcEventDocumentTable.dataSource"
								:height="340"
								:rowNumber="true"
								:rowSelection="true"
								:toolbar="false"/>
					</a-card>
				</a-form>
			<!-- </a-spin> -->
			</div>
			
			<div class="button-container" style="background-color: #fff6f0; margin: auto;  text-align: center; margin-top: 20px;">
				<a-button preIcon="ant-design:close-outlined" style="margin-left: 5px;" type="default" @click="cancel"> 关闭</a-button>
			</div>

		</div>
	</div>
<LdcClueModal ref="registerModal" @success="getResult"></LdcClueModal>
<RegulatoryVersionListModal ref="registerModal2" @success="getRegulatory"/>
<LdcClueDetailModal ref="registerModal3"></LdcClueDetailModal>
</template>

<script lang="ts" setup>
import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted, onBeforeMount } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';
import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
import JSelectUser from '/@/components/Form/src/jeecg/components/JSelectUser.vue';
import { getValueType } from '/@/utils';
import { saveOrUpdate, queryAllEventClassifyList, 
		 getMyOrganDepartInfo, getRecycleListByManageId, submitFromForm, 
		 queryById, auditManagePass, auditManageBack, queryLdcEventDocumentListByMainId,
		 queryLedgerList, queryModuleRelInfo, queryRuleRelList } from '../LdcEventManage.api';
import { Form, message } from 'ant-design-vue';
import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
import { useUserStore } from '/@/store/modules/user';
// import {recycleDetailsColumns} from '../LdcEventManage.data';
import { useValidateAntFormAndTable } from '/@/hooks/system/useJvxeMethods';
import {JVxeTypes,JVxeColumn} from '/@/components/jeecg/JVxeTable/types';
import LdcClueModal from './LdcClueModal.vue';
import { columns } from '../../param/importorec/LdcParamOrImportOrecVersion.data';
import { getListNoPage } from '../../param/importorec/LdcParamOrImportOrecVersion.api';
import RegulatoryVersionListModal from '../../param/regulatory/components/RegulatoryVersionListModal.vue';
import { Modal } from 'ant-design-vue';
import LdcClueDetailModal from './LdcClueDetailModal.vue';
import { columnsTZ } from '@/views/ldc/clue/ledger/LedgerClue.data';
import { getMainCauseListNoPage } from '../../param/riskCause/RiskCauseVersion.api';
import { columnsCYFL } from '../../param/riskCause/RiskCauseVersion.data';
import { columnsSSSJFL } from '../../param/regulatory/RegulatoryVersion.data';
import { getEventTypeListNoPage } from '../../param/regulatory/RegulatoryVersion.api';


const rcsaRelInfoList = ref([]);

const ldcEventDocumentColumns = ref<JVxeColumn[]>([
	{
      title: '附件类型',
      key: 'documentType',
      type: JVxeTypes.select,
      options:[],
      dictCode:"ldc_document_type",
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
	  disabled: true,
    },
    {
      title: '附件说明',
      key: 'documentExplain',
      type: JVxeTypes.textarea,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
	  disabled: true,
    },
    {
      title: '附件名称',
      key: 'documentName',
      type: JVxeTypes.file,
      token:true,
      responseName:"message",
      width:"200px",
      placeholder: '请选择文件',
      defaultValue:'',
	  disabled: true,
    },
]);


const activeKeyDocument =  ref('ldcEventDocument');
const ldcEventDocumentTableRef = ref();
const ldcEventDocumentTable = reactive<Record<string, any>>({
    loading: false,
    columns: ldcEventDocumentColumns,
    dataSource: []
});




const registerModal = ref();
const registerModal2 = ref();
const registerModal3 = ref();
const recycleDetailsColumns = ref<JVxeColumn[]>([
  {
    title: '损失事件主键',
    key: 'ldcEventId',
    type: JVxeTypes.input,
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
	visible: false
  },
  {
    title: '类别',
    key: 'category',
    type: JVxeTypes.select,
    options:[],
    dictCode:"ldc_recycle_category",
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
	disabled: true,
  },
  {
    title: '子类',
    key: 'subClass',
    type: JVxeTypes.select,
    options:[],
    dictCode:"ldc_recycle_sub_class",
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
	disabled: true,
  },
  {
    title: '损失形态',
    key: 'lossForm',
    type: JVxeTypes.select,
    options:[],
    dictCode:"ldc_recycle_loss_form",
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
	disabled: true,
  },
  {
    title: '机构',
    key: 'orgId',
    type: JVxeTypes.departSelect,
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
	disabled: true,
  },
  {
    title: '部门',
    key: 'departNo',
    type: JVxeTypes.departSelect,
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
	disabled: true,
  },
  {
    title: '科目代码',
    key: 'subjectCode',
    type: JVxeTypes.select,
    options:[],
    dictCode:"ldc_recycle_category",
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
	disabled: true,
  },
  {
    title: '科目名称',
    key: 'subjectName',
    type: JVxeTypes.select,
    options:[],
    dictCode:"ldc_recycle_category",
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
	disabled: true,
  },
  {
    title: '记账币种',
    key: 'currency',
    type: JVxeTypes.select,
    options:[],
    dictCode:"ldc_recycle_category",
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
	disabled: true,
  },
  {
    title: '记账金额',
    key: 'amount',
    type: JVxeTypes.normal,
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
	disabled: true,
  },
  {
    title: '对人民币汇率',
    key: 'exchangeRate',
    type: JVxeTypes.input,
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
	disabled: true,
  },
  {
    title: '人民币金额（元）',
    key: 'cnyAmount',
    type: JVxeTypes.normal,
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
  },
  {
    title: '入账日期',
    key: 'postingDate',
    type: JVxeTypes.normal,
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
  },
  {
    title: '说明',
    key: 'explanation',
    type: JVxeTypes.textarea,
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
	disabled: true,
  },
  {
    title: '附件',
    key: 'document',
    type: JVxeTypes.file,
	//type: JVxeTypes.normal,
    token:true,
    responseName:"message",
    width:"200px",
    placeholder: '请选择文件',
    defaultValue:'',
	disabled: true,
  },
  {
    title: '操作',
    key: 'action',
    type: JVxeTypes.slot,
    fixed: 'right',
    minWidth: 300,
    align: 'center',
    slotName: 'myAction',
  },
]);

const activeKey = ref('recycleDetails');
const ldcEventRecycleDetailsTableRef = ref();
const recycleDetailsTable = reactive<Record<string, any>>({
	loading: false,
    columns: recycleDetailsColumns,
    dataSource: []
});

// 定义锚点数据
const anchorItems = ref([
  {
    id: 'section1',
    title: '基本信息',
  },
  {
    id: 'section2',
    title: '时间信息',
  },
  {
    id: 'section3',
    title: '机构/人员信息',
  },
  {
    id: 'section4',
    title: '损失和影响信息',
  },
  {
    id: 'section5',
    title: '非财务影响信息',
  },
  {
    id: 'section6',
    title: '事件分段信息',
  },
  {
    id: 'section7',
    title: '事件属性',
  },
  {
    id: 'section8',
    title: '关联台账',
  },
  {
    id: 'section9',
    title: '事件关联RCSA',
  },
  {
    id: 'section10',
    title: '事件反映出的问题与整改措施',
  },
  {
    id: 'section11',
    title: '事件关联内外规',
  },
  {
    id: 'section12',
    title: '事件合并',
  },
]);

// 滚动到指定锚点的方法
const scrollToSection = (id: string) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ block: 'start', behavior: 'smooth'});
  }
};
function getContainer() {
	// 给组件指定渲染的容器，解决锚点不会随页面滚动而移动的问题
	return document.querySelector('.form-wrapper');
}
/** Anchor 锚点形式点击锚点 */
function handleClickAnchor(e, link) {
	// 阻止点击的默认事件修改路由
	e.preventDefault();
	if (link.href) {
		const element = document.querySelector(link.href);
		element && element.scrollIntoView({ block: 'start', behavior: 'smooth', alignToTop: 'false' });
	}
}
const props = defineProps({
	formDisabled: { type: Boolean, default: false },
	formData: { type: Object, default: () => ({}) },
	formBpm: { type: Boolean, default: true },
	operateFlag: { type: String},
	manageInfo: {type: Object},
	manageIds: {type: Array},
});

// let propsData = defineProps(['manageInfo']);

// console.log('propsData.manageInfo', propsData.manageInfo);


const isExpanded = reactive({
	eventDescrption: false,
      // 其他需要控制展开状态的字段...
    });

    const toggleExpand = (field) => {
      isExpanded[field] = !isExpanded[field];
    };

    const truncatedDescription = (text) => {
	  if (text == null || text == '') {
		return text;
	  }
      return text.length > 50 ? `${text.slice(0, 50)}...` : text;
    };


const userStore = useUserStore();
const eventClassifySecondList = ref([]);
const eventClassifyFirstList = ref([]);
const nameStandard = ref("命名规范");
const describeCriteria = ref("事件描述规则");
const eventCauseStandard = ref("事件原因描述规则");
const eventEndstutsCognizance = ref("事件结束状态认定规则");
const eventFirstDate = ref("事件初始发生日期认定规则");
const eventFoundDate = ref("事件发现发生日期认定规则");
const isShowEvent = ref<boolean>(false);
const isZHSHG = ref<boolean>(false);
const visible = ref<boolean>(false);
const tableData1 = ref([]);
const visible2 = ref<boolean>(false);
const tableData2 = ref([]);
const ledgerType_dictTexts = ref([]);
const ledgerNumbers = ref([]);
const relLedgerIds = ref([]); 
const visible3 = ref<boolean>(false);
const tableData3 = ref([]);
const visible4 = ref<boolean>(false);
const tableData4 = ref([]);







const formRef = ref();
const useForm = Form.useForm;
const emit = defineEmits(['register', 'ok', 'manageBack']);
const formData = reactive<Record<string, any>>({
	id: '',
	eventCode: '',
	eventClassifyFirst: '',
	eventClassifySecond: '',
	eventName: '',
	eventDescrption: '',
	eventReasonDescription: '',
	fillCount: 1,
	isEnd: '',
	isEnd_dictText: '',
	isLossEvent: '',
	isLossEvent_dictText: '',
	isNullify_dictText: '',
	isRemove: '0',
	isRemove_dictText: '',
	firstFillDate: '',
	thisFillDate: '',
	initialHappenDate: '',
	findDate: '',
	firstEntryDate: '',
	fillOrgan: '',
	fillOrgan_dictText: '',
	fillDepart: '',
	fillDepart_dictText: '',
	fillUser: '',
	fillUser_dictText: '',
	fillUserContact: '',
	duetManageDepart: '',
	duetManageDepart_dictText: '',
	happenOrgan: '',
	happenOrgan_dictText: '',
	happenDepart: '',
	happenDepart_dictText: '',
	maxEstimateLossMoney: undefined,
	totalLossMoney: undefined,
	cfmdRecycleMoney: undefined,
	netLossMoney: undefined,
	cfmdIndemnity: undefined,
	reputationDamage: '',
	reputationDamage_dictText: '',
	operationInterruption: '',
	operationInterruption_dictText: '',
	customerServiceQuality: '',
	customerServiceQuality_dictText: '',
	regulatoryActions: '',
	regulatoryActions_dictText: '',
	employeeSafety: '',
	employeeSafety_dictText: '',
	severityClassification: '',
	severityClassification_dictText: '',
	isMajorRiskEvent: '',
	isMajorRiskEvent_dictText: '',
	mainCauseClassification: '',
	eventTypeFirst: '',
	eventTypeSecond: '',
	eventTypeThird: '',
	creditRiskRelevant: '',
	creditRiskRelevant_dictText: '',
	isIntoMetering: '0',
	isIntoMetering_dictText: '',
	relLedgerType: '',
	relLedgerType_dictText: '',
	relLedgerCode: '',
	mainProcess: '',
	isInitiateRcsa: '',
	isInitiateRcsa_dictText:'',
	rcsaPlanCode: '',
	rcsaPlanName: '',
	rcsaPlanState: '',
	isIntoHistory: '',
	isIntoHistory_dictText: '',
	historyCode: '',
	isViolateRegulations: '',
	isViolateRegulations_dictText: '',
	regulationId: '',
	mergeState: '1',
	mergeState_dictText: '',
	status_dictText: '',
	previousAuditState: '',
	previousAuditUserName: '',
	previousAuditOpinion: '',
	applyUserName: '',
	applyTime: '',
	reachTime: '',
	applyDepartName: '',
	relLedgerId: '',
});
const { createMessage } = useMessage();
const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 7 } });
const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 14 } });

const confirmLoading = ref<boolean>(false);
//表单验证
const validatorRules = reactive({

});
const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

// 表单禁用
const disabled = computed(() => {
	if (props.formBpm === true) {
		if (props.formData.disabled === false) {
			return false;
		} else {
			return true;
		}
	}
	return props.formDisabled;
});


/**
 * 新增
 */
function add() {
	edit({});
}

/**
 * 编辑
 */
function edit(record) {

	// duetManageDepart: formData.duetManageDepart
    //       ? formData.duetManageDepart.split(",")
    //       : [],
	nextTick(() => {
		resetFields();
		const tmpData = {};
		Object.keys(formData).forEach((key) => {
			if (record.hasOwnProperty(key)) {
				tmpData[key] = record[key]
			}
		})
		//赋值
		Object.assign(formData, tmpData);
	});
}

const {getSubFormAndTableData, transformData} = useValidateAntFormAndTable(activeKey, {
    'ldcEventRecycleDetails': ldcEventRecycleDetailsTableRef,
});

const manageIds = props.manageIds;

/**
 * 审核通过
 */
async function auditPass() {
	if (formData.opinionType != null && formData.opinionType != '') {
		message.warning("当审核通过时，意见类型无需填写");
		return;
	}
	let param = {
		'id': formData.id,
		'auditOpinion': formData.auditOpinion
	}
	const res = await auditManagePass(param);
	if (res.success) {
		message.success("审核通过成功");
		if (manageIds.length > 0) {
			manageIds.splice(0, 1);
			if (manageIds.length == 0) {
				cancel();
			} else {
				getManageInfo();
				scrollToTop();
			}
		} 
	} else {
		message.warning("审核通过异常");
		return;
	}
	// confirmLoading.value = true;
}

async function auditBack() {
	if (formData.opinionType == null || formData.opinionType == '') {
		message.warning("当审核退回时，意见类型必须填写");
		return;
	}
	if (formData.auditOpinion == null || formData.auditOpinion == '') {
		message.warning("当审核退回时，处理意见必须填写");
		return;
	}
	let param = {
		'id': formData.id,
		'auditOpinion': formData.auditOpinion,
		'opinionType': formData.opinionType
	}
	const res = await auditManageBack(param);
	if (res.success) {
		message.success("审核退回成功");
		if (manageIds.length > 0) {
			manageIds.splice(0, 1);
			if (manageIds.length == 0) {
				cancel();
			} else {
				getManageInfo();
				scrollToTop();
			}
		} 
	} else {
		message.warning("审核退回异常");
		return;
	}
}

async function submit() {
	const subData = await getSubFormAndTableData();
	const list = subData.ldcEventRecycleDetailsList;
	try {
		// 触发表单验证
		await validate();
	} catch ({ errorFields }) {
		if (errorFields) {
			const firstField = errorFields[0];
			if (firstField) {
				formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
			}
		}
		return Promise.reject(errorFields);
	}
	confirmLoading.value = true;
	//时间格式化
	let model = formData;
	//循环数据
	for (let data in model) {
		//如果该数据是数组并且是字符串类型
		if (model[data] instanceof Array) {
			let valueType = getValueType(formRef.value.getProps, data);
			//如果是字符串类型的需要变成以逗号分割的字符串
			if (valueType === 'string') {
				model[data] = model[data].join(',');
			}
		}
	}
	model.detailsList = list;

	const res = await submitFromForm(model);
	if (res.success) {
		message.success(res.message);
		emit('manageBack');
	} else {
		message.warn(res.message);
	}
	confirmLoading.value = false;
}

// 重置
function reset() {
	Modal.confirm({
		title: '确认重置',
		content: '是否确认将已填写的内容进行清空？',
		okText: '确认',
		cancelText: '取消',
		onOk() {  // 确认回调
			formRef.value.resetFields();
			getManageInfo();
			// 损失事件置空
			recycleDetailsTable.dataSource = [];
		},
		onCancel() {  // 取消回调
			console.log('取消重置操作');
		}
	});
}

// 取消
function cancel() {
	emit('manageBack');
}


/**
 * 提交数据
 */
async function submitForm() {
	// formData.duetManageDepart =formData.duetManageDepart
    //     ? formData.duetManageDepart.join()
    //     : "";

	try {
		// 触发表单验证
		await validate();
	} catch ({ errorFields }) {
		if (errorFields) {
			const firstField = errorFields[0];
			if (firstField) {
				formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
			}
		}
		return Promise.reject(errorFields);
	}
	confirmLoading.value = true;
	const isUpdate = ref<boolean>(false);
	//时间格式化
	let model = formData;
	if (model.id) {
		isUpdate.value = true;
	}
	//循环数据
	for (let data in model) {
		//如果该数据是数组并且是字符串类型
		if (model[data] instanceof Array) {
			let valueType = getValueType(formRef.value.getProps, data);
			//如果是字符串类型的需要变成以逗号分割的字符串
			if (valueType === 'string') {
				model[data] = model[data].join(',');
			}
		}
	}
	await saveOrUpdate(model, isUpdate.value)
		.then((res) => {
			if (res.success) {
				createMessage.success(res.message);
				emit('ok');
			} else {
				createMessage.warning(res.message);
			}
		})
		.finally(() => {
			confirmLoading.value = false;
		});
}

onBeforeMount(async () => {
	getManageInfo();
});

async function getManageInfo() {
	confirmLoading.value = true;
	// const res = await queryAllEventClassifyList();
	// eventClassifySecondList.value = res;
	// eventClassifyFirstList.value = res;
	
	// 判断用户角色
    // 判断当前用户有没有权限
    if (userStore.getUserInfo.roleList.includes('zh_ldc_shg')) {
		isZHSHG.value = true;
    } else {
		isZHSHG.value = false;
	}

	// 查询2-1.5操作风险原因分类(待定)
	// const result = await queryRiskCaseList();
	// if (result.success) {

	// }

	//const manageInfo = await queryById({id: manageIds[0]});

	

	let record = props.manageInfo;

	// 查询关联的RCSA
    let rcsaParam = {
      'moduleId2': record.id,
      'moduleFlag': '1',
    }
    const rcsaList = await queryModuleRelInfo(rcsaParam);
    rcsaRelInfoList.value = rcsaList.result;

	let ruleParam = {
      'id': record.id
    }
    const ruleResult = await queryRuleRelList(ruleParam);
    ruleExternalInternalDetailTable.dataSource = ruleResult.result.interiorList;// 内规
    ruleExternalLawDetailTable.dataSource = ruleResult.result.externalList;// 外规
	
	nextTick(() => {
		resetFields();
		const tmpData = {};
		Object.keys(formData).forEach((key) => {
			if (record.hasOwnProperty(key)) {
				tmpData[key] = record[key]
			}
		})
		//赋值
		Object.assign(formData, tmpData);
		formData.auditOpinion = '';
		formData.opinionType = '';
		if (rcsaList.success) {
			formData.rcsaPlanCode = rcsaList.result.planCode;
			formData.rcsaPlanName = rcsaList.result.planTitle;
			formData.rcsaPlanState = rcsaList.result.stateName;
		}
	});
	// 根据LDC主键查找损失回收明细
	const res4 = await getRecycleListByManageId(record);
	recycleDetailsTable.dataSource = res4.result;

	// 查询附件
	const res5 = await queryLdcEventDocumentListByMainId(record.id);
	ldcEventDocumentTable.dataSource = res5;

	// 查询关联台账
	let param2 = {
		'eventId': record.id,
		'pageNo': 1,
		'pageSize': 300,
	};
	const res6 = await queryLedgerList(param2);
	tableData2.value = res6.records;
	if (tableData2.value == null || tableData2.value.length == 0) {
		formData.relLedgerType_dictText == '';
		formData.relLedgerCode == '';
		formData.relLedgerId == '';
	} else {
		tableData2.value.forEach((item, index) => {
			if (ledgerType_dictTexts.value.indexOf(item.ledgerType_dictText) > -1) {

			} else {
				ledgerType_dictTexts.value.push(item.ledgerType_dictText);
			}
			ledgerNumbers.value.push(item.ledgerNumber);
			relLedgerIds.value.push(item.id);
    	});
		formData.relLedgerType_dictText = ledgerType_dictTexts.value.join(',');
		formData.relLedgerCode = ledgerNumbers.value.join(',');
		formData.relLedgerId = relLedgerIds.value.join(',');
	}

	confirmLoading.value = false;

	
}

function getSecondInfo(item) {
	formData.eventClassifyFirst = item.eventLevelOne;
	nameStandard.value = "命名规范:" + item.namingConventions + "\n" + "名称示例:" + item.exampleName;
	describeCriteria.value = "描述标准:" + item.describeCriteria + "\n" + "规范性描述示例:" + item.specificationExamples;
	eventCauseStandard.value = "事件原因描述标准:" + item.eventCauseStandard + "\n" + "事件原因描述示例:" + item.eventCauseExample;
	eventEndstutsCognizance.value = item.eventEndstutsCognizance;
	eventFirstDate.value = item.eventFirstDate;
	eventFoundDate.value = item.eventFoundDate;
}

function validateField2(row) {
}

const currentRowIndex = ref(null);
const currentRow = ref({});
async function relEntryClue(record) {
	currentRowIndex.value = record.rowIndex;
	let row = record.row;
	const subData = await getSubFormAndTableData();
	const list = subData.ldcEventRecycleDetailsList;
	const removeIds = ref([]);
	const manualRemoveIds = ref([]);
	// 遍历list, 得到已经关联的数据（排除本身）
	list.forEach((item, index) => {
		if (currentRowIndex.value != index) {
			if (item.relIds != null && item.relIds != '' && item.relIds.length > 0) {
				item.relIds.split(",").forEach((item) => {
					removeIds.value.push(item);
    			});
			}

			if (item.manualRelIds != null && item.manualRelIds != '' && item.manualRelIds.length > 0) {
				item.manualRelIds.split(",").forEach((item) => {
					manualRemoveIds.value.push(item);
    			});
			}
			
		}
    });
	row.firstEntryDateOld = formData.firstEntryDate;
	row.removeIds = removeIds.value.join(",");
	row.manualRemoveIds = manualRemoveIds.value.join(",");

	currentRow.value = row;
	registerModal.value.showClue(row);
}

let firstEntryDateCom = "";
async function getResult(relIds, manualRelIds, detail) {
	if (currentRow != null && currentRow != '') {
		if (relIds != null && relIds.length > 0) {
			currentRow.value.relIds = relIds.join(',');
		}
		if (manualRelIds != null && manualRelIds.length > 0) {
			currentRow.value.manualRelIds = manualRelIds.join(',');
		}
		currentRow.value.orgId = detail.orginSet;
		currentRow.value.departNo = detail.departSet;
		currentRow.value.subjectCode = detail.subjectCodeSet;
		currentRow.value.subjectName = detail.subjectNameSet;
		currentRow.value.amount = detail.amountString;
		currentRow.value.currency = detail.accountingCurrencySet;

		currentRow.value.exchangeRate = detail.exchangeRateSet;
		currentRow.value.cnyAmount = detail.cnyAmount;
		currentRow.value.totalLossMoney = detail.totalLossMoney;
		currentRow.value.cfmdRecycleMoney = detail.cfmdRecycleMoney;
		currentRow.value.cfmdIndemnity = detail.cfmdIndemnity;
		currentRow.value.postingDate = detail.firstEntryDateDetail;
	}
	// 计算总金额
	const subData = await getSubFormAndTableData();
	const list = subData.ldcEventRecycleDetailsList;
	// 定义总损失金额
	let totalLossMoney = 0;
	let cfmdRecycleMoney = 0;
	let cfmdIndemnity = 0;
	
	if (list != null && list.length > 0) {
		isShowEvent.value = true;
		formData.isLossEvent = '1';
	}
	list.forEach((item) => {
		let category = item.category;
		let subClass = item.subClass;
		if (category == '1') {
			if (item.cnyAmount != null) {
				totalLossMoney = totalLossMoney + item.cnyAmount;
			}
			// if (formData.firstEntryDate == null || formData.firstEntryDate == '') {
			// 	firstEntryDateCom = detail.firstEntryDate;
			// } else {
			// 	const date1 = new Date(firstEntryDateCom);
			// 	const date2 = new Date(detail.firstEntryDate);
			// 	if(date1 > date2) {
			// 		firstEntryDateCom = detail.firstEntryDate;
			// 	}
			// }
		} else if (category == '2') {
			if (item.cnyAmount != null) {
				cfmdRecycleMoney = cfmdRecycleMoney + item.cnyAmount;
				if (subClass != null && subClass != '' && subClass == '3') {
					cfmdIndemnity = cfmdIndemnity + item.cnyAmount;
				}
			}
		}
    });
	formData.totalLossMoney = totalLossMoney;
	formData.cfmdRecycleMoney = cfmdRecycleMoney;
	formData.netLossMoney = totalLossMoney - cfmdRecycleMoney;
	formData.cfmdIndemnity = cfmdIndemnity;
	formData.firstEntryDate = detail.firstEntryDate;
	// 判断事件严重程度

}

async function showImportOrec() {
	const res = await getListNoPage();
	tableData1.value = res;
	visible.value = true;
}

async function showMainCause() {
	const res = await getMainCauseListNoPage();
	tableData3.value = res;
	visible3.value = true;
}

async function showEventType() {
	const res = await getEventTypeListNoPage();
	tableData4.value = res;
	visible4.value = true;
}



function chooseRegulatory(){
	registerModal2.value.choose();
}

function getRegulatory(regulatory) {
	formData.eventTypeFirst = regulatory.firstLevelName
	formData.eventTypeSecond = regulatory.secondLevelName
	formData.eventTypeThird = regulatory.threeLevelName
}

async function handleDeleteDetail() {
	const subData = await getSubFormAndTableData();
	const list = subData.ldcEventRecycleDetailsList;
	// 定义总损失金额
	let totalLossMoney = 0;
	let cfmdRecycleMoney = 0;
	let cfmdIndemnity = 0;



	if (list != null && list.length > 0) {
		isShowEvent.value = true;
		formData.isLossEvent = '1';
		list.forEach((item) => {
			let category = item.category;
			let subClass = item.subClass;
			if (category == '1') {
				if (item.cnyAmount != null) {
					totalLossMoney = totalLossMoney + item.cnyAmount;
				}
			} else if (category == '2') {
				if (item.cnyAmount != null) {
					cfmdRecycleMoney = cfmdRecycleMoney + item.cnyAmount;
					if (subClass != null && subClass != '' && subClass == '3') {
						cfmdIndemnity = cfmdIndemnity + item.cnyAmount;
					}
				}
			}
		})
	} else {
		isShowEvent.value = false;
		formData.isLossEvent = '';
	}
	// 重新遍历，判断字段的值以及是否可以修改
	formData.totalLossMoney = totalLossMoney;
	formData.cfmdRecycleMoney = cfmdRecycleMoney;
	formData.netLossMoney = totalLossMoney - cfmdRecycleMoney;
	formData.cfmdIndemnity = cfmdIndemnity;
	//formData.firstEntryDate = detail.firstEntryDate;

}

async function handleAddDetail() {
	const subData = await getSubFormAndTableData();
	const list = subData.ldcEventRecycleDetailsList;
	if (list != null && list.length > 0) {
		isShowEvent.value = true;
		formData.isLossEvent = '1';
	} else {
		isShowEvent.value = false;
		formData.isLossEvent = '';
	}
}

function scrollToTop() {
	const formEl = formRef.value?.$el?.closest('.form-container');
	if (formEl) {
		formEl.scrollTo({
		top: 0,
		behavior: 'smooth' // 平滑滚动效果:ml-citation{ref="7" data="citationList"}
		});
	}
}

function clueRelList(record) {
	registerModal3.value.showClue(record);
}


async function showGLTZ() {
	// if (formData.relLedgerCode == '' || formData.relLedgerCode == null) {
	// 	message.warning("不存在台账数据");
	// }
	// let param = {
	// 	'ledgerNumber': formData.relLedgerCode,
	// 	'pageNo': 1,
	// 	'pageSize': 300,
	// }
	// const res = await queryLedgerList(param);
	// tableData2.value = res.records;
	visible2.value = true;
}

  // 定义内外规表格列
  const externalLawColumns = ref([
    { title: '外规文号', dataIndex: 'num', width: '200px' },
    { title: '外规名称', dataIndex: 'name', width: '200px' },
    { title: '发文机构', dataIndex: 'dept', width: '200px' },
    { title: '时效性', dataIndex: 'timeliness', width: '200px' },
  ]);

  // 内规内容
  const ruleExternalInternalDetailTable = reactive<Record<string, any>>({
    loading: false,
    dataSource: [],
  });
  // 外规内容
  const ruleExternalLawDetailTable = reactive<Record<string, any>>({
	loading: false,
	dataSource: [],
  });
  const externalInternalColumns = ref([
    { title: '制度文号', dataIndex: 'num', width: '200px' },
    { title: '制度名称', dataIndex: 'name', width: '200px' },
    { title: '发文机构', dataIndex: 'dept', width: '200px' },
    { title: '发文部门', dataIndex: 'deptOne', width: '200px' },
    { title: '实施日期', dataIndex: 'implementationDate', width: '200px' },
  ]);




defineExpose({
	add,
	edit,
	submitForm,
});
</script>

<style lang="less" scoped>
.antd-modal-form {
	padding: 14px;
}


.page-container {
  display: flex;
  flex-direction: column;
  height: 75vh; /* 使容器高度占满视口高度 */
  overflow: hidden; /* 防止外层容器出现滚动条 */
}

.form-container {
  flex: 1; /* 使表单容器占据剩余空间 */
  overflow-x: hidden; /* 允许表单容器在垂直方向上滚动 */
  padding: 16px; /* 添加一些内边距以便更好地显示表单 */
}

.button-container {
  position: sticky; /* 使用粘性定位 */
  bottom: 0; /* 固定在页面底部 */
  background-color: #fff; /* 确保按钮背景与页面底部颜色一致（如果需要） */
  //padding: 16px; /* 添加一些内边距以便更好地显示按钮 */
  text-align: right; /* 将按钮对齐到右侧（可选） */
}

/* 滚动容器 */
.scroll-wrapper {
  overflow-x: auto;
  scrollbar-width: none; /* 隐藏 Firefox 滚动条 */
  -ms-overflow-style: none; /* 隐藏 IE 滚动条 */
  &::-webkit-scrollbar { display: none; } /* 隐藏 Chrome 滚动条 */
  padding: 0 32px; /* 两侧留出按钮空间 */
}

/* 菜单项间距优化 */
.scroll-wrapper.scroll-menu {
  white-space: nowrap;
  .ant-menu-item {
    padding: 0 24px; /* 增大间距便于滚动 */
  }
}
</style>
