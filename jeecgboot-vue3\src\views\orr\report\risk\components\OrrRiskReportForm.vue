<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol"
                name="OrrRiskReportForm">
          <a-row>
            <a-col :span="24">
              <a-form-item label="事件发现日期" v-bind="validateInfos.eventDiscoveryDate"
                           id="OrrRiskReportForm-eventDiscoveryDate" name="eventDiscoveryDate">
                <a-date-picker placeholder="请选择事件发现日期"
                               v-model:value="formData.eventDiscoveryDate" value-format="YYYY-MM-DD"
                               style="width: 100%" allow-clear/>
              </a-form-item>
            </a-col>
            <a-col :span="12" style="margin-left: 100px">
              <a-form-item label="上传文件" v-bind="validateInfos.fileList"
                           id="CmSubjectLegalPersonForm-upload" name="upload">
                <div class="upload-container">
                  <a-upload v-model:file-list="formData.fileList" name="file"
                            :before-upload="()=>false"
                            :max-count="1"
                            @change="uploadChange">
                    <a-button preIcon="ant-design:upload-outlined">
                      请选择
                    </a-button>
                  </a-upload>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
import {ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted} from 'vue';
import {useMessage} from '/@/hooks/web/useMessage';
import {getValueType} from '/@/utils';
import {saveOrUpdate} from '../OrrRiskReport.api';
import {Form} from 'ant-design-vue';
import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
import { exportToWord, DocxConverter } from '/@/utils/wordUtils';

const props = defineProps({
  formDisabled: {type: Boolean, default: false},
  formData: {type: Object, default: () => ({})},
  formBpm: {type: Boolean, default: true}
});
const formRef = ref();
const useForm = Form.useForm;
const emit = defineEmits(['register', 'ok']);
const formData = reactive<Record<string, any>>({
  id: '',
  reportName: '',
  eventDiscoveryDate: '',
  reportHtml: '',
  fileList: [],
});
const {createMessage} = useMessage();
const labelCol = ref<any>({xs: {span: 24}, sm: {span: 5}});
const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 16}});
const confirmLoading = ref<boolean>(false);
//表单验证
const validatorRules = reactive({
  eventDiscoveryDate: [{required: true, message: '请选择事件发现日期!'},],
  fileList: [{required: true, message: '请选择上传文件!'},],
});
const {
  resetFields,
  validate,
  validateInfos
} = useForm(formData, validatorRules, {immediate: false});

// 表单禁用
const disabled = computed(() => {
  if (props.formBpm === true) {
    if (props.formData.disabled === false) {
      return false;
    } else {
      return true;
    }
  }
  return props.formDisabled;
});


/**
 * 新增
 */
function add() {
  edit({});
}

/**
 * 编辑
 */
function edit(record) {
  nextTick(() => {
    resetFields();
    const tmpData = {};
    Object.keys(formData).forEach((key) => {
      if (record.hasOwnProperty(key)) {
        tmpData[key] = record[key]
      }
    })
    //赋值
    Object.assign(formData, tmpData);
  });
}

/**
 * 提交数据
 */
async function submitForm() {

  try {
    // 触发表单验证
    await validate();
  } catch ({errorFields}) {
    if (errorFields) {
      const firstField = errorFields[0];
      if (firstField) {
        formRef.value.scrollToField(firstField.name, {behavior: 'smooth', block: 'center'});
      }
    }
    return Promise.reject(errorFields);
  }
  confirmLoading.value = true;
  const isUpdate = ref<boolean>(false);
  //时间格式化
  let model = formData;
  if (model.id) {
    isUpdate.value = true;
  }
  //循环数据
  for (let data in model) {
    //如果该数据是数组并且是字符串类型
    if (model[data] instanceof Array) {
      let valueType = getValueType(formRef.value.getProps, data);
      //如果是字符串类型的需要变成以逗号分割的字符串
      if (valueType === 'string') {
        model[data] = model[data].join(',');
      }
    }
  }
  await saveOrUpdate(model, isUpdate.value)
    .then((res) => {
      if (res.success) {
        createMessage.success(res.message);
        emit('ok');
      } else {
        createMessage.warning(res.message);
      }
    })
    .finally(() => {
      confirmLoading.value = false;
    });
}

/**
 * 上传组件事件
 * @param data
 */
async function uploadChange(data) {
  formData.reportName = data.file.name.replace(/\.[^/.]+$/, "");
  formData.reportHtml = await DocxConverter.convertToHtml(data.file);
}

defineExpose({
  add,
  edit,
  submitForm,
});
</script>

<style lang="less" scoped>
.upload-container {
  display: flex;
  align-items: center;
  width: 100%;

  :deep(.ant-upload) {
    margin-right: 0;
  }

  .download-link {
    padding-bottom: 10px;
    margin-left: 12px;
    white-space: nowrap;
  }

  :deep(.ant-upload-list) {
    flex: 0 0 100%;
    margin-top: 8px;
  }
}

.antd-modal-form {
  padding: 14px;
}
</style>
