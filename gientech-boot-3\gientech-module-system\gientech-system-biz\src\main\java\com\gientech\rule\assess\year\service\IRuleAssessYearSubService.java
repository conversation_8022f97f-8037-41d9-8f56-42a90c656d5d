package com.gientech.rule.assess.year.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gientech.rule.assess.year.entity.RuleAssessYearSub;
import com.gientech.rule.assess.year.vo.RuleAssessYearSubVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 存量制度全面评估任务-子任务
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-29
 */
public interface IRuleAssessYearSubService extends IService<RuleAssessYearSub> {

    /**
     * 根据主任务ID查询子任务列表（支持分页）
     *
     * @param yearId 主任务ID
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @return 分页结果
     */
    IPage<RuleAssessYearSub> getByYearId(String yearId, Integer pageNo, Integer pageSize);

    /**
     * 查询子任务信息（分页）
     *
     * @param page 分页
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    Page<RuleAssessYearSubVO> selectVOPage(
            Page<RuleAssessYearSubVO> page,
            @Param(Constants.WRAPPER) Wrapper<RuleAssessYearSubVO> queryWrapper);

    /**
     * 提交子任务评估结果
     *
     * @param subId 子任务ID
     * @return 是否成功
     */
    boolean submitSubTask(String subId);

    /**
     * 更新子任务统计数据
     *
     * @param subId 子任务ID
     */
    void updateSubTaskStatistics(String subId);

}
