import { BasicColumn } from '/@/components/Table';

//列表数据
export const columns: BasicColumn[] = [
  {
    title: '维护任务编号',
    align: 'center',
    dataIndex: 'taskNum',
  },
  {
    title: '维护任务类型',
    align: 'center',
    dataIndex: 'taskType_dictText',
  },
  {
    title: '维护任务状态',
    align: 'center',
    dataIndex: 'taskStatus_dictText',
  },
  {
    title: '流程状态',
    align: 'center',
    dataIndex: 'processStatus_dictText',
  },
  {
    title: '制度文号',
    align: 'center',
    dataIndex: 'documentNumber',
  },
  {
    title: '制度版本号',
    align: 'center',
    dataIndex: 'version',
  },
  {
    title: '制度名称',
    align: 'center',
    dataIndex: 'systemName',
  },
  {
    title: '新建/修订原因',
    align: 'center',
    dataIndex: 'newRevisionCause_dictText',
  },
  {
    title: '是否计划内',
    align: 'center',
    dataIndex: 'isPlaned_dictText',
  },
  {
    title: '关联计划编号',
    align: 'center',
    dataIndex: 'associatedPlanNumber',
  },
  {
    title: '制度发文机构',
    align: 'center',
    dataIndex: 'systemIssuingBody_dictText',
  },
  {
    title: '制度发文部门（一级）',
    align: 'center',
    dataIndex: 'issuingDeptOne_dictText',
  },
  {
    title: '制度管理人',
    align: 'center',
    dataIndex: 'systemManager',
  },
  {
    title: '合规审核意见',
    align: 'center',
    dataIndex: 'complianceOpinion',
  },
  {
    title: '合规审核/审批意见',
    align: 'center',
    dataIndex: 'complianceAuditOpinion',
  },
];
