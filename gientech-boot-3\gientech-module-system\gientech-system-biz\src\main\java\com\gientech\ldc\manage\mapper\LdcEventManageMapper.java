package com.gientech.ldc.manage.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.ldc.manage.entity.LdcEventManage;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 损失事件管理表
 * @Author: jeecg-boot
 * @Date:   2025-04-29
 * @Version: V1.0
 */
public interface LdcEventManageMapper extends BaseMapper<LdcEventManage> {

    // 根据填报机构获取编号-fill_depart
    String getMaxSerialIndex(String fillDepart);

    List<LdcEventManage> queryAuditedList(@Param("page") Page<LdcEventManage> page, @Param("manage") LdcEventManage ldcEventManage);

    IPage<LdcEventManage> selectEventPageList(@Param("page") Page<LdcEventManage> page,
                                              @Param(Constants.WRAPPER) QueryWrapper<LdcEventManage> queryWrapper,
                                              @Param("unionQuery") QueryWrapper<LdcEventManage> queryWrapper2,
                                              @Param("orgId") String orgId);

    IPage<LdcEventManage> selectEventPageListByParam(
            @Param("page") Page<LdcEventManage> page,
            @Param("processList") List<String> processFlagList,
            @Param("manage") LdcEventManage ldcEventManage,
            @Param("orgId") String orgId,
            @Param("departList") List<String> fillDepartList);

    IPage<LdcEventManage> selectEventPageListByDepart(
            @Param("page") Page<LdcEventManage> page,
            @Param("manage") LdcEventManage ldcEventManage,
            @Param("orgId") String orgId);

    List<String> judgeCanOperate(@Param("ids") List<String> ids,
                        @Param("processList") List<String> processFlagList,
                        @Param("departList") List<String> fillDepartList);

    int updateEditStates(@Param("id") String id);

    List<LdcEventManage> selectEventPageListForExport(@Param("manage") LdcEventManage ldcEventManage,
                                                      @Param("processList") List<String> processFlagList,
                                                      @Param("departList") List<String> fillDepartList,
                                                      @Param("orgId") String orgId);

    List<String> getCauseClassification();

    List<Map<String, Object>> querySubjectCodeList();

    List<Map<String, Object>> querySubjectNameList();

    List<LdcEventManage> queryLdcRelRcsaList(@Param("page") Page<LdcEventManage> page,
                                             @Param("info") Map<String, Object> queryMap);

    List<LdcEventManage> selectList111();
}
