import {defHttp} from '/@/utils/http/axios';
import {useMessage} from "/@/hooks/web/useMessage";

const {createConfirm} = useMessage();

enum Api {
  list = '/orr/report/regular/orrRegularReport/list',
  save = '/orr/report/regular/orrRegularReport/add',
  edit = '/orr/report/regular/orrRegularReport/edit',
  queryById = '/orr/report/regular/orrRegularReport/queryById',
  batchQueryById = '/orr/report/regular/orrRegularReport/batchQueryById',
  resetReport = '/orr/report/regular/orrRegularReport/resetReport',
  deleteOne = '/orr/report/regular/orrRegularReport/delete',
  deleteBatch = '/orr/report/regular/orrRegularReport/deleteBatch',
  passBatch = '/orr/report/regular/orrRegularReport/passBatch',
  returnBatch = '/orr/report/regular/orrRegularReport/returnBatch',
  batchSubmit = '/orr/report/regular/orrRegularReport/batchSubmit',
  batchRevoke = '/orr/report/regular/orrRegularReport/batchRevoke',
  dataImport = '/orr/report/regular/orrRegularReport/dataImport',
  importExcel = '/orr/report/regular/orrRegularReport/importExcel',
  exportXls = '/orr/report/regular/orrRegularReport/exportXls',
  process = '/orr/report/regular/orrRegularReport/process',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({url: Api.list, params});

/**
 * 复核列表接口
 * @param params
 */
export const processList = (params) => {

  if (params.reportStatus == undefined && params.processKey == 1) {
    params.reportStatus = "2,5";
  } else if (params.reportStatus == undefined && params.processKey == 2) {
    params.reportStatus = "1,3,4";
  }
  return defHttp.get({url: Api.list, params})
};

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({
        url: Api.deleteBatch,
        data: params
      }, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params}, {isTransformResponse: false});
}

/**
 * 批量提交
 * @param params
 */
export const batchSubmit = (params) => {
  return defHttp.post({url: Api.batchSubmit, params}, {
    isTransformResponse: false,
    joinParamsToUrl: true
  });
}

/**
 * 批量撤销
 * @param params
 */
export const batchRevoke = (params) => {
  return defHttp.post({url: Api.batchRevoke, params}, {
    isTransformResponse: false,
    joinParamsToUrl: true
  });
}

/**
 * 批量通过
 * @param params
 */
export const batchPass = (ids) => {
  const params = {ids: ids};
  return defHttp.post({url: Api.passBatch, params}, {
    isTransformResponse: false,
    joinParamsToUrl: true
  });
}

/**
 * 批量退回
 * @param params
 */
export const batchReturn = (ids) => {
  const params = {ids: ids};
  return defHttp.post({url: Api.returnBatch, params}, {
    isTransformResponse: false,
    joinParamsToUrl: true
  });
}

/**
 * 通过id查询
 * @param params
 */
export const queryById = (params) => defHttp.get({
  url: Api.queryById,
  params
}, {joinParamsToUrl: true, isTransformResponse: false});

/**
 * 通过id批量查询
 * @param params
 */
export const batchQueryById = (params) => defHttp.post({
  url: Api.batchQueryById,
  params
}, {joinParamsToUrl: true, isTransformResponse: false});

/**
 * 通过id重置
 * @param params
 */
export const resetReport = (params) => defHttp.get({
  url: Api.resetReport,
  params
}, {joinParamsToUrl: true, isTransformResponse: false});

/**
 * 数据导入
 * @param params
 */
export const dataImport = (params) => {
  return defHttp.post({url: Api.dataImport, params}, {
    isTransformResponse: false,
  });
}

/**
 * 处理过程
 * @param params
 */
export const getProcess = (params: any) => {
  return defHttp.get({ url: Api.process, params }, {isTransformResponse:false, joinParamsToUrl: true });
};
