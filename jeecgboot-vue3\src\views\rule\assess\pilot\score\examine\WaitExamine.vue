<template>
  <div>
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="reload" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 128 }">
          <!-- 默认显示的查询条件 -->
          <a-col :span="8">
            <a-form-item name="taskCode">
              <template #label><span title="任务编号">任务编号</span></template>
              <a-input placeholder="请输入任务编号" v-model:value="queryParam.taskCode" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="taskName">
              <template #label><span title="任务名称">任务名称</span></template>
              <a-input placeholder="请输入任务名称" v-model:value="queryParam.taskName" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="assessType">
              <template #label><span title="评估类型">评估类型</span></template>
              <j-dict-select-tag
                v-model:value="queryParam.assessType"
                dictCode="rule_assess_pilot_assess_type"
                placeholder="请选择评估类型"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="endTime">
              <template #label><span title="结束时间">结束时间</span></template>
              <a-date-picker
                placeholder="请选择结束时间"
                v-model:value="queryParam.endTime"
                valueFormat="YYYY-MM-DD"
                style="width: 100%"
                allow-clear
              />
            </a-form-item>
          </a-col>

          <!-- 展开显示的其余查询条件 -->
          <template v-if="toggleSearchStatus">
            <a-col :span="8">
              <a-form-item name="assessOrgCode">
                <template #label><span title="评估机构">评估机构</span></template>
                <j-select-dept v-model:value="queryParam.assessOrgCode" placeholder="请选择评估机构" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="assessDeptCode">
                <template #label><span title="评估部门">评估部门</span></template>
                <j-select-dept v-model:value="queryParam.assessDeptCode" placeholder="请选择评估部门" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="taskStatus">
                <template #label><span title="任务状态">任务状态</span></template>
                <j-dict-select-tag
                  v-model:value="queryParam.taskStatus"
                  dictCode="rule_assess_pilot_sub_task_status"
                  placeholder="请选择任务状态"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item name="processStatus">
                <template #label><span title="流程状态">流程状态</span></template>
                <j-dict-select-tag
                  v-model:value="queryParam.processStatus"
                  dictCode="rule_assess_pilot_sub_process_status"
                  placeholder="请选择流程状态"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </template>

          <!-- 操作按钮区域 -->
          <a-col :span="8">
            <span style="float: right; overflow: hidden" class="table-page-search-submitButtons">
              <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
              </a>
              <a-button preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
              <a-button type="primary" preIcon="ant-design:search-outlined" style="margin-left: 8px" @click="reload">查询</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button
          type="primary"
          v-auth="'rule.assess:rule_assess_pilot_sub:examine'"
          :disabled="selectedRowKeys.length === 0"
          preIcon="ant-design:check-outlined"
          @click="handlePass"
        >
          通过
        </a-button>
        <a-button
          type="default"
          v-auth="'rule.assess:rule_assess_pilot_sub:examine'"
          :disabled="selectedRowKeys.length === 0"
          preIcon="ant-design:export-outlined"
          @click="handleGiveback"
        >
          退回
        </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <RuleAssessScoreModal ref="registerModal" @success="handleSuccess" />
    <ProcessModal ref="processRef" :getProcess="getProcess" />
  </div>
</template>

<script lang="ts" name="rule.assess-ruleAssessPilotScoreExamine" setup>
  import { ref, reactive } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns } from '../RuleAssessPilotScore.data';
  import { list } from '../RuleAssessPilotScore.api';
  import { getProcess, passRequest, givebackRequest } from './RuleAssessPilotScoreExamine.api';
  import RuleAssessScoreModal from '../components/RuleAssessScoreModal.vue';
  import { useUserStore } from '/@/store/modules/user';
  import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import ProcessModal from '@/views/kri/input/components/ProcessModal.vue';
  // import { useExamine } from '@/hooks/api/useExamine';
  import { Icon } from '/@/components/Icon';

  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const processRef = ref();
  const userStore = useUserStore();
  const { createMessage } = useMessage();

  // // 使用审核钩子函数
  // const { passRequest, givebackRequest, getProcess } = useExamine('/rule/assess/pilot/score');

  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '制度试运行评分审核-待审核',
      api: list,
      columns,
      canResize: false,
      useSearchForm: false,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      defSort: {
        column: 'createTime',
        order: 'desc',
      },
      beforeFetch: async (params) => {
        // 将查询参数合并到请求参数中
        const mergedParams = Object.assign({}, params);

        // 只添加非空的查询参数
        Object.keys(queryParam).forEach(key => {
          const value = queryParam[key];
          if (value !== null && value !== undefined && value !== '') {
            mergedParams[key] = value;
          }
        });

        return mergedParams;
      },
    },
  });

  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] =
    tableContext;

  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }

  /**
   * 处理过程
   */
  function toProcess(record: Recordable) {
    processRef.value.handleOpen(record.id);
  }

  /**
   * 审核通过
   */
  function handlePass() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请选择要审核的记录!');
      return;
    }
    const ids = selectedRowKeys.value.join(',');
    passRequest({ ids }, handleSuccess);
  }

  /**
   * 审核退回
   */
  function handleGiveback() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请选择要退回的记录!');
      return;
    }
    const ids = selectedRowKeys.value.join(',');
    givebackRequest({ ids }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '查看',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '处理过程',
        onClick: toProcess.bind(null, record),
      },
    ];
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }

  defineExpose({
    reload,
  });
</script>

<style scoped lang="less">
  .jeecg-basic-table-form-container {
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    margin-bottom: 16px;
  }
</style>
