<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam"
              :label-col="labelCol"
              :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="8">
            <a-form-item name="reportYear">
              <template #label><span title="报告年度">报告年度</span></template>
              <a-date-picker picker="year" valueFormat="YYYY" placeholder="请选择报告年度"
                             v-model:value="queryParam.reportYear"
                             style="width: 100%"
                             allow-clear/>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item name="reportQuarterly">
              <template #label><span title="报告季度">报告季度</span></template>
              <j-select-multiple placeholder="请选择报告季度"
                                 v-model:value="queryParam.reportQuarterly"
                                 dictCode="orr_report_quarterly" allow-clear/>
              <!--              <a-date-picker picker="quarter" placeholder="请选择报告季度"-->
              <!--                             v-model:value="queryParam.reportQuarterly"-->
              <!--                             @change="handleQuarterChange"-->
              <!--                             style="width: 100%"-->
              <!--                             allow-clear/>-->
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :lg="8">
              <a-form-item name="reportName">
                <template #label><span title="报告名称">报告名称</span></template>
                <JInput placeholder="请输入报告名称" v-model:value="queryParam.reportName"/>
              </a-form-item>
            </a-col>
            <a-col :lg="8">
              <a-form-item name="reportStatus">
                <template #label><span title="报告状态">报告状态</span></template>
                <j-select-multiple placeholder="请选择报告状态"
                                   v-model:value="queryParam.reportStatus"
                                   dictCode="orr_report_status" allow-clear/>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset"
                          style="margin-left: 8px">重置</a-button>
                <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <Icon
                    :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'"/>
                </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="''" @click="handleExamine"
                  preIcon="ant-design:check-outlined"> 提交审核
        </a-button>
        <a-button type="primary" v-auth="''" @click="handleRevoke"
                  preIcon="ant-design:rollback-outlined"> 撤销
        </a-button>
        <j-upload-button type="primary" v-auth="'orr.report.regular:orr_regular_report:importExcel'"
                         preIcon="ant-design:import-outlined" @click="handleImportXls"> 上传
        </j-upload-button>
        <a-button type="primary" v-auth="'orr.report.regular:orr_regular_report:exportXls'"
                  preIcon="ant-design:export-outlined" @click="handleExportXls"> 导出
        </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"/>
      </template>
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
    <ProcessModal ref="processRef" :getProcess="getProcess"/>
  </div>
</template>

<script lang="ts" name="orr.report.regular-orrRegularReport" setup>
import {ref, reactive, defineProps} from 'vue';
import {BasicTable, TableAction} from '/@/components/Table';
import {useListPage} from '/@/hooks/system/useListPage';
import {columns} from './OrrRegularReport.data';
import {
  list,
  batchSubmit,
  batchRevoke,
  batchQueryById,
  saveOrUpdate,
  getProcess
} from './OrrRegularReport.api';
import {useUserStore} from '/@/store/modules/user';
import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
import JInput from "/@/components/Form/src/jeecg/components/JInput.vue";
import {useMessage} from "@/hooks/web/useMessage";
import {useGo} from "@/hooks/web/usePage";
import {exportToWord, DocxConverter} from '/@/utils/wordUtils';
import ProcessModal from "@/views/kri/input/components/ProcessModal.vue";

const props = defineProps({
  // 1-全行常规报告 2-总行部门常规报告/分行常规报告
  reportType: {type: Number, default: ''},
});

const formRef = ref();
const processRef = ref();
const queryParam = reactive<any>({});
const toggleSearchStatus = ref<boolean>(false);
const userStore = useUserStore();
const {createMessage} = useMessage();
const go = useGo();
//注册table数据
const {prefixCls, tableContext} = useListPage({
  tableProps: {
    title: '操作风险报告-常规报告管理',
    api: list,
    columns,
    canResize: false,
    useSearchForm: false,
    actionColumn: {
      width: 180,
      fixed: 'right',
    },
    defSort: {
      column: 'reportDeadlineDate',
      order: 'desc'
    },
    beforeFetch: async (params) => {

      // 设置类型
      queryParam.type = props.reportType;
      Object.assign(params, queryParam)

      // // 处理季度报告
      // if (params.reportQuarterlyStr) {
      //   params.reportQuarterly = null;
      //
      //   const reportQuarterlyStr = params.reportQuarterlyStr;
      //   const arr = reportQuarterlyStr.split("-");
      //
      //   params.reportYear = arr[0];
      //
      //   const reportQuarterly = arr[1];
      //   if (reportQuarterly == "Q1") {
      //     params.reportQuarterly = "第一季度";
      //   } else if (reportQuarterly == "Q2") {
      //     params.reportQuarterly = "第二季度";
      //   } else if (reportQuarterly == "Q3") {
      //     params.reportQuarterly = "第三季度";
      //   } else if (reportQuarterly == "Q4") {
      //     params.reportQuarterly = "第四季度";
      //   }
      // }

      return params;
    },
  },
});
const [registerTable, {
  reload,
  collapseAll,
  updateTableDataRecord,
  findTableDataRecord,
  getDataSource
}, {rowSelection, selectedRowKeys, selectedRows}] = tableContext;
const labelCol = reactive({
  xs: 8,
  sm: 8,
  xl: 8,
  xxl: 6
});
const wrapperCol = reactive({
  xs: 16,
  sm: 16,
});

/**
 * 提交审核
 */
async function handleExamine() {

  let rowKeys = selectedRowKeys.value;

  if (rowKeys.length <= 0) {
    createMessage.warn("请选择报告！");
    return;
  }

  const hasState1Or4 = selectedRows.value.some(item => item.reportStatus != '1' && item.reportStatus != '4');
  if (hasState1Or4) {
    createMessage.warn("仅支持对状态为“草稿”和“审核退回”的报告进行提交审核操作！");
    return;
  }

  await batchSubmit({ids: rowKeys})
    .then((res) => {
      if (res.success) {
        createMessage.success(res.message);
        handleSuccess();
      } else {
        createMessage.warn(res.message);
      }
    })
    .finally(() => {

    })

}

/**
 * 撤销
 */
async function handleRevoke() {

  let rowKeys = selectedRowKeys.value;

  if (rowKeys.length <= 0) {
    createMessage.warn("请选择报告！");
    return;
  }

  const hasState2Or3 = selectedRows.value.some(item => item.reportStatus != '2' && item.reportStatus != '3');
  if (hasState2Or3) {
    createMessage.warn("仅支持对状态为“审核中”和“审核通过”的报告进行撤销操作！");
    return;
  }

  await batchRevoke({ids: rowKeys})
    .then((res) => {
      if (res.success) {
        createMessage.success(res.message);
        handleSuccess();
      } else {
        createMessage.warn(res.message);
      }
    })
    .finally(() => {

    })

}

/**
 * 上传
 */
async function handleImportXls(data) {

  let rowKeys = selectedRowKeys.value;

  if (rowKeys.length <= 0) {
    createMessage.warn("请选择报告！");
    return;
  }

  if (rowKeys.length > 1) {
    createMessage.warn("只可以选择一个报告！");
    return;
  }

  const file = data.file;

  if (!file) {
    createMessage.warn("未提供上传文件！");
    return;
  }

  // 获取文件扩展名并转换为小写
  const fileExtension = file.name.split('.').pop().toLowerCase();

  // 检查是否是支持的格式
  if (fileExtension !== 'docx') {
    createMessage.warn("请上传.docx格式的Word报告！");
    return;
  }

  const reportName = data.file.name.replace(/\.[^/.]+$/, "");
  const htmlContent = await DocxConverter.convertToHtml(data.file);

  // 去更新
  await saveOrUpdate({
    id: rowKeys[0],
    reportHtml: htmlContent,
    reportName: reportName
  }, true)
    .then((res) => {
      if (res.success) {
        createMessage.success(res.message);
        handleSuccess();
      } else {
        createMessage.warning(res.message);
      }
    })
    .finally(() => {

    });
}

/**
 * 导出
 */
async function handleExportXls() {

  let rowKeys = selectedRowKeys.value;

  if (rowKeys.length <= 0) {
    createMessage.warn("请选择报告！");
    return;
  }

  // 获取数据并下载
  await batchQueryById({ids: rowKeys})
    .then((res) => {
      if (res.success) {
        const rows = res.result;
        rows.forEach((item) => {
          exportToWord(item.reportName, item.reportHtml);
        })
      }
    })
    .finally(() => {

    })

}

/**
 * 查看
 */
function handleDetail(record: Recordable) {
  go({
    path: '/report/detail',
    query: {id: record.id}
  });
}

/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {

  if (record.reportStatus != '1') {
    createMessage.warn("仅支持对状态为“草稿”的报告进行编辑操作！");
    return;
  }

  go({
    path: '/report/edit',
    query: {id: record.id}
  });
}

/**
 * 处理过程
 */
function handleProcess(record: Recordable) {
  processRef.value.handleOpen(record.id);
}

/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '查看',
      onClick: handleDetail.bind(null, record),
    },
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      auth: 'orr.report.regular:orr_regular_report:edit'
    },
    {
      label: '处理过程',
      onClick: handleProcess.bind(null, record),
    },
  ];
}

/**
 * 查询
 */
function searchQuery() {
  reload();
}

/**
 * 重置
 */
function searchReset() {
  queryParam.reportQuarterlyStr = null;
  formRef.value.resetFields();
  selectedRowKeys.value = [];
  //刷新数据
  reload();
}

function handleQuarterChange(data, dataStr) {
  if (data) {
    queryParam.reportQuarterlyStr = dataStr;
  } else {
    queryParam.reportQuarterlyStr = null;
  }
}

</script>

<style lang="less" scoped>
.jeecg-basic-table-form-container {
  padding: 0;

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }

  .query-group-cust {
    min-width: 100px !important;
  }

  .query-group-split-cust {
    width: 30px;
    display: inline-block;
    text-align: center
  }

  .ant-form-item:not(.ant-form-item-with-help) {
    margin-bottom: 16px;
    height: 32px;
  }

  :deep(.ant-picker), :deep(.ant-input-number) {
    width: 100%;
  }
}
</style>
