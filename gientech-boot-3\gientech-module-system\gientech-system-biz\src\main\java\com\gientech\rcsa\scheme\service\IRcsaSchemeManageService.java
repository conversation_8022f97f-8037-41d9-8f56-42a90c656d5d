package com.gientech.rcsa.scheme.service;

import com.gientech.rcsa.scheme.entity.RcsaSchemeManage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: RCSA评估方案表
 * @Author: jeecg-boot
 * @Date:   2025-07-16
 * @Version: V1.0
 */
public interface IRcsaSchemeManageService extends IService<RcsaSchemeManage> {

    Result<String> submit(RcsaSchemeManage schemeManage);

    void auditPass(RcsaSchemeManage schemeManage);

    void auditReject(RcsaSchemeManage schemeManage);

    Result<String> edit(RcsaSchemeManage rcsaSchemeManage);

    Result<String> submitByWorkflow(RcsaSchemeManage schemeManage);
}
