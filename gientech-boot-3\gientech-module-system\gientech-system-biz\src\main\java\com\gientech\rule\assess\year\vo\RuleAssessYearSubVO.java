package com.gientech.rule.assess.year.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 年度评估子任务列表VO - 用于列表展示
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-30
 */
@Data
@Accessors(chain = true)
@Schema(description = "年度评估子任务列表VO")
public class RuleAssessYearSubVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 子任务ID
     */
    @Schema(description = "子任务ID")
    private String id;

    /**
     * 主任务ID
     */
    @Schema(description = "主任务ID")
    private String yearId;

    /**
     * 任务编号（子任务）
     */
    @Schema(description = "任务编号（子任务）")
    private String taskCode;

    /**
     * 任务名称（主任务）
     */
    @Schema(description = "任务名称（主任务）")
    private String taskName;

    /**
     * 评估类型（主任务）
     */
    @Dict(dicCode = "rule_assess_pilot_assess_type")
    @Schema(description = "评估类型（主任务）")
    private String assessType;

    /**
     * 结束时间（主任务）
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "结束时间（主任务）")
    private Date endTime;

    /**
     * 评估机构（子任务）
     */
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "评估机构（子任务）")
    private String assessOrgCode;

    /**
     * 评估部门（子任务）
     */
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "评估部门（子任务）")
    private String assessDeptCode;

    /**
     * 任务状态（子任务）
     */
    @Dict(dicCode = "rule_assess_pilot_sub_task_status")
    @Schema(description = "任务状态（子任务）")
    private String taskStatus;

    /**
     * 流程状态（子任务）
     */
    @Dict(dicCode = "rule_assess_year_sub_process_status")
    @Schema(description = "流程状态（子任务）")
    private String processStatus;

    /**
     * 需修订数量
     */
    @Schema(description = "需修订数量")
    private Integer needEditNumber;

    /**
     * 需新增数量
     */
    @Schema(description = "需新增数量")
    private Integer needAddNumber;

    /**
     * 需废止数量
     */
    @Schema(description = "需废止数量")
    private Integer needDeleteNumber;

    /**
     * 反馈人
     */
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "反馈人")
    private String feedbackPerson;

    /**
     * 反馈时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "反馈时间")
    private Date feedbackTime;
}
