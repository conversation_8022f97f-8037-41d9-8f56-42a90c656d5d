<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="8">
            <a-form-item name="taskCode">
              <template #label><span title="评估编号">评估编号</span></template>
              <a-input placeholder="请输入评估编号" v-model:value="queryParam.taskCode" allow-clear ></a-input>
            </a-form-item>
          </a-col>
        <a-col :lg="8">
        <a-form-item name="planTitle">
        <template #label><span title="计划标题">计划标题</span></template>
            <JInput v-model:value="queryParam.planTitle"/>
        </a-form-item>
        </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :lg="8">
              <a-form-item name="schemeTitle">
              <template #label><span title="方案标题">方案标题</span></template>
                <JInput v-model:value="queryParam.schemeTitle"/>
              </a-form-item>
            </a-col>
            <a-col :lg="8">
              <a-form-item name="planType">
                <template #label><span title="计划类型">计划类型</span></template>
                <j-select-multiple placeholder="请选择计划类型" v-model:value="queryParam.planType" dictCode="rcsa_plan_type" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :lg="8">
              <a-form-item name="planType">
                <template #label><span title="矩阵名称">矩阵名称</span></template>
                <a-select mode="multiple" v-model:value="queryParam.matrixName" placeholder="请选择矩阵名称" allow-clear>
                  <a-select-option v-for="item in matrixList" :key="item.matrixName" :value="item.matrixName">
                      {{item.matrixName}}
                  </a-select-option>
                </a-select>
                <!-- <j-select-multiple placeholder="请选择矩阵名称" v-model:value="queryParam.planType" dictCode="rcsa_plan_type" allow-clear /> -->
              </a-form-item>
            </a-col>
            <a-col :lg="8">
              <a-form-item name="evaluateEndDate">
                <template #label><span title="评估截止日期">评估截止日期</span></template>
                <a-range-picker value-format="YYYY-MM-DD"  v-model:value="queryParam.evaluateEndDate" class="query-group-cust"/>
              </a-form-item>
            </a-col>
            <a-col :lg="8">
              <a-form-item name="taskState">
                <template #label><span title="任务状态">任务状态</span></template>
                <j-select-multiple placeholder="请选择任务状态" v-model:value="queryParam.taskState" dictCode="rcsa_plan_state" allow-clear />
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'rcsa.task:rcsa_task_manage:submit'"  @click="handleSubmit" preIcon="ant-design:check-outlined"> 提交</a-button>
        <!-- <a-button type="primary" v-auth="'rcsa.task:rcsa_task_manage:add'"  @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button> -->
        <!-- <j-upload-button  type="primary" v-auth="'rcsa.task:rcsa_task_manage:importExcel'"  preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'rcsa.task:rcsa_task_manage:deleteBatch'">批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown> -->
        <!-- 高级查询 -->
        <!-- <super-query :config="superQueryConfig" @search="handleSuperQuery" /> -->
      </template>
      <template #toolbar>
        <a-button type="primary" style="" v-auth="'rcsa.task:rcsa_task_manage:exportXls'" preIcon="ant-design:upload-outlined" @click="onExportXls"> 导出</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <RcsaTaskManageModal ref="registerModal" @success="handleSuccess"></RcsaTaskManageModal>
  </div>
  <RcsaPlanManageHistoryListModal ref="registerModal2"></RcsaPlanManageHistoryListModal>
</template>

<script lang="ts" name="rcsa.task-rcsaTaskManage" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, superQuerySchema } from './RcsaTaskManage.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, taskSubmit} from './RcsaTaskManage.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import RcsaTaskManageModal from './components/RcsaTaskManageModal.vue'
  import { useUserStore } from '/@/store/modules/user';
  import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
  import JInput from "/@/components/Form/src/jeecg/components/JInput.vue";
  import { cloneDeep } from "lodash-es";
  import { queryMatrixList } from '../../scheme/RcsaSchemeManage.api';
  import { useGo } from "@/hooks/web/usePage";
  import { message } from 'ant-design-vue';
  import RcsaPlanManageHistoryListModal from '../../history/RcsaPlanManageHistoryListModal.vue';
  
  const go = useGo();
  const matrixList = ref([]);
  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const registerModal2 = ref();
  const userStore = useUserStore();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      api: list,
      columns,
      canResize:false,
      useSearchForm: false,
      showTableSetting: false,
      actionColumn: {
        width: 200,
        fixed: 'right',
      },
      beforeFetch: async (params) => {
        let rangerQuery = await setRangeQuery();
        return Object.assign(params, rangerQuery);
      },
      defSort: {
        column: "taskCode",
        order: "asc"
      },
    },
    exportConfig: {
      name: "RCSA评估任务表",
      url: getExportUrl,
      params: setRangeQuery,
    },
	  importConfig: {
	    url: getImportUrl,
	    success: handleSuccess
	  },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] = tableContext;
  const labelCol = reactive({
    xs:24,
    sm:4,
    xl:6,
    xxl:4
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  onMounted(async () => {
    // 查询启用的本部门的底稿
    let param3 = {
      'evaluateDepart': '',
    }
    const res3 = await queryMatrixList(param3);
    matrixList.value = res3;
  });

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    registerModal.value.disableSubmit = false;
    registerModal.value.add();
  }
  
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    registerModal.value.disableSubmit = false;
    registerModal.value.edit(record);
  }
   
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }
   
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
   
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
   
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
   
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '查看',
        // onClick: handleDetail.bind(null, record),
        onClick: handleView.bind(null, record),
      },
      {
        label: '评估',
        onClick: handleEvaluate.bind(null, record),
        disabled: record.taskState == '2' || record.taskState == '3',
      },
      {
        label: '处理过程',
        onClick: handleProcess.bind(null, record),
      },
    ];
  }

  function handleEvaluate(record) {
    go("/rcsa/task/baseinfo/rcsaTaskBasicInfoList/" + record.id + "/1");
  }

  function handleView(record) {
    go("/rcsa/task/baseinfo/rcsaTaskBasicInfoList/" + record.id + "/2");
  }

  function handleProcess(record) {
    registerModal2.value.viewHistory(record);
  }
   
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }, {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'rcsa.task:rcsa_task_manage:delete'
      }
    ]
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }
  
  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }
  
  let rangeField = 'evaluateEndDate'
  
  /**
   * 设置范围查询条件
   */
  async function setRangeQuery(){
    let queryParamClone = cloneDeep(queryParam);
    if (rangeField) {
      let fieldsValue = rangeField.split(',');
      fieldsValue.forEach(item => {
        if (queryParamClone[item]) {
          let range = queryParamClone[item];
          queryParamClone[item+'_begin'] = range[0];
          queryParamClone[item+'_end'] = range[1];
          delete queryParamClone[item];
        } else {
          queryParamClone[item+'_begin'] = '';
          queryParamClone[item+'_end'] = '';
        }
      })
    }
    queryParamClone.pageFlag = '1';
    queryParamClone.taskState='1,4';
    return queryParamClone;
  }

  async function handleSubmit() {
    let selectedLength = rowSelection.selectedRows.length;
    if (selectedLength != 1 ) {
      message.warning("请选择一条数据");
      return;
    }
    let taskInfo = rowSelection.selectedRows[0];
    if (taskInfo.taskState != '1' && taskInfo.taskState != '4') {
      message.warning("只能对状态为“草稿”和“审核退回”的评估任务进行提交");
      return;
    }
    await taskSubmit({ id: taskInfo.id }, handleSuccess);
  }

</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust{
      min-width: 100px !important;
    }
    .query-group-split-cust{
      width: 30px;
      display: inline-block;
      text-align: center
    }
    .ant-form-item:not(.ant-form-item-with-help){
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),:deep(.ant-input-number){
      width: 100%;
    }
  }
</style>
