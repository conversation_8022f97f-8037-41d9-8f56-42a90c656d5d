package com.gientech.orr.report.risk.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import com.gientech.common.process.enitity.CommonProcess;
import com.gientech.common.process.service.ICommonProcessService;
import com.gientech.orr.report.regular.entity.OrrRegularReport;
import com.gientech.orr.report.risk.vo.OrrRiskReportVo;
import com.gientech.orr.report.template.entity.OrrReportTemplate;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import com.gientech.orr.report.risk.entity.OrrRiskReport;
import com.gientech.orr.report.risk.service.IOrrRiskReportService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 操作风险报告-重大操作风险事件管理
 * @Author: jeecg-boot
 * @Date: 2025-07-16
 * @Version: V1.0
 */
@Tag(name = "操作风险报告-重大操作风险事件管理")
@RestController
@RequestMapping("/orr/report/risk/orrRiskReport")
@Slf4j
public class OrrRiskReportController extends JeecgController<OrrRiskReport, IOrrRiskReportService> {

    private final String businessKey = "orrRiskReport";

    @Autowired
    private IOrrRiskReportService orrRiskReportService;

    @Autowired
    private ICommonProcessService processService;

    /**
     * 分页列表查询
     *
     * @param orrRiskReportVo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "操作风险报告-重大操作风险事件管理-分页列表查询")
    @Operation(summary = "操作风险报告-重大操作风险事件管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<OrrRiskReport>> queryPageList(OrrRiskReportVo orrRiskReportVo,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      HttpServletRequest req) {

        OrrRiskReport orrRiskReport = new OrrRiskReport();
        BeanUtils.copyProperties(orrRiskReportVo,orrRiskReport);

        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("reportStatus", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<OrrRiskReport> queryWrapper = QueryGenerator.initQueryWrapper(orrRiskReport, req.getParameterMap(), customeRuleMap);

        // 加入部门权限
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.likeRight("report_department", sysUser.getOrgCode());

        // 当前日期
        Date today = new Date();

        // 加入是否逾期的条件（1-是 0-否）
        String isOverdue = orrRiskReportVo.getIsOverdue();
        if ("1".equals(isOverdue)) {
            // 逾期条件：提交日期 > 截止日期 OR (截止日期 < 当前日期 AND 提交日期为空)
            queryWrapper.apply(
                    "(report_submit_date > report_deadline_date OR " +
                            "(report_deadline_date < {0} AND report_submit_date IS NULL))",
                    today
            );
        } else if ("0".equals(isOverdue)) {
            // 非逾期条件：提交日期 <= 截止日期 OR (提交日期为空 AND 截止日期 >= 当前日期)
            queryWrapper.apply(
                    "(report_submit_date <= report_deadline_date OR " +
                            "(report_submit_date IS NULL AND report_deadline_date >= {0}))",
                    today
            );
        }

        Page<OrrRiskReport> page = new Page<OrrRiskReport>(pageNo, pageSize);
        IPage<OrrRiskReport> pageList = orrRiskReportService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param orrRiskReport
     * @return
     */
    @AutoLog(value = "操作风险报告-重大操作风险事件管理-添加")
    @Operation(summary = "操作风险报告-重大操作风险事件管理-添加")
    @RequiresPermissions("orr.report.risk:orr_risk_report:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody OrrRiskReport orrRiskReport) {
        return orrRiskReportService.add(orrRiskReport);
    }

    /**
     * 编辑
     *
     * @param orrRiskReport
     * @return
     */
    @AutoLog(value = "操作风险报告-重大操作风险事件管理-编辑")
    @Operation(summary = "操作风险报告-重大操作风险事件管理-编辑")
    @RequiresPermissions("orr.report.risk:orr_risk_report:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody OrrRiskReport orrRiskReport) {
        orrRiskReportService.updateById(orrRiskReport);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "操作风险报告-重大操作风险事件管理-通过id删除")
    @Operation(summary = "操作风险报告-重大操作风险事件管理-通过id删除")
    @RequiresPermissions("orr.report.risk:orr_risk_report:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        orrRiskReportService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "操作风险报告-重大操作风险事件管理-批量删除")
    @Operation(summary = "操作风险报告-重大操作风险事件管理-批量删除")
    @RequiresPermissions("orr.report.risk:orr_risk_report:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.orrRiskReportService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "操作风险报告-重大操作风险事件管理-通过id查询")
    @Operation(summary = "操作风险报告-重大操作风险事件管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<OrrRiskReport> queryById(@RequestParam(name = "id", required = true) String id) {
        OrrRiskReport orrRiskReport = orrRiskReportService.getById(id);
        if (orrRiskReport == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(orrRiskReport);
    }

    /**
     * 通过ids批量查询查询
     *
     * @param ids
     * @return
     */
    //@AutoLog(value = "操作风险报告-重大操作风险事件管理-通过ids批量查询查询")
    @Operation(summary="操作风险报告-重大操作风险事件管理-通过ids批量查询查询")
    @PostMapping(value = "/batchQueryById")
    public Result<List<OrrRiskReport>> batchQueryById(@RequestParam(name="ids",required=true) List<String> ids) {
        return orrRiskReportService.batchQueryById(ids);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param orrRiskReport
     */
    @RequiresPermissions("orr.report.risk:orr_risk_report:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OrrRiskReport orrRiskReport) {
        return super.exportXls(request, orrRiskReport, OrrRiskReport.class, "操作风险报告-重大操作风险事件管理");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("orr.report.risk:orr_risk_report:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, OrrRiskReport.class);
    }

    /**
     * 批量提交
     *
     * @param ids
     * @return
     */
    @Operation(summary = "操作风险报告-重大操作风险事件管理-批量提交")
//	 @RequiresPermissions("orr.report.risk:orr_risk_report:batchSubmit")
    @RequestMapping(value = "/batchSubmit", method = RequestMethod.POST)
    public Result<String> batchSubmit(@RequestParam(name = "ids", required = true) List<String> ids) {
        return orrRiskReportService.batchSubmit(ids);
    }

    /**
     * 批量撤销
     *
     * @param ids
     * @return
     */
    @Operation(summary = "操作风险报告-重大操作风险事件管理-批量撤销")
//	 @RequiresPermissions("orr.report.risk:orr_risk_report:batchRevoke")
    @RequestMapping(value = "/batchRevoke", method = RequestMethod.POST)
    public Result<String> batchRevoke(@RequestParam(name = "ids", required = true) List<String> ids) {
        return orrRiskReportService.batchRevoke(ids);
    }

    /**
     * 批量通过
     *
     * @param ids
     * @return
     */
    @Operation(summary = "操作风险报告-重大操作风险事件管理-批量通过")
//	 @RequiresPermissions("orr.report.risk:orr_risk_report:passBatch")
    @RequestMapping(value = "/passBatch", method = RequestMethod.POST)
    public Result<String> passBatch(@RequestParam(name = "ids", required = true) List<String> ids) {
        return orrRiskReportService.passBatch(ids);
    }

    /**
     * 批量退回
     *
     * @param ids
     * @return
     */
    @Operation(summary = "操作风险报告-重大操作风险事件管理-批量退回")
//	 @RequiresPermissions("orr.report.risk:orr_risk_report:returnBatch")
    @RequestMapping(value = "/returnBatch", method = RequestMethod.POST)
    public Result<String> returnBatch(@RequestParam(name = "ids", required = true) List<String> ids) {
        return orrRiskReportService.returnBatch(ids);
    }

    /**
     * 下载模板
     *
     * @param
     * @return
     */
    @Operation(summary = "操作风险报告-重大操作风险事件管理-下载模板")
//	 @RequiresPermissions("orr.report.risk:orr_risk_report:downloadTemplate")
    @RequestMapping(value = "/downloadTemplate", method = RequestMethod.GET)
    public Result<OrrReportTemplate> downloadTemplate() {
        return orrRiskReportService.downloadTemplate();
    }


    /**
     * 处理过程
     *
     * @param id 数据录入对象id
     * @return 处理过程列表
     */
    @AutoLog(value = "操作风险报告-重大操作风险事件管理-处理过程")
    @Operation(summary = "操作风险报告-重大操作风险事件管理-处理过程")
    @GetMapping(value = "/process")
//    @RequiresPermissions("orr.report.risk:orr_risk_report:process")
    public Result<IPage<CommonProcess>> process(@RequestParam(name = "id", required = true) String id) {
        // 借用分页的字典翻译
        IPage<CommonProcess> page = new Page<>();
        page.setRecords(processService.getProcess(businessKey, id));
        return Result.ok(page);
    }
}
