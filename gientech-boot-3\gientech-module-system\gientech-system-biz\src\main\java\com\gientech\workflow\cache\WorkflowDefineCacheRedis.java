package com.gientech.workflow.cache;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gientech.workflow.constant.WorkflowConstant;
import com.gientech.workflow.define.WorkflowDefine;
import com.gientech.workflow.mapper.WorkflowDefineMapper;
import com.gientech.workflow.parser.JsonWorkflowDefineParser;
import com.gientech.workflow.parser.WorkflowDefineParser;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 流程定义缓存 Redis实现
 *
 * <AUTHOR>
 * @since 2025年05月14日 08:51
 */
@Slf4j
@Component
public class WorkflowDefineCacheRedis implements WorkflowDefineCache {

    private final String prefix = "workflow_define_cache_";

    /**
     * RedisTemplate只有原始使用，如果使用带泛型参数的{@link RedisTemplate} 需要手动创建
     */
    private RedisTemplate<String, Object> redisTemplate;

    private WorkflowDefineMapper workflowDefineMapper;

    @Autowired
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Autowired
    public void setWorkflowDefineMapper(WorkflowDefineMapper workflowDefineMapper) {
        this.workflowDefineMapper = workflowDefineMapper;
    }

    @PostConstruct
    public void initCache() {
        this.refreshCache();
    }

    @Override
    public void refreshCache() {
        log.info("=========工作流-流程定义刷新缓存(Redis)=========");
        LambdaQueryWrapper<WorkflowDefine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkflowDefine::getIsEffective, WorkflowConstant.IS_YES);
        List<WorkflowDefine> defineList = workflowDefineMapper.selectList(queryWrapper);
        Map<String, WorkflowDefine> map = new HashMap<>();
        for (WorkflowDefine define : defineList) {
            if (define.getDefinition() == null || define.getDefinition().isEmpty()) {
                log.error("{}流程定义为空", define.getBusinessKey());
                continue;
            }
            log.info("流程定义解析:{}", define.getBusinessKey());
            WorkflowDefineParser parser = new JsonWorkflowDefineParser(define);
            WorkflowDefine parse = parser.parse();
            map.put(prefix + define.getBusinessKey(), parse);
            map.put(prefix + define.getId(), parse);
        }
        redisTemplate.opsForValue().multiSet(map);
        log.info("=========工作流-流程定义刷新完成(Redis)=========");
    }

    @Override
    public void set(String businessKey) {
        log.info("启用流程定义解析:{}", businessKey);
        Map<String, WorkflowDefine> map = new HashMap<>();

        LambdaQueryWrapper<WorkflowDefine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkflowDefine::getIsEffective, WorkflowConstant.IS_YES);
        queryWrapper.eq(WorkflowDefine::getBusinessKey, businessKey);
        WorkflowDefine define = workflowDefineMapper.selectOne(queryWrapper);
        WorkflowDefineParser parser = new JsonWorkflowDefineParser(define);
        WorkflowDefine parse = parser.parse();
        map.put(prefix + define.getBusinessKey(), parse);
        map.put(prefix + define.getId(), parse);
        redisTemplate.opsForValue().multiSet(map);
        log.info("{}流程定义解析完成", businessKey);
    }

    @Override
    public boolean containsKey(String businessKey) {
        return redisTemplate.hasKey(prefix + businessKey);
    }

    @Override
    public WorkflowDefine get(String businessKey) {
        return (WorkflowDefine) redisTemplate.opsForValue().get(prefix + businessKey);
    }

    @Override
    public WorkflowDefine getAndDelete(String businessKey) {
        if (redisTemplate.hasKey(prefix + businessKey)) {
            return (WorkflowDefine) redisTemplate.opsForValue().get(prefix + businessKey);
        }
        return null;
    }

    @Override
    public WorkflowDefine getAndDeleteById(String defineId) {
        if (redisTemplate.hasKey(prefix + defineId)) {
            return (WorkflowDefine) redisTemplate.opsForValue().get(prefix + defineId);
        }
        return null;
    }

    @Override
    public WorkflowDefine getByDefineId(String defineId) {
        return (WorkflowDefine) redisTemplate.opsForValue().get(prefix + defineId);
    }
}
