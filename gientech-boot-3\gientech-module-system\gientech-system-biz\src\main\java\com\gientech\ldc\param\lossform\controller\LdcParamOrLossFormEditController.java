package com.gientech.ldc.param.lossform.controller;

import java.util.Arrays;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import com.gientech.ldc.param.lossform.entity.LdcParamOrLossFormEdit;
import com.gientech.ldc.param.lossform.service.ILdcParamOrLossFormEditService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.system.entity.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 操作风险事件损失形态分类_编辑表
 * @Author: jeecg-boot
 * @Date: 2025-04-18
 * @Version: V1.0
 */
@Tag(name = "操作风险事件损失形态分类_编辑表")
@RestController
@RequestMapping("/ldc/param/lossForm/edit")
@Slf4j
public class LdcParamOrLossFormEditController extends JeecgController<LdcParamOrLossFormEdit, ILdcParamOrLossFormEditService> {
    @Autowired
    private ILdcParamOrLossFormEditService ldcParamOrLossFormEditService;

    /**
     * 分页列表查询
     *
     * @param ldcParamOrLossFormEdit 参数
     * @param pageNo                 页码
     * @param pageSize               分页大小
     * @param req                    请求参数
     * @return 查询结果
     */
    @AutoLog(value = "操作风险事件损失形态分类-查询编辑数据")
    @Operation(summary = "操作风险事件损失形态分类-查询编辑数据")
    @GetMapping(value = "/list")
    public Result<IPage<LdcParamOrLossFormEdit>> queryPageList(LdcParamOrLossFormEdit ldcParamOrLossFormEdit,
                                                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                               HttpServletRequest req) {
        QueryWrapper<LdcParamOrLossFormEdit> queryWrapper = QueryGenerator.initQueryWrapper(ldcParamOrLossFormEdit, req.getParameterMap());
        // 只查询当前用户的编辑数据
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.eq("create_id", sysUser.getId());
        Page<LdcParamOrLossFormEdit> page = new Page<LdcParamOrLossFormEdit>(pageNo, pageSize);
        IPage<LdcParamOrLossFormEdit> pageList = ldcParamOrLossFormEditService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 初始化/重置编辑数据
     *
     * @return 成功：编辑数据列表   失败：错误信息
     */
    @AutoLog(value = "操作风险事件损失形态分类-初始化/重置编辑数据")
    @Operation(summary = "操作风险事件损失形态分类-初始化/重置编辑数据")
//    @RequiresPermissions("ldc.param.lossform:ldc_param_or_loss_form_edit:edit")
    @PostMapping(value = "/resetEditList")
    public Result<List<LdcParamOrLossFormEdit>> reset() {
        // service内实现了 根据操作人员id执行业务
        List<LdcParamOrLossFormEdit> editList = service.resetEditList();
        if (editList.isEmpty()) {
            return Result.error("创建草稿失败！");
        }
        return Result.OK(editList);
    }

    /**
     * 添加
     *
     * @param ldcParamOrLossFormEdit 损失形态
     * @return 是否成功
     */
    @AutoLog(value = "操作风险事件损失形态分类_编辑表-添加")
    @Operation(summary = "操作风险事件损失形态分类_编辑表-添加")
    //@RequiresPermissions("ldc.param.lossform:ldc_param_or_loss_form_edit:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody LdcParamOrLossFormEdit ldcParamOrLossFormEdit) {
        if (ldcParamOrLossFormEditService.addEdit(ldcParamOrLossFormEdit)) {
            return Result.OK("添加成功！");
        }
        return Result.error("添加失败！");
    }

    /**
     * 编辑
     *
     * @param ldcParamOrLossFormEdit 损失形态
     * @return 是否成功
     */
    @AutoLog(value = "操作风险事件损失形态分类_编辑表-编辑")
    @Operation(summary = "操作风险事件损失形态分类_编辑表-编辑")
    //@RequiresPermissions("ldc.param.lossform:ldc_param_or_loss_form_edit:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody LdcParamOrLossFormEdit ldcParamOrLossFormEdit) {
        ldcParamOrLossFormEditService.updateById(ldcParamOrLossFormEdit);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id 要删除的损失形态id
     * @return 是否成功
     */
    @AutoLog(value = "操作风险事件损失形态分类_编辑表-通过id删除")
    @Operation(summary = "操作风险事件损失形态分类_编辑表-通过id删除")
    //@RequiresPermissions("ldc.param.lossform:ldc_param_or_loss_form_edit:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        ldcParamOrLossFormEditService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids id列表
     * @return 是否成功
     */
    @AutoLog(value = "操作风险事件损失形态分类_编辑表-批量删除")
    @Operation(summary = "操作风险事件损失形态分类_编辑表-批量删除")
    //@RequiresPermissions("ldc.param.lossform:ldc_param_or_loss_form_edit:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.ldcParamOrLossFormEditService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id 查询的id
     * @return 查询结果
     */
    //@AutoLog(value = "操作风险事件损失形态分类_编辑表-通过id查询")
    @Operation(summary = "操作风险事件损失形态分类_编辑表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<LdcParamOrLossFormEdit> queryById(@RequestParam(name = "id", required = true) String id) {
        LdcParamOrLossFormEdit ldcParamOrLossFormEdit = ldcParamOrLossFormEditService.getById(id);
        if (ldcParamOrLossFormEdit == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(ldcParamOrLossFormEdit);
    }

    /**
     * 导出excel
     * TODO 根据用户id导出
     * @param request 请求参数
     * @param ldcParamOrLossFormEdit 参数
     */
    //@RequiresPermissions("ldc.param.lossform:ldc_param_or_loss_form_edit:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, LdcParamOrLossFormEdit ldcParamOrLossFormEdit) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        ldcParamOrLossFormEdit.setCreateId(sysUser.getId());
        return super.exportXls(request, ldcParamOrLossFormEdit, LdcParamOrLossFormEdit.class, "操作风险事件损失形态分类_编辑数据");
    }

    /**
     * 通过excel导入数据
     * TODO 更改导入逻辑， 保存版本号后 清空编辑表，再导入新数据（需要判断操作人员id）
     *
     * @param request
     * @param response
     * @return
     */
    //@RequiresPermissions("ldc.param.lossform:ldc_param_or_loss_form_edit:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, LdcParamOrLossFormEdit.class);
    }

}
