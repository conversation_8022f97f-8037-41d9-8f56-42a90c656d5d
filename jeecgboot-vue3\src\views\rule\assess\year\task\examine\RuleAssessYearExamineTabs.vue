<template>
  <div class="p-2">
    <a-tabs v-model:activeKey="activeKey" type="card">
      <a-tab-pane key="1" tab="待审核">
        <WaitExamine ref="waitExamineRef" />
      </a-tab-pane>

      <a-tab-pane key="2" tab="已审核">
        <HasExamine ref="hasExamineRef" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import WaitExamine from './WaitExamine.vue';
  import HasExamine from './HasExamine.vue';

  const activeKey = ref('1');
  const waitExamineRef = ref();
  const hasExamineRef = ref();

  watch(activeKey, (newValue) => {
    switch (newValue) {
      case '1':
        waitExamineRef.value.reload();
        break;
      case '2': {
        if (hasExamineRef.value) {
          hasExamineRef.value.reload();
          break;
        }
      }
    }
  });
</script>

<style scoped lang="less">
  // 清除与a-tab-pane页的间隔
  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }

  .card-container p {
    margin: 0;
  }
  .card-container > .ant-tabs-card .ant-tabs-content {
    height: 120px;
    margin-top: -16px;
  }
  .card-container > .ant-tabs-card .ant-tabs-content > .ant-tabs-tabpane {
    padding: 16px;
    background: #fff;
  }
  .card-container > .ant-tabs-card > .ant-tabs-nav::before {
    display: none;
  }
  .card-container > .ant-tabs-card .ant-tabs-tab,
  [data-theme='compact'] .card-container > .ant-tabs-card .ant-tabs-tab {
    background: transparent;
    border-color: transparent;
  }
  .card-container > .ant-tabs-card .ant-tabs-tab-active,
  [data-theme='compact'] .card-container > .ant-tabs-card .ant-tabs-tab-active {
    background: #fff;
    border-color: #fff;
  }
  #components-tabs-demo-card-top .code-box-demo {
    padding: 24px;
    overflow: hidden;
    background: #f5f5f5;
  }
  [data-theme='compact'] .card-container > .ant-tabs-card .ant-tabs-content {
    height: 120px;
    margin-top: -8px;
  }
  [data-theme='dark'] .card-container > .ant-tabs-card .ant-tabs-tab {
    background: transparent;
    border-color: transparent;
  }
  [data-theme='dark'] #components-tabs-demo-card-top .code-box-demo {
    background: #000;
  }
  [data-theme='dark'] .card-container > .ant-tabs-card .ant-tabs-content > .ant-tabs-tabpane {
    background: #141414;
  }
  [data-theme='dark'] .card-container > .ant-tabs-card .ant-tabs-tab-active {
    background: #141414;
    border-color: #141414;
  }
</style>
