package com.gientech.cm.metering.legal.controller;

import java.util.*;

import com.gientech.cm.metering.history.entity.CmMeteringLegalHistory;
import com.gientech.cm.metering.history.service.ICmMeteringLegalHistoryService;
import com.gientech.cm.rules.organization.entity.CmRulesOrganization;
import com.gientech.cm.rules.organization.service.ICmRulesOrganizationService;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import com.gientech.cm.metering.legal.entity.CmMeteringLegal;
import com.gientech.cm.metering.legal.service.ICmMeteringLegalService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 操作风险资本计量-法人口径
 * @Author: jeecg-boot
 * @Date:   2025-06-16
 * @Version: V1.0
 */
@Tag(name="操作风险资本计量-法人口径")
@RestController
@RequestMapping("/metering/cmMeteringLegal")
@Slf4j
public class CmMeteringLegalController extends JeecgController<CmMeteringLegal, ICmMeteringLegalService> {
	@Autowired
	private ICmMeteringLegalService cmMeteringLegalService;
	@Autowired
	private ICmMeteringLegalHistoryService historyService;
	@Autowired
	private ISysUserService userService;
	@Autowired
	private ICmRulesOrganizationService organizationService;
	 @Autowired
	 private IWorkflowInstanceService workflowInstanceService;
	 @Autowired
	 private IWorkflowTaskService workflowTaskService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cmMeteringLegal
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "操作风险资本计量-法人口径-分页列表查询")
	@Operation(summary="操作风险资本计量-法人口径-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CmMeteringLegal>> queryPageList(CmMeteringLegal cmMeteringLegal,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("auditVersion", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("state", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<CmMeteringLegal> queryWrapper = QueryGenerator.initQueryWrapper(cmMeteringLegal, req.getParameterMap(),customeRuleMap);
		Page<CmMeteringLegal> page = new Page<CmMeteringLegal>(pageNo, pageSize);
		IPage<CmMeteringLegal> pageList = cmMeteringLegalService.page(page, queryWrapper);
		// 查询法人的层级
		QueryWrapper<CmRulesOrganization> wrapper = new QueryWrapper<>();
		wrapper.eq("org_type", "2");
		wrapper.eq("del_flag", 0);
		wrapper.eq("org_state", "2");
		CmRulesOrganization organization = organizationService.getOne(wrapper);
		if (organization != null) {
			List<CmMeteringLegal> records = pageList.getRecords();
			for (CmMeteringLegal legal : records) {
				if (legal.getMeteringFlag().equals("2")) {
					legal.setLedgerAccount(organization.getLedgerAccount());
				}
			}

		}
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cmMeteringLegal
	 * @return
	 */
	@AutoLog(value = "操作风险资本计量-法人口径-添加")
	@Operation(summary="操作风险资本计量-法人口径-添加")
	@RequiresPermissions("metering:cm_metering_legal:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CmMeteringLegal cmMeteringLegal) {
//		cmMeteringLegalService.save(cmMeteringLegal);
//		return Result.OK("添加成功！");
		return cmMeteringLegalService.add(cmMeteringLegal);
	}
	
	/**
	 *  编辑
	 *
	 * @param cmMeteringLegal
	 * @return
	 */
	@AutoLog(value = "操作风险资本计量-法人口径-编辑")
	@Operation(summary="操作风险资本计量-法人口径-编辑")
	@RequiresPermissions("metering:cm_metering_legal:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CmMeteringLegal cmMeteringLegal) {
		cmMeteringLegalService.updateById(cmMeteringLegal);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "操作风险资本计量-法人口径-通过id删除")
	@Operation(summary="操作风险资本计量-法人口径-通过id删除")
	@RequiresPermissions("metering:cm_metering_legal:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cmMeteringLegalService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "操作风险资本计量-法人口径-批量删除")
	@Operation(summary="操作风险资本计量-法人口径-批量删除")
	@RequiresPermissions("metering:cm_metering_legal:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cmMeteringLegalService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "操作风险资本计量-法人口径-通过id查询")
	@Operation(summary="操作风险资本计量-法人口径-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CmMeteringLegal> queryById(@RequestParam(name="id",required=true) String id) {
		CmMeteringLegal cmMeteringLegal = cmMeteringLegalService.getById(id);
		if(cmMeteringLegal==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cmMeteringLegal);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cmMeteringLegal
    */
    @RequiresPermissions("metering:cm_metering_legal:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CmMeteringLegal cmMeteringLegal) {
        return super.exportXls(request, cmMeteringLegal, CmMeteringLegal.class, "操作风险资本计量-法人口径");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("metering:cm_metering_legal:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CmMeteringLegal.class);
    }


	@PostMapping(value = "getMeteringVersion")
	public Result<Map<String, Object>> getMeteringVersion(@RequestBody CmMeteringLegal cmMeteringLegal) {
		return cmMeteringLegalService.getMeteringVersion(cmMeteringLegal);
	}

	 /**
	  * 批量提交
	  * @param ids
	  * @return
	  */
	 @AutoLog(value = "操作风险资本计量-法人口径-批量提交")
	 @RequestMapping(value = "/batchSubmit", method = RequestMethod.POST)
	 @RequiresPermissions("metering:cm_metering_legal:submit")
	 public Result<String> batchSubmit(@RequestParam(name="ids",required=true) List<String> ids) throws Exception {
		 //return cmMeteringLegalService.batchSubmit(ids);
		 try {
			 return cmMeteringLegalService.batchSubmitByWorkflow(ids);
		 } catch (Exception e) {
			 log.error("提交异常！", e);
			 return Result.error("提交异常！");
		 }
	 }

	 /**
	  * 批量撤销
	  * @param ids
	  * @return
	  */
	 @AutoLog(value = "操作风险资本计量-法人口径-批量撤销")
	 @RequestMapping(value = "/batchRevoke", method = RequestMethod.POST)
	 @RequiresPermissions("metering:cm_metering_legal:revoke")
	 public Result<String> batchRevoke(@RequestParam(name="ids",required=true) List<String> ids) throws Exception {
		 //return cmMeteringLegalService.batchRevoke(ids);
		 try {
			 return cmMeteringLegalService.batchRevokeByWorkflow(ids);
		 } catch (Exception e) {
			 log.error("撤销异常！", e);
			 return Result.error("撤销异常！");
		 }
	 }

	 /**
	  * 复核/审核(未处理)页面数据查询
	  *
	  * @param cmMeteringLegal
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  * @return
	  */
	 @Operation(summary="操作风险资本计量-复核/审核(未处理)--分页列表查询")
	 @GetMapping(value = "/getAuditList")
	 public Result<IPage<CmMeteringLegal>> getAuditList(CmMeteringLegal cmMeteringLegal,
														 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
														 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
														 HttpServletRequest req) {
		 // 自定义查询规则
		 Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
		 // 自定义多选的查询规则为：LIKE_WITH_OR
		 customeRuleMap.put("auditVersion", QueryRuleEnum.LIKE_WITH_OR);
		 customeRuleMap.put("state", QueryRuleEnum.LIKE_WITH_OR);
		 QueryWrapper<CmMeteringLegal> queryWrapper = QueryGenerator.initQueryWrapper(cmMeteringLegal, req.getParameterMap(),customeRuleMap);
		 Page<CmMeteringLegal> page = new Page<CmMeteringLegal>(pageNo, pageSize);

		 // 如果是复核角色
		 LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		 // 获取人员角色
		 List<String> roleList = Arrays.asList(sysUser.getRoleCode().split(","));
		 List<String> stateList = new ArrayList<>();

		 // 查询法人的层级
		 QueryWrapper<CmRulesOrganization> wrapper = new QueryWrapper<>();
		 wrapper.eq("org_type", "2");
		 wrapper.eq("del_flag", 0);
		 wrapper.eq("org_state", "2");
		 CmRulesOrganization organization = organizationService.getOne(wrapper);

		 if (roleList.contains("admin")) {
			 stateList.add("4");
			 stateList.add("8");
			 stateList.add("5");
			 stateList.add("9");
		 } else {
			 if (roleList.contains("zh_cfzbjlfhg")) {
				 // 如果是复核角色
				 stateList.add("4");
				 stateList.add("8");
			 }
			 if (roleList.contains("zh_cfzbjlshg")) {
				 // 如果是审核角色
				 stateList.add("5");
				 stateList.add("9");
			 }
		 }
		 queryWrapper.in("state", stateList);
		 IPage<CmMeteringLegal> pageList = cmMeteringLegalService.page(page, queryWrapper);
		 // 放入上一步审核人的意见
		 List<CmMeteringLegal> records = pageList.getRecords();
		 for (CmMeteringLegal legal : records) {
			 if (organization!= null && legal.getMeteringFlag().equals("2")) {
				 legal.setLedgerAccount(organization.getLedgerAccount());
			 }
			 String state = legal.getState();
			 if (state.equals("5") || state.equals("8") || state.equals("9")) {
				 // 获取处理流程中最近一次审核记录放入
				 QueryWrapper<CmMeteringLegalHistory> queryHistoryWrapper = new QueryWrapper<>();
				 queryHistoryWrapper.eq("metering_legal_id", legal.getId());
				 queryHistoryWrapper.eq("is_audit", "1");
				 queryHistoryWrapper.orderByDesc("id");
				 List<CmMeteringLegalHistory> list = historyService.list(queryHistoryWrapper);
				 if (list != null && !list.isEmpty()) {
					 CmMeteringLegalHistory cmMeteringLegalHistory = list.get(0);
					 SysUser sysUser1 = userService.getUserByName(cmMeteringLegalHistory.getOperateUser());
					 String operateRole = cmMeteringLegalHistory.getOperateRole();
					 String operateType = cmMeteringLegalHistory.getOperateType();
					 legal.setPreviousAuditUser(operateRole + "-" + sysUser1.getRealname());
					 legal.setPreviousAuditState(operateType);
					 legal.setPreviousAuditOpinion(cmMeteringLegalHistory.getOperateOpinion());
				 }
			 }
		 }
		 pageList.setRecords(records);
		 return Result.OK(pageList);
	 }

	 /**
	  * 审核退回
	  * @param cmMeteringLegal
	  * @return
	  * @throws Exception
	  */
	 @AutoLog(value = "操作风险资本计量-法人口径-审核退回")
	 @PostMapping(value = "/auditReject")
	 @RequiresPermissions("metering:cm_metering_legal:reject")
	 public Result<String> auditReject(@RequestBody CmMeteringLegal cmMeteringLegal) throws Exception {
		 //return cmMeteringLegalService.auditReject(cmMeteringLegal);
		 String businessKey = "";
		 CmMeteringLegal legal = cmMeteringLegalService.getById(cmMeteringLegal.getId());
		 if (legal.getState().equals("8") || legal.getState().equals("9")) {
			 businessKey = "meteringRejectProcess";
		 } else {
			 businessKey = "meteringProcess";
		 }
		 legal.setAuditOpinion(cmMeteringLegal.getAuditOpinion());

		 WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, legal.getId());
		 if (workflowTask != null) {
			 Map<String, Object> executeVariables = new HashMap<>();
			 executeVariables.put("approval", false);
			 executeVariables.put(businessKey, legal);
			 workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
		 }
		 return Result.ok("审核退回成功!");
	 }

	 /**
	  * 审核通过
	  * @param cmMeteringLegal
	  * @return
	  * @throws Exception
	  */
	 @AutoLog(value = "操作风险资本计量-法人口径-审核通过")
	 @PostMapping(value = "/auditPass")
	 @RequiresPermissions("metering:cm_metering_legal:pass")
	 public Result<String> auditPass(@RequestBody CmMeteringLegal cmMeteringLegal) throws Exception {
		 //return cmMeteringLegalService.auditPass(cmMeteringLegal);
		 String businessKey = "";
		 CmMeteringLegal legal = cmMeteringLegalService.getById(cmMeteringLegal.getId());

		 if (legal.getState().equals("8") || legal.getState().equals("9")) {
			 businessKey = "meteringRejectProcess";
		 } else {
			 businessKey = "meteringProcess";
		 }
		 legal.setAuditOpinion(cmMeteringLegal.getAuditOpinion());

		 WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, legal.getId());
		 if (workflowTask != null) {
			 Map<String, Object> executeVariables = new HashMap<>();
			 executeVariables.put("approval", true);
			 executeVariables.put(businessKey, legal);
			 workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
		 }
		 return Result.ok("审核通过成功!");
	 }

	 /**
	  * 复核/审核(已处理)-页面数据查询
	  *
	  * @param cmMeteringLegal
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  * @return
	  */
	 @Operation(summary="操作风险资本计量-复核/审核(已处理)-分页列表查询")
	 @GetMapping(value = "/getAuditedList")
	 public Result<IPage<CmMeteringLegal>> getAuditedList(CmMeteringLegal cmMeteringLegal,
														@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
														@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
														HttpServletRequest req) {
		 // 自定义查询规则
		 Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
		 // 自定义多选的查询规则为：LIKE_WITH_OR
		 customeRuleMap.put("auditVersion", QueryRuleEnum.LIKE_WITH_OR);
		 customeRuleMap.put("state", QueryRuleEnum.LIKE_WITH_OR);
		 QueryWrapper<CmMeteringLegal> queryWrapper = QueryGenerator.initQueryWrapper(cmMeteringLegal, req.getParameterMap(),customeRuleMap);
		 Page<CmMeteringLegal> page = new Page<CmMeteringLegal>(pageNo, pageSize);

		 // 如果是复核角色
		 LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		 // 获取人员角色
		 List<String> roleList = Arrays.asList(sysUser.getRoleCode().split(","));
		 List<String> stateList = new ArrayList<>();

		 if (roleList.contains("admin")) {

		 } else {
			 if (roleList.contains("zh_cfzbjlfhg")) {
				 // 如果是复核角色
				 queryWrapper.exists("select 1 from cm_metering_legal_history where " +
						 "cm_metering_legal_history.metering_legal_id = cm_metering_legal.id and " +
						 "cm_metering_legal_history.state in ('5', '9', '7', '12')");
			 }
			 if (roleList.contains("zh_cfzbjlshg")) {
				 // 如果是审核角色
				 queryWrapper.exists("select 1 from cm_metering_legal_history where " +
						 "cm_metering_legal_history.metering_legal_id = cm_metering_legal.id and " +
						 "cm_metering_legal_history.state in ('6', '10', '11', '13')");
			 }
		 }
		 //queryWrapper.in("state", stateList);
		 queryWrapper.eq("is_audited", "1");
		 IPage<CmMeteringLegal> pageList = cmMeteringLegalService.page(page, queryWrapper);
		 // 放入上一步审核人的意见
		 List<CmMeteringLegal> records = pageList.getRecords();
		 for (CmMeteringLegal legal : records) {
			 // 获取处理流程中最近一次审核记录放入
			 QueryWrapper<CmMeteringLegalHistory> queryHistoryWrapper = new QueryWrapper<>();
			 queryHistoryWrapper.eq("metering_legal_id", legal.getId());
			 queryHistoryWrapper.eq("is_audit", "1");
			 queryHistoryWrapper.orderByDesc("id");
			 List<CmMeteringLegalHistory> list = historyService.list(queryHistoryWrapper);
			 if (list != null && !list.isEmpty()) {
				 CmMeteringLegalHistory cmMeteringLegalHistory = list.get(0);
				 SysUser sysUser1 = userService.getUserByName(cmMeteringLegalHistory.getOperateUser());
				 String operateRole = cmMeteringLegalHistory.getOperateRole();
				 String operateType = cmMeteringLegalHistory.getOperateType();
				 legal.setPreviousAuditUser(operateRole + "-" + sysUser1.getRealname());
				 legal.setPreviousAuditState(operateType);
				 legal.setPreviousAuditOpinion(cmMeteringLegalHistory.getOperateOpinion());
			 }
		 }
		 pageList.setRecords(records);
		 return Result.OK(pageList);
	 }

	 @Operation(summary="操作风险资本计量-法人-导出")
	 @RequiresPermissions("metering:cm_metering_legal:exportXls")
	 @RequestMapping(value = "/getExportOneUrl")
	 public void getExportOneUrl(HttpServletRequest request,
										 HttpServletResponse response,
										 CmMeteringLegal cmMeteringLegal) throws Exception {
		 cmMeteringLegalService.exportXls(request, response, cmMeteringLegal);
		 // return super.exportXls(request, cmMeteringLegal, CmMeteringLegal.class, cmMeteringLegal.getVersion());
	 }

	 @AutoLog(value = "操作风险资本计量-法人口径-测算")
	 @PostMapping(value = "/reckonMetering")
	 public Result<String> reckonMetering(@RequestBody CmMeteringLegal cmMeteringLegal) {
		 return cmMeteringLegalService.reckonMetering(cmMeteringLegal);
	 }

	 @AutoLog(value = "操作风险资本计量-集团口径-测算")
	 @PostMapping(value = "/reckonMeteringGroup")
	 public Result<String> reckonMeteringGroup(@RequestBody CmMeteringLegal cmMeteringLegal) throws Exception {
		 return cmMeteringLegalService.reckonMeteringGroup(cmMeteringLegal);
	 }

	 @Operation(summary="操作风险资本计量-集团-获取会计科目映射表")
	 @GetMapping(value = "/getGroupSubjectList")
	 public Result<List<Map<String, Object>>> getGroupSubjectList(CmMeteringLegal cmMeteringLegal,
														  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
														  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		 return cmMeteringLegalService.getGroupSubjectList(cmMeteringLegal, pageNo, pageSize);

	 }

	 @Operation(summary="操作风险资本计量-集团-导出")
	 //@RequiresPermissions("metering:cm_metering_legal:exportXls")
	 @RequestMapping(value = "/getExportGroupUrl")
	 public void getExportGroupUrl(HttpServletRequest request,
								 HttpServletResponse response,
								 CmMeteringLegal cmMeteringLegal) throws Exception {

		 cmMeteringLegalService.exportGroupXls(request, response, cmMeteringLegal);
	 }
}
