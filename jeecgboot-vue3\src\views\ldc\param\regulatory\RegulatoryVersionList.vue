<template>
  <div class="p-2">
    <!--查询区域-->
    <!-- <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
        <a-col :lg="6">
        <a-form-item name="firstLevelName">
        <template #label><span title="一级分类">一级分类</span></template>
            <JInput v-model:value="queryParam.firstLevelName"/>
        </a-form-item>
        </a-col>
        <a-col :lg="6">
        <a-form-item name="secondLevelName">
        <template #label><span title="二级分类">二级分类</span></template>
            <JInput v-model:value="queryParam.secondLevelName"/>
        </a-form-item>
        </a-col>
          <template v-if="toggleSearchStatus">
          <a-col :lg="6">
          <a-form-item name="threeLevelName">
          <template #label><span title="三级分类">三级分类</span></template>
              <JInput v-model:value="queryParam.threeLevelName"/>
          </a-form-item>
          </a-col>
            <a-col :lg="6">
              <a-form-item name="furtherExplanation">
                <template #label><span title="进一步说明或举例">进一步说</span></template>
                <a-input placeholder="请输入进一步说明或举例" v-model:value="queryParam.furtherExplanation" allow-clear ></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
               <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                 {{ toggleSearchStatus ? '收起' : '展开' }}
                 <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
               </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div> -->
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <div style="color: #262626; font-family: '黑体', sans-serif; font-weight: bold; font-size: 18px;
            display: flex; justify-content: center; align-items: center; margin-left: 42%;">
           {{title}}
        </div>
<!--        <a-button type="primary" v-auth="'ldc.param.regulatory:ldc_param_or_regulatory_version:add'"  @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>-->
        <!-- <a-button  type="primary" v-auth="'ldc.param.regulatory:ldc_param_or_regulatory_version:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button> -->
<!--        <j-upload-button  type="primary" v-auth="'ldc.param.regulatory:ldc_param_or_regulatory_version:importExcel'"  preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>-->
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
           <a-menu>
             <a-menu-item key="1" @click="batchHandleDelete">
               <Icon icon="ant-design:delete-outlined"></Icon>
               删除
             </a-menu-item>
           </a-menu>
          </template>
          <a-button v-auth="'ldc.param.regulatory:ldc_param_or_regulatory_version:deleteBatch'">批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown> -->
        <!-- 高级查询 -->
<!--        <super-query :config="superQueryConfig" @search="handleSuperQuery" />-->
      </template>
      <!--操作栏-->
      <!-- <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"/>
      </template> -->
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <RegulatoryVersionModal ref="registerModal" @success="handleSuccess"></RegulatoryVersionModal>
    <div style="margin: auto;  margin-left: 45%; margin-top: 20px;">
        <a-button type="default" @click="bankList"> < 返回   </a-button>
    </div>
  </div>
</template>

<script lang="ts" name="ldc.param.regulatory-regulatoryVersion" setup>
  import { ref, reactive } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, superQuerySchema } from './RegulatoryVersion.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './RegulatoryVersion.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import RegulatoryVersionModal from './components/RegulatoryVersionModal.vue'
  import { useUserStore } from '/@/store/modules/user';
  import JInput from "/@/components/Form/src/jeecg/components/JInput.vue";

  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const userStore = useUserStore();
  const title = ref("操作风险损失事件类型（监管分类）");
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '操作风险损失事件类型（监管分类）（正式表）',
      api: list,
      columns,
      canResize:false,
      useSearchForm: false,
      showActionColumn: false,
      showHeader: true,
      showTableSetting: false,
      showIndexColumn: true,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: async (params) => {
        return Object.assign(params, queryParam);
      },
      defSort: {
        column: ["firstLevelName", "secondLevelName"],
        order: "asc"
      },
    },
    exportConfig: {
      name: "操作风险损失事件类型（监管分类）（正式表）",
      url: getExportUrl,
      params: queryParam,
    },
	  importConfig: {
	    url: getImportUrl,
	    success: handleSuccess
	  },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] = tableContext;
  const labelCol = reactive({
    xs:24,
    sm:4,
    xl:6,
    xxl:4
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    registerModal.value.disableSubmit = false;
    registerModal.value.add();
  }
  
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    registerModal.value.disableSubmit = false;
    registerModal.value.edit(record);
  }
   
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }
   
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
   
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
   
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
   
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      // {
      //   label: '编辑',
      //   onClick: handleEdit.bind(null, record),
      //   auth: 'ldc.param.regulatory:ldc_param_or_regulatory_version:edit'
      // },
    ];
  }
   
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }, {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'ldc.param.regulatory:ldc_param_or_regulatory_version:delete'
      }
    ]
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }
  
  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }

  // 返回
  const emit = defineEmits(['reguVersionBack']);
  function bankList() {
    emit('reguVersionBack');
  }
  




</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust{
      min-width: 100px !important;
    }
    .query-group-split-cust{
      width: 30px;
      display: inline-block;
      text-align: center
    }
    .ant-form-item:not(.ant-form-item-with-help){
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),:deep(.ant-input-number){
      width: 100%;
    }
  }
</style>
