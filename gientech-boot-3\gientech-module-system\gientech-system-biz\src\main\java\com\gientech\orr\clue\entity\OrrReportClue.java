package com.gientech.orr.clue.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 操作风险报告-线索表
 * @Author: jeecg-boot
 * @Date:   2025-07-10
 * @Version: V1.0
 */
@Data
@TableName("orr_report_clue")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="操作风险报告-线索表")
public class OrrReportClue implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;

	/**主键*/
    @Schema(description = "所属报告的id")
    private java.lang.String reportId;

    /**线索编号*/
	@Excel(name = "线索编号", width = 15)
    @Schema(description = "线索编号")
    private java.lang.String clueNumber;
	/**线索名称*/
	@Excel(name = "线索名称", width = 15)
    @Schema(description = "线索名称")
    private java.lang.String clueName;
	/**所属报告年度*/
	@Excel(name = "所属报告年度", width = 15, format = "yyyy")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy")
    @DateTimeFormat(pattern="yyyy")
    @Schema(description = "所属报告年度")
    private java.util.Date reportYear;
	/**所属报告季度*/
	@Excel(name = "所属报告季度", width = 15)
    @Schema(description = "所属报告季度")
    private java.lang.String reportQuarterly;
	/**报告名称*/
	@Excel(name = "报告名称", width = 15)
    @Schema(description = "报告名称")
    private java.lang.String reportName;
    /**锚点位置id*/
    @Excel(name = "锚点位置id", width = 15)
    @Schema(description = "锚点位置id")
    private java.lang.String anchorId;
	/**报告待填写内容*/
	@Excel(name = "报告待填写内容", width = 15)
    @Schema(description = "报告待填写内容")
    private java.lang.String reportFilledContent;
	/**线索反馈内容要求*/
	@Excel(name = "线索反馈内容要求", width = 15)
    @Schema(description = "线索反馈内容要求")
    private java.lang.String clueFeedbackContent;
	/**线索反馈截止日期*/
	@Excel(name = "线索反馈截止日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "线索反馈截止日期")
    private java.util.Date clueFeedbackDeadlineDate;
	/**线索接收机构*/
	@Excel(name = "线索接收机构", width = 15)
    @Dict(dictTable ="sys_depart",dicText = "depart_name",dicCode = "org_code")
    @Schema(description = "线索接收机构")
    private java.lang.String clueReceivingInstitution;
	/**线索接收部门*/
	@Excel(name = "线索接收部门", width = 15)
    @Dict(dictTable ="sys_depart",dicText = "depart_name",dicCode = "org_code")
    @Schema(description = "线索接收部门")
    private java.lang.String clueReceivingDepartment;
	/**线索状态*/
	@Excel(name = "线索状态", width = 15, dicCode = "orr_clue_status")
	@Dict(dicCode = "orr_clue_status")
    @Schema(description = "线索状态")
    private java.lang.String clueStatus;
	/**线索反馈日期*/
	@Excel(name = "线索反馈日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "线索反馈日期")
    private java.util.Date clueFeedbackDate;
	/**线索反馈录入*/
	@Excel(name = "线索反馈录入", width = 15)
    @Schema(description = "线索反馈录入")
    private java.lang.String clueFeedbackEnter;
	/**附件Url*/
	@Excel(name = "附件Url", width = 15)
    @Schema(description = "附件Url")
    private java.lang.String attachmentUrl;
    /**附件名称*/
	@Excel(name = "附件名称", width = 15)
    @Schema(description = "附件名称")
    private java.lang.String attachmentName;
	/**删除标记*/
	@Excel(name = "删除标记", width = 15)
    @Schema(description = "删除标记")
    @TableLogic
    private java.lang.Integer delFlag;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;

    public void handlePass(String clueStatus) {
        this.clueStatus = clueStatus;
        this.clueFeedbackDate = new Date();
    }

    public void handleReturn(String clueStatus) {
        this.clueStatus = clueStatus;
        this.clueFeedbackDate = null;
    }
}
