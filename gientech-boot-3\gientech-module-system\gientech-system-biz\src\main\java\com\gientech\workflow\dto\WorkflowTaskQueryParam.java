package com.gientech.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 工作流任务查询参数DTO
 * 用于传递查询条件，支持单个和批量查询
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Data
@Accessors(chain = true)
@Schema(description = "工作流任务查询参数DTO")
public class WorkflowTaskQueryParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务键
     */
    @Schema(description = "业务键")
    private String businessKey;

    /**
     * 委托人
     */
    @Schema(description = "委托人")
    private String assignee;

    /**
     * 委托人列表（支持批量查询）
     */
    @Schema(description = "委托人列表")
    private List<String> assigneeList;

    /**
     * 委托人机构
     */
    @Schema(description = "委托人机构")
    private String assigneeOrgCode;

    /**
     * 委托人机构列表（支持批量查询）
     */
    @Schema(description = "委托人机构列表")
    private List<String> assigneeOrgCodeList;

    /**
     * 是否已完成
     */
    @Schema(description = "是否已完成")
    private Boolean isComplete;

    /**
     * 任务状态列表
     */
    @Schema(description = "任务状态列表")
    private List<String> taskStatusList;

    /**
     * 实例状态列表
     */
    @Schema(description = "实例状态列表")
    private List<String> instanceStatusList;

    /**
     * 是否只返回业务ID列表
     */
    @Schema(description = "是否只返回业务ID列表")
    private Boolean onlyBusinessIds = false;

    /**
     * 分页页码
     */
    @Schema(description = "分页页码")
    private Integer pageNo;

    /**
     * 分页大小
     */
    @Schema(description = "分页大小")
    private Integer pageSize;
}
