package com.gientech.rcsa.task.service;

import com.gientech.rcsa.task.entity.RcsaTaskManage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gientech.rcsa.task.vo.RcsaTaskExportAllVo;
import com.gientech.rcsa.task.vo.RcsaTaskExportColumnVo;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: RCSA评估任务表
 * @Author: jeecg-boot
 * @Date:   2025-07-17
 * @Version: V1.0
 */
public interface IRcsaTaskManageService extends IService<RcsaTaskManage> {

    Result<String> submit(RcsaTaskManage taskManage);

    void auditPass(RcsaTaskManage taskManage);

    void auditReject(RcsaTaskManage taskManage);

    void exportOneXls(HttpServletRequest request, HttpServletResponse response,
                      RcsaTaskExportColumnVo columnVo) throws Exception;

    void exportAllUrl(HttpServletRequest request, HttpServletResponse response,
                      RcsaTaskExportAllVo taskInfo) throws Exception;

    Result<String> submitByWorkflow(RcsaTaskManage taskManage);
}
