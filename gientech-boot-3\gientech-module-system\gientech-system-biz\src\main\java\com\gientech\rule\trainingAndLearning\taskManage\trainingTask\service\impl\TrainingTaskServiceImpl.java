package com.gientech.rule.trainingAndLearning.taskManage.trainingTask.service.impl;

import com.gientech.rule.trainingAndLearning.Examination.entity.Examination;
import com.gientech.rule.trainingAndLearning.Examination.mapper.ExaminationMapper;
import com.gientech.rule.trainingAndLearning.taskManage.trainingTask.entity.TrainingTask;
import com.gientech.rule.trainingAndLearning.taskManage.trainingTask.mapper.TrainingTaskMapper;
import com.gientech.rule.trainingAndLearning.taskManage.trainingTask.service.ITrainingTaskService;
import com.gientech.rule.trainingAndLearning.taskManage.trainingTask.dto.TrainingTaskRequestDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.DictModel;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 制度培训任务管理
 * @Author: jeecg-boot
 * @Date: 2025-01-XX
 * @Version: V1.0
 */
@Slf4j
@Service
public class TrainingTaskServiceImpl extends ServiceImpl<TrainingTaskMapper, TrainingTask> implements ITrainingTaskService {

    @Autowired
    private TrainingTaskMapper trainingTaskMapper;
    @Autowired
    private ExaminationMapper examinationMapper;
    @Autowired
    private ISysBaseAPI sysBaseAPI;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String saveTrainingTask(TrainingTaskRequestDTO requestDTO) {
        TrainingTask trainingTask = convertToEntity(requestDTO);
        // 设置任务状态为未派发
        trainingTask.setTaskStatus("2");
        // 获取当前用户信息
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        DictModel institutions = sysBaseAPI.getParentDepartId(loginUser.getOrgId());
        trainingTask.setInstitutions(institutions.getText());
        trainingTask.setDept(loginUser.getOrgId());
        // 生成任务编号
        String taskNum = generateTaskNum();
        trainingTask.setTaskNum(taskNum);
        // 保存任务
        this.save(trainingTask);
        return trainingTask.getId();
    }

    @Override
    public void updateTrainingTask(TrainingTaskRequestDTO requestDTO) {
        TrainingTask trainingTask = convertToEntity(requestDTO);
        this.updateById(trainingTask);
    }

    @Override
    public void publishTasks(List<String> ids) {
        // 批量更新任务状态为未派发
        for (String id : ids) {
            TrainingTask task = this.getById(id);
            if (task != null && "1".equals(task.getTaskStatus())) {
                task.setTaskStatus("2"); // 未派发
                this.updateById(task);
            }
        }
    }

    @Override
    public void assignTask(String taskId, Object assignData) {
        // 实现任务转派逻辑
        TrainingTask originalTask = this.getById(taskId);
        if (originalTask == null) {
            throw new RuntimeException("原任务不存在");
        }
        // 解析转派数据
        Map<String, Object> assignMap = (Map<String, Object>) assignData;
        List<String> assignDepartments = (List<String>) assignMap.get("assignDepartments");
        List<String> assignInstitutions = (List<String>) assignMap.get("assignInstitutions");
        List<Map<String, Object>> assignUsers = (List<Map<String, Object>>) assignMap.get("assignUsers");
        String assignRemark = (String) assignMap.get("assignRemark");
        // 判断是转派给部门还是转派给用户
        boolean hasUsers = assignUsers != null && !assignUsers.isEmpty();
        boolean hasDepartments = assignDepartments != null && !assignDepartments.isEmpty();
        if (!hasUsers && !hasDepartments) {
            throw new RuntimeException("转派部门或转派用户不能都为空");
        }
        if (hasUsers && hasDepartments) {
            throw new RuntimeException("不能同时选择转派部门和转派用户");
        }
        if (hasUsers) {
            // 转派给用户：在rule_training_examination表中创建记录
            log.info("转派给用户，用户数量: {}", assignUsers.size());
            for (Map<String, Object> userMap : assignUsers) {
                String userId = (String) userMap.get("userId");
                String username = (String) userMap.get("username");
                String realname = (String) userMap.get("realname");
                Examination examination = convertToTrainingExamination(
                    originalTask, userId, realname, username);
                examination.setCreateBy(username);
                if(!"".equals(originalTask.getOldId()) && originalTask.getOldId() != null){
                    examination.setOldId(originalTask.getOldId());
                } else {
                    examination.setOldId(originalTask.getId());
                }
                examinationMapper.insert(examination);
            }
        } else {
            // 转派给部门：在rule_training_task表中创建记录
            log.info("转派给部门，部门数量: {}", assignDepartments.size());
            for (String departmentId : assignDepartments) {

                TrainingTask newTask = copyTaskForAssign(originalTask, departmentId, assignInstitutions);
                if(!"".equals(originalTask.getOldId()) && originalTask.getOldId() != null){
                    newTask.setOldId(originalTask.getOldId());
                } else {
                    newTask.setOldId(originalTask.getId());
                }
                newTask.setTaskStatus("3");//未完成转派
                // 保存新任务
                this.save(newTask);
                log.info("任务转派成功，原任务ID: {}, 新任务ID: {}, 转派部门: {}",
                        taskId, newTask.getId(), departmentId);
            }
        }
        originalTask.setTaskStatus("3");//未完成转派
        originalTask.setUpdateTime(new Date());
        this.updateById(originalTask);
        log.info("任务转派完成，原任务ID: {}, 转派类型: {}",
                taskId, hasUsers ? "用户转派" : "部门转派");
    }

    @Override
    public void recallTask(String taskId, String recallReason) {
        // 实现任务撤回逻辑
        TrainingTask task = this.getById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }
        // 检查任务状态，只有已发布的任务才能撤回
        /*if (!"3".equals(task.getTaskStatus())) {
            throw new RuntimeException("只有已发布的任务才能撤回");
        }*/
        // 更新任务状态为撤回状态
        task.setTaskStatus("4"); // 4表示已撤回状态
        task.setUpdateTime(new Date());
        // 设置撤回原因
        task.setReason(recallReason);
        this.updateById(task);
        log.info("任务撤回成功，任务ID: {}, 撤回原因: {}", taskId, recallReason);
    }

    @Override
    public void closeTask(String taskId, String recallReason) {
        // 实现任务撤回逻辑
        TrainingTask task = this.getById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }
        // 检查任务状态，只有代办任务才能关闭
        /*if (!"3".equals(task.getTaskStatus())) {
            throw new RuntimeException("只有代办的任务才能关闭");
        }*/
        // 更新任务状态为关闭状态
        task.setTaskStatus("7"); // 7表示已关闭状态
        task.setUpdateTime(new Date());
        // 设置关闭原因
        task.setReason(recallReason);
        this.updateById(task);
        log.info("任务关闭成功，任务ID: {}, 关闭原因: {}", taskId, recallReason);
    }


    /**
     * 生成任务编号
     * @return
     */
    public String generateTaskNum() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String yearMonth = sdf.format(new Date());
        // 查询当月最大编号
        String maxNum = trainingTaskMapper.getMaxTaskNumByYearMonth(yearMonth);
        int sequence = 1;
        if (maxNum != null && maxNum.length() >= 4) {
            String seqStr = maxNum.substring(maxNum.length() - 4);
            sequence = Integer.parseInt(seqStr) + 1;
        }
        return "ZDPX" + yearMonth + String.format("%04d", sequence);
    }

    /**
     * 将DTO转换为实体类
     * @param requestDTO
     * @return
     */
    private TrainingTask convertToEntity(TrainingTaskRequestDTO requestDTO) {
        TrainingTask trainingTask = new TrainingTask();
        trainingTask.setId(requestDTO.getId());
        trainingTask.setTaskNum(requestDTO.getTaskNum());
        trainingTask.setTrainingName(requestDTO.getTrainingName());
        trainingTask.setTrainingType(requestDTO.getTrainingType());
        trainingTask.setRegulationName(requestDTO.getRegulationName());
        trainingTask.setRegulationId(requestDTO.getRegulationId());
        trainingTask.setTrainingForm(requestDTO.getTrainingForm());
        trainingTask.setTaskStatus(requestDTO.getTaskStatus());
        trainingTask.setTrainingScope(requestDTO.getTrainingScope());
        trainingTask.setTrainingTime(requestDTO.getTrainingTime());
        trainingTask.setParticipants(requestDTO.getParticipants());
        trainingTask.setMinTrainingDuration(requestDTO.getMinTrainingDuration());
        trainingTask.setMaterialType(requestDTO.getMaterialType());
        trainingTask.setNeedExam(requestDTO.getNeedExam());
        trainingTask.setExamDuration(requestDTO.getExamDuration());
        trainingTask.setPassScore(requestDTO.getPassScore());

        // 转换考题配置为JSON字符串
        if (requestDTO.getQuestionConfigs() != null && !requestDTO.getQuestionConfigs().isEmpty()) {
            try {
                String questionConfigsJson = objectMapper.writeValueAsString(requestDTO.getQuestionConfigs());
                trainingTask.setQuestionConfigs(questionConfigsJson);
            } catch (JsonProcessingException e) {
                throw new RuntimeException("考题配置JSON转换失败", e);
            }
        }
        return trainingTask;
    }

    /**
     * 复制任务用于转派
     * @param originalTask 原任务
     * @param departmentId 转派部门ID
     * @param assignInstitutions 转派机构列表
     * @return 新任务对象
     */
    private TrainingTask copyTaskForAssign(TrainingTask originalTask, String departmentId, List<String> assignInstitutions) {
        TrainingTask newTask = new TrainingTask();
        // 复制所有业务字段
        newTask.setTaskNum(originalTask.getTaskNum());
        newTask.setTrainingName(originalTask.getTrainingName());
        newTask.setTrainingType(originalTask.getTrainingType());
        newTask.setRegulationName(originalTask.getRegulationName());
        newTask.setRegulationId(originalTask.getRegulationId());
        newTask.setTrainingForm(originalTask.getTrainingForm());
        newTask.setTrainingScope(originalTask.getTrainingScope());
        newTask.setTrainingTime(originalTask.getTrainingTime());
        newTask.setParticipants(originalTask.getParticipants());
        newTask.setMinTrainingDuration(originalTask.getMinTrainingDuration());
        newTask.setMaterialType(originalTask.getMaterialType());
        newTask.setNeedExam(originalTask.getNeedExam());
        newTask.setExamDuration(originalTask.getExamDuration());
        newTask.setQuestionConfigs(originalTask.getQuestionConfigs());
        newTask.setPassScore(originalTask.getPassScore());
        // 设置转派相关信息
        newTask.setSysOrgCode(departmentId); // 设置为转派的部门
        newTask.setDept(departmentId);
        // 设置机构信息
        if (assignInstitutions != null && !assignInstitutions.isEmpty()) {
            // 使用转派指定的机构
            newTask.setInstitutions(assignInstitutions.get(0));
        } else {
            // 使用原任务的机构
            newTask.setInstitutions(originalTask.getInstitutions());
        }
        // 设置oldId为原任务ID，用于查询原任务的题目
        newTask.setOldId(originalTask.getId());
        // 设置任务状态为已发布
        newTask.setTaskStatus("3");//未完成转派
        // 设置创建信息
        Date currentTime = new Date();
        newTask.setCreateTime(currentTime);
        newTask.setUpdateTime(currentTime);
        // 清空ID，让数据库自动生成新ID
        newTask.setId(null);
        log.debug("复制任务完成，原任务: {}, 转派部门: {}", originalTask.getId(), departmentId);
        return newTask;
    }

    /**
     * 将TrainingTask转换为TrainingExamination
     * @param trainingTask 培训任务
     * @param assignedUserId 转派用户ID
     * @param assignedUserName 转派用户名
     * @param assignedUsername 转派用户账号
     * @return TrainingExamination对象
     */
    public Examination convertToTrainingExamination(TrainingTask trainingTask,
                                                           String assignedUserId,
                                                           String assignedUserName,
                                                           String assignedUsername) {
        if (trainingTask == null) {
            throw new IllegalArgumentException("培训任务不能为空");
        }
        Examination examination = new Examination();
        // 复制培训任务的基本信息
        examination.setTaskNum(trainingTask.getTaskNum());
        examination.setTrainingName(trainingTask.getTrainingName());
        examination.setTrainingType(trainingTask.getTrainingType());
        examination.setRegulationName(trainingTask.getRegulationName());
        examination.setRegulationId(trainingTask.getRegulationId());
        examination.setTrainingForm(trainingTask.getTrainingForm());
        examination.setTrainingScope(trainingTask.getTrainingScope());
        examination.setTrainingTime(trainingTask.getTrainingTime());
        examination.setParticipants(trainingTask.getParticipants());
        examination.setMinTrainingDuration(trainingTask.getMinTrainingDuration());
        examination.setMaterialType(trainingTask.getMaterialType());
        examination.setNeedExam(trainingTask.getNeedExam());
        examination.setExamDuration(trainingTask.getExamDuration());
        examination.setQuestionConfigs(trainingTask.getQuestionConfigs());
        examination.setPassScore(trainingTask.getPassScore());
        examination.setSysOrgCode(trainingTask.getSysOrgCode());
        // 设置考试状态为待考试
        examination.setExamStatus("1"); // 1-待考试, 2-考试中, 3-已完成, 4-已过期
        log.info("TrainingTask转换为TrainingExamination完成，原任务ID: {}, 转派用户: {}",
                trainingTask.getId(), assignedUserName);
        return examination;
    }
}