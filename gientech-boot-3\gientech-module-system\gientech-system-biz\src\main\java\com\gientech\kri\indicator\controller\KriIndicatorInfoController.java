package com.gientech.kri.indicator.controller;

import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.common.process.enitity.CommonProcess;
import com.gientech.common.process.service.ICommonProcessService;
import com.gientech.kri.indicator.entity.KriIndicatorInfo;
import com.gientech.kri.indicator.entity.KriIndicatorInfoThreshold;
import com.gientech.kri.indicator.service.IKriIndicatorInfoService;
import com.gientech.kri.indicator.service.IKriIndicatorInfoThresholdService;
import com.gientech.kri.indicator.service.impl.KriIndicatorInfoThresholdServiceImpl;
import com.gientech.kri.indicator.vo.KriIndicatorInfoVo;
import com.gientech.kri.querty.entity.KriQueryIndicator;
import com.gientech.kri.querty.mapper.KriQueryIndicatorMapper;
import com.gientech.workflow.dto.WorkflowTaskQueryParam;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 关键风险指控-指标设置
 * @Author: jeecg-boot
 * @Date: 2025-05-21
 * @Version: V1.0
 */
@Tag(name = "关键风险指控-指标设置")
@RestController
@RequestMapping("/kri/indicator/kriIndicatorInfo")
@Slf4j
public class KriIndicatorInfoController extends JeecgController<KriIndicatorInfo, IKriIndicatorInfoService> {

    @Autowired
    private IKriIndicatorInfoService kriIndicatorInfoService;

    @Autowired
    private IKriIndicatorInfoThresholdService kriIndicatorInfoThresholdService;

    // 工作流
    @Autowired
    private IWorkflowInstanceService workflowInstanceService;

    @Autowired
    private IWorkflowTaskService workflowTaskService;

    private final String businessKey = "kriIndicator";
    // 启用/停用操作类型
    private final String operateTypeKey = "operateType";
    private final String ENABLE = "0";
    private final String DEACTIVATE = "1";

    // 处理过程
    @Autowired
    private ICommonProcessService processService;

    @Autowired
    private KriQueryIndicatorMapper kriQueryIndicatorMapper;

    @Autowired
    private KriIndicatorInfoThresholdServiceImpl kriIndicatorInfoThresholdServiceImpl;

    /**
     * 指标查询
     *
     * @param kriIndicatorInfo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "关键风险指控-指标设置-指标查询")
    @Operation(summary = "关键风险指控-指标设置-指标查询")
    @GetMapping(value = "/listQuery")
    public Result<IPage<KriIndicatorInfo>> listQuery(KriIndicatorInfo kriIndicatorInfo,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                     HttpServletRequest req) {
        Map<String, String[]> mapcollect = req.getParameterMap();
        String preid = null;
        if (mapcollect.containsKey("collect")) {
            preid = mapcollect.get("collect")[0];
        }
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("monitoringFrequency", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("indicatorType", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("state", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<KriIndicatorInfo> queryWrapper = QueryGenerator.initQueryWrapper(kriIndicatorInfo, req.getParameterMap(), customeRuleMap);
        // 加入查询权限
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.likeRight("sys_org_code", sysUser.getOrgCode());
        queryWrapper.eq("state", "5");
        // 联表查询
        if (preid != null) {
            if ("1".equals(preid)) {
                // preid=1时，查询关联的所有数据
                queryWrapper.inSql("id", "SELECT indicator_id FROM kri_query_indicator WHERE name = '" + sysUser.getId() + "'");
            } else if ("2".equals(preid)) {
                // preid=2时，查询非关联的所有数据
                queryWrapper.notInSql("id", "SELECT indicator_id FROM kri_query_indicator WHERE name = '" + sysUser.getId() + "'");
            }
        }
        Page<KriIndicatorInfo> page = new Page<KriIndicatorInfo>(pageNo, pageSize);
        IPage<KriIndicatorInfo> pageList = kriIndicatorInfoService.page(page, queryWrapper);
        for (KriIndicatorInfo info : pageList.getRecords()) {
            Map map = new HashMap();
            map.put("indicator_id", info.getId());
            map.put("name", sysUser.getId());
            List<KriQueryIndicator> kriQueryIndicator = kriQueryIndicatorMapper.selectByMap(map);
            // 这里可以根据具体需求修改赋值内容
            if (kriQueryIndicator.size() > 0) {
                info.setCollect("1");//已关注
            } else {
                info.setCollect("2");//未关注
            }
        }
        return Result.OK(pageList);
    }


    /**
     * 批量关注
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "关键风险指控-指标设置-关注")
    @Operation(summary = "关键风险指控-指标设置-关注")
    @PostMapping(value = "/batchCollect")
    public Result<String> batchCollect(@RequestParam(name = "ids", required = true) String ids) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> list = Arrays.asList(ids.split(","));
        for (String str : list) {
            Map map = new HashMap();
            map.put("indicator_id", str);
            map.put("name", sysUser.getId());
            List<KriQueryIndicator> kriQueryIndicators = kriQueryIndicatorMapper.selectByMap(map);
            // 这里可以根据具体需求修改赋值内容
            if (kriQueryIndicators.size() > 0) {
                kriQueryIndicatorMapper.deleteById(kriQueryIndicators.get(0).getId());
            } else {
                KriQueryIndicator k = new KriQueryIndicator();
                k.setIndicatorId(str);
                k.setName(sysUser.getId());
                kriQueryIndicatorMapper.insert(k);
            }
        }
        return Result.OK("关注成功!");
    }

    /**
     * 分页列表查询
     *
     * @param kriIndicatorInfo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "关键风险指控-指标设置-分页列表查询")
    @Operation(summary = "关键风险指控-指标设置-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<KriIndicatorInfo>> queryPageList(KriIndicatorInfo kriIndicatorInfo,
                                                         @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                         HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("monitoringFrequency", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("indicatorType", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("state", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<KriIndicatorInfo> queryWrapper = QueryGenerator.initQueryWrapper(kriIndicatorInfo, req.getParameterMap(), customeRuleMap);

        // 加入查询权限
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.likeRight("sys_org_code", sysUser.getOrgCode());

//		queryWrapper.select("DISTINCT ON (indicator_number) *");
//		queryWrapper.orderByAsc("sys_org_code");
//		queryWrapper.eq("del_flag", 0);

        Page<KriIndicatorInfo> page = new Page<KriIndicatorInfo>(pageNo, pageSize);
        IPage<KriIndicatorInfo> pageList = kriIndicatorInfoService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 分页提炼列表查询
     *
     * @param kriIndicatorInfo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "关键风险指控-指标设置-分页列表查询")
    @Operation(summary = "关键风险指控-指标设置-分页提炼列表查询")
    @GetMapping(value = "/refineList")
    public Result<IPage<KriIndicatorInfoVo>> refineList(KriIndicatorInfo kriIndicatorInfo,
                                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                        HttpServletRequest req) {

        Page<KriIndicatorInfoVo> voPage = new Page<>(pageNo, pageSize);

        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("monitoringFrequency", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("indicatorType", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("state", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<KriIndicatorInfo> queryWrapper = QueryGenerator.initQueryWrapper(kriIndicatorInfo, req.getParameterMap(), customeRuleMap);

        // 加入查询权限
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.likeRight("sys_org_code", sysUser.getOrgCode());

        Page<KriIndicatorInfo> page = new Page<KriIndicatorInfo>(pageNo, pageSize);
        IPage<KriIndicatorInfo> pageList = kriIndicatorInfoService.page(page, queryWrapper);

        if (pageList.getRecords().size() <= 0) {
            return Result.ok(voPage);
        }

        List<KriIndicatorInfo> kriIndicatorInfos = pageList.getRecords();
        List<String> idList = kriIndicatorInfos.stream()
                .map(KriIndicatorInfo::getId)
                .collect(Collectors.toList());

        // 查询子表信息
        QueryWrapper<KriIndicatorInfoThreshold> thresholdQueryWrapper = new QueryWrapper<>();
        thresholdQueryWrapper.eq("refine_indicator_name", "0");
        thresholdQueryWrapper.in("info_id", idList);

        List<KriIndicatorInfoThreshold> kriIndicatorInfoThresholdList = kriIndicatorInfoThresholdService.list(thresholdQueryWrapper);

        // 数据合并
        Map<String, List<KriIndicatorInfoThreshold>> thresholdMap = kriIndicatorInfoThresholdList.stream()
                .collect(Collectors.groupingBy(KriIndicatorInfoThreshold::getInfoId));

        // 2. 转换主表数据为Vo列表
        List<KriIndicatorInfoVo> voList = kriIndicatorInfos.stream().map(info -> {
            // 3. 创建Vo对象并复制属性
            KriIndicatorInfoVo vo = new KriIndicatorInfoVo();
            BeanUtils.copyProperties(info, vo);

            // 4. 查找并设置对应的阈值数据
            List<KriIndicatorInfoThreshold> thresholds = thresholdMap.get(info.getId());
            if (thresholds != null) {
                vo.setThresholdList(thresholds);
            } else {
                vo.setThresholdList(Collections.emptyList());
            }

            return vo;
        }).collect(Collectors.toList());

        // 创建新的分页对象
        voPage.setRecords(voList);
        voPage.setTotal(pageList.getTotal());
        voPage.setCurrent(pageList.getCurrent());
        voPage.setSize(pageList.getSize());

        return Result.OK(voPage);
    }

    /**
     * 添加
     *
     * @param kriIndicatorInfo
     * @return
     */
    @AutoLog(value = "关键风险指控-指标设置-添加")
    @Operation(summary = "关键风险指控-指标设置-添加")
    @RequiresPermissions("kri.indicator:kri_indicator_info:add")
    @PostMapping(value = "/addEdit")
    public Result<?> addEdit(@RequestBody KriIndicatorInfo kriIndicatorInfo) {
        String id = kriIndicatorInfo.getId();
        if (StringUtils.isEmpty(id)) {
            return kriIndicatorInfoService.add(kriIndicatorInfo);
        } else {
            return kriIndicatorInfoService.edit(kriIndicatorInfo);
        }
    }

    /**
     * 编辑
     *
     * @param kriIndicatorInfo
     * @return
     */
    @AutoLog(value = "关键风险指控-指标设置-编辑")
    @Operation(summary = "关键风险指控-指标设置-编辑")
    @RequiresPermissions("kri.indicator:kri_indicator_info:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody KriIndicatorInfo kriIndicatorInfo) {
        kriIndicatorInfoService.updateById(kriIndicatorInfo);
        return Result.OK("编辑成功!");
    }

    /**
     * 分页列表查询
     *
     * @param kriIndicatorInfo 查询参数
     * @param pageNo 分页码
     * @param pageSize 分页大小
     * @param req 请求参数
     * @return 结果列表
     */
    @Operation(summary = "关键风险指控-指标启停复核/审核-分页列表查询")
    @GetMapping(value = "/examineList")
    public Result<IPage<KriIndicatorInfo>> examineList(KriIndicatorInfo kriIndicatorInfo,
                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                       HttpServletRequest req) {
        QueryWrapper<KriIndicatorInfo> queryWrapper = QueryGenerator.initQueryWrapper(kriIndicatorInfo, req.getParameterMap());
        // 加入查询权限
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.likeRight("monitoring_department", sysUser.getOrgCode());
        boolean isComplete = Boolean.parseBoolean(req.getParameter("isComplete"));
        // 构建委托人和机构列表，使用优化的批量查询
        List<String> assigneeList = Arrays.stream(sysUser.getRoleCode().split(",")).collect(Collectors.toList());

        List<String> idList = new ArrayList<>();
        if (!assigneeList.isEmpty()) {
            // 使用优化的批量查询方法
            WorkflowTaskQueryParam param = new WorkflowTaskQueryParam()
                    .setBusinessKey(businessKey)
                    .setAssigneeList(assigneeList)
                    .setAssigneeOrgCode(sysUser.getOrgCode())
                    .setIsComplete(isComplete);
            idList = workflowTaskService.getWorkflowTaskBusinessIdListOptimized(param);
        }
        Page<KriIndicatorInfo> page = new Page<KriIndicatorInfo>(pageNo, pageSize);
        if (idList.isEmpty()) {
            return Result.OK(page);
        }
        queryWrapper.in("id", idList.toArray());
        IPage<KriIndicatorInfo> pageList = kriIndicatorInfoService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 启用
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "关键风险指控-指标设置-启用")
    @Operation(summary = "关键风险指控-指标设置-启用")
//	@RequiresPermissions("kri.indicator:kri_indicator_info:batchEnable")
    @RequestMapping(value = "/batchEnable", method = {RequestMethod.POST})
    public Result<String> batchEnable(@RequestParam("ids") String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<KriIndicatorInfo> infoList = service.listByIds(idList);
        for (KriIndicatorInfo kriIndicator : infoList) {
            // 创建指标启用工作流
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, kriIndicator);
            variables.put(operateTypeKey, ENABLE);
            workflowInstanceService.createWorkflowInstance(businessKey, kriIndicator.getId(), variables);
        }
        processService.saveProcessBatch(businessKey, idList, "指标启用");
        return Result.ok("启用成功，请等待审核");
    }

    /**
     * 停用
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "关键风险指控-指标设置-停用")
    @Operation(summary = "关键风险指控-指标设置-停用")
//	@RequiresPermissions("kri.indicator:kri_indicator_info:batchDeactivate")
    @RequestMapping(value = "/batchDeactivate", method = {RequestMethod.POST})
    public Result<String> batchDeactivate(@RequestParam("ids") String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<KriIndicatorInfo> infoList = service.listByIds(idList);
        for (KriIndicatorInfo kriIndicator : infoList) {
            // 创建指标启用工作流
            Map<String, Object> variables = new HashMap<>();
            variables.put(businessKey, kriIndicator);
            variables.put(operateTypeKey, DEACTIVATE);
            workflowInstanceService.createWorkflowInstance(businessKey, kriIndicator.getId(), variables);
        }
        processService.saveProcessBatch(businessKey, idList, "指标停用");
        return Result.ok("停用成功，请等待审核");
    }

    /**
     * 审核通过
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "关键风险指控-指标设置-审核通过")
    @Operation(summary = "关键风险指控-指标设置-审核通过")
//	@RequiresPermissions("kri.indicator:kri_indicator_info:passRequest")
    @RequestMapping(value = "/passRequest", method = {RequestMethod.POST})
    public Result<String> passRequest(@RequestParam("ids") String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<KriIndicatorInfo> infoList = service.listByIds(idList);
        for (KriIndicatorInfo kriIndicator : infoList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, kriIndicator.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approval", true);
                executeVariables.put(businessKey, kriIndicator);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }
        processService.saveProcessBatch(businessKey, idList, "复核/审核通过");
        return Result.ok("审核通过成功");
    }

    /**
     * 审核退回
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "关键风险指控-指标设置-审核退回")
    @Operation(summary = "关键风险指控-指标设置-审核退回")
//	@RequiresPermissions("kri.indicator:kri_indicator_info:passRequest")
    @RequestMapping(value = "/givebackRequest", method = {RequestMethod.POST})
    public Result<String> givebackRequest(@RequestParam("ids") String ids) {
        List<String> idList = Arrays.stream(ids.split(",")).toList();
        List<KriIndicatorInfo> infoList = service.listByIds(idList);
        for (KriIndicatorInfo kriIndicator : infoList) {
            WorkflowTask workflowTask = workflowTaskService.getWorkflowTask(businessKey, kriIndicator.getId());
            if (workflowTask != null) {
                Map<String, Object> executeVariables = new HashMap<>();
                executeVariables.put("approval", false);
                executeVariables.put(businessKey, kriIndicator);
                workflowTaskService.completeTask(workflowTask.getId(), executeVariables);
            }
        }
        processService.saveProcessBatch(businessKey, idList, "复核/审核退回");
        return Result.ok("审核退回成功");
    }

    /**
     * 处理过程
     *
     * @param id 数据录入对象id
     * @return 处理过程列表
     */
    @AutoLog(value = "处理过程")
    @Operation(summary = "处理过程")
    @GetMapping(value = "/process")
    @RequiresPermissions("kri:input:process")
    public Result<IPage<CommonProcess>> process(@RequestParam(name = "id", required = true) String id) {
        LambdaQueryWrapper<CommonProcess> processQueryWrapper = new LambdaQueryWrapper<>();
        processQueryWrapper.eq(CommonProcess::getBusinessKey, businessKey);
        processQueryWrapper.eq(CommonProcess::getBusinessId, id);
        processQueryWrapper.orderByAsc(CommonProcess::getCreateTime);
        List<CommonProcess> list = processService.list(processQueryWrapper);

        // 借用分页的字典翻译
        IPage<CommonProcess> page = new Page<>();
        page.setRecords(list);
        return Result.ok(page);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "关键风险指控-指标设置-通过id删除")
    @Operation(summary = "关键风险指控-指标设置-通过id删除")
    @RequiresPermissions("kri.indicator:kri_indicator_info:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        kriIndicatorInfoService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "关键风险指控-指标设置-批量删除")
    @Operation(summary = "关键风险指控-指标设置-批量删除")
    @RequiresPermissions("kri.indicator:kri_indicator_info:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.kriIndicatorInfoService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "关键风险指控-指标设置-通过id查询")
    @Operation(summary = "关键风险指控-指标设置-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<KriIndicatorInfoVo> queryById(@RequestParam(name = "id", required = true) String id) {
        KriIndicatorInfoVo kriIndicatorInfoVo = kriIndicatorInfoService.queryById(id);
        if (kriIndicatorInfoVo == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(kriIndicatorInfoVo);
    }

    /**
     * 调整（将数据复制一份）
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "关键风险指控-指标设置-通过id查询")
    @Operation(summary = "关键风险指控-指标设置-调整")
    @GetMapping(value = "/toAdjust")
    public Result<KriIndicatorInfoVo> toAdjust(@RequestParam(name = "id", required = true) String id) {
        KriIndicatorInfoVo kriIndicatorInfoVo = kriIndicatorInfoService.toAdjust(id, "1", "");
        return Result.OK(kriIndicatorInfoVo);
    }


    /**
     * 批量承接
     *
     * @param ids
     * @return
     */
    //@AutoLog(value = "关键风险指控-指标设置-通过id查询")
    @Operation(summary = "关键风险指控-指标设置-批量承接")
    @PostMapping(value = "/batchUndertake")
    public Result<String> batchUndertake(@RequestParam(name = "ids", required = true) List<String> ids) {
        return kriIndicatorInfoService.batchUndertake(ids);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param kriIndicatorInfo
     */
    @RequiresPermissions("kri.indicator:kri_indicator_info:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, KriIndicatorInfo kriIndicatorInfo) {
        return super.exportXls(request, kriIndicatorInfo, KriIndicatorInfo.class, "关键风险指控-指标设置");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("kri.indicator:kri_indicator_info:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, KriIndicatorInfo.class);
    }

}
