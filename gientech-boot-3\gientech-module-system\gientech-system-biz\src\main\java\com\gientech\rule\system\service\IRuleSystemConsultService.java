package com.gientech.rule.system.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gientech.rule.system.entity.RuleSystemConsult;
import com.gientech.rule.system.vo.RuleSystemConsultFeedbackVO;
import com.gientech.rule.system.vo.RuleSystemConsultVO;
import org.apache.ibatis.annotations.Param;

/**
 * 内外规模块-制度咨询表
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-21
 */
public interface IRuleSystemConsultService extends IService<RuleSystemConsult> {

    /**
     * 查询制度咨询信息（分页）
     *
     * @param page 分页
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    Page<RuleSystemConsultVO> selectVOPage(
            Page<RuleSystemConsultVO> page,
            @Param(Constants.WRAPPER) Wrapper<RuleSystemConsultVO> queryWrapper
    );

    /**
     * 新建咨询新
     *
     * @param ruleSystemConsultVO 主子结构数据（包含咨询信息）
     * @return 是否成功
     */
    boolean saveVO(RuleSystemConsultVO ruleSystemConsultVO);

    /**
     * 保存咨询信息
     *
     * @param ruleSystemConsultVO 主子结构数据（包含咨询信息）
     * @return 是否成功
     */
    boolean updateVO(RuleSystemConsultVO ruleSystemConsultVO);

    /**
     * 查询制度咨询信息-反馈（分页）
     *
     * @param page 分页
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    Page<RuleSystemConsultFeedbackVO> selectFeedbackVOPage(
            Page<RuleSystemConsultFeedbackVO> page,
            @Param(Constants.WRAPPER) Wrapper<RuleSystemConsultFeedbackVO> queryWrapper
    );

    /**
     * 保存反馈信息
     *
     * @param ruleSystemConsultFeedbackVO 建议反馈信息
     * @return 是否成功
     */
    boolean feedback(RuleSystemConsultFeedbackVO ruleSystemConsultFeedbackVO);

}